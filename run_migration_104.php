<?php
/**
 * Run Migration 104: Add year column to user_monthly_retrocession_amounts
 * 
 * This migration adds year support to the retrocession amounts table,
 * allowing different configurations for different years.
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

echo "=== Running Migration 104: Add Year to Retrocession Amounts ===\n\n";

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connected successfully\n";
} catch (Exception $e) {
    die("✗ Database connection failed: " . $e->getMessage() . "\n");
}

// Check if year column already exists
$stmt = $pdo->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
if ($stmt->rowCount() > 0) {
    echo "ℹ Year column already exists in user_monthly_retrocession_amounts table\n";
    exit(0);
}

echo "\nMigration will:\n";
echo "1. Add 'year' column to user_monthly_retrocession_amounts\n";
echo "2. Set default year to 2025 for existing records\n";
echo "3. Update unique constraint to include year\n";
echo "4. Add indexes for better performance\n";

echo "\nDo you want to continue? (yes/no): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
if (trim($line) != 'yes') {
    echo "Migration cancelled.\n";
    exit(0);
}
fclose($handle);

// Begin transaction
$pdo->beginTransaction();

try {
    // Read and execute migration SQL
    $sql = file_get_contents(__DIR__ . '/database/migrations/104_add_year_to_retrocession_amounts.sql');
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)), 'strlen');
    
    foreach ($statements as $statement) {
        if (strpos($statement, '--') !== 0 && !empty($statement)) {
            echo "\nExecuting: " . substr($statement, 0, 60) . "...\n";
            $pdo->exec($statement);
            echo "✓ Success\n";
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo "\n✓ Migration 104 completed successfully!\n";
    
    // Show current data
    echo "\nCurrent retrocession amounts with years:\n";
    $stmt = $pdo->query("
        SELECT u.first_name, u.last_name, uma.month, uma.year, uma.cns_amount, uma.patient_amount
        FROM user_monthly_retrocession_amounts uma
        JOIN users u ON u.id = uma.user_id
        ORDER BY u.id, uma.year, uma.month
        LIMIT 10
    ");
    
    echo "\nUser | Month | Year | CNS Amount | Patient Amount\n";
    echo "------|-------|------|------------|---------------\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        printf("%-20s | %2d | %4d | %10.2f | %10.2f\n", 
            $row['first_name'] . ' ' . $row['last_name'],
            $row['month'],
            $row['year'],
            $row['cns_amount'],
            $row['patient_amount']
        );
    }
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "\n✗ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n\nNext steps:\n";
echo "1. Update the user interface to allow year selection when configuring amounts\n";
echo "2. Test the invoice generation for different years\n";
echo "3. Consider copying 2025 data to 2026 if rates remain the same\n";
?>