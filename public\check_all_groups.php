<?php
// Check all groups and their members

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>All Groups and Members</h1>";

// Get all groups with member counts
$stmt = $db->query("
    SELECT g.*, COUNT(DISTINCT ugm.user_id) as member_count,
           COUNT(DISTINCT CASE WHEN u.course_name IS NOT NULL AND u.course_name != '' THEN u.id END) as coaches_count
    FROM user_groups g
    LEFT JOIN user_group_members ugm ON g.id = ugm.group_id
    LEFT JOIN users u ON ugm.user_id = u.id AND u.is_active = 1 AND u.can_be_invoiced = 1
    GROUP BY g.id
    ORDER BY g.id
");
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5' style='width: 100%;'>";
echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Total Members</th><th>Members with Courses</th><th>Action</th></tr>";
foreach ($groups as $group) {
    $rowClass = $group['coaches_count'] > 0 ? 'style="background-color: #e8f5e9;"' : '';
    echo "<tr $rowClass>";
    echo "<td>{$group['id']}</td>";
    echo "<td><strong>{$group['name']}</strong></td>";
    echo "<td>{$group['description']}</td>";
    echo "<td>{$group['member_count']}</td>";
    echo "<td>" . ($group['coaches_count'] > 0 ? "<strong>{$group['coaches_count']} coaches</strong>" : "0") . "</td>";
    echo "<td>";
    if ($group['coaches_count'] > 0) {
        echo "<button onclick='setAsCoachGroup({$group['id']})'>Set as Coach Group</button>";
    }
    echo "</td>";
    echo "</tr>";
    
    // Show members with courses for this group
    if ($group['coaches_count'] > 0) {
        $stmt2 = $db->prepare("
            SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, u.course_name
            FROM users u
            JOIN user_group_members ugm ON u.id = ugm.user_id
            WHERE ugm.group_id = ?
            AND u.course_name IS NOT NULL AND u.course_name != ''
            AND u.is_active = 1 AND u.can_be_invoiced = 1
            ORDER BY u.first_name, u.last_name
        ");
        $stmt2->execute([$group['id']]);
        $coaches = $stmt2->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<tr><td colspan='6' style='padding-left: 40px; background-color: #f5f5f5;'>";
        echo "<strong>Coaches in this group:</strong><br>";
        foreach ($coaches as $coach) {
            echo "• {$coach['name']} ({$coach['username']}) - Course: {$coach['course_name']}<br>";
        }
        echo "</td></tr>";
    }
}
echo "</table>";

// Check current configuration
echo "<h2>Current Configuration:</h2>";
$coachGroupId = 2; // Default
// First check if config_settings table exists and has the right columns
try {
    $stmt = $db->prepare("SHOW COLUMNS FROM config_settings");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Check for different possible column names
    $nameColumn = 'name';
    if (in_array('key', $columns)) {
        $nameColumn = 'key';
    } elseif (in_array('setting_name', $columns)) {
        $nameColumn = 'setting_name';
    } elseif (in_array('config_key', $columns)) {
        $nameColumn = 'config_key';
    }
    
    $stmt = $db->prepare("SELECT value FROM config_settings WHERE $nameColumn = 'coach_group_id'");
    $stmt->execute();
    $configValue = $stmt->fetchColumn();
} catch (Exception $e) {
    // If config_settings doesn't exist, try config table
    try {
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'coach_group_id'");
        $stmt->execute();
        $configValue = $stmt->fetchColumn();
    } catch (Exception $e2) {
        $configValue = false;
    }
}
if ($configValue !== false) {
    $coachGroupId = intval($configValue);
    echo "<p>✅ coach_group_id is configured: <strong>$coachGroupId</strong></p>";
} else {
    echo "<p>❌ coach_group_id is not configured (using default: 2)</p>";
}

// Check if configured group exists
$stmt = $db->prepare("SELECT name FROM user_groups WHERE id = ?");
$stmt->execute([$coachGroupId]);
$groupName = $stmt->fetchColumn();
if ($groupName) {
    echo "<p>Configured coach group exists: <strong>$groupName</strong> (ID: $coachGroupId)</p>";
} else {
    echo "<p style='color: red;'>⚠️ Configured coach group (ID: $coachGroupId) does not exist!</p>";
}

// Recommendation
echo "<h2>Recommendation:</h2>";
$recommendedGroup = null;
foreach ($groups as $group) {
    if ($group['coaches_count'] > 0 && !$recommendedGroup) {
        $recommendedGroup = $group;
    }
}

if ($recommendedGroup) {
    echo "<p>Group <strong>{$recommendedGroup['name']}</strong> (ID: {$recommendedGroup['id']}) has {$recommendedGroup['coaches_count']} users with courses.</p>";
    echo "<p>You should set coach_group_id = {$recommendedGroup['id']} in the configuration.</p>";
    echo "<form method='post' action='update_coach_group_config.php'>";
    echo "<input type='hidden' name='group_id' value='{$recommendedGroup['id']}'>";
    echo "<button type='submit' style='padding: 10px 20px; background: #4CAF50; color: white; border: none; cursor: pointer;'>";
    echo "Update Coach Group to {$recommendedGroup['name']} (ID: {$recommendedGroup['id']})";
    echo "</button>";
    echo "</form>";
}

?>

<script>
function setAsCoachGroup(groupId) {
    if (confirm('Set group ' + groupId + ' as the coach group?')) {
        window.location.href = 'update_coach_group_config.php?group_id=' + groupId;
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; }
th { background: #e0e0e0; }
td, th { padding: 8px; }
button { padding: 5px 10px; cursor: pointer; }
</style>

<hr>
<p><a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a></p>