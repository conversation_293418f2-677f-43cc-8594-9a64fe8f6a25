<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

$db = Flight::db();

echo "<h2>Email Templates Initialization</h2>";

try {
    // Check if table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() == 0) {
        // Create the table
        echo "<p>Creating email_templates table...</p>";
        
        $createTableSQL = "
CREATE TABLE IF NOT EXISTS `email_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `code` varchar(50) NOT NULL,
    `email_type` varchar(50) NOT NULL,
    `invoice_type` varchar(50) DEFAULT NULL,
    `subject` varchar(255) NOT NULL,
    `body_html` text NOT NULL,
    `body_text` text NOT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `priority` int(11) DEFAULT 100,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_unique_code` (`code`),
    KEY `idx_email_templates_type` (`email_type`),
    KEY `idx_email_templates_invoice_type` (`invoice_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ";
        
        $db->exec($createTableSQL);
        echo "<p>✓ Created email_templates table</p>";
    }
    
    // Check if email_logs table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
    if ($stmt->rowCount() == 0) {
        echo "<p>Creating email_logs table...</p>";
        
        $createLogsTable = "
CREATE TABLE IF NOT EXISTS `email_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `invoice_id` int(11) DEFAULT NULL,
    `template_id` int(11) DEFAULT NULL,
    `recipient_email` varchar(255) NOT NULL,
    `subject` varchar(255) DEFAULT NULL,
    `status` enum('sent','failed') NOT NULL,
    `sent_at` timestamp NULL DEFAULT NULL,
    `error_message` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_email_logs_invoice` (`invoice_id`),
    KEY `idx_email_logs_template` (`template_id`),
    CONSTRAINT `fk_email_logs_invoice` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_email_logs_template` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ";
        
        $db->exec($createLogsTable);
        echo "<p>✓ Created email_logs table</p>";
    }
    
    // Check if templates exist
    $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        echo "<p>Adding default email templates...</p>";
        
        // Default templates
        $templates = [
            [
                'name' => 'Nouvelle facture - Standard',
                'code' => 'new_invoice_standard',
                'email_type' => 'new_invoice',
                'invoice_type' => null,
                'subject' => 'Facture {INVOICE_NUMBER} - {COMPANY_NAME}',
                'body_html' => '<div style="font-family: Arial, sans-serif; color: #333;">
<p>Bonjour {CLIENT_NAME},</p>

<p>Nous avons le plaisir de vous transmettre votre facture.</p>

<p><strong>Détails de la facture :</strong></p>
<ul style="list-style: none; padding-left: 0;">
    <li>Numéro de facture : <strong>{INVOICE_NUMBER}</strong></li>
    <li>Date d\'émission : <strong>{ISSUE_DATE}</strong></li>
    <li>Montant total : <strong>{TOTAL_AMOUNT} €</strong></li>
    <li>Date d\'échéance : <strong>{DUE_DATE}</strong></li>
</ul>

<p>Vous trouverez la facture détaillée en pièce jointe de cet email.</p>

<p>Pour toute question concernant cette facture, n\'hésitez pas à nous contacter.</p>

<p>Cordialement,<br>
<strong>{COMPANY_NAME}</strong><br>
{COMPANY_EMAIL}<br>
{COMPANY_PHONE}</p>
</div>',
                'body_text' => 'Bonjour {CLIENT_NAME},

Nous avons le plaisir de vous transmettre votre facture.

Détails de la facture :
- Numéro de facture : {INVOICE_NUMBER}
- Date d\'émission : {ISSUE_DATE}
- Montant total : {TOTAL_AMOUNT} €
- Date d\'échéance : {DUE_DATE}

Vous trouverez la facture détaillée en pièce jointe de cet email.

Pour toute question concernant cette facture, n\'hésitez pas à nous contacter.

Cordialement,
{COMPANY_NAME}
{COMPANY_EMAIL}
{COMPANY_PHONE}',
                'is_active' => 1,
                'priority' => 100
            ],
            [
                'name' => 'Nouvelle facture - Location (LOY/DIV)',
                'code' => 'new_invoice_rental',
                'email_type' => 'new_invoice',
                'invoice_type' => 'rental',
                'subject' => 'Facture {INVOICE_NUMBER} - {SUBJECT} - {MONTH_NAME} {YEAR}',
                'body_html' => '<div style="font-family: Arial, sans-serif; color: #333;">
<p>Bonjour {CLIENT_NAME},</p>

<p>Veuillez trouver ci-joint votre facture pour <strong>{MONTH_NAME} {YEAR}</strong>.</p>

<p><strong>Détails de la facture :</strong></p>
<ul style="list-style: none; padding-left: 0;">
    <li>Numéro : <strong>{INVOICE_NUMBER}</strong></li>
    <li>Objet : <strong>{SUBJECT}</strong></li>
    <li>Période : <strong>{PERIOD}</strong></li>
    <li>Montant total TTC : <strong>{TOTAL_AMOUNT} €</strong></li>
    <li>Date d\'échéance : <strong>{DUE_DATE}</strong></li>
</ul>

<p>La facture détaillée est disponible en pièce jointe.</p>

<p>Cordialement,<br>
<strong>{COMPANY_NAME}</strong></p>
</div>',
                'body_text' => 'Bonjour {CLIENT_NAME},

Veuillez trouver ci-joint votre facture pour {MONTH_NAME} {YEAR}.

Détails de la facture :
- Numéro : {INVOICE_NUMBER}
- Objet : {SUBJECT}
- Période : {PERIOD}
- Montant total TTC : {TOTAL_AMOUNT} €
- Date d\'échéance : {DUE_DATE}

La facture détaillée est disponible en pièce jointe.

Cordialement,
{COMPANY_NAME}',
                'is_active' => 1,
                'priority' => 110
            ],
            [
                'name' => 'Nouvelle facture - Rétrocession 30%',
                'code' => 'new_invoice_retrocession_30',
                'email_type' => 'new_invoice',
                'invoice_type' => 'retrocession_30',
                'subject' => 'Facture de rétrocession {INVOICE_NUMBER} - {MONTH_NAME} {YEAR}',
                'body_html' => '<div style="font-family: Arial, sans-serif; color: #333;">
<p>Bonjour {PRACTITIONER_NAME},</p>

<p>Veuillez trouver ci-joint votre facture de rétrocession pour le mois de <strong>{MONTH_NAME} {YEAR}</strong>.</p>

<p><strong>Détails de la rétrocession :</strong></p>
<ul style="list-style: none; padding-left: 0;">
    <li>Numéro de facture : <strong>{INVOICE_NUMBER}</strong></li>
    <li>Montant CNS : <strong>{CNS_AMOUNT} €</strong></li>
    <li>Montant Patients : <strong>{PATIENT_AMOUNT} €</strong></li>
    <li>Frais de secrétariat TVAC (17%) : <strong>{SECRETARIAT_AMOUNT} €</strong></li>
    <li><strong>Total à payer : {TOTAL_AMOUNT} €</strong></li>
</ul>

<p>Date d\'échéance : <strong>{DUE_DATE}</strong></p>

<p>La facture détaillée est disponible en pièce jointe.</p>

<p>Cordialement,<br>
<strong>{COMPANY_NAME}</strong></p>
</div>',
                'body_text' => 'Bonjour {PRACTITIONER_NAME},

Veuillez trouver ci-joint votre facture de rétrocession pour le mois de {MONTH_NAME} {YEAR}.

Détails de la rétrocession :
- Numéro de facture : {INVOICE_NUMBER}
- Montant CNS : {CNS_AMOUNT} €
- Montant Patients : {PATIENT_AMOUNT} €
- Frais de secrétariat TVAC (17%) : {SECRETARIAT_AMOUNT} €
- Total à payer : {TOTAL_AMOUNT} €

Date d\'échéance : {DUE_DATE}

La facture détaillée est disponible en pièce jointe.

Cordialement,
{COMPANY_NAME}',
                'is_active' => 1,
                'priority' => 120
            ],
            [
                'name' => 'Rappel de paiement - Niveau 1',
                'code' => 'reminder_1',
                'email_type' => 'reminder',
                'invoice_type' => null,
                'subject' => 'Rappel - Facture {INVOICE_NUMBER} en attente de paiement',
                'body_html' => '<div style="font-family: Arial, sans-serif; color: #333;">
<p>Bonjour {CLIENT_NAME},</p>

<p>Nous vous rappelons que la facture <strong>{INVOICE_NUMBER}</strong> d\'un montant de <strong>{TOTAL_AMOUNT} €</strong> est en attente de paiement.</p>

<p>Cette facture était due le <strong>{DUE_DATE}</strong> (retard de {DAYS_OVERDUE} jours).</p>

<p>Nous vous remercions de bien vouloir régulariser cette situation dans les meilleurs délais.</p>

<p>Si vous avez déjà effectué ce paiement, veuillez ignorer ce rappel.</p>

<p>Cordialement,<br>
<strong>{COMPANY_NAME}</strong></p>
</div>',
                'body_text' => 'Bonjour {CLIENT_NAME},

Nous vous rappelons que la facture {INVOICE_NUMBER} d\'un montant de {TOTAL_AMOUNT} € est en attente de paiement.

Cette facture était due le {DUE_DATE} (retard de {DAYS_OVERDUE} jours).

Nous vous remercions de bien vouloir régulariser cette situation dans les meilleurs délais.

Si vous avez déjà effectué ce paiement, veuillez ignorer ce rappel.

Cordialement,
{COMPANY_NAME}',
                'is_active' => 1,
                'priority' => 100
            ]
        ];
        
        // Insert templates
        $insertStmt = $db->prepare("
            INSERT INTO email_templates (
                name, code, email_type, invoice_type,
                subject, body_html, body_text,
                is_active, priority
            ) VALUES (
                :name, :code, :email_type, :invoice_type,
                :subject, :body_html, :body_text,
                :is_active, :priority
            )
        ");
        
        $inserted = 0;
        foreach ($templates as $template) {
            try {
                $insertStmt->execute($template);
                $inserted++;
                echo "<p>✓ Added template: {$template['name']}</p>";
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) { // Duplicate entry
                    echo "<p>⚠️ Template already exists: {$template['name']}</p>";
                } else {
                    echo "<p>❌ Error adding template '{$template['name']}': " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<p><strong>Added $inserted email templates</strong></p>";
    } else {
        echo "<p>Email templates already exist ({$result['count']} templates found)</p>";
        
        // Show existing templates
        $stmt = $db->query("SELECT * FROM email_templates ORDER BY email_type, priority DESC");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Existing Templates:</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Type</th><th>Invoice Type</th><th>Active</th></tr>";
        
        foreach ($templates as $t) {
            $active = $t['is_active'] ? '✓' : '✗';
            $invoiceType = $t['invoice_type'] ?: 'All';
            echo "<tr>";
            echo "<td>{$t['id']}</td>";
            echo "<td>{$t['name']}</td>";
            echo "<td>{$t['code']}</td>";
            echo "<td>{$t['email_type']}</td>";
            echo "<td>{$invoiceType}</td>";
            echo "<td style='text-align:center'>$active</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Add company config if missing
    echo "<h3>Company Configuration</h3>";
    
    $configs = [
        'company_name' => 'Fit360 AdminDesk',
        'company_email' => '<EMAIL>',
        'company_phone' => '+352 26 12 34 56'
    ];
    
    foreach ($configs as $key => $defaultValue) {
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        $value = $stmt->fetch(PDO::FETCH_COLUMN);
        
        if (!$value) {
            $stmt = $db->prepare("INSERT INTO config (`key`, `value`) VALUES (?, ?)");
            $stmt->execute([$key, $defaultValue]);
            echo "<p>✓ Added config: $key = $defaultValue</p>";
        } else {
            echo "<p>Config $key = $value</p>";
        }
    }
    
    echo "<hr>";
    echo "<p><strong>Email system is now ready!</strong></p>";
    echo "<p><a href='/fit/public/invoices'>Back to Invoices</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}