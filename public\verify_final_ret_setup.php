<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Final RET Types Verification</h2>";
    
    // Check config_invoice_types
    echo "<h3>config_invoice_types table:</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30') OR prefix LIKE '%RET%'
        ORDER BY code
    ");
    $configTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Active</th></tr>";
    foreach ($configTypes as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        $highlight = in_array($row['code'], ['ret25', 'ret30']) ? "style='background-color: #d4edda;'" : "";
        echo "<tr $highlight>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check invoice_types
    echo "<h3>invoice_types table:</h3>";
    $stmt = $db->query("
        SELECT * FROM invoice_types 
        WHERE code IN ('RET', 'RET25', 'RET30')
        ORDER BY code
    ");
    $invoiceTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>";
    foreach ($invoiceTypes as $row) {
        $highlight = in_array($row['code'], ['RET25', 'RET30']) ? "style='background-color: #d4edda;'" : "";
        echo "<tr $highlight>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td>{$row['name']}</td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test the logic
    echo "<h3>Testing Invoice Type Selection Logic:</h3>";
    
    // Simulate Frank Huet (5% secretary)
    echo "<p><strong>Frank Huet (5% secretary):</strong></p>";
    $stmt = $db->prepare("
        SELECT secretary_value FROM user_retrocession_settings 
        WHERE user_id = 1 
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    $secretaryValue = $settings['secretary_value'] ?? 10;
    
    echo "<ul>";
    echo "<li>Secretary percentage: {$secretaryValue}%</li>";
    echo "<li>Selected code: " . ($secretaryValue == 5 ? 'ret25' : 'ret30') . "</li>";
    
    // Get the invoice type that would be used
    $selectedCode = $secretaryValue == 5 ? 'ret25' : 'ret30';
    $stmt = $db->prepare("
        SELECT * FROM config_invoice_types 
        WHERE code = :code
    ");
    $stmt->execute(['code' => $selectedCode]);
    $selectedType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($selectedType) {
        echo "<li>Invoice prefix: <strong>{$selectedType['prefix']}</strong></li>";
        echo "<li>Expected invoice number: <strong>{$selectedType['prefix']}-2025-0001</strong></li>";
    } else {
        echo "<li style='color: red;'>ERROR: Type not found!</li>";
    }
    echo "</ul>";
    
    // Check config value
    echo "<h3>Config Table:</h3>";
    $stmt = $db->prepare("SELECT * FROM config WHERE `key` = 'ret_invoice_type'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>ret_invoice_type = " . ($config ? "<strong>{$config['value']}</strong>" : "<em>not set</em>") . "</p>";
    
    echo "<hr>";
    echo "<h3>Summary:</h3>";
    
    $ret25Exists = false;
    $ret30Exists = false;
    foreach ($configTypes as $type) {
        if ($type['code'] == 'ret25' && $type['prefix'] == 'FAC-RET25') $ret25Exists = true;
        if ($type['code'] == 'ret30' && $type['prefix'] == 'FAC-RET30') $ret30Exists = true;
    }
    
    if ($ret25Exists && $ret30Exists) {
        echo "<p style='color: green; font-size: 1.2em;'>✓ Configuration is correct! The system is ready to generate:</p>";
        echo "<ul>";
        echo "<li><strong>FAC-RET25-2025-XXXX</strong> for 5% secretary fee</li>";
        echo "<li><strong>FAC-RET30-2025-XXXX</strong> for 10% secretary fee</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>⚠️ Configuration incomplete:</p>";
        if (!$ret25Exists) echo "<p>- Missing ret25 with FAC-RET25 prefix</p>";
        if (!$ret30Exists) echo "<p>- Missing ret30 with FAC-RET30 prefix</p>";
    }
    
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession' class='btn btn-primary'>Test Bulk Generation</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}