<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h3>Users Table Structure:</h3>\n";
    $stmt = $pdo->query("DESCRIBE users");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "{$row['Field']} - {$row['Type']}\n";
    }
    
    echo "\n<h3>User-Client Relationship:</h3>\n";
    // Check if users have a link to clients
    $stmt = $pdo->query("
        SELECT u.id, u.first_name, u.last_name, c.id as client_id, c.name, c.is_practitioner
        FROM users u
        LEFT JOIN clients c ON c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name
        WHERE u.id IN (1, 10, 12, 18)
        ORDER BY u.id
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "User #{$row['id']} {$row['first_name']} {$row['last_name']} -> ";
        if ($row['client_id']) {
            echo "Client #{$row['client_id']} {$row['name']} (Practitioner: " . ($row['is_practitioner'] ? 'Yes' : 'No') . ")\n";
        } else {
            echo "No matching client found\n";
        }
    }
    
    echo "\n<h3>Invoice Types:</h3>\n";
    $stmt = $pdo->query("SELECT * FROM invoice_types WHERE code IN ('RET', 'RENT', 'LOY')");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "Code: {$row['code']}, Name: {$row['name']}, ID: {$row['id']}\n";
    }
    
    echo "\n<h3>Recent Retrocession Generation Attempts:</h3>\n";
    // Check logs if they exist
    $stmt = $pdo->query("SHOW TABLES LIKE 'logs'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("
            SELECT * FROM logs 
            WHERE message LIKE '%retrocession%' OR message LIKE '%generateRetrocession%'
            ORDER BY id DESC LIMIT 5
        ");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "{$row['created_at']} - {$row['level']} - {$row['message']}\n";
        }
    } else {
        // Check PHP error log
        $errorLog = ini_get('error_log');
        if ($errorLog && file_exists($errorLog)) {
            $lines = file($errorLog);
            $recentLines = array_slice($lines, -20);
            foreach ($recentLines as $line) {
                if (stripos($line, 'retrocession') !== false || stripos($line, 'UnifiedInvoiceGenerator') !== false) {
                    echo $line;
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}