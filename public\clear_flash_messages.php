<?php
/**
 * Clear Flash Messages
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

// Session is already started in bootstrap

echo "<h1>Clear Flash Messages</h1>";
echo "<pre>";

// Show current flash messages
if (isset($_SESSION['flash_messages'])) {
    echo "Current flash messages count: " . count($_SESSION['flash_messages']) . "\n\n";
    
    // Group messages by type
    $grouped = [];
    foreach ($_SESSION['flash_messages'] as $msg) {
        $grouped[$msg['type']][] = $msg['message'];
    }
    
    foreach ($grouped as $type => $messages) {
        echo strtoupper($type) . ":\n";
        $counts = array_count_values($messages);
        foreach ($counts as $message => $count) {
            echo "  - $message";
            if ($count > 1) {
                echo " (x$count)";
            }
            echo "\n";
        }
        echo "\n";
    }
    
    // Clear them
    if (isset($_GET['clear']) && $_GET['clear'] === 'yes') {
        unset($_SESSION['flash_messages']);
        echo "\n✓ Flash messages cleared!\n";
        echo '<meta http-equiv="refresh" content="2;url=clear_flash_messages.php">';
    } else {
        echo '<a href="?clear=yes" onclick="return confirm(\'Clear all flash messages?\')">Click here to clear all flash messages</a>';
    }
} else {
    echo "No flash messages in session.\n";
}

echo "</pre>";

echo "<hr>";
echo '<p><a href="/fit/public/invoices">Go to Invoices</a></p>';
?>