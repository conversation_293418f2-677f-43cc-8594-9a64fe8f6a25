<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\UserCourse;
use App\Models\UserRetrocessionSetting;
use Flight;

class UserController extends Controller
{
    /**
     * Display users list
     */
    public function index(Request $request, Response $response)
    {
        // Get all users with their groups
        $allUsers = User::getAllWithGroups();
        
        // Get all active groups with member counts
        $groups = UserGroup::getAllWithCounts();
        
        // Organize users by group
        $usersByGroup = [];
        $noGroupUsers = [];
        
        foreach ($allUsers as $user) {
            if (!empty($user['group_ids'])) {
                $groupIds = explode(',', $user['group_ids']);
                foreach ($groupIds as $groupId) {
                    if (!isset($usersByGroup[$groupId])) {
                        $usersByGroup[$groupId] = [];
                    }
                    $usersByGroup[$groupId][] = $user;
                }
            } else {
                $noGroupUsers[] = $user;
            }
        }
        
        // Calculate statistics
        $statistics = [
            'total_users' => count($allUsers),
            'active_users' => count(array_filter($allUsers, function($u) { return $u['is_active'] == 1; })),
            'total_groups' => count($groups),
            'online_users' => 0 // You can implement this based on your session tracking
        ];
        
        // Get current user's groups
        $currentUserId = $_SESSION['user_id'] ?? null;
        $currentUserGroupId = null;
        
        if ($currentUserId) {
            $userGroups = User::getWithGroups($currentUserId);
            if ($userGroups && !empty($userGroups['groups'])) {
                // Check if user is an administrator
                $isAdmin = false;
                $adminGroupId = null;
                
                foreach ($userGroups['groups'] as $group) {
                    if ($group['name'] === 'Administrators') {
                        $isAdmin = true;
                        $adminGroupId = $group['id'];
                        break;
                    }
                }
                
                // If user is admin, use admin group, otherwise use first group
                if ($isAdmin) {
                    $currentUserGroupId = intval($adminGroupId);
                } else {
                    $currentUserGroupId = intval($userGroups['groups'][0]['id']);
                }
            }
        }
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'users/index-modern';
        
        return $this->render($viewName, [
            'title' => __('users.user_management'),
            'users' => $allUsers,
            'groups' => $groups,
            'usersByGroup' => $usersByGroup,
            'noGroupUsers' => $noGroupUsers,
            'statistics' => $statistics,
            'currentUserGroupId' => $currentUserGroupId
        ]);
    }
    
    /**
     * Show create user form
     */
    public function create(Request $request, Response $response)
    {
        $groups = UserGroup::getAll();
        
        $template = $this->getTemplate();
        $viewName = 'users/form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.create_user'),
            'user' => null,
            'groups' => $groups,
            'userGroups' => []
        ]);
    }
    
    /**
     * Store new user
     */
    public function store(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            $data = $_POST;
            
            // Debug: Log the incoming data
            error_log('User create - Raw POST data: ' . json_encode($data));
            
            // Validate required fields (username will be generated if empty)
            $required = ['email', 'password', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Generate username if not provided
            if (empty($data['username'])) {
                // Clean the first name
                $cleanFirstName = strtolower($data['first_name']);
                // Remove accents
                $cleanFirstName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanFirstName);
                // Remove special characters and spaces
                $cleanFirstName = preg_replace('/[^a-z0-9]/', '', $cleanFirstName);
                
                // Clean the last name for potential use
                $cleanLastName = strtolower($data['last_name']);
                $cleanLastName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanLastName);
                $cleanLastName = preg_replace('/[^a-z0-9]/', '', $cleanLastName);
                
                if (!empty($cleanFirstName)) {
                    // Try first name only
                    $username = $cleanFirstName;
                    
                    // Check if username exists
                    if (User::findByUsername($username)) {
                        // Try first name + first letter of last name
                        if (!empty($cleanLastName)) {
                            $username = $cleanFirstName . substr($cleanLastName, 0, 1);
                            
                            // If still exists, add numbers
                            $counter = 2;
                            $baseUsername = $username;
                            while (User::findByUsername($username)) {
                                $username = $baseUsername . $counter;
                                $counter++;
                            }
                        } else {
                            // No last name, just add numbers
                            $counter = 2;
                            while (User::findByUsername($username)) {
                                $username = $cleanFirstName . $counter;
                                $counter++;
                            }
                        }
                    }
                    
                    $data['username'] = $username;
                }
            }
            
            // Validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new \Exception(__('users.username_format'));
            }
            
            // Validate username length
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new \Exception(__('users.username_length'));
            }
            
            // Generate default email if not provided or not @fit-360.lu
            if (empty($data['email']) || !str_ends_with($data['email'], '@fit-360.lu')) {
                // Clean the first name
                $cleanName = strtolower($data['first_name']);
                // Remove accents
                $cleanName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanName);
                // Remove special characters and spaces
                $cleanName = preg_replace('/[^a-z0-9]/', '', $cleanName);
                
                if (!empty($cleanName)) {
                    // Check for uniqueness
                    $baseEmail = $cleanName . '@fit-360.lu';
                    $email = $baseEmail;
                    $counter = 1;
                    
                    while (User::findByEmail($email)) {
                        $counter++;
                        $email = $cleanName . $counter . '@fit-360.lu';
                    }
                    
                    $data['email'] = $email;
                }
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('users.invalid_email'));
            }
            
            // Validate password length
            if (strlen($data['password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Set defaults only if the fields are truly empty
            $data['language'] = (!empty($data['language'])) ? $data['language'] : 'fr';
            $data['timezone'] = (!empty($data['timezone'])) ? $data['timezone'] : 'Europe/Luxembourg';
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;
            
            // Only set address defaults if the fields are not provided or truly empty
            if (!isset($data['address']) || $data['address'] === '') {
                $data['address'] = '15, am Pëtz';
            }
            if (!isset($data['postal_code']) || $data['postal_code'] === '') {
                $data['postal_code'] = 'L-9579';
            }
            if (!isset($data['city']) || $data['city'] === '') {
                $data['city'] = 'Weidingen';
            }
            if (!isset($data['country']) || $data['country'] === '') {
                $data['country'] = 'LU';
            }
            
            // Debug: Log processed data
            error_log('User create - Processed data: ' . json_encode([
                'address' => $data['address'] ?? 'null',
                'postal_code' => $data['postal_code'] ?? 'null', 
                'city' => $data['city'] ?? 'null',
                'country' => $data['country'] ?? 'null'
            ]));
            
            // Handle avatar upload
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $data['avatar'] = $this->uploadAvatar($_FILES['avatar']);
            }
            
            // Create user
            $userId = User::create($data);
            
            // Assign groups
            if (!empty($data['groups'])) {
                User::syncGroups($userId, $data['groups']);
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.user_created'),
                'data' => ['id' => $userId]
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(Request $request, Response $response, $id)
    {
        $user = User::getWithGroups($id);
        
        if (!$user) {
            Flight::redirect(Flight::get('flight.base_url') . '/users');
            return;
        }
        
        $groups = UserGroup::getAll();
        $userGroupIds = array_column($user['groups'], 'id');
        
        // Get active retrocession settings for user
        try {
            $retrocessionSettings = \App\Models\UserRetrocessionSetting::getActiveSettingsForUser($id);
        } catch (\Exception $e) {
            // Table might not exist, set to null
            $retrocessionSettings = null;
        }
        
        // Get financial obligations for user
        $financialObligations = User::getCurrentFinancialObligations($id);
        $obligationsHistory = User::getFinancialObligationsHistory($id);
        
        // Check if current user can edit financial obligations
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $canEditFinancial = User::canEditFinancialObligations($currentUserId);
        
        // Check if user is a coach - check both group membership AND practitioner type
        $coachGroupId = (string)(Flight::get('coach_group_id') ?? 24);
        $isCoachByGroup = in_array($coachGroupId, $userGroupIds);
        
        // Also check if user is a coach based on client practitioner_type
        $isCoachByType = false;
        if (!empty($user['client_id'])) {
            $db = Flight::db();
            $stmt = $db->prepare("SELECT is_practitioner, practitioner_type FROM clients WHERE id = ?");
            $stmt->execute([$user['client_id']]);
            $client = $stmt->fetch(\PDO::FETCH_ASSOC);
            $isCoachByType = ($client && $client['is_practitioner'] == 1 && $client['practitioner_type'] == 'coach');
        }
        
        // User is coach if either condition is true
        $isCoach = $isCoachByGroup || $isCoachByType;
        $courses = [];
        
        // Debug logging (disabled - enable if needed)
        // error_log("=== UserController::edit Course Loading Debug ===");
        // error_log("User ID: " . $id);
        // error_log("Is Coach By Group: " . ($isCoachByGroup ? 'true' : 'false'));
        // error_log("Is Coach By Type: " . ($isCoachByType ? 'true' : 'false'));
        // error_log("Is Coach (combined): " . ($isCoach ? 'true' : 'false'));
        
        if ($isCoach) {
            $courses = UserCourse::getByUserId($id);
            // error_log("Courses loaded: " . count($courses) . " courses");
            // error_log("Courses data: " . json_encode($courses));
        } else {
            // error_log("Not loading courses - user is not a coach");
        }
        
        // Get monthly course counts if coach
        $monthlyCourseCountsRaw = [];
        $monthlyCourseInvoices = [];
        if ($isCoach) {
            try {
                $db = Flight::db();
                $currentYear = date('Y');
                
                // Get course counts
                $countsStmt = $db->prepare("
                    SELECT month, course_id, course_count, invoice_id
                    FROM user_monthly_course_counts
                    WHERE user_id = :user_id AND year = :year
                    ORDER BY month, course_id
                ");
                $countsStmt->execute([
                    'user_id' => $id,
                    'year' => $currentYear
                ]);
                
                // Organize by month and course
                while ($row = $countsStmt->fetch(\PDO::FETCH_ASSOC)) {
                    if (!isset($monthlyCourseCountsRaw[$row['month']])) {
                        $monthlyCourseCountsRaw[$row['month']] = [];
                    }
                    $monthlyCourseCountsRaw[$row['month']][$row['course_id']] = $row;
                }
                
                // Get existing course invoices for current year
                $invoiceStmt = $db->prepare("
                    SELECT DISTINCT mcc.month, i.id, i.invoice_number, i.status
                    FROM user_monthly_course_counts mcc
                    INNER JOIN invoices i ON i.id = mcc.invoice_id
                    WHERE mcc.user_id = :user_id AND mcc.year = :year
                    ORDER BY mcc.month
                ");
                $invoiceStmt->execute([
                    'user_id' => $id,
                    'year' => $currentYear
                ]);
                
                while ($invoice = $invoiceStmt->fetch(\PDO::FETCH_ASSOC)) {
                    $monthlyCourseInvoices[$invoice['month']] = $invoice;
                }
            } catch (\Exception $e) {
                error_log("Failed to get monthly course counts: " . $e->getMessage());
            }
        }
        
        // Check if user is in practitioner group (Kiné)
        $practitionerGroupId = (string)(Flight::get('practitioner_group_id') ?? 4);
        $isPractitioner = in_array($practitionerGroupId, $userGroupIds);
        
        // Check if current user can edit retrocession settings
        $canEditRetrocession = User::isAdmin($currentUserId) || User::isManager($currentUserId);
        
        // Get monthly retrocession amounts if practitioner
        $monthlyAmounts = [];
        $monthlyInvoices = [];
        if ($isPractitioner) {
            try {
                $db = Flight::db();
                $currentYear = date('Y');
                
                // Check if year column exists
                $yearColumnCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
                $hasYearColumn = $yearColumnCheck->rowCount() > 0;
                
                if ($hasYearColumn) {
                    $stmt = $db->prepare("
                        SELECT month, cns_amount, patient_amount, is_active 
                        FROM user_monthly_retrocession_amounts 
                        WHERE user_id = :user_id AND year = :year
                        ORDER BY month
                    ");
                    $stmt->execute(['user_id' => $id, 'year' => $currentYear]);
                } else {
                    $stmt = $db->prepare("
                        SELECT month, cns_amount, patient_amount, is_active 
                        FROM user_monthly_retrocession_amounts 
                        WHERE user_id = :user_id
                        ORDER BY month
                    ");
                    $stmt->execute(['user_id' => $id]);
                }
                $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                
                // Create indexed array by month
                foreach ($results as $row) {
                    $monthlyAmounts[$row['month']] = $row;
                }
                
                // Get existing retrocession invoices for current year using unified system
                $invoiceStmt = $db->prepare("
                    SELECT ugi.period_month, i.id, i.invoice_number, i.status
                    FROM user_generated_invoices ugi
                    INNER JOIN invoices i ON i.id = ugi.invoice_id
                    WHERE ugi.user_id = :user_id 
                    AND ugi.invoice_type = 'RET'
                    AND ugi.period_year = :year
                    ORDER BY ugi.period_month
                ");
                $invoiceStmt->execute([
                    'user_id' => $id,
                    'year' => $currentYear
                ]);
                while ($invoice = $invoiceStmt->fetch(\PDO::FETCH_ASSOC)) {
                    $monthlyInvoices[$invoice['period_month']] = $invoice;
                }
            } catch (\Exception $e) {
                // Table might not exist
                error_log("Failed to get monthly amounts: " . $e->getMessage());
            }
        }
        
        // Get Loyer invoices tracking data using unified system
        $monthlyLoyerInvoices = [];
        try {
            $currentYear = date('Y');
            $db = Flight::db();
            
            // Get Loyer invoices from unified tracking table
            $loyerStmt = $db->prepare("
                SELECT ugi.period_month, ugi.invoice_id, i.invoice_number, i.status, i.total_ttc
                FROM user_generated_invoices ugi
                INNER JOIN invoices i ON i.id = ugi.invoice_id
                WHERE ugi.user_id = :user_id 
                AND ugi.invoice_type = 'LOY'
                AND ugi.period_year = :year
                ORDER BY ugi.period_month
            ");
            $loyerStmt->execute([
                'user_id' => $id,
                'year' => $currentYear
            ]);
            
            while ($invoice = $loyerStmt->fetch(\PDO::FETCH_ASSOC)) {
                $monthlyLoyerInvoices[$invoice['period_month']] = $invoice;
            }
        } catch (\Exception $e) {
            error_log("Failed to get Loyer invoices: " . $e->getMessage());
        }
        
        $template = $this->getTemplate();
        $viewName = 'users/form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.edit_user'),
            'user' => $user,
            'groups' => $groups,
            'userGroups' => $userGroupIds,
            'retrocession_settings' => $retrocessionSettings,
            'financial_obligations' => $financialObligations,
            'obligations_history' => $obligationsHistory,
            'can_edit_financial' => $canEditFinancial,
            'currency' => 'EUR',
            'is_coach' => $isCoach,
            'courses' => $courses,
            'is_practitioner' => $isPractitioner,
            'can_edit_retrocession' => $canEditRetrocession,
            'monthly_amounts' => $monthlyAmounts,
            'monthly_invoices' => $monthlyInvoices,
            'monthly_course_counts' => $monthlyCourseCountsRaw,
            'monthly_course_invoices' => $monthlyCourseInvoices,
            'monthly_loyer_invoices' => $monthlyLoyerInvoices
        ]);
    }
    
    /**
     * Update user
     */
    public function update(Request $request, Response $response, $id)
    {
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
        }
        
        try {
            $data = $_POST;
            
            // Validate required fields
            $required = ['username', 'email', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new \Exception(__('users.username_format'));
            }
            
            // Validate username length
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new \Exception(__('users.username_length'));
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('users.invalid_email'));
            }
            
            // If password is provided, validate it
            if (!empty($data['password']) && strlen($data['password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Set defaults for update - don't override existing values with defaults
            $data['language'] = (!empty($data['language'])) ? $data['language'] : 'fr';
            $data['timezone'] = (!empty($data['timezone'])) ? $data['timezone'] : 'Europe/Luxembourg';
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;
            
            // For update, only set address fields if they are provided in the form
            // Don't set defaults as user might want to clear them
            
            // Handle avatar upload
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $data['avatar'] = $this->uploadAvatar($_FILES['avatar'], $id);
            }
            
            // Handle avatar removal
            if (isset($data['remove_avatar']) && $data['remove_avatar']) {
                $this->removeAvatar($id);
                $data['avatar'] = null;
            }
            
            // Update user
            User::update($id, $data);
            
            // Update groups
            $groups = $data['groups'] ?? [];
            User::syncGroups($id, $groups);
            
            // Handle retrocession settings if provided
            if (isset($data['retrocession']) && is_array($data['retrocession'])) {
                $retrocession = $data['retrocession'];
                
                // Validate retrocession data
                if (!empty($retrocession['valid_from'])) {
                    // First, end any existing active settings that would overlap
                    $validFrom = $retrocession['valid_from'];
                    $validFromDate = new \DateTime($validFrom);
                    $validFromDate->modify('-1 day');
                    $endDate = $validFromDate->format('Y-m-d');
                    
                    // End existing active settings
                    $db = \Flight::db();
                    $updateSql = "UPDATE user_retrocession_settings 
                                 SET valid_to = :end_date 
                                 WHERE user_id = :user_id 
                                 AND is_active = 1 
                                 AND (valid_to IS NULL OR valid_to >= :valid_from)";
                    $updateStmt = $db->prepare($updateSql);
                    $updateStmt->execute([
                        'end_date' => $endDate,
                        'user_id' => $id,
                        'valid_from' => $validFrom
                    ]);
                    
                    // Prepare retrocession data
                    $retrocessionData = [
                        'user_id' => $id,
                        'cns_type' => $retrocession['cns_type'] ?? 'percentage',
                        'cns_value' => floatval($retrocession['cns_value'] ?? 20),
                        'cns_label' => $retrocession['cns_label'] ?? 'RÉTROCESSION CNS',
                        'patient_type' => $retrocession['patient_type'] ?? 'percentage',
                        'patient_value' => floatval($retrocession['patient_value'] ?? 20),
                        'patient_label' => $retrocession['patient_label'] ?? 'RÉTROCESSION PATIENTS',
                        'secretary_type' => $retrocession['secretary_type'] ?? 'percentage',
                        'secretary_value' => floatval($retrocession['secretary_value'] ?? 10),
                        'secretary_label' => $retrocession['secretary_label'] ?? 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL',
                        'ceiling_enabled' => isset($retrocession['ceiling_enabled']) ? 1 : 0,
                        'ceiling_amount' => floatval($retrocession['ceiling_amount'] ?? 5000),
                        'valid_from' => $retrocession['valid_from'],
                        'valid_to' => $retrocession['valid_to'] ?: null,
                        'notes' => $retrocession['notes'] ?? null,
                        'created_by' => $_SESSION['user_id'],
                        'is_active' => 1
                    ];
                    
                    // Create new retrocession setting
                    \App\Models\UserRetrocessionSetting::create($retrocessionData);
                }
            }
            
            // Handle monthly retrocession amounts if provided
            if (isset($data['monthly_amounts']) && is_array($data['monthly_amounts'])) {
                $db = \Flight::db();
                
                // Delete existing monthly amounts
                $deleteStmt = $db->prepare("DELETE FROM user_monthly_retrocession_amounts WHERE user_id = :user_id");
                $deleteStmt->execute(['user_id' => $id]);
                
                // Insert new monthly amounts
                $insertStmt = $db->prepare("
                    INSERT INTO user_monthly_retrocession_amounts 
                    (user_id, month, cns_amount, patient_amount, is_active, created_by, created_at)
                    VALUES (:user_id, :month, :cns_amount, :patient_amount, :is_active, :created_by, NOW())
                ");
                
                foreach ($data['monthly_amounts'] as $month => $monthData) {
                    if (!is_numeric($month) || $month < 1 || $month > 12) {
                        continue;
                    }
                    
                    $insertStmt->execute([
                        'user_id' => $id,
                        'month' => $month,
                        'cns_amount' => floatval($monthData['cns_amount'] ?? 0),
                        'patient_amount' => floatval($monthData['patient_amount'] ?? 0),
                        'is_active' => isset($monthData['is_active']) ? 1 : 0,
                        'created_by' => $_SESSION['user_id']
                    ]);
                }
            }
            
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => __('users.user_updated')
                ]);
                exit;
            } else {
                // Set flash message and redirect back to edit page
                $_SESSION['flash']['success'] = __('users.user_updated');
                
                // Preserve active tab
                $activeTab = $data['active_tab'] ?? 'personal';
                $_SESSION['flash']['active_tab'] = $activeTab;
                
                // Redirect with hash for tab
                Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/edit#' . $activeTab);
                exit;
            }
        } catch (\Exception $e) {
            if ($isAjax) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                // Set flash error message and redirect back to edit form
                $_SESSION['flash']['error'] = $e->getMessage();
                
                // Preserve active tab
                $activeTab = $data['active_tab'] ?? 'personal';
                $_SESSION['flash']['active_tab'] = $activeTab;
                
                // Redirect with hash for tab
                Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/edit#' . $activeTab);
                exit;
            }
        }
    }
    
    /**
     * Delete user
     */
    public function delete(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Don't allow self-deletion
            if ($id == $_SESSION['user_id']) {
                throw new \Exception(__('users.cannot_delete_self'));
            }
            
            User::delete($id);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.user_deleted')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Toggle user active status
     */
    public function toggleActive(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Don't allow self-deactivation
            if ($id == $_SESSION['user_id']) {
                throw new \Exception(__('users.cannot_deactivate_self'));
            }
            
            User::toggleActive($id);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.status_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Show user profile
     */
    public function profile(Request $request, Response $response, $id = null)
    {
        // If no ID provided, show current user's profile
        if (!$id) {
            $id = $_SESSION['user_id'];
        }
        
        $user = User::getWithGroups($id);
        
        if (!$user) {
            Flight::redirect(Flight::get('flight.base_url') . '/users');
            return;
        }
        
        $permissions = User::getPermissions($id);
        
        // Organize permissions by category
        $permissionsByCategory = [];
        foreach ($permissions as $permission) {
            $category = $permission['category'];
            if (!isset($permissionsByCategory[$category])) {
                $permissionsByCategory[$category] = [];
            }
            $permissionsByCategory[$category][] = $permission;
        }
        
        // Check if current user can edit this profile
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $currentUser = User::find($currentUserId);
        $canEdit = false;
        
        if ($currentUser) {
            // Can edit if: own profile OR current user is admin/manager
            $canEdit = ($id == $currentUserId) || 
                       User::isAdmin($currentUserId) || 
                       User::isManager($currentUserId);
        }
        
        $template = $this->getTemplate();
        $viewName = 'users/profile-modern';
        
        return $this->render($viewName, [
            'title' => __('users.user_profile'),
            'user' => $user,
            'permissions' => $permissionsByCategory,
            'isOwnProfile' => ($id == $_SESSION['user_id']),
            'canEdit' => $canEdit,
            'currentUser' => $currentUser
        ]);
    }
    
    /**
     * Upload avatar
     */
    private function uploadAvatar($file, $userId = null)
    {
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new \Exception(__('users.invalid_file_type'));
        }
        
        // Validate file size (max 5MB)
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            throw new \Exception(__('users.file_size_exceeded'));
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = dirname(__DIR__, 2) . '/public/uploads/avatars';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('avatar_') . '.' . $extension;
        $uploadPath = $uploadDir . '/' . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // If updating, remove old avatar
            if ($userId) {
                $this->removeAvatar($userId);
            }
            
            // Return the web path
            return '{{ base_url }}/uploads/avatars/' . $filename;
        } else {
            throw new \Exception(__('users.upload_failed'));
        }
    }
    
    /**
     * Remove avatar
     */
    private function removeAvatar($userId)
    {
        $user = User::find($userId);
        if ($user && $user['avatar']) {
            $filePath = dirname(__DIR__, 2) . '/public' . str_replace('{{ base_url }}', '', $user['avatar']);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
    
    /**
     * Get retrocession history for a user
     */
    public function retrocessionHistory(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check if user exists
            $user = User::find($id);
            if (!$user) {
                throw new \Exception(__('users.user_not_found'));
            }
            
            // Get retrocession history
            try {
                $history = \App\Models\UserRetrocessionSetting::getAllForUser($id);
            } catch (\Exception $e) {
                $history = [];
            }
            
            echo json_encode([
                'success' => true,
                'history' => $history
            ]);
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Show financial obligations for a user
     */
    public function financialObligations(Request $request, Response $response, $id)
    {
        $user = User::find($id);
        if (!$user) {
            Flight::redirect('/users');
            return;
        }
        
        // Check if current user can edit
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $canEdit = User::canEditFinancialObligations($currentUserId);
        
        // Get current and historical obligations
        $currentObligations = User::getCurrentFinancialObligations($id);
        $obligationsHistory = User::getFinancialObligationsHistory($id);
        
        $template = $this->getTemplate();
        $viewName = 'users/financial-obligations-modern';
        
        return $this->render($viewName, [
            'title' => __('users.financial_obligations') . ' - ' . $user['first_name'] . ' ' . $user['last_name'],
            'user' => $user,
            'currentObligations' => $currentObligations,
            'obligationsHistory' => $obligationsHistory,
            'canEdit' => $canEdit,
            'currency' => 'EUR'
        ]);
    }
    
    /**
     * Update financial obligations for a user
     */
    public function updateFinancialObligations(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $currentUserId = $_SESSION['user_id'] ?? 0;
            
            // Check permissions
            if (!User::canEditFinancialObligations($currentUserId)) {
                throw new \Exception(__('users.unauthorized_financial_edit'));
            }
            
            $data = $_POST;
            
            // Validate required fields
            $required = ['rent_amount', 'charges_amount', 'effective_date'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || $data[$field] === '') {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Calculate total (rent + charges + secretary_tvac_17 only)
            $data['total_tvac'] = floatval($data['rent_amount']) + floatval($data['charges_amount']) + 
                                  floatval($data['secretary_tvac_17'] ?? 0);
            
            // Add created_by
            $data['created_by'] = $currentUserId;
            
            // Update obligations
            User::updateFinancialObligations($id, $data, $currentUserId);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.financial_obligations_updated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Reset password for a user (admin only)
     */
    public function resetPassword(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $currentUserId = $_SESSION['user_id'] ?? 0;
            $currentUser = User::find($currentUserId);
            
            // Check permissions - only admins and managers can reset passwords
            if (!$currentUser || (!User::isAdmin($currentUserId) && !User::isManager($currentUserId))) {
                throw new \Exception(__('users.unauthorized_password_reset'));
            }
            
            // Check if trying to reset own password
            if ($id == $currentUserId) {
                throw new \Exception(__('users.use_change_password_instead'));
            }
            
            $data = $_POST;
            
            // Validate new password
            if (empty($data['new_password'])) {
                throw new \Exception(__('users.new_password_required'));
            }
            
            if (strlen($data['new_password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Validate password confirmation
            if (empty($data['confirm_password'])) {
                throw new \Exception(__('users.confirm_password_required'));
            }
            
            if ($data['new_password'] !== $data['confirm_password']) {
                throw new \Exception(__('users.password_mismatch'));
            }
            
            // Get target user
            $targetUser = User::find($id);
            if (!$targetUser) {
                throw new \Exception(__('users.user_not_found'));
            }
            
            // Update password
            User::update($id, [
                'password' => $data['new_password']
            ]);
            
            // Send email notification if requested
            if (!empty($data['send_email']) && filter_var($targetUser['email'], FILTER_VALIDATE_EMAIL)) {
                // TODO: Implement email notification
                // For now, just log the action
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.password_reset_success')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update user preferences (language, theme, etc.)
     */
    public function updatePreferences(Request $request, Response $response)
    {
        try {
            $userId = $_SESSION['user_id'] ?? 0;
            if (!$userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = $_POST;
            
            // Only allow updating specific preference fields
            $allowedFields = ['language', 'timezone', 'theme', 'email_notifications'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            // Update user preferences
            User::update($userId, $updateData);
            
            // Update session if language or theme changed
            if (isset($updateData['language'])) {
                $_SESSION['user_language'] = $updateData['language'];
            }
            if (isset($updateData['theme'])) {
                $_SESSION['template'] = $updateData['theme'];
            }
            
            // Redirect back to profile with success message
            $_SESSION['flash']['success'] = __('users.preferences_updated');
            Flight::redirect(Flight::get('flight.base_url') . '/profile');
            
        } catch (\Exception $e) {
            $_SESSION['flash']['error'] = $e->getMessage();
            Flight::redirect(Flight::get('flight.base_url') . '/profile');
        }
    }
    
    /**
     * Get courses for a user (AJAX)
     */
    public function courses(Request $request, Response $response, $id)
    {
        try {
            $courses = UserCourse::getByUserId($id);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $courses
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Get retrocession settings for a user
     */
    public function retrocessionSettings(Request $request, Response $response, $id)
    {
        try {
            // Get active settings for the user
            $settings = UserRetrocessionSetting::getActiveSettingsForUser($id);
            
            // If no settings exist, return defaults
            if (!$settings) {
                $settings = [
                    'cns_type' => 'percentage',
                    'cns_value' => 20.00,
                    'patient_type' => 'percentage',
                    'patient_value' => 20.00,
                    'secretary_type' => 'percentage',
                    'secretary_value' => 10.00,
                    'is_default' => true
                ];
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $settings
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Get financial obligations for a user (API endpoint)
     */
    public function financialObligationsApi(Request $request, Response $response, $id)
    {
        try {
            // Get current financial obligations
            $obligations = User::getCurrentFinancialObligations($id);
            
            // Return all financial obligation data for invoice creation
            // Ensure numeric values are returned
            $data = [
                'rent_amount' => $obligations ? floatval($obligations['rent_amount']) : 0,
                'charges_amount' => $obligations ? floatval($obligations['charges_amount']) : 0,
                'secretary_tvac_17' => $obligations ? floatval($obligations['secretary_tvac_17']) : 0,
                'secretary_htva' => $obligations ? floatval($obligations['secretary_htva']) : 0,
                'tva_17' => $obligations ? floatval($obligations['tva_17']) : 0
            ];
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $data
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Get financial obligations by year
     */
    public function getFinancialObligationsByYear(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $year = $request->query->year ?? date('Y');
            
            // Get financial obligations for the specified year
            $db = Flight::db();
            
            // Get the obligation that was effective during the specified year
            $stmt = $db->prepare("
                SELECT * FROM user_financial_obligations 
                WHERE user_id = :user_id 
                AND YEAR(effective_date) <= :year
                AND (end_date IS NULL OR YEAR(end_date) >= :year2)
                ORDER BY effective_date DESC
                LIMIT 1
            ");
            
            $stmt->execute([
                'user_id' => $id,
                'year' => $year,
                'year2' => $year
            ]);
            
            $currentObligation = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            // Get all obligations history for the user
            $stmt = $db->prepare("
                SELECT * FROM user_financial_obligations 
                WHERE user_id = :user_id
                ORDER BY effective_date DESC
            ");
            $stmt->execute(['user_id' => $id]);
            $history = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'obligations' => $currentObligation,
                'history' => $history,
                'year' => $year
            ]);
            
        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Store a new course for a user
     */
    public function storeCourse(Request $request, Response $response, $id)
    {
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                error_log("CSRF token validation failed in storeCourse");
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            $data = $_POST;
            
            // Debug logging (disabled - enable if needed)
            // error_log("=== UserController::storeCourse Debug ===");
            // error_log("User ID: " . $id);
            // error_log("POST data: " . json_encode($data));
            
            // Validate required fields
            if (empty($data['course_name'])) {
                throw new \Exception(__('courses.name_required'));
            }
            
            if (!isset($data['hourly_rate']) || $data['hourly_rate'] === '') {
                throw new \Exception(__('courses.rate_required'));
            }
            
            // Ensure numeric values
            $data['hourly_rate'] = floatval($data['hourly_rate']);
            $data['vat_rate'] = isset($data['vat_rate']) ? floatval($data['vat_rate']) : 16.0;
            $data['user_id'] = $id;
            $data['is_active'] = isset($data['is_active']) ? 1 : 1; // Default to active
            
            // error_log("Prepared data: " . json_encode($data));
            
            $course = UserCourse::createCourse($data);
            
            // error_log("Course created successfully with ID: " . $course['id']);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.created_successfully'),
                'data' => $course
            ]);
            exit;
        } catch (\Exception $e) {
            error_log("Error in storeCourse: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update a course
     */
    public function updateCourse(Request $request, Response $response, $userId, $courseId)
    {
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                error_log("CSRF token validation failed in updateCourse");
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            $data = $_POST;
            
            // Validate ownership
            $course = UserCourse::find($courseId);
            if (!$course || $course->user_id != $userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            // Validate required fields
            if (empty($data['course_name'])) {
                throw new \Exception(__('courses.name_required'));
            }
            
            if (!isset($data['hourly_rate']) || $data['hourly_rate'] === '') {
                throw new \Exception(__('courses.rate_required'));
            }
            
            // Prepare update data
            $updateData = [
                'course_name' => $data['course_name'],
                'hourly_rate' => floatval($data['hourly_rate']),
                'vat_rate' => isset($data['vat_rate']) ? floatval($data['vat_rate']) : 16.0,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];
            
            UserCourse::updateCourse($courseId, $updateData);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.updated_successfully')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Delete a course
     */
    public function deleteCourse(Request $request, Response $response, $userId, $courseId)
    {
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                error_log("CSRF token validation failed in deleteCourse");
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            // Validate ownership
            $course = UserCourse::find($courseId);
            if (!$course || $course->user_id != $userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            UserCourse::deleteCourse($courseId);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.deleted_successfully')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update course display order
     */
    public function updateCourseOrder(Request $request, Response $response, $id)
    {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (empty($data['order'])) {
                throw new \Exception(__('courses.invalid_order'));
            }
            
            $success = UserCourse::updateDisplayOrder($id, $data['order']);
            
            if (!$success) {
                throw new \Exception(__('courses.order_update_failed'));
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.order_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * API endpoint to get basic user info
     */
    public function apiGetUser(Request $request, Response $response, $id)
    {
        try {
            $user = User::find($id);
            
            if (!$user) {
                throw new \Exception('User not found');
            }
            
            // Return basic user info
            $data = [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'billing_email' => $user->billing_email,
                'username' => $user->username
            ];
            
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'user' => $data]);
            exit;
        } catch (\Exception $e) {
            http_response_code(404);
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }
    
    /**
     * Generate retrocession invoice using unified generator
     */
    public function generateRetrocession(Request $request, Response $response, $id)
    {
        // Clear any output buffers to ensure clean JSON response
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        header('Content-Type: application/json');
        
        try {
            // Check permissions
            $currentUserId = $_SESSION['user_id'] ?? 0;
            if (!User::isAdmin($currentUserId) && !User::isManager($currentUserId)) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            $month = $data['month'] ?? date('n');
            $year = $data['year'] ?? date('Y');
            
            // Use unified invoice generator
            $generator = new \App\Services\UnifiedInvoiceGenerator(
                $id,
                'RET',
                $month,
                $year,
                $currentUserId
            );
            
            $result = $generator->generate();
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'],
                    'invoice_id' => $result['invoice']['id'],
                    'invoice_number' => $result['invoice']['invoice_number'] ?? ''
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }
            exit;
            
        } catch (\Exception $e) {
            error_log("UserController::generateRetrocession - Error: " . $e->getMessage());
            
            // Clear any output that might have been generated
            while (ob_get_level()) {
                ob_end_clean();
            }
            
            http_response_code(400);
            header('Content-Type: application/json');
            
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Save retrocession settings for a user
     */
    public function saveRetrocessionSettings(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check permissions
            $currentUserId = $_SESSION['user_id'] ?? 0;
            if (!User::isAdmin($currentUserId) && !User::isManager($currentUserId)) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            $settings = $data['retrocession'] ?? [];
            
            // Validate required fields
            if (empty($settings['valid_from'])) {
                throw new \Exception(__('validation.required', ['field' => 'valid_from']));
            }
            
            $db = Flight::db();
            
            // Check if user_retrocession_settings table exists
            $stmt = $db->query("SHOW TABLES LIKE 'user_retrocession_settings'");
            if (!$stmt->fetch()) {
                // Create table if it doesn't exist
                $createTableSql = file_get_contents(__DIR__ . '/../../database/migrations/070_create_user_retrocession_settings_table.sql');
                $db->exec($createTableSql);
                
                // Add label columns
                $db->exec("
                    ALTER TABLE user_retrocession_settings
                    ADD COLUMN IF NOT EXISTS cns_label VARCHAR(255) DEFAULT 'RÉTROCESSION CNS' AFTER cns_value,
                    ADD COLUMN IF NOT EXISTS patient_label VARCHAR(255) DEFAULT 'RÉTROCESSION PATIENTS' AFTER patient_value,
                    ADD COLUMN IF NOT EXISTS secretary_label VARCHAR(255) DEFAULT 'FRAIS SECRÉTARIAT' AFTER secretary_value
                ");
            }
            
            // Check if there's already a record with the same start date
            $stmt = $db->prepare("
                SELECT id FROM user_retrocession_settings 
                WHERE user_id = :user_id AND valid_from = :valid_from
                LIMIT 1
            ");
            $stmt->execute([
                'user_id' => $id,
                'valid_from' => $settings['valid_from']
            ]);
            
            if ($stmt->fetch()) {
                throw new \Exception(__('validation.duplicate_entry', ['field' => 'valid_from']));
            }
            
            // Get the currently active record
            $stmt = $db->prepare("
                SELECT id, valid_from FROM user_retrocession_settings 
                WHERE user_id = :user_id AND is_active = 1
                LIMIT 1
            ");
            $stmt->execute(['user_id' => $id]);
            $activeRecord = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            // If there's an active record and the new start date is after it
            if ($activeRecord && $settings['valid_from'] > $activeRecord['valid_from']) {
                // Deactivate the current active record
                $stmt = $db->prepare("
                    UPDATE user_retrocession_settings 
                    SET is_active = 0 
                    WHERE id = :id
                ");
                $stmt->execute(['id' => $activeRecord['id']]);
                
                // Set its end date to one day before the new start date
                $validFromDate = new \DateTime($settings['valid_from']);
                $validFromDate->modify('-1 day');
                $endDate = $validFromDate->format('Y-m-d');
                
                $stmt = $db->prepare("
                    UPDATE user_retrocession_settings 
                    SET valid_to = :end_date
                    WHERE id = :id AND (valid_to IS NULL OR valid_to > :end_date)
                ");
                $stmt->execute([
                    'id' => $activeRecord['id'],
                    'end_date' => $endDate
                ]);
            }
            
            // Update any other records that need their end dates adjusted
            $validFromDate = new \DateTime($settings['valid_from']);
            $validFromDate->modify('-1 day');
            $endDate = $validFromDate->format('Y-m-d');
            
            $stmt = $db->prepare("
                UPDATE user_retrocession_settings 
                SET valid_to = :end_date
                WHERE user_id = :user_id 
                AND valid_from < :valid_from
                AND (valid_to IS NULL OR valid_to > :end_date)
                AND id != :exclude_id
            ");
            $stmt->execute([
                'user_id' => $id,
                'valid_from' => $settings['valid_from'],
                'end_date' => $endDate,
                'exclude_id' => $activeRecord['id'] ?? 0
            ]);
            
            // Insert new settings
            $stmt = $db->prepare("
                INSERT INTO user_retrocession_settings (
                    user_id, cns_type, cns_value, cns_label,
                    patient_type, patient_value, patient_label,
                    secretary_type, secretary_value, secretary_label,
                    ceiling_enabled, ceiling_amount,
                    valid_from, valid_to, is_active, notes,
                    created_by, approved_by, approved_at
                ) VALUES (
                    :user_id, :cns_type, :cns_value, :cns_label,
                    :patient_type, :patient_value, :patient_label,
                    :secretary_type, :secretary_value, :secretary_label,
                    :ceiling_enabled, :ceiling_amount,
                    :valid_from, :valid_to, :is_active, :notes,
                    :created_by, :approved_by, NOW()
                )
            ");
            
            // Prepare parameter values
            $params = [
                'user_id' => $id,
                'cns_type' => $settings['cns_type'] ?? 'percentage',
                'cns_value' => floatval($settings['cns_value'] ?? 20),
                'cns_label' => $settings['cns_label'] ?? 'Rétrocession CNS',
                'patient_type' => $settings['patient_type'] ?? 'percentage',
                'patient_value' => floatval($settings['patient_value'] ?? 20),
                'patient_label' => $settings['patient_label'] ?? 'Rétrocession PATIENTS',
                'secretary_type' => $settings['secretary_type'] ?? 'percentage',
                'secretary_value' => floatval($settings['secretary_value'] ?? 10),
                'secretary_label' => $settings['secretary_label'] ?? 'Frais secrétariat et mise à disposition matériel',
                'ceiling_enabled' => isset($settings['ceiling_enabled']) && $settings['ceiling_enabled'] ? 1 : 0,
                'ceiling_amount' => floatval($settings['ceiling_amount'] ?? 5000),
                'valid_from' => $settings['valid_from'],
                'valid_to' => !empty($settings['valid_to']) ? $settings['valid_to'] : null,
                'is_active' => 1,
                'notes' => !empty($settings['notes']) ? $settings['notes'] : null,
                'created_by' => $currentUserId,
                'approved_by' => $currentUserId
            ];
            
            try {
                $result = $stmt->execute($params);
                if (!$result) {
                    throw new \Exception('Failed to insert retrocession settings: ' . implode(', ', $stmt->errorInfo()));
                }
            } catch (\PDOException $e) {
                throw new \Exception('Database error: ' . $e->getMessage() . ' - SQL State: ' . $e->getCode());
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.retrocession_settings_saved')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Save monthly retrocession amounts via AJAX
     */
    public function saveMonthlyAmounts(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            // Check permissions
            if (!$this->checkPermission('users.edit')) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.access_denied')
                ]);
                exit;
            }
            
            // Get user with groups
            $user = User::getWithGroups($id);
            if (!$user) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.user_not_found')
                ]);
                exit;
            }
            
            // Check if user is a practitioner (Kiné group)
            $practitionerGroupId = (string)(Flight::get('practitioner_group_id') ?? 4);
            $userGroups = array_column($user['groups'], 'id');
            $userGroupIds = array_map('strval', $userGroups);
            
            if (!in_array($practitionerGroupId, $userGroupIds)) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.not_a_practitioner')
                ]);
                exit;
            }
            
            $data = $request->all();
            $year = $data['year'] ?? date('Y');
            
            // Handle monthly retrocession amounts
            if (isset($data['monthly_amounts']) && is_array($data['monthly_amounts'])) {
                $db = \Flight::db();
                
                // Check if year column exists
                $stmt = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
                $hasYearColumn = $stmt->rowCount() > 0;
                
                if ($hasYearColumn) {
                    // Delete existing monthly amounts for the specific year
                    $deleteStmt = $db->prepare("DELETE FROM user_monthly_retrocession_amounts WHERE user_id = :user_id AND year = :year");
                    $deleteStmt->execute(['user_id' => $id, 'year' => $year]);
                    
                    // Insert new monthly amounts with year
                    $insertStmt = $db->prepare("
                        INSERT INTO user_monthly_retrocession_amounts 
                        (user_id, month, year, cns_amount, patient_amount, is_active, created_by, created_at)
                        VALUES (:user_id, :month, :year, :cns_amount, :patient_amount, :is_active, :created_by, NOW())
                    ");
                } else {
                    // Old behavior - no year support
                    $deleteStmt = $db->prepare("DELETE FROM user_monthly_retrocession_amounts WHERE user_id = :user_id");
                    $deleteStmt->execute(['user_id' => $id]);
                    
                    $insertStmt = $db->prepare("
                        INSERT INTO user_monthly_retrocession_amounts 
                        (user_id, month, cns_amount, patient_amount, is_active, created_by, created_at)
                        VALUES (:user_id, :month, :cns_amount, :patient_amount, :is_active, :created_by, NOW())
                    ");
                }
                
                foreach ($data['monthly_amounts'] as $month => $monthData) {
                    if (!is_numeric($month) || $month < 1 || $month > 12) {
                        continue;
                    }
                    
                    $params = [
                        'user_id' => $id,
                        'month' => $month,
                        'cns_amount' => floatval($monthData['cns_amount'] ?? 0),
                        'patient_amount' => floatval($monthData['patient_amount'] ?? 0),
                        'is_active' => isset($monthData['is_active']) ? 1 : 0,
                        'created_by' => $_SESSION['user_id']
                    ];
                    
                    if ($hasYearColumn) {
                        $params['year'] = $year;
                    }
                    
                    $insertStmt->execute($params);
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => __('common.saved_successfully')
                ]);
                exit;
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.invalid_data')
                ]);
                exit;
            }
            
        } catch (\Exception $e) {
            error_log("Error saving monthly amounts: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Get monthly amounts for a specific year via AJAX
     */
    public function getMonthlyAmounts(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check permissions
            if (!$this->checkPermission('users.view')) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.access_denied')
                ]);
                exit;
            }
            
            $year = $request->get('year', date('Y'));
            $db = \Flight::db();
            
            // Check if year column exists
            $stmt = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
            $hasYearColumn = $stmt->rowCount() > 0;
            
            // Get monthly amounts
            if ($hasYearColumn) {
                $stmt = $db->prepare("
                    SELECT month, cns_amount, patient_amount, is_active
                    FROM user_monthly_retrocession_amounts
                    WHERE user_id = :user_id AND year = :year
                    ORDER BY month
                ");
                $stmt->execute(['user_id' => $id, 'year' => $year]);
            } else {
                // No year column - get all amounts
                $stmt = $db->prepare("
                    SELECT month, cns_amount, patient_amount, is_active
                    FROM user_monthly_retrocession_amounts
                    WHERE user_id = :user_id
                    ORDER BY month
                ");
                $stmt->execute(['user_id' => $id]);
            }
            
            $monthlyAmounts = [];
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $monthlyAmounts[$row['month']] = $row;
            }
            
            // Get monthly invoices for the year
            $stmt = $db->prepare("
                SELECT ugi.period_month, i.id, i.invoice_number
                FROM user_generated_invoices ugi
                JOIN invoices i ON i.id = ugi.invoice_id
                WHERE ugi.user_id = :user_id 
                AND ugi.invoice_type = 'RET'
                AND ugi.period_year = :year
            ");
            $stmt->execute(['user_id' => $id, 'year' => $year]);
            
            $monthlyInvoices = [];
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $monthlyInvoices[$row['period_month']] = [
                    'id' => $row['id'],
                    'invoice_number' => $row['invoice_number']
                ];
            }
            
            echo json_encode([
                'success' => true,
                'monthly_amounts' => $monthlyAmounts,
                'monthly_invoices' => $monthlyInvoices,
                'year' => $year
            ]);
            
        } catch (\Exception $e) {
            error_log("Error getting monthly amounts: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => __('common.error_occurred')
            ]);
        }
    }
    
    /**
     * Save monthly course counts via AJAX
     */
    public function saveMonthlyCourseCounts(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            // Check permissions
            if (!$this->checkPermission('users.edit')) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.access_denied')
                ]);
                exit;
            }
            
            // Get user with groups
            $user = User::getWithGroups($id);
            if (!$user) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.user_not_found')
                ]);
                exit;
            }
            
            // Check if user is a coach
            $coachGroupId = (string)(Flight::get('coach_group_id') ?? 24);
            $userGroups = array_column($user['groups'], 'id');
            $userGroupIds = array_map('strval', $userGroups);
            
            if (!in_array($coachGroupId, $userGroupIds)) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.not_a_coach')
                ]);
                exit;
            }
            
            $data = $request->all();
            $courseCounts = json_decode($data['course_counts'] ?? '{}', true);
            $currentYear = $data['current_year'] ?? date('Y');
            
            if (!$courseCounts) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.invalid_data')
                ]);
                exit;
            }
            
            $db = \Flight::db();
            
            // Get user's courses with hourly rates
            $coursesStmt = $db->prepare("
                SELECT id, hourly_rate 
                FROM user_courses 
                WHERE user_id = :user_id AND is_active = 1
            ");
            $coursesStmt->execute(['user_id' => $id]);
            $courses = $coursesStmt->fetchAll(\PDO::FETCH_KEY_PAIR);
            
            // Delete existing course counts for the year
            $deleteStmt = $db->prepare("
                DELETE FROM user_monthly_course_counts 
                WHERE user_id = :user_id AND year = :year
            ");
            $deleteStmt->execute(['user_id' => $id, 'year' => $currentYear]);
            
            // Insert new course counts
            $insertStmt = $db->prepare("
                INSERT INTO user_monthly_course_counts 
                (user_id, course_id, month, year, course_count, hourly_rate, is_active, created_by, created_at)
                VALUES (:user_id, :course_id, :month, :year, :course_count, :hourly_rate, :is_active, :created_by, NOW())
            ");
            
            foreach ($courseCounts as $month => $monthCourses) {
                if (!is_numeric($month) || $month < 1 || $month > 12) {
                    continue;
                }
                
                foreach ($monthCourses as $courseId => $count) {
                    if (!isset($courses[$courseId]) || $count <= 0) {
                        continue;
                    }
                    
                    $insertStmt->execute([
                        'user_id' => $id,
                        'course_id' => $courseId,
                        'month' => $month,
                        'year' => $currentYear,
                        'course_count' => intval($count),
                        'hourly_rate' => $courses[$courseId],
                        'is_active' => 1,
                        'created_by' => $_SESSION['user_id']
                    ]);
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('common.saved_successfully')
            ]);
            exit;
            
        } catch (\Exception $e) {
            error_log("Error saving monthly course counts: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Generate course invoice using unified generator
     */
    public function generateCourseInvoice(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            // Check permissions
            if (!$this->checkPermission('invoices.create')) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.access_denied')
                ]);
                exit;
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            $month = $data['month'] ?? date('n');
            $year = $data['year'] ?? date('Y');
            
            // Get user and verify they're a coach
            $user = User::getWithGroups($id);
            if (!$user) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.user_not_found')
                ]);
                exit;
            }
            
            $coachGroupId = (string)(Flight::get('coach_group_id') ?? 24);
            $userGroups = array_column($user['groups'], 'id');
            $userGroupIds = array_map('strval', $userGroups);
            
            if (!in_array($coachGroupId, $userGroupIds)) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.not_a_coach')
                ]);
                exit;
            }
            
            // Use unified invoice generator
            $generator = new \App\Services\UnifiedInvoiceGenerator(
                $id,
                'LOC',
                $month,
                $year,
                $_SESSION['user_id']
            );
            
            $result = $generator->generate();
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'],
                    'invoice' => [
                        'id' => $result['invoice']['id'],
                        'invoice_number' => $result['invoice']['invoice_number']
                    ]
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }
            exit;
            
        } catch (\Exception $e) {
            error_log("Error generating course invoice: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Generate Loyer invoice using unified generator
     */
    public function generateLoyerInvoice(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            if (!$this->validateCsrfToken()) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.csrf_token_invalid')
                ]);
                exit;
            }
            
            // Check permissions
            if (!$this->checkPermission('invoices.create')) {
                echo json_encode([
                    'success' => false,
                    'message' => __('common.access_denied')
                ]);
                exit;
            }
            
            $data = $_POST;
            $month = $data['month'] ?? date('n');
            $year = $data['year'] ?? date('Y');
            
            // Get user
            $user = User::getWithGroups($id);
            if (!$user) {
                echo json_encode([
                    'success' => false,
                    'message' => __('users.user_not_found')
                ]);
                exit;
            }
            
            // Use unified invoice generator
            $generator = new \App\Services\UnifiedInvoiceGenerator(
                $id,
                'LOY',
                $month,
                $year,
                $_SESSION['user_id']
            );
            
            $result = $generator->generate();
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'],
                    'invoice' => [
                        'id' => $result['invoice']['id'],
                        'invoice_number' => $result['invoice']['invoice_number']
                    ]
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }
            exit;
            
        } catch (\Exception $e) {
            error_log("Error generating loyer invoice: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ]);
            exit;
        }
    }
}