<?php
/**
 * Web-based runner for Monthly Course Tracking migrations
 * Access this through your browser to run the specific migrations
 */

// Load dependencies at the top of the file
require_once dirname(__DIR__) . '/vendor/autoload.php';

use Dotenv\Dotenv;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Run Course Tracking Migrations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .success { color: #198754; }
        .error { color: #dc3545; }
        .info { color: #0dcaf0; }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="mb-4">Monthly Course Tracking - Migration Setup</h1>
        
        <div class="alert alert-info">
            <strong>This will run the following migrations:</strong>
            <ul class="mb-0 mt-2">
                <li>101_create_user_monthly_course_counts.sql - Creates the table for storing monthly course counts</li>
                <li>102_add_cours_invoice_type.sql - Adds the COURS invoice type</li>
            </ul>
        </div>
        
        <?php if (!isset($_POST['run'])): ?>
            <form method="POST">
                <button type="submit" name="run" value="1" class="btn btn-primary btn-lg">
                    Run Migrations
                </button>
                <a href="../users" class="btn btn-secondary btn-lg ms-2">Cancel</a>
            </form>
        <?php else: ?>
            <div class="output">
<?php
// Run the migrations
$dotenv = Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Database connection from .env
$host = $_ENV['DB_HOST'];
$dbname = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo '<span class="success">✓ Connected to database successfully.</span>' . "\n\n";
    
    // Define the specific migrations for this feature
    $migrations = [
        '101_create_user_monthly_course_counts.sql',
        '102_add_cours_invoice_type.sql'
    ];
    
    $migrationsPath = dirname(__DIR__) . '/database/migrations/';
    $successCount = 0;
    
    foreach ($migrations as $migrationFile) {
        $fullPath = $migrationsPath . $migrationFile;
        
        echo "Processing: <strong>$migrationFile</strong>\n";
        
        if (!file_exists($fullPath)) {
            echo '<span class="error">  ✗ File not found: ' . htmlspecialchars($fullPath) . '</span>' . "\n\n";
            continue;
        }
        
        try {
            // Read the SQL file
            $sql = file_get_contents($fullPath);
            
            // Split by semicolons
            $statements = array_filter(array_map('trim', preg_split('/;\s*$/m', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    // Show a preview of the statement
                    $preview = substr(preg_replace('/\s+/', ' ', $statement), 0, 60);
                    echo "  → Executing: " . htmlspecialchars($preview) . "...\n";
                    
                    $pdo->exec($statement);
                }
            }
            
            echo '<span class="success">  ✓ Successfully completed: ' . $migrationFile . '</span>' . "\n\n";
            $successCount++;
            
        } catch (PDOException $e) {
            echo '<span class="error">  ✗ Error: ' . htmlspecialchars($e->getMessage()) . '</span>' . "\n\n";
            
            // If it's a duplicate entry error, it might be okay
            if (strpos($e->getMessage(), 'Duplicate entry') !== false || 
                strpos($e->getMessage(), 'already exists') !== false) {
                echo '<span class="info">  ℹ Note: This might mean the migration was already applied.</span>' . "\n\n";
            }
        }
    }
    
    echo str_repeat('-', 50) . "\n";
    echo "Summary: <strong>$successCount out of " . count($migrations) . " migrations completed successfully.</strong>\n\n";
    
    // Verify the changes
    echo "<strong>Verifying changes:</strong>\n";
    
    // Check if table exists
    try {
        $result = $pdo->query("SHOW TABLES LIKE 'user_monthly_course_counts'");
        if ($result->rowCount() > 0) {
            echo '<span class="success">  ✓ Table \'user_monthly_course_counts\' exists</span>' . "\n";
            
            // Show table structure
            $columns = $pdo->query("SHOW COLUMNS FROM user_monthly_course_counts");
            echo "    Columns: ";
            $cols = [];
            while ($col = $columns->fetch(PDO::FETCH_ASSOC)) {
                $cols[] = $col['Field'];
            }
            echo htmlspecialchars(implode(', ', $cols)) . "\n";
        } else {
            echo '<span class="error">  ✗ Table \'user_monthly_course_counts\' not found</span>' . "\n";
        }
    } catch (PDOException $e) {
        echo '<span class="error">  ✗ Could not verify table: ' . htmlspecialchars($e->getMessage()) . '</span>' . "\n";
    }
    
    // Check if invoice type exists
    try {
        $stmt = $pdo->prepare("SELECT * FROM invoice_types WHERE code = 'COURS'");
        $stmt->execute();
        $invoiceType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoiceType) {
            echo '<span class="success">  ✓ Invoice type \'COURS\' exists (ID: ' . $invoiceType['id'] . ', Prefix: ' . $invoiceType['prefix'] . ')</span>' . "\n";
        } else {
            echo '<span class="error">  ✗ Invoice type \'COURS\' not found</span>' . "\n";
        }
    } catch (PDOException $e) {
        echo '<span class="error">  ✗ Could not verify invoice type: ' . htmlspecialchars($e->getMessage()) . '</span>' . "\n";
    }
    
    echo "\n" . '<span class="success"><strong>✓ Migration process completed!</strong></span>' . "\n";
    
} catch (Exception $e) {
    echo '<span class="error">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</span>' . "\n";
}
?>
            </div>
            
            <div class="mt-4">
                <div class="alert alert-success">
                    <strong>Next Steps:</strong>
                    <ol class="mb-0 mt-2">
                        <li>Navigate to a coach user's edit page</li>
                        <li>Go to the "Courses" tab</li>
                        <li>Add courses if not already present</li>
                        <li>Use the "Monthly Course Counts" section to track courses and generate invoices</li>
                    </ol>
                </div>
                
                <a href="../users" class="btn btn-primary">Go to Users</a>
                <a href="../" class="btn btn-secondary">Go to Dashboard</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>