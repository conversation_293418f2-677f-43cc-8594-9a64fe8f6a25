<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/database.php';

// Load environment
$dotenv = \Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_NAME'] ?? 'healthcenter_billing';
$username = $_ENV['DB_USER'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';
$port = $_ENV['DB_PORT'] ?? '3306';

try {
    $pdo = new PDO(
        "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "=== Testing user_retrocession_settings table structure ===\n\n";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_retrocession_settings'");
    if (!$stmt->fetch()) {
        echo "❌ Table 'user_retrocession_settings' does not exist!\n";
        exit(1);
    }
    
    echo "✅ Table exists\n\n";
    
    // Get column information
    echo "Columns in user_retrocession_settings:\n";
    echo str_repeat("-", 60) . "\n";
    
    $stmt = $pdo->query("DESCRIBE user_retrocession_settings");
    $columns = $stmt->fetchAll();
    
    $columnNames = [];
    foreach ($columns as $column) {
        $columnNames[] = $column['Field'];
        printf("%-20s %-20s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'] === 'YES' ? 'NULL' : 'NOT NULL'
        );
    }
    
    echo "\n";
    
    // Check for problematic columns
    $problemColumns = ['cns_percentage', 'patient_percentage'];
    $foundProblems = array_intersect($problemColumns, $columnNames);
    
    if (!empty($foundProblems)) {
        echo "⚠️  Found problematic column names that should not exist:\n";
        foreach ($foundProblems as $col) {
            echo "   - $col\n";
        }
    }
    
    // Check for correct columns
    $correctColumns = ['cns_value', 'patient_value', 'cns_type', 'patient_type'];
    $missingColumns = array_diff($correctColumns, $columnNames);
    
    if (!empty($missingColumns)) {
        echo "❌ Missing required columns:\n";
        foreach ($missingColumns as $col) {
            echo "   - $col\n";
        }
    } else {
        echo "✅ All required columns present (cns_value, patient_value, cns_type, patient_type)\n";
    }
    
    // Test a sample query
    echo "\n=== Testing sample query ===\n";
    
    try {
        $stmt = $pdo->query("
            SELECT user_id, cns_type, cns_value, patient_type, patient_value, 
                   cns_label, patient_label, valid_from
            FROM user_retrocession_settings 
            LIMIT 1
        ");
        
        $result = $stmt->fetch();
        if ($result) {
            echo "✅ Query successful. Sample data:\n";
            foreach ($result as $key => $value) {
                printf("   %-15s: %s\n", $key, $value ?? 'NULL');
            }
        } else {
            echo "⚠️  Query successful but no data found in table\n";
        }
    } catch (PDOException $e) {
        echo "❌ Query failed: " . $e->getMessage() . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ Test complete!\n";