<?php
/**
 * Email Diagnostic Script
 * Tests email configuration and sends a test email
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

echo "<h1>Email Diagnostic Test</h1>";
echo "<pre>";

// Test 1: Check environment configuration
echo "=== Environment Configuration ===\n";
echo "MAIL_HOST: " . ($_ENV['MAIL_HOST'] ?? 'not set') . "\n";
echo "MAIL_PORT: " . ($_ENV['MAIL_PORT'] ?? 'not set') . "\n";
echo "MAIL_USERNAME: " . ($_ENV['MAIL_USERNAME'] ?? 'not set') . "\n";
echo "MAIL_ENCRYPTION: " . ($_ENV['MAIL_ENCRYPTION'] ?? 'not set') . "\n";
echo "MAIL_FROM_ADDRESS: " . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'not set') . "\n";
echo "MAIL_FROM_NAME: " . ($_ENV['MAIL_FROM_NAME'] ?? 'not set') . "\n\n";

// Test 2: Test SMTP connection
echo "=== Testing SMTP Connection ===\n";
$host = $_ENV['MAIL_HOST'] ?? 'localhost';
$port = $_ENV['MAIL_PORT'] ?? 1025;

// Try to connect to SMTP server
$timeout = 5;
$socket = @fsockopen($host, $port, $errno, $errstr, $timeout);

if ($socket) {
    echo "✓ Successfully connected to $host:$port\n";
    echo "SMTP Banner: " . fgets($socket, 1024);
    fclose($socket);
} else {
    echo "✗ Failed to connect to $host:$port\n";
    echo "Error: $errstr (errno: $errno)\n";
    
    // Check if it's a local development issue
    if ($host === 'localhost' && $port == 1025) {
        echo "\n⚠️  Mailhog doesn't appear to be running!\n";
        echo "To fix this:\n";
        echo "1. Download Mailhog from: https://github.com/mailhog/MailHog/releases\n";
        echo "2. Run: mailhog.exe (or ./mailhog on Linux/Mac)\n";
        echo "3. Mailhog will start on port 1025 (SMTP) and 8025 (Web UI)\n";
        echo "4. Visit http://localhost:8025 to see captured emails\n";
    }
}
echo "\n";

// Test 3: Test PHPMailer configuration
echo "=== Testing PHPMailer ===\n";
try {
    $mail = new PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host       = $host;
    $mail->Port       = $port;
    $mail->SMTPDebug  = SMTP::DEBUG_CONNECTION;
    $mail->Debugoutput = function($str, $level) {
        echo "SMTP: $str";
    };
    
    if ($host === 'localhost' && $port == 1025) {
        $mail->SMTPAuth   = false;
        $mail->SMTPSecure = false;
        $mail->SMTPAutoTLS = false;
    }
    
    // Test connection
    if ($mail->smtpConnect()) {
        echo "✓ PHPMailer can connect to SMTP server\n";
        $mail->smtpClose();
    } else {
        echo "✗ PHPMailer cannot connect to SMTP server\n";
    }
} catch (Exception $e) {
    echo "✗ PHPMailer Error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 4: Send a test email
if (isset($_GET['send']) && $_GET['send'] === 'true') {
    echo "=== Sending Test Email ===\n";
    $to = $_GET['email'] ?? '<EMAIL>';
    
    try {
        $mail = new PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host       = $host;
        $mail->Port       = $port;
        $mail->SMTPDebug  = SMTP::DEBUG_SERVER;
        $mail->Debugoutput = function($str, $level) {
            echo "SMTP: $str";
        };
        
        if ($host === 'localhost' && $port == 1025) {
            $mail->SMTPAuth   = false;
            $mail->SMTPSecure = false;
            $mail->SMTPAutoTLS = false;
        }
        
        // Recipients
        $mail->setFrom($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>', $_ENV['MAIL_FROM_NAME'] ?? 'Fit360');
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from Fit360';
        $mail->Body    = '<h1>Test Email</h1><p>This is a test email sent from the diagnostic script.</p><p>Time: ' . date('Y-m-d H:i:s') . '</p>';
        $mail->AltBody = 'This is a test email sent from the diagnostic script. Time: ' . date('Y-m-d H:i:s');
        
        $mail->send();
        echo "\n✓ Test email sent successfully to: $to\n";
        
        if ($host === 'localhost' && $port == 1025) {
            echo "Check Mailhog at: http://localhost:8025\n";
        }
    } catch (Exception $e) {
        echo "\n✗ Failed to send test email\n";
        echo "Error: " . $mail->ErrorInfo . "\n";
        echo "Exception: " . $e->getMessage() . "\n";
    }
} else {
    echo "=== Send Test Email ===\n";
    echo "To send a test email, add ?send=true&email=<EMAIL> to the URL\n";
    echo "Example: " . $_SERVER['REQUEST_URI'] . "?send=true&email=<EMAIL>\n";
}

echo "\n=== Alternative Solutions ===\n";
echo "If Mailhog isn't working, you can:\n";
echo "1. Use a real SMTP server (Gmail, SendGrid, etc.)\n";
echo "2. Install a different mail catcher (MailCatcher, Papercut)\n";
echo "3. Use PHP's built-in mail() function (less reliable)\n";

echo "\nFor Gmail SMTP:\n";
echo "MAIL_HOST=smtp.gmail.com\n";
echo "MAIL_PORT=587\n";
echo "MAIL_USERNAME=<EMAIL>\n";
echo "MAIL_PASSWORD=your-app-password\n";
echo "MAIL_ENCRYPTION=tls\n";

echo "</pre>";
?>