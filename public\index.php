<?php
/**
 * Fit360 AdminDesk - Healthcare Billing Management System
 * 
 * @package    Fit360
 * <AUTHOR> Team
 * @copyright  2025 Fit360
 * @license    Proprietary
 * @version    2.3.4
 * @link       http://localhost/fit/public/
 */

// Define the application path
define('APP_PATH', dirname(__DIR__));

// Check if vendor autoload exists
if (!file_exists(APP_PATH . '/vendor/autoload.php')) {
    die('Error: Vendor autoload not found. Please run "composer install" in the project root.');
}

// Load Composer autoloader
require APP_PATH . '/vendor/autoload.php';

// Load environment variables (in case bootstrap doesn't)
if (!getenv('DB_DATABASE') && file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Check if bootstrap file exists
if (!file_exists(APP_PATH . '/app/config/bootstrap.php')) {
    die('Error: Bootstrap file not found at /app/config/bootstrap.php');
}

// Load application bootstrap
require APP_PATH . '/app/config/bootstrap.php';

// Start the application
try {
    // The bootstrap file should have initialized Flight
    // Start Flight framework
    Flight::start();
} catch (Exception $e) {
    // Log error
    error_log('Fit360 Fatal Error: ' . $e->getMessage());
    
    // Display error in development mode
    if (getenv('APP_ENV') === 'development' || getenv('APP_DEBUG') === 'true') {
        echo '<h1>Application Error</h1>';
        echo '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        // Production error page
        http_response_code(500);
        echo '<h1>Application Error</h1>';
        echo '<p>An error occurred. Please contact the administrator.</p>';
    }
}