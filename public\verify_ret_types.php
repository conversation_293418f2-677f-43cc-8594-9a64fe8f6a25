<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Verifying RET Types</h2>";
    
    // Get all RET types
    $stmt = $db->query("
        SELECT id, code, prefix, name, is_active
        FROM config_invoice_types 
        WHERE code LIKE '%ret%' OR prefix LIKE '%RET%'
        ORDER BY id DESC
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($types) . " RET-related types:</p>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Active</th>";
    echo "</tr>";
    
    $ret25Found = false;
    $ret30Found = false;
    
    foreach ($types as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $rowStyle = '';
        if ($row['code'] == 'ret25') {
            $rowStyle = 'background-color: #d4edda;';
            $ret25Found = $row;
        } elseif ($row['code'] == 'ret30') {
            $rowStyle = 'background-color: #cce5ff;';
            $ret30Found = $row;
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($ret25Found && $ret30Found) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Both RET25 and RET30 exist!</h3>";
        echo "<p>ret25 ID: {$ret25Found['id']}, Prefix: {$ret25Found['prefix']}</p>";
        echo "<p>ret30 ID: {$ret30Found['id']}, Prefix: {$ret30Found['prefix']}</p>";
        echo "<p><a href='/fit/public/fix_frank_invoice_simple.php' style='color: #155724; font-weight: bold;'>→ Now fix Frank's invoice</a></p>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>⚠️ Missing types: ";
        if (!$ret25Found) echo "ret25 ";
        if (!$ret30Found) echo "ret30";
        echo "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}