<?php
// Capture the users page HTML to find the jQuery error

// Initialize session to simulate logged-in user
session_start();
$_SESSION['user_id'] = 1; // Simulate logged-in user

// Capture the output
ob_start();
include '../public/index.php';
$output = ob_get_clean();

// Find line 1993
$lines = explode("\n", $output);

echo "<h1>Debug: Line 1993 and surrounding context</h1>";
echo "<p>Total lines in output: " . count($lines) . "</p>";

if (count($lines) >= 1993) {
    echo "<h2>Lines 1990-1995:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    for ($i = 1989; $i <= 1994 && $i < count($lines); $i++) {
        $lineNum = $i + 1;
        $line = htmlspecialchars($lines[$i]);
        if ($lineNum == 1993) {
            echo "<span style='background: yellow; font-weight: bold;'>Line $lineNum: $line</span>\n";
        } else {
            echo "Line $lineNum: $line\n";
        }
    }
    echo "</pre>";
    
    // Search for all jQuery usage
    echo "<h2>All lines containing $ or jQuery:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;'>";
    foreach ($lines as $i => $line) {
        if (preg_match('/\$\(|\$\.|jQuery\(|jQuery\./', $line)) {
            $lineNum = $i + 1;
            echo "Line $lineNum: " . htmlspecialchars(trim($line)) . "\n";
        }
    }
    echo "</pre>";
    
    // Find where jQuery is loaded
    echo "<h2>jQuery script tag location:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    foreach ($lines as $i => $line) {
        if (strpos($line, 'jquery-3.7.1.min.js') !== false) {
            $lineNum = $i + 1;
            echo "Line $lineNum: " . htmlspecialchars(trim($line)) . "\n";
            break;
        }
    }
    echo "</pre>";
} else {
    echo "<p style='color: red;'>Output has only " . count($lines) . " lines, cannot show line 1993</p>";
}

// Save full output for inspection
file_put_contents('users_page_output.html', $output);
echo "<p>Full output saved to: users_page_output.html</p>";
?>