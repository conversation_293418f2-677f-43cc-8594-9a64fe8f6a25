<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Invoice Types</h2>";
    
    // Check invoice types
    $stmt = $db->prepare("
        SELECT id, code, name, is_active
        FROM invoice_types 
        ORDER BY id
    ");
    $stmt->execute();
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . htmlspecialchars($type['code']) . "</td>";
        echo "<td>" . htmlspecialchars($type['name']) . "</td>";
        echo "<td>" . ($type['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check which type is used for retrocession
    echo "<h3>Retrocession Invoice Type</h3>";
    $stmt = $db->prepare("
        SELECT i.invoice_type_id, it.name, COUNT(*) as count
        FROM invoices i
        JOIN invoice_types it ON it.id = i.invoice_type_id
        WHERE i.subject LIKE '%RETROCESSION%' OR i.subject LIKE '%Rétrocession%'
        GROUP BY i.invoice_type_id, it.name
    ");
    $stmt->execute();
    $retTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($retTypes) {
        foreach ($retTypes as $type) {
            echo "<p>Type ID " . $type['invoice_type_id'] . " - " . htmlspecialchars($type['name']) . " (" . $type['count'] . " invoices)</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}