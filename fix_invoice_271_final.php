<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Final Fix for Invoice 271 - Remove CNS from Description</h2>";
    
    // Get invoice details
    $invoiceSql = "SELECT i.*, u.first_name, u.last_name 
                   FROM invoices i
                   LEFT JOIN users u ON i.user_id = u.id
                   WHERE i.id = :id";
    
    $invoiceStmt = $db->prepare($invoiceSql);
    $invoiceStmt->execute(['id' => $invoiceId]);
    $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>User: {$invoice['first_name']} {$invoice['last_name']}</li>";
    echo "<li>Period: {$invoice['period']}</li>";
    echo "</ul>";
    
    // Get current invoice items
    $itemsSql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id ORDER BY id";
    $itemsStmt = $db->prepare($itemsSql);
    $itemsStmt->execute(['invoice_id' => $invoiceId]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($items)) {
        echo "<p style='color: red;'>No invoice items found!</p>";
        exit;
    }
    
    echo "<h3>Current Invoice Items:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Current Description</th><th>Will Be Changed To</th></tr>";
    
    $updates = [];
    foreach ($items as $item) {
        echo "<tr>";
        echo "<td>{$item['id']}</td>";
        echo "<td>{$item['description']}</td>";
        
        // Check if this line contains "RÉTROCESSION CNS"
        if (strpos($item['description'], 'RÉTROCESSION CNS') !== false) {
            $newDescription = str_replace('RÉTROCESSION CNS', 'RÉTROCESSION', $item['description']);
            echo "<td style='background-color: #ffe6e6;'><strong>$newDescription</strong></td>";
            $updates[$item['id']] = $newDescription;
        } else {
            echo "<td style='color: green;'>No change</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    if (!empty($updates)) {
        echo "<h3>Updating Invoice Items...</h3>";
        
        // Update each item individually
        $updateSql = "UPDATE invoice_items SET description = :description WHERE id = :id";
        $updateStmt = $db->prepare($updateSql);
        
        foreach ($updates as $itemId => $newDescription) {
            $updateStmt->execute([
                'description' => $newDescription,
                'id' => $itemId
            ]);
            echo "<p>✓ Updated item ID $itemId</p>";
        }
        
        echo "<h3 style='color: green;'>✓ Successfully updated " . count($updates) . " item(s)!</h3>";
        
        // Show final result
        echo "<h3>Final Invoice Items:</h3>";
        $itemsStmt->execute(['invoice_id' => $invoiceId]);
        $finalItems = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Description</th><th>Amount</th><th>VAT</th><th>Total</th></tr>";
        foreach ($finalItems as $item) {
            $isUpdated = isset($updates[$item['id']]);
            echo "<tr" . ($isUpdated ? " style='background-color: #e6ffe6;'" : "") . ">";
            echo "<td><strong>{$item['description']}</strong></td>";
            echo "<td>€" . number_format($item['unit_price'], 2) . "</td>";
            echo "<td>{$item['vat_rate']}%</td>";
            echo "<td>€" . number_format($item['total_amount'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Clear any cache that might exist
        echo "<h3>Additional Steps:</h3>";
        echo "<ul>";
        echo "<li>The database has been updated successfully</li>";
        echo "<li>If you still see 'CNS' in the invoice, try:</li>";
        echo "<ul>";
        echo "<li>Clear your browser cache (Ctrl+F5)</li>";
        echo "<li>Clear the application cache if one exists</li>";
        echo "<li>Regenerate the PDF if viewing a cached PDF</li>";
        echo "</ul>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: green;'>All items are already correct - no 'CNS' found in descriptions.</p>";
    }
    
    echo "<p><a href='/fit/public/invoices/$invoiceId' class='btn' style='background: blue; color: white; padding: 10px; text-decoration: none; margin-right: 10px;'>View Invoice</a>";
    echo "<a href='/fit/public/invoices/$invoiceId/print' target='_blank' class='btn' style='background: green; color: white; padding: 10px; text-decoration: none;'>View Print Version</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>