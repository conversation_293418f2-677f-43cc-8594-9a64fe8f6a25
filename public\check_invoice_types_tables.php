<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Invoice Types Tables</h2>";
    
    // Check invoice_types table
    echo "<h3>1. invoice_types table (used by UnifiedInvoiceGenerator):</h3>";
    $stmt = $db->query("
        SELECT * FROM invoice_types 
        WHERE code LIKE '%RET%' OR code LIKE '%ret%'
        ORDER BY id
    ");
    $invoiceTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($invoiceTypes)) {
        echo "<p style='color: red;'>⚠️ No RET types found in invoice_types table!</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>";
        foreach ($invoiceTypes as $type) {
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td><strong>{$type['code']}</strong></td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>" . ($type['is_active'] ? '✓' : '✗') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check config_invoice_types table
    echo "<h3>2. config_invoice_types table (has the data):</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code LIKE '%ret%'
        ORDER BY id
    ");
    $configTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($configTypes as $type) {
        $nameData = json_decode($type['name'], true);
        $displayName = is_array($nameData) ? ($nameData['fr'] ?? $type['name']) : $type['name'];
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td><strong>{$type['code']}</strong></td>";
        echo "<td>{$type['prefix']}</td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Issue Analysis:</h3>";
    echo "<p>The UnifiedInvoiceGenerator is looking for codes in the <strong>invoice_types</strong> table, but your RET types only exist in <strong>config_invoice_types</strong>.</p>";
    
    echo "<h3>Solution:</h3>";
    echo "<p>We need to either:</p>";
    echo "<ol>";
    echo "<li>Add the RET types to the invoice_types table</li>";
    echo "<li>Update UnifiedInvoiceGenerator to use config_invoice_types table</li>";
    echo "</ol>";
    
    // Try to add missing types to invoice_types
    echo "<h3>Adding missing types to invoice_types table:</h3>";
    
    $typesToAdd = [
        ['code' => 'RET', 'name' => 'Rétrocession'],
        ['code' => 'RET2', 'name' => 'Rétrocession 25%'],
        ['code' => 'RET3', 'name' => 'Rétrocession 30%']
    ];
    
    foreach ($typesToAdd as $type) {
        try {
            // Check if exists
            $stmt = $db->prepare("SELECT id FROM invoice_types WHERE code = :code");
            $stmt->execute(['code' => $type['code']]);
            
            if (!$stmt->fetch()) {
                // Add it
                $stmt = $db->prepare("INSERT INTO invoice_types (code, name, is_active) VALUES (:code, :name, 1)");
                $stmt->execute($type);
                echo "<p style='color: green;'>✓ Added {$type['code']} - {$type['name']}</p>";
            } else {
                echo "<p>✓ {$type['code']} already exists</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error adding {$type['code']}: " . $e->getMessage() . "</p>";
        }
    }
    
    // Verify
    echo "<h3>Final verification:</h3>";
    $stmt = $db->query("SELECT * FROM invoice_types WHERE code IN ('RET', 'RET2', 'RET3')");
    $final = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($final) == 3) {
        echo "<p style='color: green;'>✓ All required types are now in invoice_types table!</p>";
        echo "<p>Invoice generation should work now.</p>";
    } else {
        echo "<p style='color: red;'>⚠️ Some types are still missing.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}