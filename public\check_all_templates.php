<?php
// Check all templates in database
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
try {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $db = new PDO($dsn, $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Get ALL templates
$stmt = $db->query("SELECT * FROM invoice_templates ORDER BY code, id");
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<pre>";
echo "Total templates in database: " . count($templates) . "\n\n";
echo "ID | Code | Name | Type | Owner Type | Created At\n";
echo str_repeat("-", 80) . "\n";

foreach ($templates as $t) {
    echo str_pad($t['id'], 4) . " | ";
    echo str_pad($t['code'], 20) . " | ";
    echo str_pad(substr($t['name'], 0, 30), 30) . " | ";
    echo str_pad($t['invoice_type'], 15) . " | ";
    echo str_pad($t['owner_type'], 10) . " | ";
    echo $t['created_at'] . "\n";
}

echo "\n\nDuplicate Analysis:\n";
$stmt = $db->query("
    SELECT code, owner_type, COUNT(*) as count, GROUP_CONCAT(id ORDER BY id) as ids
    FROM invoice_templates
    GROUP BY code, owner_type
    HAVING count > 1
");
$dups = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($dups as $d) {
    echo "Code: {$d['code']}, Type: {$d['owner_type']}, Count: {$d['count']}, IDs: {$d['ids']}\n";
}
echo "</pre>";
?>