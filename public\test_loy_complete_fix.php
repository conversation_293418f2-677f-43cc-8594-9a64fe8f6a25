<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Complete Fix Test ✅</h2>";
    
    echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>What has been fixed:</h3>";
    echo "<ol>";
    echo "<li>✅ LOY invoices no longer load courses</li>";
    echo "<li>✅ Added API endpoint: <code>/api/users/{id}/financial-obligations</code></li>";
    echo "<li>✅ Created <code>loadUserFinancialObligations()</code> function</li>";
    echo "<li>✅ LOY invoices now load only rent and charges from financial obligations</li>";
    echo "<li>✅ Subject auto-fills with: <strong>LOYER + CHARGES</strong></li>";
    echo "<li>✅ Period now uses CURRENT month (e.g., JUILLET 2025)</li>";
    echo "<li>✅ Global invoice numbering sequence works across all types</li>";
    echo "</ol>";
    echo "</div>";
    
    // Check LOY invoice type
    $stmt = $db->prepare("SELECT * FROM invoice_types WHERE prefix = 'LOY'");
    $stmt->execute();
    $loyType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($loyType) {
        echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
        echo "<h3>LOY Invoice Type Status:</h3>";
        echo "<p style='color: green;'>✅ LOY invoice type exists in database</p>";
        echo "<ul>";
        echo "<li>ID: {$loyType['id']}</li>";
        echo "<li>Name: {$loyType['name']}</li>";
        echo "<li>Prefix: {$loyType['prefix']}</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Check a sample user's financial obligations
    echo "<h3>Sample Financial Obligations:</h3>";
    $stmt = $db->prepare("
        SELECT u.id, u.first_name, u.last_name, ufo.rent_amount, ufo.charges_amount 
        FROM users u
        LEFT JOIN user_financial_obligations ufo ON u.id = ufo.user_id AND ufo.end_date IS NULL
        WHERE ufo.rent_amount > 0
        LIMIT 3
    ");
    $stmt->execute();
    $obligations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($obligations) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>User</th><th>Rent Amount</th><th>Charges Amount</th><th>Total</th>";
        echo "</tr>";
        
        foreach ($obligations as $ob) {
            $total = $ob['rent_amount'] + $ob['charges_amount'];
            echo "<tr>";
            echo "<td>{$ob['first_name']} {$ob['last_name']}</td>";
            echo "<td>€ " . number_format($ob['rent_amount'], 2) . "</td>";
            echo "<td>€ " . number_format($ob['charges_amount'], 2) . "</td>";
            echo "<td><strong>€ " . number_format($total, 2) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No users with financial obligations found</p>";
    }
    
    // Test API endpoint
    echo "<h3>API Endpoint Test:</h3>";
    echo "<p>Test the API endpoint for user 6:</p>";
    echo "<pre style='background-color: #f0f0f0; padding: 10px; border-radius: 5px;'>";
    echo "GET /api/users/6/financial-obligations\n\n";
    echo "Expected response:\n";
    echo "{\n";
    echo '    "success": true,
    "data": {
        "rent_amount": "180.00",
        "charges_amount": "40.00"
    }
}';
    echo "</pre>";
    
    // Current date info
    $currentMonth = date('n');
    $currentYear = date('Y');
    $frenchMonths = [
        1 => 'JANVIER', 2 => 'FÉVRIER', 3 => 'MARS', 4 => 'AVRIL',
        5 => 'MAI', 6 => 'JUIN', 7 => 'JUILLET', 8 => 'AOÛT',
        9 => 'SEPTEMBRE', 10 => 'OCTOBRE', 11 => 'NOVEMBRE', 12 => 'DÉCEMBRE'
    ];
    
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Expected LOY Invoice Behavior:</h3>";
    echo "<ul>";
    echo "<li>Invoice Number: FAC-LOY-{$currentYear}-XXXX (next available sequence)</li>";
    echo "<li>Subject: <strong>LOYER + CHARGES</strong></li>";
    echo "<li>Period: <strong>{$frenchMonths[$currentMonth]} {$currentYear}</strong> (current month)</li>";
    echo "<li>Lines:";
    echo "<ol>";
    echo "<li>Loyer mensuel - Quantity: 1, VAT: 0%</li>";
    echo "<li>Charges location - Quantity: 1, VAT: 0%</li>";
    echo "</ol>";
    echo "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Clear browser cache (Ctrl+F5)</li>";
    echo "<li>Go to: <a href='/fit/public/invoices/create?type=loyer' target='_blank'><strong>/invoices/create?type=loyer</strong></a></li>";
    echo "<li>Select a user from Medical or Managers group</li>";
    echo "<li>The invoice should automatically load rent and charges</li>";
    echo "<li>Verify Subject is 'LOYER + CHARGES' and Period is '{$frenchMonths[$currentMonth]} {$currentYear}'</li>";
    echo "</ol>";
    
    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Important:</h3>";
    echo "<p>Make sure the selected user has financial obligations set up in their profile.</p>";
    echo "<p>You can check/edit at: <code>/users/{id}/financial-obligations</code></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<script>
console.log('LOY invoice fix complete - test page loaded');
</script>