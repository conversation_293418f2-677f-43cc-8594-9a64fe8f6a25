<?php
require_once '../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

// Database connection using .env
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing Bulk Generation Fix</h2>";
    
    // Test parameters - generating in July for June data
    $month = 7;  // July
    $year = 2025;
    
    echo "<p>Generating invoices in: $month/$year</p>";
    
    // Calculate data month (previous month for retrocession)
    $dataMonth = $month - 1;
    $dataYear = $year;
    if ($dataMonth < 1) {
        $dataMonth = 12;
        $dataYear--;
    }
    
    echo "<p>Using data from: $dataMonth/$dataYear</p>";
    
    // Run the exact query that bulk generation now uses
    $retQuery = "
        SELECT 
            u.id as user_id,
            CONCAT(u.first_name, ' ', u.last_name) as name,
            u.email,
            uma.cns_amount,
            uma.patient_amount,
            (uma.cns_amount + uma.patient_amount) as total_amount,
            ugi.invoice_id,
            i.invoice_number
        FROM users u
        INNER JOIN user_monthly_retrocession_amounts uma ON uma.user_id = u.id
        LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
            AND ugi.invoice_type = 'RET' 
            AND ugi.period_month = :month 
            AND ugi.period_year = :year
        LEFT JOIN invoices i ON i.id = ugi.invoice_id
        WHERE uma.month = :data_month 
        AND uma.year = :data_year
        AND uma.is_active = 1
        AND (uma.cns_amount > 0 OR uma.patient_amount > 0)
        ORDER BY u.first_name, u.last_name
    ";
    
    $stmt = $db->prepare($retQuery);
    $stmt->execute([
        'month' => $month,
        'year' => $year,
        'data_month' => $dataMonth,
        'data_year' => $dataYear
    ]);
    $retrocessionUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Results:</h3>";
    echo "<p>Found " . count($retrocessionUsers) . " users with June 2025 data</p>";
    
    if (count($retrocessionUsers) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User</th><th>CNS</th><th>Patient</th><th>Total</th><th>Status</th></tr>";
        
        foreach ($retrocessionUsers as $user) {
            $highlight = (strpos($user['name'], 'Rémi') !== false) ? "style='background-color: #d4edda;'" : "";
            echo "<tr $highlight>";
            echo "<td>{$user['name']}</td>";
            echo "<td>" . number_format($user['cns_amount'], 2) . " €</td>";
            echo "<td>" . number_format($user['patient_amount'], 2) . " €</td>";
            echo "<td>" . number_format($user['total_amount'], 2) . " €</td>";
            echo "<td>" . ($user['invoice_id'] ? "✓ Generated (#{$user['invoice_number']})" : "⏳ Ready to generate") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check specifically for Rémi
        $remiFound = false;
        foreach ($retrocessionUsers as $user) {
            if (strpos($user['name'], 'Rémi') !== false) {
                $remiFound = true;
                break;
            }
        }
        
        if ($remiFound) {
            echo "<p style='color: green;'>✓ Rémi Heine is in the list!</p>";
        } else {
            echo "<p style='color: red;'>✗ Rémi Heine is NOT in the list</p>";
        }
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>Fix Status:</h3>";
    echo "<ol>";
    echo "<li>✓ Bulk generation view now looks at previous month data</li>";
    echo "<li>✓ Query includes year field to match 2025 data</li>";
    echo "<li>✓ Should match what UnifiedInvoiceGenerator expects</li>";
    echo "</ol>";
    echo "<p><strong>Next step:</strong> Go to <a href='" . $_ENV['APP_URL'] . "/invoices/bulk-generation?tab=retrocession'>Bulk Generation Page</a> and try generating for July 2025</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}