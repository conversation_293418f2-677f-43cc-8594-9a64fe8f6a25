<?php
// Debug invoice data structure
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Load application bootstrap
require_once __DIR__ . '/../app/config/bootstrap.php';

$invoiceId = $_GET['id'] ?? 246;

// Load invoice data
$invoiceModel = new \App\Models\Invoice();
$invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);

if (!$invoice) {
    die("Invoice not found");
}

// Get raw database data
$db = Flight::db();
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = :id");
$stmt->execute(['id' => $invoiceId]);
$rawInvoice = $stmt->fetch(PDO::FETCH_ASSOC);

// Get invoice type data
$stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = :id");
$stmt->execute(['id' => $invoice['invoice_type_id']]);
$invoiceType = $stmt->fetch(PDO::FETCH_ASSOC);

// Get document type data
$stmt = $db->prepare("SELECT * FROM document_types WHERE id = :id");
$stmt->execute(['id' => $invoice['document_type_id']]);
$documentType = $stmt->fetch(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Debug Invoice Data</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        h2 { color: #333; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow: auto; }
        .highlight { background: yellow; }
    </style>
</head>
<body>
    <h1>Debug Invoice Data - ID: <?php echo $invoiceId; ?></h1>
    
    <h2>Raw Database Invoice Data:</h2>
    <pre><?php 
    echo "invoice_number: <span class='highlight'>" . htmlspecialchars($rawInvoice['invoice_number']) . "</span>\n";
    echo "issue_date: " . htmlspecialchars($rawInvoice['issue_date']) . "\n";
    echo "created_at: " . htmlspecialchars($rawInvoice['created_at']) . "\n";
    echo "invoice_type_id: " . htmlspecialchars($rawInvoice['invoice_type_id'] ?? '') . "\n";
    echo "document_type_id: " . htmlspecialchars($rawInvoice['document_type_id']) . "\n";
    ?></pre>
    
    <h2>Invoice Type Data:</h2>
    <pre><?php print_r($invoiceType); ?></pre>
    
    <h2>Document Type Data:</h2>
    <pre><?php print_r($documentType); ?></pre>
    
    <h2>Model Loaded Invoice Data:</h2>
    <pre><?php 
    echo "invoice['invoice_number']: <span class='highlight'>" . htmlspecialchars($invoice['invoice_number']) . "</span>\n";
    echo "invoice['type']['prefix']: " . htmlspecialchars($invoice['type']['prefix'] ?? 'NOT SET') . "\n";
    echo "invoice['document_type']['code']: " . htmlspecialchars($invoice['document_type']['code'] ?? 'NOT SET') . "\n";
    echo "invoice['invoice_type_code']: " . htmlspecialchars($invoice['invoice_type_code'] ?? 'NOT SET') . "\n";
    ?></pre>
    
    <h2>Full Invoice Model Data:</h2>
    <pre><?php print_r($invoice); ?></pre>
    
    <h2>Filename Generation Test:</h2>
    <?php
    // Test filename generation
    $invoiceYear = date('Y', strtotime($invoice['issue_date'] ?? $invoice['created_at'] ?? date('Y-m-d')));
    
    // Clean the invoice number
    $cleanInvoiceNumber = $invoice['invoice_number'];
    echo "<p>Original invoice_number: <span class='highlight'>" . htmlspecialchars($cleanInvoiceNumber) . "</span></p>";
    
    // Test regex patterns
    if (preg_match('/^([A-Z]{3})-([A-Z]{3,5})-(\d{4})-(\d+)$/i', $cleanInvoiceNumber, $matches)) {
        echo "<p>Full format regex matched! Full match: " . htmlspecialchars($matches[0]) . "</p>";
        echo "<p>Doc prefix: " . htmlspecialchars($matches[1]) . "</p>";
        echo "<p>Type prefix: " . htmlspecialchars($matches[2]) . "</p>";
        echo "<p>Year: " . htmlspecialchars($matches[3]) . "</p>";
        echo "<p>Number part: " . htmlspecialchars($matches[4]) . "</p>";
        $cleanInvoiceNumber = $matches[4];
    } elseif (preg_match('/^(INVOICE|FAC|FACTURE)-\d{4}-(.+)$/i', $cleanInvoiceNumber, $matches)) {
        echo "<p>Old format regex matched! Full match: " . htmlspecialchars($matches[0]) . "</p>";
        echo "<p>Prefix part: " . htmlspecialchars($matches[1]) . "</p>";
        echo "<p>Number part: " . htmlspecialchars($matches[2]) . "</p>";
        $cleanInvoiceNumber = $matches[2];
    } else {
        echo "<p>No regex pattern matched</p>";
    }
    
    echo "<p>Cleaned invoice_number: <span class='highlight'>" . htmlspecialchars($cleanInvoiceNumber) . "</span></p>";
    
    // Build filename as invoice-pdf.php would
    $docPrefix = 'FAC';
    if (!empty($invoice['document_type']['prefix'])) {
        // Use the prefix field from document_type, not the code
        $docPrefix = strtoupper($invoice['document_type']['prefix']);
    } elseif (!empty($invoice['document_type']['code'])) {
        // Fallback: map common codes to prefixes
        $docCodeMap = [
            'invoice' => 'FAC',
            'credit_note' => 'AVO',
            'quote' => 'DEV',
            'proforma' => 'PRO'
        ];
        $docPrefix = $docCodeMap[strtolower($invoice['document_type']['code'])] ?? 'FAC';
    }
    
    // Debug: Show what we're getting
    echo "<p>Debug document_type data:</p>";
    echo "<pre>";
    echo "invoice['document_type']['code'] = " . htmlspecialchars($invoice['document_type']['code'] ?? 'NULL') . "\n";
    echo "invoice['document_type']['prefix'] = " . htmlspecialchars($invoice['document_type']['prefix'] ?? 'NULL') . "\n";
    echo "Expected docPrefix should be: FAC (from document_type prefix field)\n";
    echo "Actual docPrefix being used: " . htmlspecialchars($docPrefix) . "\n";
    echo "</pre>";
    
    $typePrefix = '';
    // Check type_details first (this is where the prefix is actually stored)
    if (!empty($invoice['type_details']['prefix'])) {
        $typePrefix = strtoupper($invoice['type_details']['prefix']);
    } elseif (!empty($invoice['type']['prefix'])) {
        $typePrefix = strtoupper($invoice['type']['prefix']);
    } elseif (!empty($invoice['invoice_type_code'])) {
        $typePrefixMap = [
            'rental' => 'LOY',
            'loyer' => 'LOY',
            'retrocession_30' => 'RET30',
            'retrocession_25' => 'RET25',
            'retrocession' => 'RET',
            'service' => 'SRV',
            'hourly' => 'HOR'
        ];
        $typePrefix = $typePrefixMap[$invoice['invoice_type_code']] ?? '';
    }
    
    // Debug type prefix
    echo "<p>Debug type_prefix data:</p>";
    echo "<pre>";
    echo "invoice['type_details']['prefix'] = " . htmlspecialchars($invoice['type_details']['prefix'] ?? 'NULL') . "\n";
    echo "invoice['type']['prefix'] = " . htmlspecialchars($invoice['type']['prefix'] ?? 'NULL') . "\n";
    echo "</pre>";
    
    echo "<p>Doc Prefix: " . htmlspecialchars($docPrefix) . "</p>";
    echo "<p>Type Prefix: " . htmlspecialchars($typePrefix) . "</p>";
    echo "<p>Year: " . htmlspecialchars($invoiceYear) . "</p>";
    
    if ($typePrefix) {
        $filename = sprintf('%s-%s-%s-%s.pdf', $docPrefix, $typePrefix, $invoiceYear, $cleanInvoiceNumber);
    } else {
        $filename = sprintf('%s-%s-%s.pdf', $docPrefix, $invoiceYear, $cleanInvoiceNumber);
    }
    
    echo "<p>Final filename: <span class='highlight'>" . htmlspecialchars($filename) . "</span></p>";
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/invoice-pdf.php?id=<?php echo $invoiceId; ?>&action=download">Test Download PDF</a> | 
        <a href="/fit/public/invoices/<?php echo $invoiceId; ?>">Back to Invoice</a>
    </p>
</body>
</html>