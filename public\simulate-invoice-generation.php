<?php
/**
 * Web-based Invoice Generation Simulation
 * This displays how invoices would be generated without making any database changes
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Parse query parameters
$filterMonth = $_GET['month'] ?? date('n');
$filterYear = $_GET['year'] ?? date('Y');
$filterUser = $_GET['user'] ?? null;
$filterType = $_GET['type'] ?? null;

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Helper function to format money
function formatMoney($amount) {
    return '€' . number_format($amount, 2);
}

// Helper function to calculate VAT
function calculateVAT($amount, $rate, $isInclusive = true) {
    if ($isInclusive) {
        // Amount includes VAT, extract base and VAT
        $base = $amount / (1 + $rate / 100);
        $vat = $amount - $base;
        return ['base' => $base, 'vat' => $vat, 'total' => $amount];
    } else {
        // Amount excludes VAT, add VAT
        $vat = $amount * ($rate / 100);
        $total = $amount + $vat;
        return ['base' => $amount, 'vat' => $vat, 'total' => $total];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generation Simulation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .filter-form {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .filter-form input, .filter-form select {
            margin: 0 10px 10px 0;
            padding: 5px 10px;
        }
        .invoice-type {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .user-invoice {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .invoice-lines {
            margin: 10px 0;
            padding: 10px;
            background: #f1f3f4;
            border-radius: 3px;
        }
        .invoice-line {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #ccc;
        }
        .invoice-line:last-child {
            border-bottom: none;
        }
        .total {
            font-weight: bold;
            color: #28a745;
            font-size: 1.1em;
            margin-top: 10px;
        }
        .warning {
            color: #ffc107;
            background: #fff3cd;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .summary {
            background: #e9ecef;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }
        .note {
            color: #6c757d;
            font-style: italic;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Invoice Generation Simulation</h1>
        
        <div class="filter-form">
            <form method="GET">
                <label>Month: 
                    <select name="month">
                        <?php for($m = 1; $m <= 12; $m++): ?>
                            <option value="<?= $m ?>" <?= $m == $filterMonth ? 'selected' : '' ?>>
                                <?= date('F', mktime(0, 0, 0, $m, 1)) ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </label>
                
                <label>Year: 
                    <input type="number" name="year" value="<?= $filterYear ?>" min="2020" max="2030">
                </label>
                
                <label>User ID: 
                    <input type="number" name="user" value="<?= $filterUser ?>" placeholder="All users">
                </label>
                
                <label>Type: 
                    <select name="type">
                        <option value="">All Types</option>
                        <option value="RET" <?= $filterType === 'RET' ? 'selected' : '' ?>>RET (Retrocession)</option>
                        <option value="LOY" <?= $filterType === 'LOY' ? 'selected' : '' ?>>LOY (Loyer)</option>
                        <option value="LOC" <?= $filterType === 'LOC' ? 'selected' : '' ?>>LOC (Location)</option>
                    </select>
                </label>
                
                <button type="submit">Simulate</button>
            </form>
        </div>

        <?php
        // Check available invoice types
        $stmt = $pdo->query("SELECT * FROM config_invoice_types WHERE prefix IN ('RET', 'LOY', 'LOC')");
        $invoiceTypes = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $invoiceTypes[$row['prefix']] = $row;
        }
        
        if (!isset($invoiceTypes['RET'])) {
            echo '<div class="warning">⚠️ RET invoice type not configured in config_invoice_types table</div>';
        }
        ?>

        <?php if (!$filterType || $filterType === 'RET'): ?>
            <div class="invoice-type">
                <h2>RET (Retrocession) Invoices</h2>
                
                <?php
                // Get users with retrocession data
                $sql = "
                    SELECT 
                        u.id as user_id,
                        u.first_name,
                        u.last_name,
                        uma.cns_amount,
                        uma.patient_amount,
                        urs.cns_value,
                        urs.patient_value,
                        urs.cns_label,
                        urs.patient_label,
                        ugi.invoice_id as existing_invoice_id,
                        i.invoice_number as existing_invoice_number
                    FROM user_monthly_retrocession_amounts uma
                    JOIN users u ON u.id = uma.user_id
                    LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
                    LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
                        AND ugi.invoice_type = 'RET' 
                        AND ugi.period_month = :month 
                        AND ugi.period_year = :year
                    LEFT JOIN invoices i ON i.id = ugi.invoice_id
                    WHERE uma.month = :month2
                ";
                
                if ($filterUser) {
                    $sql .= " AND u.id = :user_id";
                }
                
                $stmt = $pdo->prepare($sql);
                $params = ['month' => $filterMonth, 'year' => $filterYear, 'month2' => $filterMonth, 'year2' => $filterYear];
                if ($filterUser) {
                    $params['user_id'] = $filterUser;
                }
                $stmt->execute($params);
                
                $retCount = 0;
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $retCount++;
                    $name = $row['first_name'] . ' ' . $row['last_name'];
                    ?>
                    
                    <div class="user-invoice">
                        <h3>User #<?= $row['user_id'] ?>: <?= htmlspecialchars($name) ?></h3>
                        
                        <?php if ($row['existing_invoice_id']): ?>
                            <div class="warning">⚠️ Invoice already exists: <?= htmlspecialchars($row['existing_invoice_number']) ?></div>
                        <?php elseif (!$row['cns_value'] || !$row['patient_value']): ?>
                            <div class="error">✗ Missing retrocession settings (percentages)</div>
                        <?php else: ?>
                            <div>
                                <strong>Amounts:</strong> CNS: <?= formatMoney($row['cns_amount']) ?>, 
                                Patient: <?= formatMoney($row['patient_amount']) ?><br>
                                <strong>Percentages:</strong> CNS: <?= $row['cns_value'] ?>%, 
                                Patient: <?= $row['patient_value'] ?>%
                            </div>
                            
                            <div class="invoice-lines">
                                <strong>Invoice Lines:</strong>
                                <?php
                                $cnsRetrocession = $row['cns_amount'] * ($row['cns_value'] / 100);
                                $patientRetrocession = $row['patient_amount'] * ($row['patient_value'] / 100);
                                $subtotal = $cnsRetrocession + $patientRetrocession;
                                $secretaryBase = $subtotal * 0.10;
                                $secretaryVAT = calculateVAT($secretaryBase, 17, false);
                                ?>
                                
                                <div class="invoice-line">
                                    1. <?= htmlspecialchars($row['cns_label'] ?: 'Rétrocession CNS') ?>: 
                                    <?= formatMoney($cnsRetrocession) ?> (0% VAT)
                                </div>
                                <div class="invoice-line">
                                    2. <?= htmlspecialchars($row['patient_label'] ?: 'Rétrocession Patient') ?>: 
                                    <?= formatMoney($patientRetrocession) ?> (0% VAT)
                                </div>
                                <div class="invoice-line">
                                    3. Secrétariat (10%): <?= formatMoney($secretaryVAT['base']) ?> + 
                                    <?= formatMoney($secretaryVAT['vat']) ?> VAT = <?= formatMoney($secretaryVAT['total']) ?>
                                </div>
                                
                                <div class="total">TOTAL: <?= formatMoney($subtotal + $secretaryVAT['total']) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php } ?>
                
                <?php if ($retCount === 0): ?>
                    <div class="warning">No users found with retrocession data for <?= $filterMonth ?>/<?= $filterYear ?></div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if (!$filterType || $filterType === 'LOY'): ?>
            <div class="invoice-type">
                <h2>LOY (Loyer/Rent) Invoices</h2>
                
                <?php
                // Get users with financial obligations
                $sql = "
                    SELECT 
                        u.id as user_id,
                        u.first_name,
                        u.last_name,
                        ufo.rent_amount,
                        ufo.charges_amount,
                        ufo.secretary_tvac_17,
                        ufo.secretary_htva,
                        ufo.tva_17,
                        ugi.invoice_id as existing_invoice_id,
                        i.invoice_number as existing_invoice_number
                    FROM user_financial_obligations ufo
                    JOIN users u ON u.id = ufo.user_id
                    LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
                        AND ugi.invoice_type = 'LOY' 
                        AND ugi.period_month = :month 
                        AND ugi.period_year = :year
                    LEFT JOIN invoices i ON i.id = ugi.invoice_id
                    WHERE 1=1
                ";
                
                if ($filterUser) {
                    $sql .= " AND u.id = :user_id";
                }
                
                $stmt = $pdo->prepare($sql);
                $params = ['month' => $filterMonth, 'year' => $filterYear];
                if ($filterUser) {
                    $params['user_id'] = $filterUser;
                }
                $stmt->execute($params);
                
                $loyCount = 0;
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // Skip if no amounts configured
                    if (!$row['rent_amount'] && !$row['charges_amount'] && 
                        !$row['secretary_tvac_17'] && !$row['secretary_htva']) {
                        continue;
                    }
                    
                    $loyCount++;
                    $name = $row['first_name'] . ' ' . $row['last_name'];
                    ?>
                    
                    <div class="user-invoice">
                        <h3>User #<?= $row['user_id'] ?>: <?= htmlspecialchars($name) ?></h3>
                        
                        <?php if ($row['existing_invoice_id']): ?>
                            <div class="warning">⚠️ Invoice already exists: <?= htmlspecialchars($row['existing_invoice_number']) ?></div>
                        <?php else: ?>
                            <div class="invoice-lines">
                                <strong>Invoice Lines:</strong>
                                <?php
                                $totalAmount = 0;
                                $lineNum = 1;
                                
                                if ($row['rent_amount'] > 0) {
                                    echo '<div class="invoice-line">';
                                    echo $lineNum . '. Loyer: ' . formatMoney($row['rent_amount']) . ' (0% VAT)';
                                    echo '</div>';
                                    $totalAmount += $row['rent_amount'];
                                    $lineNum++;
                                }
                                
                                if ($row['charges_amount'] > 0) {
                                    echo '<div class="invoice-line">';
                                    echo $lineNum . '. Charges: ' . formatMoney($row['charges_amount']) . ' (0% VAT)';
                                    echo '</div>';
                                    $totalAmount += $row['charges_amount'];
                                    $lineNum++;
                                }
                                
                                if ($row['secretary_tvac_17'] > 0) {
                                    $vat = calculateVAT($row['secretary_tvac_17'], 17, true);
                                    echo '<div class="invoice-line">';
                                    echo $lineNum . '. Secrétariat: ' . formatMoney($vat['base']) . ' + ' . 
                                         formatMoney($vat['vat']) . ' VAT = ' . formatMoney($vat['total']);
                                    echo '</div>';
                                    $totalAmount += $row['secretary_tvac_17'];
                                } elseif ($row['secretary_htva'] > 0) {
                                    $totalSecretary = $row['secretary_htva'] + ($row['tva_17'] ?? 0);
                                    echo '<div class="invoice-line">';
                                    echo $lineNum . '. Secrétariat: ' . formatMoney($row['secretary_htva']) . ' + ' . 
                                         formatMoney($row['tva_17'] ?? 0) . ' VAT = ' . formatMoney($totalSecretary);
                                    echo '</div>';
                                    $totalAmount += $totalSecretary;
                                }
                                ?>
                                
                                <div class="total">TOTAL: <?= formatMoney($totalAmount) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php } ?>
                
                <?php if ($loyCount === 0): ?>
                    <div class="warning">No users found with financial obligations</div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if (!$filterType || $filterType === 'LOC'): ?>
            <div class="invoice-type">
                <h2>LOC (Location/Course) Invoices</h2>
                
                <?php
                // Get users with course data
                $sql = "
                    SELECT 
                        u.id as user_id,
                        u.first_name,
                        u.last_name,
                        cc.course_id,
                        cc.hours_count,
                        c.course_name,
                        c.hourly_rate,
                        c.vat_rate,
                        ugi.invoice_id as existing_invoice_id,
                        i.invoice_number as existing_invoice_number
                    FROM user_monthly_course_counts cc
                    JOIN users u ON u.id = cc.user_id
                    JOIN user_courses c ON c.id = cc.course_id
                    LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
                        AND ugi.invoice_type = 'LOC' 
                        AND ugi.period_month = :month 
                        AND ugi.period_year = :year
                    LEFT JOIN invoices i ON i.id = ugi.invoice_id
                    WHERE cc.month = :month2 AND cc.year = :year2
                ";
                
                if ($filterUser) {
                    $sql .= " AND u.id = :user_id";
                }
                
                $sql .= " ORDER BY u.id, c.course_name";
                
                $stmt = $pdo->prepare($sql);
                $params = ['month' => $filterMonth, 'year' => $filterYear, 'month2' => $filterMonth, 'year2' => $filterYear];
                if ($filterUser) {
                    $params['user_id'] = $filterUser;
                }
                $stmt->execute($params);
                
                $currentUserId = null;
                $userCourses = [];
                $locCount = 0;
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    if ($currentUserId !== $row['user_id']) {
                        // Display previous user if any
                        if ($currentUserId !== null && !empty($userCourses)) {
                            displayUserCourses($userCourses);
                        }
                        
                        $currentUserId = $row['user_id'];
                        $userCourses = [];
                        
                        if (!$row['existing_invoice_id']) {
                            $locCount++;
                        }
                    }
                    
                    if ($currentUserId !== null) {
                        $userCourses[] = $row;
                    }
                }
                
                // Display last user
                if ($currentUserId !== null && !empty($userCourses)) {
                    displayUserCourses($userCourses);
                }
                
                if ($locCount === 0): ?>
                    <div class="warning">No users found with course data for <?= $filterMonth ?>/<?= $filterYear ?></div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <div class="summary">
            <h2>Summary</h2>
            <p>Period: <?= $filterMonth ?>/<?= $filterYear ?></p>
            
            <?php
            // Count potential invoices
            $sql = "SELECT 
                'RET' as type,
                COUNT(DISTINCT uma.user_id) as count
            FROM user_monthly_retrocession_amounts uma
            LEFT JOIN user_generated_invoices ugi ON ugi.user_id = uma.user_id 
                AND ugi.invoice_type = 'RET' 
                AND ugi.period_month = :month 
                AND ugi.period_year = :year
            WHERE uma.month = :month2 AND uma.year = :year2 AND ugi.id IS NULL
            
            UNION ALL
            
            SELECT 
                'LOY' as type,
                COUNT(DISTINCT ufo.user_id) as count
            FROM user_financial_obligations ufo
            LEFT JOIN user_generated_invoices ugi ON ugi.user_id = ufo.user_id 
                AND ugi.invoice_type = 'LOY' 
                AND ugi.period_month = :month3 
                AND ugi.period_year = :year3
            WHERE ugi.id IS NULL
            
            UNION ALL
            
            SELECT 
                'LOC' as type,
                COUNT(DISTINCT cc.user_id) as count
            FROM user_monthly_course_counts cc
            LEFT JOIN user_generated_invoices ugi ON ugi.user_id = cc.user_id 
                AND ugi.invoice_type = 'LOC' 
                AND ugi.period_month = :month4
                AND ugi.period_year = :year4
            WHERE cc.month = :month5 AND cc.year = :year5 AND ugi.id IS NULL";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                'month' => $filterMonth, 'year' => $filterYear,
                'month2' => $filterMonth, 'year2' => $filterYear,
                'month3' => $filterMonth, 'year3' => $filterYear,
                'month4' => $filterMonth, 'year4' => $filterYear,
                'month5' => $filterMonth, 'year5' => $filterYear
            ]);
            ?>
            
            <h3>Potential invoices to generate:</h3>
            <ul>
                <?php while ($row = $stmt->fetch(PDO::FETCH_ASSOC)): ?>
                    <li><?= $row['type'] ?>: <?= $row['count'] ?> invoices</li>
                <?php endwhile; ?>
            </ul>
        </div>

        <div class="note">
            <p><strong>Note:</strong> This is a simulation. No invoices were created.</p>
            <p>To generate actual invoices, use the <a href="/fit/public/invoices/bulk-generation">bulk generation page</a>.</p>
        </div>
    </div>
</body>
</html>

<?php
// Helper function to display course invoices
function displayUserCourses($courses) {
    if (empty($courses)) return;
    
    $firstCourse = $courses[0];
    $name = $firstCourse['first_name'] . ' ' . $firstCourse['last_name'];
    ?>
    <div class="user-invoice">
        <h3>User #<?= $firstCourse['user_id'] ?>: <?= htmlspecialchars($name) ?></h3>
        
        <?php if ($firstCourse['existing_invoice_id']): ?>
            <div class="warning">⚠️ Invoice already exists: <?= htmlspecialchars($firstCourse['existing_invoice_number']) ?></div>
        <?php else: ?>
            <div class="invoice-lines">
                <strong>Invoice Lines:</strong>
                <?php
                $totalAmount = 0;
                $lineNum = 1;
                
                foreach ($courses as $course) {
                    $subtotal = $course['hours_count'] * $course['hourly_rate'];
                    $vat = calculateVAT($subtotal, $course['vat_rate'], false);
                    
                    echo '<div class="invoice-line">';
                    echo $lineNum . '. ' . htmlspecialchars($course['course_name']) . ': ';
                    echo $course['hours_count'] . 'h × ' . formatMoney($course['hourly_rate']) . '/h = ' . formatMoney($subtotal);
                    if ($course['vat_rate'] > 0) {
                        echo ' + ' . formatMoney($vat['vat']) . ' VAT (' . $course['vat_rate'] . '%) = ' . formatMoney($vat['total']);
                    }
                    echo '</div>';
                    
                    $totalAmount += $vat['total'];
                    $lineNum++;
                }
                ?>
                
                <div class="total">TOTAL: <?= formatMoney($totalAmount) ?></div>
            </div>
        <?php endif; ?>
    </div>
    <?php
}
?>