<?php
/**
 * Configure PHP mail settings for WAMP64
 * 
 * This script updates php.ini to use Mailhog as the SMTP server.
 * Run this script as administrator for best results.
 */

echo "PHP Mail Configuration for Mailhog\n";
echo "==================================\n\n";

// Get PHP configuration file location
$phpIniPath = php_ini_loaded_file();
echo "PHP configuration file: $phpIniPath\n\n";

// Display current mail settings
echo "Current mail settings:\n";
echo "SMTP = " . ini_get('SMTP') . "\n";
echo "smtp_port = " . ini_get('smtp_port') . "\n";
echo "sendmail_from = " . ini_get('sendmail_from') . "\n\n";

// Create a backup of php.ini
$backupPath = $phpIniPath . '.backup_' . date('Y-m-d_His');
echo "Creating backup: $backupPath\n";
if (copy($phpIniPath, $backupPath)) {
    echo "✓ Backup created successfully\n\n";
} else {
    echo "✗ Failed to create backup. Please run as administrator.\n";
    exit(1);
}

// Read php.ini content
$phpIniContent = file_get_contents($phpIniPath);

// Update mail settings
$updates = [
    'SMTP = localhost' => 'SMTP = localhost',
    'smtp_port = 25' => 'smtp_port = 1025',
    ';sendmail_from = <EMAIL>' => 'sendmail_from = <EMAIL>'
];

echo "Updating mail settings:\n";
foreach ($updates as $search => $replace) {
    // Check if setting exists
    if (preg_match('/^' . preg_quote(explode(' = ', $search)[0], '/') . '\s*=.*/m', $phpIniContent)) {
        // Update existing setting
        $phpIniContent = preg_replace(
            '/^' . preg_quote(explode(' = ', $search)[0], '/') . '\s*=.*/m',
            $replace,
            $phpIniContent
        );
        echo "✓ Updated: $replace\n";
    } else {
        // Add new setting in [mail function] section
        $phpIniContent = preg_replace(
            '/(\[mail function\].*?)(\r?\n\r?\n|\z)/s',
            "$1\n$replace$2",
            $phpIniContent
        );
        echo "✓ Added: $replace\n";
    }
}

// Write updated content back to php.ini
echo "\nWriting changes to php.ini...\n";
if (file_put_contents($phpIniPath, $phpIniContent)) {
    echo "✓ php.ini updated successfully!\n\n";
    
    echo "IMPORTANT: Please restart Apache for changes to take effect:\n";
    echo "1. Right-click on WAMP icon in system tray\n";
    echo "2. Select 'Restart All Services'\n\n";
    
    echo "After restart, emails sent via PHP mail() will be captured by Mailhog.\n";
    echo "View emails at: http://localhost:8025\n";
} else {
    echo "✗ Failed to update php.ini. Please run as administrator.\n";
    echo "You can manually update these settings in php.ini:\n";
    foreach ($updates as $search => $replace) {
        echo "  $replace\n";
    }
}

echo "\nAlternative: Runtime Configuration\n";
echo "===================================\n";
echo "If you cannot modify php.ini, add these lines to your PHP scripts:\n";
echo "ini_set('SMTP', 'localhost');\n";
echo "ini_set('smtp_port', '1025');\n";
echo "ini_set('sendmail_from', '<EMAIL>');\n";