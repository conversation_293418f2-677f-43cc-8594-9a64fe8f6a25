<?php

namespace App\Controllers;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\User;
use App\Models\RateProfile;
use App\Models\DocumentType;
use App\Services\RetrocessionCalculator;
use App\Helpers\MoneyHelper;
use App\Helpers\InvoiceTTCHelper;
use App\Services\EmailService;
use Exception;
use PDO;

class InvoiceController extends \App\Core\Controller
{
    private $invoice;
    private $client;
    private $rateProfile;
    private $documentType;
    private $retrocessionCalculator;
    private $invoiceTypesCache = null;
    
    public function __construct()
    {
        // Don't call parent constructor if it doesn't exist
        $this->invoice = new Invoice();
        $this->client = new Client();
        $this->rateProfile = new RateProfile();
        $this->documentType = new DocumentType();
        $this->retrocessionCalculator = new RetrocessionCalculator();
    }
    
    /**
     * List invoices with filters
     */
    public function index()
    {
        // Check if we're resetting filters
        if (Flight::request()->query->reset_filters === '1') {
            // Clear will be handled by JavaScript
            $this->redirect('/invoices');
            return;
        }
        
        // Get filters from query string
        $filters = [
            'status' => Flight::request()->query->status,
            'type' => Flight::request()->query->type,
            'document_type' => Flight::request()->query->document_type,
            'client_id' => Flight::request()->query->client_id,
            'date_from' => Flight::request()->query->date_from,
            'date_to' => Flight::request()->query->date_to,
            'search' => Flight::request()->query->search
        ];
        
        $page = Flight::request()->query->page ?: 1;
        $limit = Flight::request()->query->limit ?: 20;
        
        $invoices = $this->getInvoices($filters, $page, $limit);
        $clients = $this->client->getAll(['client_type' => 'practitioner']);
        $invoiceTypes = $this->getInvoiceTypes();
        
        // Format invoice types for select dropdown
        $invoiceTypesForSelect = [];
        foreach ($invoiceTypes as $type) {
            // Use the ID as the key instead of the JSON name
            $invoiceTypesForSelect[$type['id']] = $type['display_name'];
        }
        
        // Get document types for filter
        $documentTypes = DocumentType::getActiveTypes();
        $documentTypesForSelect = [];
        foreach ($documentTypes as $docType) {
            $documentTypesForSelect[$docType['id']] = $docType['name'];
        }
        
        // Get statistics
        $statistics = $this->getInvoiceStatistics();
        
        // Get column order configuration
        $columnOrderConfig = $this->getTableColumnOrder('invoices');
        
        // Convert column order from ID mapping to index array
        // Define the column IDs in their default order
        // Note: The tableHelper macro automatically adds checkbox as first column when showCheckbox=true
        // So the actual indices in the rendered table are:
        // 0: checkbox (auto-added), 1: invoice_number, 2: document_type, etc.
        $columnIds = ['checkbox', 'invoice_number', 'document_type', 'invoice_type', 'client_patient', 'issue_date', 'due_date', 'amount', 'status', 'actions'];
        
        // Convert the config to an array of indices for the table helper
        $columnOrder = [];
        if (!empty($columnOrderConfig)) {
            // Create position to index mapping
            $positionToIndex = [];
            foreach ($columnOrderConfig as $columnId => $position) {
                // Find the original index of this column
                $originalIndex = array_search($columnId, $columnIds);
                if ($originalIndex !== false) {
                    $positionToIndex[intval($position)] = $originalIndex;
                }
            }
            
            // Fill the column order array
            for ($i = 0; $i < count($columnIds); $i++) {
                if (isset($positionToIndex[$i])) {
                    $columnOrder[] = $positionToIndex[$i];
                }
            }
            
            // Add any missing columns at the end
            $usedIndices = array_values($columnOrder);
            for ($i = 0; $i < count($columnIds); $i++) {
                if (!in_array($i, $usedIndices)) {
                    $columnOrder[] = $i;
                }
            }
        }
        
        // Get template preference
        $template = $this->getTemplate();
        // Use standard modern version
        $viewName = 'invoices/index-modern';
        
        $this->render($viewName, [
            'invoices' => $invoices['data'],
            'pagination' => $invoices['pagination'],
            'current_page' => $invoices['pagination']['page'],
            'total_pages' => $invoices['pagination']['pages'],
            'filters' => $filters,
            'clients' => $clients,
            'invoiceTypes' => $invoiceTypesForSelect,
            'documentTypes' => $documentTypesForSelect,
            'statistics' => $statistics,
            'statuses' => [
                Invoice::STATUS_DRAFT => __('invoices.status.draft'),
                Invoice::STATUS_SENT => __('invoices.status.sent'),
                Invoice::STATUS_PAID => __('invoices.status.paid'),
                Invoice::STATUS_PARTIAL => __('invoices.status.partial'),
                Invoice::STATUS_OVERDUE => __('invoices.status.overdue'),
                Invoice::STATUS_CANCELLED => __('invoices.status.cancelled')
            ],
            'currency' => Flight::get('config')['currency_symbol'] ?? '€',
            'columnOrder' => $columnOrder,
            'canGenerateMonthly' => User::canEditFinancialObligations($_SESSION['user_id'] ?? 0),
            'isAdmin' => User::isAdmin($_SESSION['user_id'] ?? 0)
        ]);
    }
    
    /**
     * Show create invoice form
     */
    public function create()
    {
        $type = Flight::request()->query->type ?: 'rental';
        $clientId = Flight::request()->query->client_id;
        $duplicateId = Flight::request()->query->duplicate;
        
        $client = $clientId ? $this->client->getById($clientId) : null;
        $invoiceTypes = $this->getInvoiceTypes();
        $vatRates = $this->getVatRates();
        $templates = $this->getInvoiceTemplates($type);
        
        // Handle duplication
        $duplicateData = null;
        if ($duplicateId) {
            $duplicateInvoice = $this->invoice->getById($duplicateId);
            if ($duplicateInvoice) {
                // Get invoice items/lines
                $db = Flight::db();
                $stmt = $db->prepare("
                    SELECT il.*, ci.code as item_code, ci.name as item_name
                    FROM invoice_lines il
                    LEFT JOIN catalog_items ci ON il.item_id = ci.id
                    WHERE il.invoice_id = :invoice_id
                    ORDER BY il.id
                ");
                $stmt->execute(['invoice_id' => $duplicateId]);
                $duplicateItems = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                
                // Prepare duplicate data with correct billable logic
                $duplicateData = [
                    'type_id' => $duplicateInvoice['type_id'],
                    'invoice_type_id' => $duplicateInvoice['type_id'], // Add this for the form field
                    'document_type_id' => $duplicateInvoice['document_type_id'],
                    'client_id' => $duplicateInvoice['client_id'],
                    'user_id' => $duplicateInvoice['user_id'],
                    'subject' => $duplicateInvoice['subject'] ?? '',
                    'period' => $duplicateInvoice['period'] ?? null,
                    'items' => $duplicateItems,
                    'notes' => $duplicateInvoice['notes'] ?? '',
                    'internal_notes' => $duplicateInvoice['internal_notes'] ?? '',
                    'payment_term_id' => $duplicateInvoice['payment_term_id'] ?? null,
                    'cns_base_amount' => $duplicateInvoice['cns_base_amount'] ?? 0,
                    'secretariat_vat_amount' => $duplicateInvoice['secretariat_vat_amount'] ?? 0
                ];
                
                // Set billable_type and billable_id based on what's actually set
                if ($duplicateInvoice['user_id']) {
                    $duplicateData['billable_type'] = 'user';
                    $duplicateData['billable_id'] = $duplicateInvoice['user_id'];
                } elseif ($duplicateInvoice['client_id']) {
                    $duplicateData['billable_type'] = 'client';
                    $duplicateData['billable_id'] = $duplicateInvoice['client_id'];
                } else {
                    // Handle the case where neither is set (shouldn't happen in practice)
                    $duplicateData['billable_type'] = $duplicateInvoice['billable_type'] ?? 'client';
                    $duplicateData['billable_id'] = $duplicateInvoice['billable_id'] ?? null;
                }
                
                // Override client if coming from duplication
                if ($duplicateInvoice['client_id']) {
                    $client = $this->client->getById($duplicateInvoice['client_id']);
                    $clientId = $duplicateInvoice['client_id'];
                }
            }
        }
        
        // Get all active clients for dropdown
        $clients = $this->client->getAllActive();
        
        // Format client names for display
        foreach ($clients as &$c) {
            $c['name'] = trim($c['company_name'] ?: $c['first_name'] . ' ' . $c['last_name']);
        }
        
        // Get all active users who can be invoiced
        $db = Flight::db();
        
        // Get coach and practitioner group IDs from config
        $coachGroupId = 24; // Default coach group (updated to match your setup)
        $practitionerGroupId = 4; // Default practitioner group (Kiné)
        
        // Try config_settings first, then config table
        try {
            $configStmt = $db->prepare("SELECT value FROM config_settings WHERE `key` = 'coach_group_id' OR `name` = 'coach_group_id'");
            $configStmt->execute();
            $configValue = $configStmt->fetchColumn();
            if ($configValue !== false) {
                $coachGroupId = intval($configValue);
            }
        } catch (\Exception $e) {
            // Try config table
            try {
                $configStmt = $db->prepare("SELECT value FROM config WHERE `key` = 'coach_group_id'");
                $configStmt->execute();
                $configValue = $configStmt->fetchColumn();
                if ($configValue !== false) {
                    $coachGroupId = intval($configValue);
                }
            } catch (\Exception $e2) {
                // Use default if neither table works
            }
        }
        
        try {
            $configStmt = $db->prepare("SELECT value FROM config_settings WHERE name = 'practitioner_group_id'");
            $configStmt->execute();
            $configValue = $configStmt->fetchColumn();
            if ($configValue !== false) {
                $practitionerGroupId = intval($configValue);
            }
        } catch (\Exception $e) {
            // Use default if config not found
        }
        
        $stmt = $db->prepare("
            SELECT u.id, u.username, u.email, 
                   CONCAT(u.first_name, ' ', u.last_name) as name,
                   u.billing_email, u.billing_address, u.billing_city,
                   u.billing_postal_code, u.billing_country, u.vat_number,
                   u.course_name, u.hourly_rate, u.hourly_vat_rate,
                   uip.invoice_language, uip.discount_percentage,
                   -- Get first active course from user_courses table
                   (SELECT uc.course_name FROM user_courses uc 
                    WHERE uc.user_id = u.id AND uc.is_active = 1 
                    ORDER BY uc.display_order LIMIT 1) as primary_course_name,
                   -- Flag to identify coaches (members of coach group)
                   CASE WHEN ugm.user_id IS NOT NULL THEN 1 ELSE 0 END as is_coach,
                   -- Flag to identify practitioners (members of practitioner group)
                   CASE WHEN ugm2.user_id IS NOT NULL THEN 1 ELSE 0 END as is_practitioner
            FROM users u
            LEFT JOIN user_invoice_preferences uip ON u.id = uip.user_id
            LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.group_id = :coach_group_id
            LEFT JOIN user_group_members ugm2 ON u.id = ugm2.user_id AND ugm2.group_id = :practitioner_group_id
            WHERE u.is_active = 1 AND u.can_be_invoiced = 1
            ORDER BY u.first_name, u.last_name
        ");
        $stmt->execute([
            'coach_group_id' => $coachGroupId,
            'practitioner_group_id' => $practitionerGroupId
        ]);
        $users = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Separate coaches from regular users for better organization
        $coaches = array_filter($users, function($user) {
            return $user['is_coach'] == 1;
        });
        $coaches = array_values($coaches); // Reset array keys
        
        // Update coaches to use primary_course_name if course_name is empty
        foreach ($coaches as &$coach) {
            if (empty($coach['course_name']) && !empty($coach['primary_course_name'])) {
                $coach['course_name'] = $coach['primary_course_name'];
            }
        }
        
        // Separate practitioners from regular users for retrocession invoices
        $practitioners = array_filter($users, function($user) {
            return $user['is_practitioner'] == 1;
        });
        $practitioners = array_values($practitioners); // Reset array keys
        
        // Get first invoice type as default if available
        $defaultTypeId = !empty($invoiceTypes) ? $invoiceTypes[0]['id'] : null;
        
        // Get document types
        $documentTypes = DocumentType::getUserAllowedTypes();
        
        // Generate suggested invoice number using the default document type and matching invoice type
        $suggestedNumber = '';
        $defaultDocType = DocumentType::getByCode('invoice');
        if ($defaultDocType) {
            // Find the invoice type that matches the requested type
            $defaultInvoiceTypeId = null;
            
            // If we're duplicating an invoice, use the duplicate's invoice type
            if (!empty($duplicateData) && !empty($duplicateData['invoice_type_id'])) {
                $defaultInvoiceTypeId = $duplicateData['invoice_type_id'];
            } else {
                // Map retrocession types to their specific invoice types
                if ($type === 'retrocession_30') {
                    // First try RT30, then fall back to RET
                    foreach ($invoiceTypes as $invoiceType) {
                        if ($invoiceType['code'] === 'RT30' || $invoiceType['code'] === 'RET') {
                            $defaultInvoiceTypeId = $invoiceType['id'];
                            break;
                        }
                    }
                } elseif ($type === 'retrocession_25') {
                    // Look for RT25 type
                    foreach ($invoiceTypes as $invoiceType) {
                        if ($invoiceType['code'] === 'RT25') {
                            $defaultInvoiceTypeId = $invoiceType['id'];
                            break;
                        }
                    }
                } elseif ($type === 'location') {
                    // Look for LOY (Loyer) type for location invoices
                    foreach ($invoiceTypes as $invoiceType) {
                        if ($invoiceType['code'] === 'LOY' || $invoiceType['prefix'] === 'LOY') {
                            $defaultInvoiceTypeId = $invoiceType['id'];
                            break;
                        }
                    }
                } else {
                    // For other types, try to match by code
                    foreach ($invoiceTypes as $invoiceType) {
                        if ($invoiceType['code'] === strtoupper(substr($type, 0, 3))) {
                            $defaultInvoiceTypeId = $invoiceType['id'];
                            break;
                        }
                    }
                }
                
                // Fall back to first type if no match found
                if (!$defaultInvoiceTypeId && !empty($invoiceTypes)) {
                    $defaultInvoiceTypeId = $invoiceTypes[0]['id'];
                }
            }
            $suggestedNumber = $this->invoice->suggestDocumentNumber($defaultDocType['id'], $defaultInvoiceTypeId);
        }
        
        // Get rate profile if practitioner
        $rateProfile = null;
        if ($client && $client['client_type'] === 'practitioner') {
            $rateProfile = $this->rateProfile->getEffectiveRate(
                $clientId, 
                'secretariat_percent', 
                date('Y-m-d')
            );
        }
        
        // Get active payment terms
        $paymentTerms = $this->getActivePaymentTerms();
        
        // Find default payment term
        $defaultPaymentTermId = null;
        $defaultDays = 30; // Default to 30 days if no default term
        foreach ($paymentTerms as $term) {
            if ($term['is_default']) {
                $defaultPaymentTermId = $term['id'];
                $defaultDays = $term['days'];
                break;
            }
        }
        
        // Get secretary percentage from config
        $db = Flight::db();
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'retrocession_default_secretary_percent'");
        $stmt->execute();
        $secretaryPercent = $stmt->fetchColumn() ?: 10;
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'invoices/create-modern';

        $this->render($viewName, [
            'type' => $type,
            'client' => $client,
            'clients' => $clients,
            'users' => $users,
            'coaches' => $coaches,  // Add coaches array for location invoices
            'practitioners' => $practitioners,  // Add practitioners array for retrocession invoices
            'invoiceTypes' => $invoiceTypes,
            'documentTypes' => $documentTypes,
            'vatRates' => $vatRates,
            'paymentTerms' => $paymentTerms,
            'defaultPaymentTermId' => $defaultPaymentTermId,
            'templates' => $templates,
            'rateProfile' => $rateProfile,
            'defaultDueDate' => date('Y-m-d', strtotime('+' . $defaultDays . ' days')),
            'today' => date('Y-m-d'),
            'suggestedNumber' => $suggestedNumber,
            'secretary_percent' => $secretaryPercent,
            'duplicateData' => $duplicateData,
            'defaultInvoiceTypeId' => $defaultInvoiceTypeId
        ]);
    }
    
    /**
     * Store new invoice
     */
    public function store()
    {
        // Write to a file to ensure we're reaching this method
        file_put_contents(__DIR__ . '/../../storage/invoice_store_debug.txt', 
            date('Y-m-d H:i:s') . " - Store method called\n" . 
            "Method: " . Flight::request()->method . "\n" .
            "URL: " . Flight::request()->url . "\n" .
            "Session user: " . ($_SESSION['user_id'] ?? 'NOT SET') . "\n" .
            "POST data keys: " . implode(', ', array_keys($_POST)) . "\n\n",
            FILE_APPEND
        );
        
        try {
            // Debug mode - log everything
            error_log('=== INVOICE STORE METHOD CALLED ===');
            error_log('Request method: ' . Flight::request()->method);
            error_log('Request URL: ' . Flight::request()->url);
            error_log('Session ID: ' . session_id());
            error_log('User ID in session: ' . ($_SESSION['user_id'] ?? 'NOT SET'));
            
            // Get data using the same pattern as ClientController
            $data = $this->getRequestData();
            
            // Debug: Log the incoming data
            error_log('Invoice store - Incoming data: ' . json_encode($data));
            error_log('Data keys: ' . implode(', ', array_keys($data)));
            
            // Check if data is empty
            if (empty($data)) {
                error_log('WARNING: No data received from getRequestData()!');
                error_log('POST data available: ' . json_encode($_POST));
                error_log('Flight request data: ' . json_encode(Flight::request()->data->getData()));
                
                // Emergency fallback - use $_POST directly
                if (!empty($_POST)) {
                    error_log('Using $_POST as emergency fallback');
                    $data = $_POST;
                }
            } else {
                error_log('SUCCESS: Data received, count: ' . count($data));
            }
            
            // Check CSRF token
            $csrfToken = $data['csrf_token'] ?? '';
            $sessionToken = $_SESSION['csrf_token'] ?? '';
            error_log('CSRF check - Submitted: ' . substr($csrfToken, 0, 20) . '...');
            error_log('CSRF check - Session: ' . substr($sessionToken, 0, 20) . '...');
            
            // Check the action (save as draft or save and send)
            $action = $data['action'] ?? 'save';
            
            // Parse billable_type and billable_id
            if (isset($data['billable_id']) && !empty($data['billable_id'])) {
                // billable_id comes in format "type_id" (e.g., "client_123" or "user_456")
                $parts = explode('_', $data['billable_id'], 2);
                if (count($parts) === 2) {
                    $billableType = $parts[0];
                    $billableId = intval($parts[1]);
                    
                    // Set appropriate field based on type
                    if ($billableType === 'client') {
                        $data['client_id'] = $billableId;
                        $data['user_id'] = null;
                    } elseif ($billableType === 'user') {
                        $data['user_id'] = $billableId;
                        $data['client_id'] = null;
                    }
                }
            }
            
            // Set status based on action
            if ($action === 'save') {
                $data['status'] = Invoice::STATUS_DRAFT;
            } elseif ($action === 'save_and_send') {
                $data['status'] = Invoice::STATUS_SENT;
            }
            
            // Handle CNS and secretary fee data
            if (isset($data['secretary_fee_amount'])) {
                $data['secretariat_vat_amount'] = $data['secretary_fee_amount'];
                unset($data['secretary_fee_amount']);
            }
            
            // Process items and calculate totals
            if (isset($data['items']) && is_array($data['items'])) {
                $subtotal = 0;
                $vatAmount = 0;
                $lines = [];
                
                // Get VAT rates for calculation
                $db = Flight::db();
                $vatRates = [];
                $stmt = $db->query("SELECT id, rate FROM config_vat_rates");
                while ($rate = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                    $vatRates[$rate['id']] = $rate['rate'];
                }
                
                // Track unique lines to prevent exact duplicates
                $uniqueLineKeys = [];
                $lineIndex = 0;
                
                // Check if this is a LOC invoice that should use TTC calculation
                $useTTC = false;
                $totalTTC = 0;
                $commonVatRate = 0;
                
                if (isset($data['invoice_number']) && InvoiceTTCHelper::shouldUseTTC($data['invoice_number'])) {
                    $useTTC = true;
                } elseif (isset($data['invoice_type']) && InvoiceTTCHelper::shouldUseTTC($data['invoice_type'])) {
                    $useTTC = true;
                } elseif (isset($data['invoice_type_id'])) {
                    // Check by type ID
                    $stmt = $db->prepare("SELECT code FROM config_invoice_types WHERE id = ?");
                    $stmt->execute([$data['invoice_type_id']]);
                    $typeData = $stmt->fetch(\PDO::FETCH_ASSOC);
                    if ($typeData && ($typeData['code'] === 'LOC' || $typeData['code'] === 'LOY')) {
                        $useTTC = true;
                    }
                }
                
                foreach ($data['items'] as $index => $item) {
                    if (!empty($item['description'])) {
                        $quantity = floatval($item['quantity'] ?? 1);
                        $unitPrice = floatval($item['unit_price'] ?? 0);
                        $vatRateId = $item['vat_rate_id'] ?? null;
                        $vatRate = $vatRateId ? ($vatRates[$vatRateId] ?? 0) : 0;
                        
                        if ($useTTC) {
                            // For LOC invoices: accumulate TTC totals
                            $ttcPrice = $unitPrice;
                            $ttcLineTotal = $quantity * $ttcPrice;
                            $totalTTC += $ttcLineTotal;
                            
                            // Get common VAT rate from first item
                            if ($index === 0) {
                                $commonVatRate = $vatRate;
                            }
                            
                            // For display purposes, keep TTC price
                            $lineTotal = $ttcLineTotal;
                            $displayUnitPrice = $ttcPrice;
                            $lineVat = 0; // Will be calculated from total later
                        } else {
                            // Standard calculation: prices are HT, add VAT
                            $lineTotal = $quantity * $unitPrice;
                            $lineVat = $lineTotal * ($vatRate / 100);
                            $displayUnitPrice = $unitPrice;
                            $subtotal += $lineTotal;
                            $vatAmount += $lineVat;
                        }
                        
                        // Create unique key to detect exact duplicates
                        $lineKey = $item['description'] . '|' . $quantity . '|' . $unitPrice . '|' . $vatRate;
                        
                        // Skip if this is an exact duplicate
                        if (in_array($lineKey, $uniqueLineKeys)) {
                            error_log('Skipping duplicate invoice line: ' . $item['description']);
                            continue;
                        }
                        
                        $uniqueLineKeys[] = $lineKey;
                        
                        // Add to lines array with sort_order
                        $lines[] = [
                            'description' => $item['description'],
                            'quantity' => $quantity,
                            'unit_price' => $displayUnitPrice,
                            'vat_rate' => $vatRate,
                            'vat_rate_id' => $vatRateId,
                            'total' => $lineTotal,
                            'sort_order' => $lineIndex++
                        ];
                    }
                }
                
                // Calculate final totals
                if ($useTTC && $totalTTC > 0) {
                    // For LOC invoices: extract VAT from total TTC
                    $divisor = 1 + ($commonVatRate / 100);
                    $subtotal = MoneyHelper::round($totalTTC / $divisor);
                    $vatAmount = MoneyHelper::round($totalTTC - $subtotal);
                    $total = $totalTTC;
                } else {
                    // Standard calculation already done
                    $total = $subtotal + $vatAmount;
                }
                
                // Set calculated values
                $data['lines'] = $lines;
                $data['subtotal'] = $subtotal;
                $data['vat_amount'] = $vatAmount;
                $data['total'] = $total;
            }
            
            // Map invoice_type_id to invoice_type if needed
            if (isset($data['invoice_type_id']) && !isset($data['invoice_type'])) {
                // Get invoice type code from database
                $db = Flight::db();
                $stmt = $db->prepare("SELECT code FROM config_invoice_types WHERE id = ?");
                $stmt->execute([$data['invoice_type_id']]);
                $typeData = $stmt->fetch(\PDO::FETCH_ASSOC);
                
                // Map the code properly - RET should become retrocession_30
                if ($typeData && $typeData['code'] === 'RET') {
                    $data['invoice_type'] = 'retrocession_30';
                } elseif ($typeData && $typeData['code'] === 'RT25') {
                    $data['invoice_type'] = 'retrocession_25';
                } else {
                    $data['invoice_type'] = $typeData ? $typeData['code'] : null;
                }
                $data['type_id'] = $data['invoice_type_id']; // Also set type_id
            }
            
            // Validate required fields
            $this->validateInvoiceData($data);
            
            // Calculate period based on invoice type if not already set
            if (empty($data['period'])) {
                $data['period'] = $this->calculateInvoicePeriod($data);
            }
            
            // Set default subject for rental invoices if not set
            if (empty($data['subject']) && isset($data['invoice_number']) && strpos($data['invoice_number'], 'FAC-LOY') !== false) {
                $data['subject'] = 'LOYER + CHARGES';
            }
            
            // Set default subject for retrocession invoices if not set
            if (empty($data['subject']) && isset($data['invoice_number']) && 
                (strpos($data['invoice_number'], 'FAC-RET') !== false || 
                 strpos($data['invoice_number'], 'RET30') !== false || 
                 strpos($data['invoice_number'], 'RET25') !== false)) {
                $data['subject'] = 'RETROCESSION';
            }
            
            // Debug: Log processed data before creation
            error_log('Invoice store - Processed data: ' . json_encode([
                'client_id' => $data['client_id'] ?? null,
                'user_id' => $data['user_id'] ?? null,
                'type_id' => $data['type_id'] ?? null,
                'invoice_type' => $data['invoice_type'] ?? null,
                'subtotal' => $data['subtotal'] ?? 0,
                'vat_amount' => $data['vat_amount'] ?? 0,
                'total' => $data['total'] ?? 0,
                'lines_count' => isset($data['lines']) ? count($data['lines']) : 0,
                'period' => $data['period'] ?? null,
                'subject' => $data['subject'] ?? null
            ]));
            
            // Handle retrocession invoices
            if (isset($data['invoice_type']) && in_array($data['invoice_type'], ['retrocession_30', 'retrocession_25'])) {
                // For manual retrocession invoice creation with pre-filled data,
                // use the standard invoice creation method
                if (!empty($data['lines']) || !empty($data['items'])) {
                    $invoice = $this->invoice->createInvoice($data);
                } else {
                    // Use retrocession calculator only when generating from monthly data
                    $invoice = $this->createRetrocessionInvoice($data);
                }
            } else {
                $invoice = $this->invoice->createInvoice($data);
            }
            
            // Send email if action is save_and_send
            if ($action === 'save_and_send') {
                try {
                    $this->sendInvoiceEmail($invoice['id']);
                    Flight::flash('success', __('invoices.created_and_sent_successfully'));
                } catch (Exception $e) {
                    // Invoice was created but email failed
                    Flight::flash('warning', __('invoices.created_but_email_failed') . ': ' . $e->getMessage());
                }
            } else {
                Flight::flash('success', __('invoices.created_successfully'));
            }
            
            Flight::redirect('/invoices/' . $invoice['id']);
            
        } catch (Exception $e) {
            // Log the full error for debugging
            error_log('Invoice creation error: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/invoices/create');
        }
    }
    
    /**
     * Show invoice details
     */
    public function show($id)
    {
        $invoice = $this->invoice->getInvoiceWithDetails($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // If invoice_type is not set, try to determine from invoice number or type_id
        if (empty($invoice['invoice_type'])) {
            // Check if invoice number contains RET30, RET25, or FAC-RET
            if (strpos($invoice['invoice_number'], 'RET30') !== false) {
                $invoice['invoice_type'] = 'retrocession_30';
            } elseif (strpos($invoice['invoice_number'], 'RET25') !== false) {
                $invoice['invoice_type'] = 'retrocession_25';
            } elseif (strpos($invoice['invoice_number'], 'FAC-RET') !== false) {
                // Default to retrocession_30 for FAC-RET invoices
                $invoice['invoice_type'] = 'retrocession_30';
            } elseif (!empty($invoice['type_id'])) {
                // Get type code from type_id
                $db = Flight::db();
                $stmt = $db->prepare("SELECT code FROM config_invoice_types WHERE id = ?");
                $stmt->execute([$invoice['type_id']]);
                $type = $stmt->fetch(\PDO::FETCH_ASSOC);
                if ($type && $type['code']) {
                    $invoice['invoice_type'] = $type['code'];
                }
            }
        }
        
        // Check if can edit
        $canEdit = $this->invoice->canEdit($id);
        
        // Get payment history
        $payments = $this->getInvoicePayments($id);
        
        // Calculate paid amount from payments if not set
        if (!isset($invoice['paid_amount']) || $invoice['paid_amount'] === null) {
            $invoice['paid_amount'] = 0;
            foreach ($payments as $payment) {
                $invoice['paid_amount'] += $payment['amount'] ?? 0;
            }
        }
        
        // Get email history
        $emailHistory = $this->getEmailHistory($id);
        
        // Get active payment methods
        $paymentMethods = $this->getActivePaymentMethods();
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'invoices/show-modern';

        // Get company configuration
        $config = Flight::get('config') ?? [];
        
        $this->render($viewName, [
            'invoice' => $invoice,
            'canEdit' => $canEdit,
            'payments' => $payments,
            'emailHistory' => $emailHistory,
            'paymentMethods' => $paymentMethods,
            'showSecretariatNote' => $invoice['secretariat_vat_note_shown'] ?? false,
            // Add company configuration
            'company_name' => $config['company_name'] ?? 'Fit 360',
            'company_logo' => $config['company_logo'] ?? 'uploads/logos/company_logo_1750519782.png',
            'company_address' => $config['company_address'] ?? '',
            'company_city' => $config['company_city'] ?? '',
            'company_postal_code' => $config['company_postal_code'] ?? '',
            'company_country' => $config['company_country'] ?? '',
            'company_phone' => $config['company_phone'] ?? '',
            'company_email' => $config['company_email'] ?? '',
            'company_vat_number' => $config['company_vat_number'] ?? '',
            'currency' => $config['currency_symbol'] ?? '€'
        ]);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $invoice = $this->invoice->getInvoiceWithDetails($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Check if can edit
        if (!$this->invoice->canEdit($id)) {
            Flight::flash('error', __('invoices.cannot_edit'));
            Flight::redirect('/invoices/' . $id);
            return;
        }
        
        // Rename 'lines' to 'items' for template compatibility
        if (isset($invoice['lines']) && !empty($invoice['lines'])) {
            $invoice['items'] = $invoice['lines'];
            // Keep lines too for backward compatibility
        } else {
            // If no lines exist, create empty array
            $invoice['items'] = [];
            $invoice['lines'] = [];
        }
        
        // Debug log
        error_log("Invoice edit - Invoice ID: {$id}");
        error_log("Invoice edit - Items count: " . count($invoice['items']));
        
        // Get all required data for the form
        $invoiceTypes = $this->getInvoiceTypes();
        $documentTypes = DocumentType::getUserAllowedTypes();
        $vatRates = $this->getVatRates();
        $paymentTerms = $this->getActivePaymentTerms();
        $templates = $this->getInvoiceTemplates($invoice['invoice_type']);
        
        // Get all active clients for potential reassignment
        $clients = $this->client->getAllActive();
        
        // Format client names for display
        foreach ($clients as &$c) {
            $c['name'] = trim($c['company_name'] ?: $c['first_name'] . ' ' . $c['last_name']);
        }
        
        // Get currency symbol from config
        $currency = Flight::get('config')['currency_symbol'] ?? '€';
        
        // Get secretary percentage from config
        $db = Flight::db();
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'retrocession_default_secretary_percent'");
        $stmt->execute();
        $secretaryPercent = $stmt->fetchColumn() ?: 10;
        
        // Get product categories for save to product feature
        // First get all active categories
        $stmt = $db->query("SELECT id, name, sort_order FROM catalog_categories WHERE is_active = 1");
        $allCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Group by parsed name to eliminate duplicates
        $uniqueCategories = [];
        foreach ($allCategories as $category) {
            $displayName = $category['name'];
            
            // Parse JSON names if present
            if (strpos($category['name'], '{') === 0) {
                $names = json_decode($category['name'], true);
                if ($names) {
                    // Use French as default, fallback to English, then any available
                    $displayName = $names['fr'] ?? $names['en'] ?? reset($names);
                }
            }
            
            // Keep only the first occurrence of each unique name
            if (!isset($uniqueCategories[$displayName])) {
                $uniqueCategories[$displayName] = [
                    'id' => $category['id'],
                    'name' => $displayName,
                    'sort_order' => $category['sort_order']
                ];
            }
        }
        
        // Convert back to indexed array and sort
        $categories = array_values($uniqueCategories);
        usort($categories, function($a, $b) {
            return $a['sort_order'] <=> $b['sort_order'];
        });
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'invoices/edit-modern';

        $this->render($viewName, [
            'invoice' => $invoice,
            'invoiceTypes' => $invoiceTypes,
            'invoice_types' => $invoiceTypes, // Also pass with underscore for template compatibility
            'documentTypes' => $documentTypes,
            'vatRates' => $vatRates,
            'vat_rates' => $vatRates, // Also pass with underscore for template compatibility
            'paymentTerms' => $paymentTerms,
            'templates' => $templates,
            'clients' => $clients,
            'categories' => $categories,
            'currency' => $currency,
            'secretary_percent' => $secretaryPercent,
            'canEdit' => true  // We already checked canEdit above, so if we're here, it's true
        ]);
    }
    
    /**
     * Update invoice
     */
    public function update($id)
    {
        try {
            $invoice = $this->invoice->getById($id);
            
            if (!$invoice) {
                Flight::notFound();
                return;
            }
            
            // Check if can edit
            if (!$this->invoice->canEdit($id)) {
                throw new Exception(__('invoices.cannot_edit'));
            }
            
            // Get data using the same pattern as ClientController
            $data = $this->getRequestData();
            
            // Debug: Log received data
            error_log('Invoice update - Received data: ' . json_encode($data));
            
            // Get the action from the data
            $action = $data['action'] ?? 'save';
            
            // If save_and_send, update status to sent
            if ($action === 'save_and_send') {
                $data['status'] = Invoice::STATUS_SENT;
            }
            
            // Update invoice
            $this->updateInvoice($id, $data);
            
            // Send email if action is save_and_send
            if ($action === 'save_and_send') {
                try {
                    $this->sendInvoiceEmail($id);
                    Flight::flash('success', __('invoices.updated_and_sent_successfully'));
                } catch (Exception $e) {
                    // Invoice was updated but email failed
                    Flight::flash('warning', __('invoices.updated_but_email_failed') . ': ' . $e->getMessage());
                }
            } else {
                Flight::flash('success', __('invoices.updated_successfully'));
            }
            
            Flight::redirect('/invoices/' . $id);
            
        } catch (Exception $e) {
            // Debug: Log the error
            error_log('Invoice update error: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/invoices/' . $id . '/edit');
        }
    }
    
    /**
     * Delete invoice (only drafts)
     */
    /**
     * Show delete confirmation
     */
    public function confirmDelete($id)
    {
        $invoice = $this->invoice->getById($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Only allow deletion of draft invoices
        if ($invoice['status'] !== Invoice::STATUS_DRAFT) {
            Flight::flash('error', __('invoices.cannot_delete_non_draft'));
            Flight::redirect('/invoices/' . $id);
            return;
        }
        
        // For now, just delete directly (you can add a confirmation view later)
        $this->delete($id);
    }
    
    public function delete($id)
    {
        try {
            $invoice = $this->invoice->getById($id);
            
            if (!$invoice) {
                Flight::notFound();
                return;
            }
            
            // Only allow deletion of draft invoices
            if ($invoice['status'] !== Invoice::STATUS_DRAFT) {
                throw new Exception(__('invoices.cannot_delete_non_draft'));
            }
            
            $this->invoice->delete($id);
            
            Flight::flash('success', __('invoices.deleted_successfully'));
            Flight::redirect('/invoices');
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/invoices/' . $id);
        }
    }
    
    /**
     * Update invoice status
     */
    public function updateStatus($id)
    {
        try {
            $status = Flight::request()->data->status;
            
            if (!$status) {
                throw new Exception(__('invoices.status_required'));
            }
            
            $this->invoice->updateStatus($id, $status);
            
            // Send email if status is sent
            if ($status === Invoice::STATUS_SENT) {
                $this->sendInvoiceEmail($id);
            }
            
            Flight::json(['success' => true, 'message' => __('invoices.status_updated')]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Generate PDF
     */
    public function pdf($id)
    {
        $invoice = $this->invoice->getInvoiceWithDetails($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // If invoice_type is not set, try to determine from invoice number or type_id
        if (empty($invoice['invoice_type'])) {
            // Check if invoice number contains RET30, RET25, or FAC-RET
            if (strpos($invoice['invoice_number'], 'RET30') !== false) {
                $invoice['invoice_type'] = 'retrocession_30';
            } elseif (strpos($invoice['invoice_number'], 'RET25') !== false) {
                $invoice['invoice_type'] = 'retrocession_25';
            } elseif (strpos($invoice['invoice_number'], 'FAC-RET') !== false) {
                // Default to retrocession_30 for FAC-RET invoices
                $invoice['invoice_type'] = 'retrocession_30';
            } elseif (!empty($invoice['type_id'])) {
                // Get type code from type_id
                $db = Flight::db();
                $stmt = $db->prepare("SELECT code FROM config_invoice_types WHERE id = ?");
                $stmt->execute([$invoice['type_id']]);
                $type = $stmt->fetch(\PDO::FETCH_ASSOC);
                if ($type && $type['code']) {
                    $invoice['invoice_type'] = $type['code'];
                }
            }
        }
        
        // Generate PDF using PDF service
        $pdfService = new \App\Services\PdfService();
        $pdf = $pdfService->generateInvoicePdf($invoice);
        
        // Output PDF
        Flight::response()->header('Content-Type', 'application/pdf');
        Flight::response()->header('Content-Disposition', 'inline; filename="' . $invoice['invoice_number'] . '.pdf"');
        echo $pdf;
    }
    
    /**
     * Send invoice by email
     */
    public function sendEmail($id)
    {
        try {
            $invoice = $this->invoice->getInvoiceWithDetails($id);
            
            if (!$invoice) {
                Flight::notFound();
                return;
            }
            
            $result = $this->sendInvoiceEmail($id);
            
            if ($result['success']) {
                // Update status to sent if still draft
                if ($invoice['status'] === Invoice::STATUS_DRAFT) {
                    $this->invoice->updateStatus($id, Invoice::STATUS_SENT);
                }
                
                Flight::flash('success', __('invoices.email_sent_successfully'));
            } else {
                Flight::flash('error', $result['message']);
            }
            
            Flight::redirect('/invoices/' . $id);
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/invoices/' . $id);
        }
    }
    
    /**
     * Create credit note from invoice
     */
    public function createCreditNote($id)
    {
        $invoice = $this->invoice->getById($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Can only create credit note from sent/paid invoices
        if (!in_array($invoice['status'], [Invoice::STATUS_SENT, Invoice::STATUS_PAID, Invoice::STATUS_PARTIAL])) {
            Flight::flash('error', __('invoices.cannot_credit_draft'));
            Flight::redirect('/invoices/' . $id);
            return;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'invoices/credit-note-modern';

        $this->render($viewName, [
            'originalInvoice' => $invoice,
            'maxAmount' => $invoice['total']
        ]);
    }
    
    
    /**
     * Preview cascade update
     */
    public function previewCascade()
    {
        try {
            $profileId = Flight::request()->data->profile_id;
            $changes = Flight::request()->data->changes;
            
            $preview = $this->rateProfile->previewCascadeUpdate($profileId, $changes);
            
            Flight::json(['success' => true, 'preview' => $preview]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Private helper methods
     */
    
    private function getInvoices($filters, $page, $limit)
    {
        $db = Flight::db();
        
        $where = ['1=1'];
        $params = [];
        
        // Exclude archived invoices by default unless viewing archive
        if (!isset($filters['show_archived']) || !$filters['show_archived']) {
            $where[] = 'COALESCE(i.is_archived, FALSE) = FALSE';
        } else {
            $where[] = 'i.is_archived = TRUE';
        }
        
        if (!empty($filters['status'])) {
            $where[] = 'i.status = :status';
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['type'])) {
            $where[] = 'i.type_id = :type';
            $params[':type'] = $filters['type'];
        }
        
        if (!empty($filters['document_type'])) {
            $where[] = 'i.document_type_id = :document_type';
            $params[':document_type'] = $filters['document_type'];
        }
        
        if (!empty($filters['client_id'])) {
            $where[] = 'i.client_id = :client_id';
            $params[':client_id'] = $filters['client_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = 'i.issue_date >= :date_from';
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = 'i.issue_date <= :date_to';
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['search'])) {
            $where[] = '(i.invoice_number LIKE :search1 OR 
                        CASE 
                            WHEN c.client_type = "individual" THEN CONCAT(c.first_name, " ", c.last_name)
                            ELSE c.company_name 
                        END LIKE :search2 OR
                        CONCAT(u.first_name, " ", u.last_name) LIKE :search3 OR
                        u.username LIKE :search4)';
            $searchValue = '%' . $filters['search'] . '%';
            $params[':search1'] = $searchValue;
            $params[':search2'] = $searchValue;
            $params[':search3'] = $searchValue;
            $params[':search4'] = $searchValue;
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Count total
        $stmt = $db->prepare("
            SELECT COUNT(*) as total
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN users u ON i.user_id = u.id
            WHERE $whereClause
        ");
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $offset = ($page - 1) * $limit;
        $stmt = $db->prepare("
            SELECT i.*, 
                   CASE 
                       WHEN i.client_id IS NOT NULL THEN
                           CASE 
                               WHEN c.client_type = 'individual' THEN CONCAT(c.first_name, ' ', c.last_name)
                               ELSE c.company_name 
                           END
                       WHEN i.user_id IS NOT NULL THEN 
                           CONCAT(u.first_name, ' ', u.last_name)
                       ELSE 'N/A'
                   END as client_name, 
                   COALESCE(c.client_number, CONCAT('USR-', LPAD(u.id, 5, '0'))) as client_number,
                   CASE 
                       WHEN i.client_id IS NOT NULL THEN 'client'
                       WHEN i.user_id IS NOT NULL THEN 'user'
                       ELSE 'unknown'
                   END as recipient_type,
                   it.name as type_name_json, it.code as type_code, it.color as type_color,
                   dt.code as doc_type_code, dt.name as doc_type_name, dt.color as doc_type_color, dt.icon as doc_type_icon
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN users u ON i.user_id = u.id
            LEFT JOIN config_invoice_types it ON i.type_id = it.id
            LEFT JOIN document_types dt ON i.document_type_id = dt.id
            WHERE $whereClause
            ORDER BY i.created_at DESC
            LIMIT :limit OFFSET :offset
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $limit, \PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, \PDO::PARAM_INT);
        
        $stmt->execute();
        $data = $stmt->fetchAll();
        
        // Process JSON names for invoice types and document types
        $lang = $_SESSION['lang'] ?? 'fr';
        foreach ($data as &$invoice) {
            // Process invoice type name
            if (!empty($invoice['type_name_json'])) {
                $nameData = json_decode($invoice['type_name_json'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($nameData)) {
                    $invoice['type_name'] = $nameData[$lang] ?? $nameData['fr'] ?? $nameData['en'] ?? $invoice['type_name_json'];
                } else {
                    $invoice['type_name'] = $invoice['type_name_json'];
                }
            } else {
                $invoice['type_name'] = $invoice['type_code'] ?? 'N/A';
            }
            
            // Process document type name
            if (!empty($invoice['doc_type_name'])) {
                $docNameData = json_decode($invoice['doc_type_name'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($docNameData)) {
                    $invoice['doc_type_display_name'] = $docNameData[$lang] ?? $docNameData['fr'] ?? $docNameData['en'] ?? $invoice['doc_type_code'];
                } else {
                    $invoice['doc_type_display_name'] = $invoice['doc_type_name'];
                }
            } else {
                $invoice['doc_type_display_name'] = $invoice['doc_type_code'] ?? __('common.unknown');
            }
        }
        
        return [
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }
    
    private function validateInvoiceData($data)
    {
        // At least one of client_id or user_id must be present
        if (empty($data['client_id']) && empty($data['user_id'])) {
            throw new Exception(__('invoices.recipient_required'));
        }
        
        // Issue date is always required
        if (empty($data['issue_date'])) {
            throw new Exception(__('validation.required', ['field' => 'issue_date']));
        }
        
        // Due date is optional for drafts or retrocession invoices
        $isDraft = isset($data['status']) && $data['status'] === Invoice::STATUS_DRAFT;
        $isRetrocession = isset($data['invoice_type']) && 
                          in_array($data['invoice_type'], ['retrocession_30', 'retrocession_25']);
        
        if (!$isDraft && !$isRetrocession && empty($data['due_date'])) {
            throw new Exception(__('validation.required', ['field' => 'due_date']));
        }
        
        // Validate dates only if due_date is provided
        if (!empty($data['due_date']) && strtotime($data['due_date']) < strtotime($data['issue_date'])) {
            throw new Exception(__('invoices.due_date_before_issue'));
        }
    }
    
    private function createRetrocessionInvoice($data)
    {
        // Extract retrocession specific data
        // For retrocession invoices, use user_id if client_id is not set
        $practitionerId = $data['client_id'] ?? $data['user_id'] ?? null;
        
        // Extract month and year from period string (e.g., "JUIN 2025")
        if (!empty($data['period'])) {
            // French month names to numbers
            $frenchMonths = [
                'JANVIER' => 1, 'FÉVRIER' => 2, 'MARS' => 3, 'AVRIL' => 4,
                'MAI' => 5, 'JUIN' => 6, 'JUILLET' => 7, 'AOÛT' => 8,
                'SEPTEMBRE' => 9, 'OCTOBRE' => 10, 'NOVEMBRE' => 11, 'DÉCEMBRE' => 12
            ];
            
            $parts = explode(' ', $data['period']);
            if (count($parts) === 2) {
                $periodMonth = $frenchMonths[strtoupper($parts[0])] ?? date('n');
                $periodYear = $parts[1] ?? date('Y');
            } else {
                $periodMonth = date('n');
                $periodYear = date('Y');
            }
        } else {
            $periodMonth = $data['period_month'] ?? date('n');
            $periodYear = $data['period_year'] ?? date('Y');
        }
        
        // Generate invoice using calculator
        return $this->retrocessionCalculator->generateInvoice(
            $practitionerId,
            $periodMonth,
            $periodYear,
            $data
        );
    }
    
    private function updateInvoice($id, $data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Handle CNS and secretary fee data
            if (isset($data['secretary_fee_amount'])) {
                $data['secretariat_vat_amount'] = $data['secretary_fee_amount'];
                unset($data['secretary_fee_amount']);
            }
            
            // Get current invoice to check type
            $currentInvoice = $this->invoice->getById($id);
            
            // Calculate period based on invoice type if not already set
            if (empty($data['period']) && $currentInvoice) {
                // Merge current invoice data with update data for period calculation
                $mergedData = array_merge($currentInvoice, $data);
                $data['period'] = $this->calculateInvoicePeriod($mergedData);
            }
            
            // Set default subject for rental invoices if not set
            if (empty($data['subject']) && $currentInvoice && strpos($currentInvoice['invoice_number'], 'FAC-LOY') !== false) {
                $data['subject'] = 'LOYER + CHARGES';
            }
            
            // Set default subject for retrocession invoices if not set
            if (empty($data['subject']) && $currentInvoice && 
                (strpos($currentInvoice['invoice_number'], 'FAC-RET') !== false || 
                 strpos($currentInvoice['invoice_number'], 'RET30') !== false || 
                 strpos($currentInvoice['invoice_number'], 'RET25') !== false)) {
                $data['subject'] = 'RETROCESSION';
            }
            
            // Update main invoice fields
            $updates = [];
            $params = ['id' => $id];
            
            $allowedFields = [
                'status', 'issue_date', 'due_date', 'notes', 'internal_notes',
                'payment_terms', 'payment_term_id', 'footer_text', 'cns_base_amount', 'secretariat_vat_amount',
                'subject', 'period'
            ];
            
            // Allow invoice_number update only for draft invoices
            $currentInvoice = $this->invoice->getById($id);
            if ($currentInvoice && $currentInvoice['status'] === 'draft' && isset($data['invoice_number'])) {
                // Check if the new invoice number is unique
                $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ? AND id != ?");
                $stmt->execute([$data['invoice_number'], $id]);
                if ($stmt->fetch()) {
                    throw new Exception(__('invoices.invoice_number_exists') ?? 'This invoice number already exists');
                }
                $allowedFields[] = 'invoice_number';
            }
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updates[] = "$field = :$field";
                    $params[$field] = $data[$field];
                }
            }
            
            if (!empty($updates)) {
                $sql = "UPDATE invoices SET " . implode(', ', $updates) . ", updated_at = NOW() WHERE id = :id";
                error_log('Invoice update SQL: ' . $sql);
                error_log('Invoice update params: ' . json_encode($params));
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                error_log('Invoice update - Rows affected: ' . $stmt->rowCount());
            } else {
                error_log('Invoice update - No fields to update');
            }
            
            // Update lines if provided (handle both 'lines' and 'items' for compatibility)
            $lines = $data['lines'] ?? $data['items'] ?? null;
            if ($lines !== null) {
                error_log('Invoice update - Processing ' . count($lines) . ' lines/items');
                
                // Delete existing lines
                $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                $stmt->execute([$id]);
                error_log('Invoice update - Deleted old lines');
                
                // Add new lines
                $this->invoice->addInvoiceLines($id, $lines);
                error_log('Invoice update - Added new lines');
                
                // Recalculate totals
                $this->invoice->calculateTotals($id);
                error_log('Invoice update - Recalculated totals');
            }
            
            $db->commit();
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    private function sendInvoiceEmail($invoiceId)
    {
        $emailService = new EmailService();
        return $emailService->sendInvoiceEmail($invoiceId);
    }
    
    private function getInvoiceTypes()
    {
        // Use cached invoice types if available
        if ($this->invoiceTypesCache !== null) {
            return $this->invoiceTypesCache;
        }
        
        $cache = Flight::cache();
        $cacheKey = 'invoice_types_' . ($_SESSION['lang'] ?? 'fr');
        
        // Try to get from cache
        $types = $cache->get($cacheKey);
        if ($types !== null) {
            $this->invoiceTypesCache = $types;
            return $types;
        }
        
        // Fetch from database
        $db = Flight::db();
        $stmt = $db->query("SELECT * FROM config_invoice_types WHERE COALESCE(is_active, TRUE) = TRUE ORDER BY id");
        $types = $stmt->fetchAll();
        
        // Process JSON names
        $lang = $_SESSION['lang'] ?? 'fr';
        foreach ($types as &$type) {
            $nameData = json_decode($type['name'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($nameData)) {
                $type['display_name'] = $nameData[$lang] ?? $nameData['fr'] ?? $nameData['en'] ?? $type['name'];
            } else {
                $type['display_name'] = $type['name'];
            }
        }
        
        // Cache for 1 hour
        $cache->set($cacheKey, $types, 3600);
        $this->invoiceTypesCache = $types;
        
        return $types;
    }
    
    private function getVatRates()
    {
        $cache = Flight::cache();
        $currentLang = $_SESSION['language'] ?? 'en';
        $cacheKey = 'vat_rates_active_' . $currentLang; // Include language in cache key
        
        // Try cache first
        $rates = $cache->get($cacheKey);
        if ($rates !== null) {
            return $rates;
        }
        
        // Fetch from database - using config_vat_rates table
        $db = Flight::db();
        $stmt = $db->query("
            SELECT * FROM config_vat_rates 
            WHERE is_active = 1
            ORDER BY is_default DESC, rate ASC
        ");
        $rates = $stmt->fetchAll();
        
        // Process JSON names to get localized versions
        foreach ($rates as &$rate) {
            if (!empty($rate['name']) && $rate['name'] !== 'null') {
                $nameData = json_decode($rate['name'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($nameData)) {
                    // Try to get name in current language, fallback to French, then code
                    $rate['name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $rate['code'] ?? 'VAT ' . $rate['rate'] . '%';
                } else {
                    // If not JSON, use as is
                    $rate['name'] = $rate['name'];
                }
            } else {
                // Use code or generate name from rate
                $rate['name'] = $rate['code'] ?? 'VAT ' . $rate['rate'] . '%';
            }
        }
        
        // Remove duplicates by rate value
        $uniqueRates = [];
        $seenRates = [];
        
        foreach ($rates as $rate) {
            $rateValue = (string)$rate['rate'];
            if (!isset($seenRates[$rateValue])) {
                $seenRates[$rateValue] = true;
                $uniqueRates[] = $rate;
            }
        }
        
        // Cache for 6 hours
        $cache->set($cacheKey, $uniqueRates, 21600);
        
        return $uniqueRates;
    }
    
    private function getActivePaymentMethods()
    {
        $cache = Flight::cache();
        $currentLang = $_SESSION['language'] ?? 'en';
        $cacheKey = 'payment_methods_active_' . $currentLang;
        
        // Try cache first
        $methods = $cache->get($cacheKey);
        if ($methods !== null) {
            return $methods;
        }
        
        // Fetch from database
        $db = Flight::db();
        $stmt = $db->query("
            SELECT id, name, code 
            FROM payment_methods 
            WHERE is_active = TRUE 
            ORDER BY id
        ");
        $methods = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Process JSON names to get localized versions
        foreach ($methods as &$method) {
            $names = json_decode($method['name'], true);
            if (is_array($names)) {
                // Try to get name in current language, fallback to English, then any available
                if (isset($names[$currentLang])) {
                    $method['display_name'] = $names[$currentLang];
                } elseif (isset($names['en'])) {
                    $method['display_name'] = $names['en'];
                } elseif (!empty($names)) {
                    $method['display_name'] = reset($names); // Get first available name
                } else {
                    $method['display_name'] = $method['code']; // Fallback to code
                }
            } else {
                $method['display_name'] = $method['name']; // Use as is if not JSON
            }
        }
        
        // Cache for 6 hours
        $cache->set($cacheKey, $methods, 21600);
        
        return $methods;
    }
    
    private function getActivePaymentTerms()
    {
        $cache = Flight::cache();
        $currentLang = $_SESSION['user_language'] ?? $_SESSION['lang'] ?? 'fr';
        $cacheKey = 'payment_terms_active_' . $currentLang;
        
        // Try cache first
        $terms = $cache->get($cacheKey);
        if ($terms !== null) {
            return $terms;
        }
        
        // Fetch from database
        $db = Flight::db();
        $stmt = $db->query("
            SELECT id, name, code, days, description, is_default 
            FROM config_payment_terms 
            WHERE is_active = TRUE 
            ORDER BY sort_order ASC, id ASC
        ");
        $terms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Process JSON names to get localized versions
        foreach ($terms as &$term) {
            $names = json_decode($term['name'], true);
            if (is_array($names)) {
                // Try to get name in current language, fallback to English, then any available
                if (isset($names[$currentLang])) {
                    $term['display_name'] = $names[$currentLang];
                } elseif (isset($names['en'])) {
                    $term['display_name'] = $names['en'];
                } elseif (!empty($names)) {
                    $term['display_name'] = reset($names); // Get first available name
                } else {
                    $term['display_name'] = $term['code']; // Fallback to code
                }
            } else {
                $term['display_name'] = $term['name']; // Use as is if not JSON
            }
            
            // Process description if needed
            if ($term['description']) {
                $descriptions = json_decode($term['description'], true);
                if (is_array($descriptions)) {
                    if (isset($descriptions[$currentLang])) {
                        $term['display_description'] = $descriptions[$currentLang];
                    } elseif (isset($descriptions['en'])) {
                        $term['display_description'] = $descriptions['en'];
                    } else {
                        $term['display_description'] = '';
                    }
                } else {
                    $term['display_description'] = $term['description'];
                }
            } else {
                $term['display_description'] = '';
            }
        }
        
        // Cache for 6 hours
        $cache->set($cacheKey, $terms, 21600);
        
        return $terms;
    }
    
    private function getInvoiceTemplates($type)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT * FROM invoice_templates 
            WHERE invoice_type = :type 
            AND is_active = TRUE 
            ORDER BY owner_type, name
        ");
        $stmt->execute([':type' => $type]);
        return $stmt->fetchAll();
    }
    
    private function getInvoicePayments($invoiceId)
    {
        try {
            $db = Flight::db();
            $stmt = $db->prepare("
                SELECT p.*, pa.amount as allocated_amount, pm.name as payment_method_name,
                       pm.code as payment_method_code
                FROM payment_allocations pa
                JOIN payments p ON pa.payment_id = p.id
                LEFT JOIN payment_methods pm ON p.method_id = pm.id
                WHERE pa.invoice_id = :invoice_id
                ORDER BY p.payment_date DESC
            ");
            $stmt->execute([':invoice_id' => $invoiceId]);
            $payments = $stmt->fetchAll();
            
            // Decode JSON payment method names
            foreach ($payments as &$payment) {
                if ($payment['payment_method_name']) {
                    $names = json_decode($payment['payment_method_name'], true);
                    $payment['payment_method_name'] = $names[$_SESSION['language'] ?? 'fr'] ?? $names['fr'] ?? '';
                }
                // Use allocated amount for display
                $payment['amount'] = $payment['allocated_amount'];
            }
            
            return $payments;
        } catch (\PDOException $e) {
            // Table doesn't exist yet, return empty array
            return [];
        }
    }
    
    private function getEmailHistory($invoiceId)
    {
        try {
            $db = Flight::db();
            $stmt = $db->prepare("
                SELECT el.*, et.name as template_name
                FROM email_logs el
                LEFT JOIN email_templates et ON el.template_id = et.id
                WHERE el.invoice_id = :invoice_id
                ORDER BY el.created_at DESC
            ");
            $stmt->execute([':invoice_id' => $invoiceId]);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            // Table doesn't exist yet, return empty array
            return [];
        }
    }

    /**
     * Download invoice as PDF
     */
    public function download($id)
    {
        $invoice = $this->invoice->getInvoiceWithDetails($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Redirect to our new invoice PDF generator
        // The invoice-pdf.php will handle the PDF generation and set appropriate headers
        $baseUrl = rtrim(Flight::get('flight.base_url'), '/');
        header('Location: ' . $baseUrl . '/invoice-pdf.php?id=' . $id . '&action=download');
        exit;
    }

    /**
     * Print invoice - generates PDF and auto-prints
     */
    public function print($id)
    {
        $invoice = $this->invoice->getInvoiceWithDetails($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Redirect to the print helper page which handles PDF display and printing
        $baseUrl = rtrim(Flight::get('flight.base_url'), '/');
        header('Location: ' . $baseUrl . '/invoice-print.php?id=' . $id);
        exit;
    }

    /**
     * Send invoice by email
     */
    public function send($id)
    {
        $invoice = $this->invoice->getById($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Only allow sending draft invoices
        if ($invoice['status'] !== Invoice::STATUS_DRAFT) {
            Flight::flash('error', __('invoices.can_only_send_draft'));
            Flight::redirect('/invoices/' . $id);
            return;
        }
        
        try {
            // Check if email should be sent (optional)
            $sendEmail = Flight::request()->data->send_email ?? false;
            
            if ($sendEmail) {
                // Try to send email (but don't fail if email fails)
                try {
                    $this->sendInvoiceEmail($id);
                    Flight::flash('info', __('invoices.email_sent'));
                } catch (Exception $e) {
                    // Email failed but we'll still mark as sent
                    Flight::flash('warning', __('invoices.email_failed_but_marked_sent'));
                }
            }
            
            // Update invoice status to sent
            $this->invoice->updateStatus($id, Invoice::STATUS_SENT);
            
            Flight::flash('success', __('invoices.marked_as_sent'));
        } catch (Exception $e) {
            Flight::flash('error', __('invoices.send_failed') . ': ' . $e->getMessage());
        }
        
        Flight::redirect('/invoices/' . $id);
    }

    /**
     * Cancel invoice
     */
    public function cancel($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        $invoice = $this->invoice->getById($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        try {
            $this->invoice->updateStatus($id, Invoice::STATUS_CANCELLED);
            $_SESSION['success'] = __('invoices.cancelled_successfully');
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.cancel_failed') . ': ' . $e->getMessage();
        }
        
        Flight::redirect('/invoices/' . $id);
    }

    /**
     * Lock invoice
     */
    public function lock($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        try {
            $this->invoice->lockInvoice($id);
            Flight::json(['success' => true, 'message' => __('invoices.locked_successfully')]);
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }

    /**
     * Unlock invoice
     */
    public function unlock($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        // Only admins can unlock
        if (!isset($_SESSION['user']['is_admin']) || !$_SESSION['user']['is_admin']) {
            Flight::halt(403, 'Unauthorized');
            return;
        }
        
        try {
            $db = Flight::db();
            $stmt = $db->prepare("UPDATE invoices SET locked_at = NULL, locked_by = NULL WHERE id = ?");
            $stmt->execute([$id]);
            
            Flight::json(['success' => true, 'message' => __('invoices.unlocked_successfully')]);
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }

    /**
     * Record payment for invoice
     */
    public function recordPayment($id)
    {
        // Create debug response array
        $debug = [
            'step' => 'start',
            'invoice_id' => $id,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => 'Invalid CSRF token', 'debug' => $debug], 403);
            return;
        }
        
        $invoice = $this->invoice->getById($id);
        
        if (!$invoice) {
            Flight::json(['error' => 'Invoice not found', 'debug' => $debug], 404);
            return;
        }
        
        $debug['invoice_found'] = true;
        $debug['invoice_current_total'] = $invoice['total'];
        $debug['invoice_current_paid'] = $invoice['paid_amount'] ?? 0;
        
        // Get form data
        $data = [
            'invoice_id' => $id,
            'amount' => floatval(Flight::request()->data->amount),
            'payment_date' => Flight::request()->data->payment_date,
            'payment_method' => Flight::request()->data->payment_method,
            'reference' => Flight::request()->data->reference,
            'notes' => Flight::request()->data->notes
        ];
        
        $debug['payment_data'] = $data;
        
        try {
            $db = Flight::db();
            $db->beginTransaction();
            
            $debug['step'] = 'transaction_started';
            
            // Get client_id from invoice
            $clientId = $invoice['client_id'];
            $debug['client_id'] = $clientId;
            
            // Get payment method ID
            $methodId = null;
            if ($data['payment_method']) {
                $stmt = $db->prepare("SELECT id FROM payment_methods WHERE code = ? LIMIT 1");
                $stmt->execute([$data['payment_method']]);
                $method = $stmt->fetch(\PDO::FETCH_ASSOC);
                if ($method) {
                    $methodId = $method['id'];
                } else {
                    // Insert payment method if it doesn't exist
                    $names = [
                        'cash' => ['fr' => 'Espèces', 'en' => 'Cash'],
                        'card' => ['fr' => 'Carte bancaire', 'en' => 'Credit/Debit Card'],
                        'bank_transfer' => ['fr' => 'Virement bancaire', 'en' => 'Bank Transfer'],
                        'check' => ['fr' => 'Chèque', 'en' => 'Check']
                    ];
                    
                    if (isset($names[$data['payment_method']])) {
                        $stmt = $db->prepare("INSERT INTO payment_methods (name, code, is_active) VALUES (?, ?, 1)");
                        $stmt->execute([json_encode($names[$data['payment_method']]), $data['payment_method']]);
                        $methodId = $db->lastInsertId();
                        $debug['payment_method_created'] = true;
                    }
                }
            }
            $debug['method_id'] = $methodId;
            
            // Insert payment record
            $stmt = $db->prepare("
                INSERT INTO payments (client_id, method_id, amount, payment_date, reference, notes, created_at, created_by)
                VALUES (:client_id, :method_id, :amount, :payment_date, :reference, :notes, NOW(), :created_by)
            ");
            
            $paymentParams = [
                ':client_id' => $clientId,
                ':method_id' => $methodId,
                ':amount' => $data['amount'],
                ':payment_date' => $data['payment_date'],
                ':reference' => $data['reference'],
                ':notes' => $data['notes'],
                ':created_by' => $_SESSION['user_id'] ?? 1
            ];
            
            $debug['payment_insert_params'] = $paymentParams;
            
            $stmt->execute($paymentParams);
            $paymentId = $db->lastInsertId();
            
            $debug['payment_inserted'] = true;
            $debug['payment_id'] = $paymentId;
            
            // Insert payment allocation
            $stmt = $db->prepare("
                INSERT INTO payment_allocations (payment_id, invoice_id, amount, created_at)
                VALUES (:payment_id, :invoice_id, :amount, NOW())
            ");
            
            $stmt->execute([
                ':payment_id' => $paymentId,
                ':invoice_id' => $id,
                ':amount' => $data['amount']
            ]);
            
            $debug['allocation_inserted'] = true;
            
            // Update invoice paid_amount
            $stmt = $db->prepare("
                UPDATE invoices 
                SET paid_amount = COALESCE(paid_amount, 0) + :amount,
                    updated_at = NOW()
                WHERE id = :id
            ");
            $updateResult = $stmt->execute([
                ':amount' => $data['amount'],
                ':id' => $id
            ]);
            
            $debug['invoice_update_result'] = $updateResult;
            $debug['rows_affected'] = $stmt->rowCount();
            
            // Get updated totals
            $stmt = $db->prepare("SELECT total, paid_amount FROM invoices WHERE id = ?");
            $stmt->execute([$id]);
            $totals = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            $debug['updated_totals'] = $totals;
            
            // Update status based on payment
            $newStatus = $invoice['status'];
            $totalAmount = floatval($totals['total']);
            $paidAmount = floatval($totals['paid_amount']);
            
            // Handle edge cases
            if ($totalAmount <= 0 && $paidAmount > 0) {
                // Invoice with zero or negative total but has payments = PAID
                $newStatus = Invoice::STATUS_PAID;
            } elseif ($paidAmount >= $totalAmount) {
                // Paid amount equals or exceeds total = PAID
                $newStatus = Invoice::STATUS_PAID;
            } elseif ($paidAmount > 0 && $paidAmount < $totalAmount) {
                // Paid amount is less than total = PARTIAL
                $newStatus = Invoice::STATUS_PARTIAL;
            } elseif ($paidAmount <= 0 && $invoice['status'] === Invoice::STATUS_PAID) {
                // If somehow paid amount becomes 0 or negative, revert from PAID
                $newStatus = Invoice::STATUS_SENT;
            }
            
            $debug['old_status'] = $invoice['status'];
            $debug['new_status'] = $newStatus;
            
            if ($newStatus != $invoice['status']) {
                $this->invoice->updateStatus($id, $newStatus);
                $debug['status_updated'] = true;
            }
            
            $db->commit();
            $debug['transaction_committed'] = true;
            
            // Clear invoice cache
            $this->invoice->clearInvoiceCache($id);
            $debug['cache_cleared'] = true;
            
            // Return JSON response with debug info
            Flight::json([
                'success' => true,
                'message' => __('invoices.payment_recorded'),
                'redirect' => Flight::get('flight.base_url') . '/invoices/' . $id,
                'debug' => $debug
            ]);
            
        } catch (Exception $e) {
            $db->rollBack();
            $debug['error'] = $e->getMessage();
            $debug['error_trace'] = $e->getTraceAsString();
            
            Flight::json([
                'success' => false,
                'error' => __('invoices.payment_failed') . ': ' . $e->getMessage(),
                'debug' => $debug
            ], 500);
        }
    }

    /**
     * Email preview
     */
    public function emailPreview($id)
    {
        $invoice = $this->invoice->getInvoiceWithDetails($id);
        
        if (!$invoice) {
            Flight::notFound();
            return;
        }
        
        // Get email template
        $emailService = new \App\Services\EmailService();
        $preview = $emailService->previewInvoiceEmail($invoice);
        
        Flight::json([
            'success' => true,
            'subject' => $preview['subject'],
            'body' => $preview['body'],
            'to' => $invoice['client']['email']
        ]);
    }

    /**
     * Store credit note
     */
    public function storeCreditNote($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        $data = $this->getRequestData();
        
        try {
            $creditNote = $this->invoice->createCreditNote($id, $data);
            
            $_SESSION['success'] = __('invoices.credit_note_created');
            Flight::redirect('/invoices/' . $creditNote['id']);
            
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.credit_note_failed') . ': ' . $e->getMessage();
            Flight::redirect('/invoices/' . $id . '/credit-note');
        }
    }

    /**
     * Bulk send invoices
     */
    public function bulkSend()
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        // Get invoice IDs from either format
        $invoiceIds = Flight::request()->data->invoice_ids ?? 
                     Flight::request()->data->ids ?? 
                     $_POST['ids'] ?? 
                     [];
        $sent = 0;
        $failed = 0;
        
        foreach ($invoiceIds as $id) {
            try {
                $result = $this->sendInvoiceEmail($id);
                if ($result) {
                    $this->invoice->updateStatus($id, Invoice::STATUS_SENT);
                    $sent++;
                } else {
                    $failed++;
                }
            } catch (Exception $e) {
                $failed++;
            }
        }
        
        Flight::json([
            'success' => true,
            'sent' => $sent,
            'failed' => $failed,
            'message' => sprintf(__('invoices.bulk_sent_result'), $sent, $failed)
        ]);
    }

    /**
     * Bulk export invoices
     */
    public function bulkExport()
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        // Get invoice IDs from either format
        $invoiceIds = Flight::request()->data->invoice_ids ?? 
                     Flight::request()->data->ids ?? 
                     $_POST['ids'] ?? 
                     [];
        
        $filters = $this->getRequestData();
        
        // Get invoices based on filters
        $invoices = $this->getInvoices($filters, 1, 10000); // Get all matching invoices
        
        // Generate CSV or PDF based on format
        $format = $filters['format'] ?? 'csv';
        
        if ($format === 'csv') {
            $this->exportInvoicesCsv($invoices['data']);
        } else {
            $this->exportInvoicesPdf($invoices['data']);
        }
    }

    /**
     * Export invoices as CSV
     */
    private function exportInvoicesCsv($invoices)
    {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="invoices-' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // Add BOM for Excel
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Headers
        fputcsv($output, [
            __('invoices.invoice_number'),
            __('invoices.client'),
            __('invoices.issue_date'),
            __('invoices.due_date'),
            __('invoices.subtotal'),
            __('invoices.vat'),
            __('invoices.total'),
            __('invoices.status')
        ]);
        
        // Data
        foreach ($invoices as $invoice) {
            fputcsv($output, [
                $invoice['invoice_number'],
                $invoice['client_name'],
                $invoice['issue_date'],
                $invoice['due_date'],
                $invoice['subtotal'],
                $invoice['vat_amount'],
                $invoice['total'],
                $invoice['status']
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * Export invoices as PDF
     */
    private function exportInvoicesPdf($invoices)
    {
        // This would use PdfService to generate a combined PDF
        // For now, just redirect to list
        $_SESSION['error'] = __('invoices.pdf_export_not_implemented');
        Flight::redirect(Flight::get('flight.base_url') . '/invoices');
    }

    /**
     * Search billable items (AJAX)
     */
    public function searchBillable()
    {
        $query = Flight::request()->query->q ?? '';
        $type = Flight::request()->query->type ?? 'service';
        
        $results = [];
        
        try {
            $db = Flight::db();
            
            if ($type === 'patient') {
                // Load all patients
                $stmt = $db->query("
                    SELECT id, CONCAT(first_name, ' ', last_name) as name, patient_number 
                    FROM patients 
                    WHERE is_active = 1 
                    ORDER BY first_name, last_name
                ");
                $patients = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                
                foreach ($patients as $patient) {
                    $results[] = [
                        'id' => $patient['id'],
                        'name' => $patient['name'] . ' (' . $patient['patient_number'] . ')'
                    ];
                }
            } elseif ($type === 'client') {
                // Load all clients
                $stmt = $db->query("
                    SELECT id, name, client_number 
                    FROM clients 
                    WHERE is_active = 1 
                    ORDER BY name
                ");
                $clients = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                
                foreach ($clients as $client) {
                    $results[] = [
                        'id' => $client['id'],
                        'name' => $client['name'],
                        'code' => $client['client_number'],
                        'display_name' => $client['name'] . ' (' . $client['client_number'] . ')'
                    ];
                }
            } elseif ($type === 'user') {
                // Load all users
                $stmt = $db->query("
                    SELECT id, CONCAT(first_name, ' ', last_name) as name, username 
                    FROM users 
                    WHERE is_active = 1 
                    ORDER BY first_name, last_name
                ");
                $users = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                
                foreach ($users as $user) {
                    $results[] = [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'code' => $user['username'],
                        'display_name' => $user['name'] . ' (' . $user['username'] . ')'
                    ];
                }
            }
            
            // Return in the format expected by invoice-billable-fix.js
            Flight::json([
                'success' => true,
                'results' => $results
            ]);
        } catch (Exception $e) {
            Flight::json([
                'success' => false,
                'results' => [],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate invoice number (AJAX)
     */
    public function generateNumber()
    {
        $documentTypeId = Flight::request()->query->document_type_id;
        $invoiceTypeId = Flight::request()->query->invoice_type_id;
        
        if (!$documentTypeId) {
            Flight::json(['success' => false, 'message' => 'Document type ID is required'], 400);
            return;
        }
        
        try {
            // Use suggestDocumentNumber instead of generateDocumentNumber to avoid incrementing the sequence
            $number = $this->invoice->suggestDocumentNumber($documentTypeId, $invoiceTypeId);
            Flight::json(['success' => true, 'number' => $number]);
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }

    /**
     * Calculate line total (AJAX)
     */
    public function calculateLine()
    {
        $data = Flight::request()->data->getData();
        
        $quantity = floatval($data['quantity'] ?? 0);
        $unitPrice = floatval($data['unit_price'] ?? 0);
        $vatRate = floatval($data['vat_rate'] ?? 0);
        
        $subtotal = MoneyHelper::round($quantity * $unitPrice);
        $vat = MoneyHelper::calculateTax($subtotal, $vatRate, false);
        $total = MoneyHelper::round($subtotal + $vat);
        
        Flight::json([
            'subtotal' => $subtotal,
            'vat' => $vat,
            'total' => $total
        ]);
    }

    /**
     * Check voucher validity (AJAX)
     */
    public function checkVoucher()
    {
        $code = Flight::request()->query->code ?? '';
        
        if (empty($code)) {
            Flight::json(['valid' => false, 'message' => __('vouchers.code_required')]);
            return;
        }
        
        $voucher = new \App\Models\Voucher();
        $result = $voucher->validateCode($code);
        
        Flight::json($result);
    }

    /**
     * Invoice templates management
     */
    public function templates()
    {
        // Get all templates
        $db = Flight::db();
        $stmt = $db->query("SELECT * FROM invoice_templates ORDER BY is_default DESC, name");
        $templates = $stmt->fetchAll();
        
        $this->render('invoices/templates', [
            'templates' => $templates
        ]);
    }

    /**
     * Edit invoice template
     */
    public function editTemplate($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM invoice_templates WHERE id = ?");
        $stmt->execute([$id]);
        $template = $stmt->fetch();
        
        if (!$template) {
            Flight::notFound();
            return;
        }
        
        $this->render('invoices/edit-template', [
            'template' => $template,
            'available_variables' => $this->getTemplateVariables()
        ]);
    }

    /**
     * Update invoice template
     */
    public function updateTemplate($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::halt(403, 'Invalid CSRF token');
            return;
        }
        
        $data = $this->getRequestData();
        
        try {
            $db = Flight::db();
            $stmt = $db->prepare("
                UPDATE invoice_templates 
                SET name = ?, description = ?, html_template = ?, css_styles = ?, 
                    header_content = ?, footer_content = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([
                $data['name'],
                $data['description'],
                $data['html_template'],
                $data['css_styles'],
                $data['header_content'],
                $data['footer_content'],
                $id
            ]);
            
            $_SESSION['success'] = __('invoices.template_updated');
            Flight::redirect('/invoices/templates');
            
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.template_update_failed') . ': ' . $e->getMessage();
            Flight::redirect('/invoices/templates/' . $id . '/edit');
        }
    }

    /**
     * Get available template variables
     */
    private function getTemplateVariables()
    {
        return [
            'invoice' => [
                'invoice_number', 'issue_date', 'due_date', 'status',
                'subtotal', 'vat_amount', 'total', 'notes'
            ],
            'client' => [
                'name', 'email', 'phone', 'address', 'vat_number'
            ],
            'company' => [
                'name', 'address', 'phone', 'email', 'vat_number', 'logo'
            ],
            'lines' => [
                'description', 'quantity', 'unit_price', 'vat_rate', 'total'
            ]
        ];
    }
    
    /**
     * Get invoice statistics
     */
    private function getInvoiceStatistics()
    {
        $db = Flight::db();
        
        // Total invoices
        $stmt = $db->query("SELECT COUNT(*) as total FROM invoices");
        $totalInvoices = $stmt->fetch()['total'];
        
        // Total revenue (sum of paid invoices)
        $stmt = $db->query("SELECT COALESCE(SUM(total), 0) as total FROM invoices WHERE status = 'paid'");
        $totalRevenue = $stmt->fetch()['total'];
        
        // Unpaid invoices count
        $stmt = $db->query("SELECT COUNT(*) as total FROM invoices WHERE status IN ('sent', 'partial', 'overdue')");
        $unpaidInvoices = $stmt->fetch()['total'];
        
        // Outstanding amount
        $stmt = $db->query("SELECT COALESCE(SUM(total), 0) as total FROM invoices WHERE status IN ('sent', 'partial', 'overdue')");
        $outstandingAmount = $stmt->fetch()['total'];
        
        return [
            'total_invoices' => $totalInvoices,
            'total_revenue' => $totalRevenue,
            'unpaid_invoices' => $unpaidInvoices,
            'outstanding_amount' => $outstandingAmount
        ];
    }
    
    /**
     * Bulk delete invoices
     */
    public function bulkDelete()
    {
        try {
            if (Flight::request()->method === 'GET') {
                // Redirect to invoice list if accessed via GET
                Flight::redirect('/invoices');
                return;
            }
            
            // Validate CSRF token
            if (!$this->validateCsrfToken()) {
                Flight::halt(403, 'Invalid CSRF token');
                return;
            }
            
            // Get selected invoice IDs
            $invoiceIds = Flight::request()->data->invoice_ids ?? 
                         Flight::request()->data->ids ?? 
                         $_POST['ids'] ?? 
                         [];
            
            if (empty($invoiceIds)) {
                $_SESSION['error'] = __('invoices.no_invoices_selected');
                Flight::redirect('/invoices');
                return;
            }
            
            $db = Flight::db();
            $deletedCount = 0;
            $errors = [];
            
            foreach ($invoiceIds as $id) {
                $invoice = $this->invoice->getById($id);
                
                if (!$invoice) {
                    continue;
                }
                
                // Only allow deletion of draft invoices
                if ($invoice['status'] !== Invoice::STATUS_DRAFT) {
                    $errors[] = __('invoices.cannot_delete_invoice', ['number' => $invoice['invoice_number']]);
                    continue;
                }
                
                // Delete invoice lines first
                $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                $stmt->execute([$id]);
                
                // Delete invoice items if table exists
                $stmt = $db->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
                $stmt->execute([$id]);
                
                // Delete the invoice
                $stmt = $db->prepare("DELETE FROM invoices WHERE id = ? AND status = ?");
                $stmt->execute([$id, Invoice::STATUS_DRAFT]);
                
                if ($stmt->rowCount() > 0) {
                    $deletedCount++;
                }
            }
            
            if ($deletedCount > 0) {
                $_SESSION['success'] = __('invoices.bulk_deleted_success', ['count' => $deletedCount]);
            }
            
            if (!empty($errors)) {
                $_SESSION['error'] = implode('<br>', $errors);
            }
            
            Flight::redirect('/invoices');
            
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.bulk_delete_failed') . ': ' . $e->getMessage();
            Flight::redirect('/invoices');
        }
    }
    
    /**
     * Display invoice archive
     */
    public function archive()
    {
        $filters = [
            'status' => Flight::request()->query->status,
            'document_type' => Flight::request()->query->document_type,
            'date_from' => Flight::request()->query->date_from,
            'date_to' => Flight::request()->query->date_to,
            'search' => Flight::request()->query->search,
            'show_archived' => true // This tells getInvoices to show archived items
        ];
        
        $page = Flight::request()->query->page ?: 1;
        $limit = Flight::request()->query->limit ?: 20;
        
        $invoices = $this->getInvoices($filters, $page, $limit);
        $documentTypes = DocumentType::getActiveTypes();
        $archiveStats = $this->invoice->getArchiveStatistics();
        
        // Format document types for select
        $documentTypesForSelect = [];
        foreach ($documentTypes as $docType) {
            $documentTypesForSelect[$docType['id']] = $docType['name'];
        }
        
        $this->render('invoices/archive-modern', [
            'invoices' => $invoices['data'],
            'pagination' => $invoices['pagination'],
            'current_page' => $invoices['pagination']['page'],
            'total_pages' => $invoices['pagination']['pages'],
            'filters' => $filters,
            'documentTypes' => $documentTypesForSelect,
            'archiveStats' => $archiveStats,
            'statuses' => [
                Invoice::STATUS_DRAFT => __('invoices.status.draft'),
                Invoice::STATUS_SENT => __('invoices.status.sent'),
                Invoice::STATUS_PAID => __('invoices.status.paid'),
                Invoice::STATUS_PARTIAL => __('invoices.status.partial'),
                Invoice::STATUS_OVERDUE => __('invoices.status.overdue'),
                Invoice::STATUS_CANCELLED => __('invoices.status.cancelled')
            ],
            'currency' => Flight::get('config')['currency_symbol'] ?? '€'
        ]);
    }
    
    /**
     * Archive a single invoice
     */
    public function archiveInvoice($id)
    {
        try {
            if (!$this->validateCsrfToken()) {
                Flight::halt(403, 'Invalid CSRF token');
                return;
            }
            
            $reason = Flight::request()->data->reason ?? null;
            $this->invoice->archiveInvoice($id, $_SESSION['user_id'], $reason);
            
            $_SESSION['success'] = __('invoices.archived_successfully');
            Flight::redirect('/invoices');
            
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.archive_failed') . ': ' . $e->getMessage();
            Flight::redirect('/invoices/' . $id);
        }
    }
    
    /**
     * Restore an archived invoice
     */
    public function restoreInvoice($id)
    {
        try {
            if (!$this->validateCsrfToken()) {
                Flight::halt(403, 'Invalid CSRF token');
                return;
            }
            
            $reason = Flight::request()->data->reason ?? null;
            $this->invoice->restoreInvoice($id, $_SESSION['user_id'], $reason);
            
            $_SESSION['success'] = __('invoices.restored_successfully');
            Flight::redirect('/invoices/archive');
            
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.restore_failed') . ': ' . $e->getMessage();
            Flight::redirect('/invoices/archive');
        }
    }
    
    /**
     * Bulk archive invoices
     */
    public function bulkArchive()
    {
        try {
            if (!$this->validateCsrfToken()) {
                Flight::halt(403, 'Invalid CSRF token');
                return;
            }
            
            // Get invoice IDs from either format
            $invoiceIds = Flight::request()->data->invoice_ids ?? 
                         Flight::request()->data->ids ?? 
                         $_POST['ids'] ?? 
                         [];
            
            if (empty($invoiceIds)) {
                $_SESSION['error'] = __('invoices.no_invoices_selected');
                Flight::redirect('/invoices');
                return;
            }
            
            $archivedCount = 0;
            $errors = [];
            
            foreach ($invoiceIds as $id) {
                try {
                    $this->invoice->archiveInvoice($id, $_SESSION['user_id']);
                    $archivedCount++;
                } catch (Exception $e) {
                    $invoice = $this->invoice->getById($id);
                    $errors[] = __('invoices.cannot_archive_invoice', [
                        'number' => $invoice['invoice_number'] ?? $id,
                        'reason' => $e->getMessage()
                    ]);
                }
            }
            
            if ($archivedCount > 0) {
                $_SESSION['success'] = __('invoices.bulk_archived_success', ['count' => $archivedCount]);
            }
            
            if (!empty($errors)) {
                $_SESSION['error'] = implode('<br>', $errors);
            }
            
            Flight::redirect('/invoices');
            
        } catch (Exception $e) {
            $_SESSION['error'] = __('invoices.bulk_archive_failed') . ': ' . $e->getMessage();
            Flight::redirect('/invoices');
        }
    }
    
    /**
     * Show monthly rental invoice generation form
     */
    public function generateMonthly()
    {
        // Check permission
        if (!User::canEditFinancialObligations($_SESSION['user_id'])) {
            Flight::flash('error', __('common.access_denied'));
            Flight::redirect('/invoices');
            return;
        }
        
        // Get current month by default
        $defaultMonth = date('Y-m');
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'invoices/generate-monthly-modern';
        
        $this->render($viewName, [
            'defaultMonth' => $defaultMonth,
            'currentYear' => date('Y')
        ]);
    }
    
    /**
     * Preview monthly rental invoices
     */
    public function previewMonthly()
    {
        try {
            // Check permission
            if (!User::canEditFinancialObligations($_SESSION['user_id'])) {
                Flight::json(['success' => false, 'message' => __('common.access_denied')]);
                return;
            }
            
            $data = Flight::request()->data->getData();
            $month = $data['month'] ?? date('Y-m');
            $userIds = $data['user_ids'] ?? [];
            
            // Validate month format
            if (!preg_match('/^\d{4}-\d{2}$/', $month)) {
                Flight::json(['success' => false, 'message' => 'Invalid month format']);
                return;
            }
            
            $generator = new \App\Services\MonthlyInvoiceGenerator();
            $preview = $generator->getPreview($month, $userIds);
            
            Flight::json([
                'success' => true,
                'preview' => $preview,
                'month' => $month
            ]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * Generate monthly rental invoices
     */
    public function processMonthly()
    {
        try {
            // Check permission
            if (!User::canEditFinancialObligations($_SESSION['user_id'])) {
                Flight::json(['success' => false, 'message' => __('common.access_denied')]);
                return;
            }
            
            // Validate CSRF token
            $this->validateCsrfToken();
            
            $data = Flight::request()->data->getData();
            $month = $data['month'] ?? date('Y-m');
            $userIds = $data['user_ids'] ?? [];
            
            // Validate month format
            if (!preg_match('/^\d{4}-\d{2}$/', $month)) {
                Flight::json(['success' => false, 'message' => 'Invalid month format']);
                return;
            }
            
            $generator = new \App\Services\MonthlyInvoiceGenerator();
            $results = $generator->generateMonthlyInvoices($month, $userIds);
            
            // Prepare response message
            $message = '';
            if ($results['success'] > 0) {
                $message .= sprintf(__('invoices.monthly_generated_success'), $results['success']);
            }
            if ($results['errors'] > 0) {
                $message .= ' ' . sprintf(__('invoices.monthly_generated_errors'), $results['errors']);
            }
            
            Flight::json([
                'success' => $results['errors'] === 0,
                'message' => $message,
                'results' => $results
            ]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * Show monthly invoice generation form
     */
    public function showGenerateMonthly()
    {
        $monthlyGenerator = new \App\Services\MonthlyInvoiceGenerator();
        
        // Get current month for default selection
        $currentMonth = date('Y-m');
        $selectedMonth = Flight::request()->query->month ?: $currentMonth;
        
        // Get preview if requested
        $previews = [];
        if (Flight::request()->query->preview) {
            $previews = $monthlyGenerator->previewMonthlyInvoices($selectedMonth);
        }
        
        // Get list of users with financial obligations
        $userModel = new User();
        $usersWithObligations = $userModel->getUsersWithFinancialObligations();
        
        // Get template
        $template = $this->getTemplate();
        $viewName = 'invoices/generate-monthly-' . $template;
        
        $this->render($viewName, [
            'title' => __('invoices.generate_monthly'),
            'selectedMonth' => $selectedMonth,
            'currentMonth' => $currentMonth,
            'previews' => $previews,
            'usersWithObligations' => $usersWithObligations
        ]);
    }
    
    /**
     * Generate monthly rental invoices
     */
    public function generateMonthlyRentalInvoices()
    {
        try {
            $monthlyGenerator = new \App\Services\MonthlyInvoiceGenerator();
            
            $month = Flight::request()->data->month;
            $userIds = Flight::request()->data->user_ids ?: [];
            
            if (empty($month)) {
                throw new Exception(__('validation.required', ['field' => 'month']));
            }
            
            // Generate invoices
            $results = $monthlyGenerator->generateMonthlyInvoices($month, $userIds);
            
            // Prepare success message
            $successCount = count($results['success']);
            $errorCount = count($results['errors']);
            
            $message = __('invoices.monthly_generation_complete', [
                'success' => $successCount,
                'errors' => $errorCount
            ]);
            
            $baseUrl = Flight::get('base_url') ?: '/fit/public';
            
            Flight::json([
                'success' => true,
                'message' => $message,
                'results' => $results,
                'redirect' => $baseUrl . '/invoices',
                'debug' => [
                    'base_url' => Flight::get('base_url'),
                    'fallback' => $baseUrl
                ]
            ]);
            
        } catch (Exception $e) {
            Flight::json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Preview monthly invoices via AJAX
     */
    public function previewMonthlyInvoices()
    {
        try {
            $monthlyGenerator = new \App\Services\MonthlyInvoiceGenerator();
            
            $month = Flight::request()->query->month;
            $userIds = Flight::request()->query->user_ids ?: [];
            
            if (empty($month)) {
                throw new Exception(__('validation.required', ['field' => 'month']));
            }
            
            // Get previews
            $previews = $monthlyGenerator->previewMonthlyInvoices($month, $userIds);
            
            Flight::json([
                'success' => true,
                'previews' => $previews
            ]);
            
        } catch (Exception $e) {
            Flight::json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Update invoice items via AJAX
     */
    public function updateInvoiceItems($id)
    {
        try {
            // Verify CSRF token
            if (!$this->verifyCSRFToken()) {
                Flight::json(['success' => false, 'message' => 'Invalid CSRF token'], 403);
                return;
            }
            
            // Get invoice
            $invoice = $this->invoice->getById($id);
            if (!$invoice) {
                Flight::json(['success' => false, 'message' => 'Invoice not found'], 404);
                return;
            }
            
            // Check if can edit
            if (!$this->invoice->canEdit($id)) {
                Flight::json(['success' => false, 'message' => 'Cannot edit this invoice'], 403);
                return;
            }
            
            // Get items from request
            $items = Flight::request()->data->items ?? [];
            
            if (!is_array($items)) {
                Flight::json(['success' => false, 'message' => 'Invalid items data'], 400);
                return;
            }
            
            // Update invoice with new items
            $updateData = [
                'lines' => $items
            ];
            
            $this->invoice->updateInvoice($id, $updateData);
            
            // Get updated invoice
            $updatedInvoice = $this->invoice->getById($id);
            
            Flight::json([
                'success' => true,
                'message' => __('invoices.items_updated_successfully'),
                'invoice' => [
                    'subtotal' => $updatedInvoice['subtotal'],
                    'vat_amount' => $updatedInvoice['vat_amount'],
                    'total' => $updatedInvoice['total']
                ]
            ]);
            
        } catch (Exception $e) {
            error_log("Error updating invoice items: " . $e->getMessage());
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Count all invoices by status (AJAX)
     */
    public function countAllInvoices()
    {
        // Disable any output buffering
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Set JSON header
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');
        
        try {
            $db = Flight::db();
            
            // Simple count query without checking for is_archived column
            $query = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid,
                    SUM(CASE WHEN status = 'partial' THEN 1 ELSE 0 END) as partial,
                    SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM invoices
            ";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            $counts = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            // Ensure we have numeric values
            $result = array();
            foreach ($counts as $key => $value) {
                $result[$key] = intval($value);
            }
            
            echo json_encode($result);
            exit();
            
        } catch (Exception $e) {
            error_log("Error counting invoices: " . $e->getMessage());
            echo json_encode([
                'error' => $e->getMessage(),
                'total' => 0,
                'draft' => 0,
                'sent' => 0,
                'paid' => 0,
                'partial' => 0,
                'overdue' => 0,
                'cancelled' => 0
            ]);
            exit();
        }
    }
    
    /**
     * Delete all invoices (Admin only)
     */
    public function deleteAllInvoices()
    {
        // Disable any output buffering
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Set JSON header
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');
        
        try {
            // Verify admin access
            $userId = $_SESSION['user_id'] ?? 0;
            if (!User::isAdmin($userId)) {
                echo json_encode(['success' => false, 'message' => 'Unauthorized: Admin access required']);
                exit();
            }
            
            // Verify CSRF token
            if (!$this->validateCsrfToken()) {
                echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
                exit();
            }
            
            // Verify confirmation text
            $confirmation = Flight::request()->data->confirmation ?? '';
            if ($confirmation !== 'DELETE ALL') {
                echo json_encode(['success' => false, 'message' => 'Invalid confirmation']);
                exit();
            }
            
            $deleteType = Flight::request()->data->delete_type ?? 'all';
            
            $db = Flight::db();
            $db->beginTransaction();
            
            try {
                // Build where clause based on delete type
                $whereClause = '';
                $whereConditions = [];
                
                if ($deleteType === 'draft') {
                    $whereConditions[] = "status = 'draft'";
                }
                
                // Check if is_archived column exists
                try {
                    $stmt = $db->prepare("SHOW COLUMNS FROM invoices LIKE 'is_archived'");
                    $stmt->execute();
                    if ($stmt->fetch()) {
                        $whereConditions[] = "is_archived = 0";
                    }
                } catch (Exception $e) {
                    // Column doesn't exist, ignore
                }
                
                if (!empty($whereConditions)) {
                    $whereClause = " WHERE " . implode(' AND ', $whereConditions);
                }
                
                // Get invoice IDs to delete
                $stmt = $db->prepare("SELECT id FROM invoices" . $whereClause);
                $stmt->execute();
                $invoiceIds = $stmt->fetchAll(\PDO::FETCH_COLUMN);
                
                if (empty($invoiceIds)) {
                    $db->rollBack();
                    echo json_encode(['success' => false, 'message' => __('invoices.no_invoices_to_delete')]);
                    exit();
                }
                
                // Delete related data
                $idsPlaceholder = implode(',', array_map(function() { return '?'; }, $invoiceIds));
                
                // Delete invoice lines
                $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id IN ($idsPlaceholder)");
                $stmt->execute($invoiceIds);
                
                // Delete invoice items (if table exists)
                try {
                    $stmt = $db->prepare("DELETE FROM invoice_items WHERE invoice_id IN ($idsPlaceholder)");
                    $stmt->execute($invoiceIds);
                } catch (Exception $e) {
                    // Table might not exist, ignore
                }
                
                // Delete invoice payments
                try {
                    $stmt = $db->prepare("DELETE FROM invoice_payments WHERE invoice_id IN ($idsPlaceholder)");
                    $stmt->execute($invoiceIds);
                } catch (Exception $e) {
                    // Table might not exist, ignore
                }
                
                // Delete invoice email history
                try {
                    $stmt = $db->prepare("DELETE FROM invoice_email_history WHERE invoice_id IN ($idsPlaceholder)");
                    $stmt->execute($invoiceIds);
                } catch (Exception $e) {
                    // Table might not exist, ignore
                }
                
                // Finally, delete invoices
                $stmt = $db->prepare("DELETE FROM invoices" . $whereClause);
                $stmt->execute();
                $deletedCount = $stmt->rowCount();
                
                // Log the action
                error_log("Admin user $userId deleted $deletedCount invoices (type: $deleteType)");
                
                // Try to log in activity table if exists
                try {
                    $stmt = $db->prepare("
                        INSERT INTO activity_logs (user_id, action, description, ip_address, created_at)
                        VALUES (?, 'delete_all_invoices', ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $userId,
                        "Deleted $deletedCount invoices (type: $deleteType)",
                        $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
                    ]);
                } catch (Exception $e) {
                    // Activity log table might not exist
                }
                
                $db->commit();
                
                $message = $deleteType === 'draft' 
                    ? __('invoices.draft_invoices_deleted', ['count' => $deletedCount])
                    : __('invoices.all_invoices_deleted', ['count' => $deletedCount]);
                
                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'deleted_count' => $deletedCount
                ]);
                exit();
                
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (Exception $e) {
            error_log("Error deleting all invoices: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ]);
            exit();
        }
    }
    
    /**
     * Calculate period for invoice based on type
     */
    private function calculateInvoicePeriod($data)
    {
        // French month names
        $frenchMonths = [
            1 => 'JANVIER', 2 => 'FÉVRIER', 3 => 'MARS', 4 => 'AVRIL',
            5 => 'MAI', 6 => 'JUIN', 7 => 'JUILLET', 8 => 'AOÛT',
            9 => 'SEPTEMBRE', 10 => 'OCTOBRE', 11 => 'NOVEMBRE', 12 => 'DÉCEMBRE'
        ];
        
        // Get invoice date
        $invoiceDate = $data['issue_date'] ?? date('Y-m-d');
        $timestamp = strtotime($invoiceDate);
        
        // Check if this is a rental invoice (FAC-LOY)
        $isRentalInvoice = false;
        if (isset($data['invoice_number']) && strpos($data['invoice_number'], 'FAC-LOY') !== false) {
            $isRentalInvoice = true;
        } elseif (isset($data['invoice_type']) && in_array($data['invoice_type'], ['rental', 'loyer'])) {
            $isRentalInvoice = true;
        }
        
        // Check if this is a retrocession invoice (FAC-RET)
        $isRetrocessionInvoice = false;
        if (isset($data['invoice_number'])) {
            if (strpos($data['invoice_number'], 'FAC-RET') !== false ||
                strpos($data['invoice_number'], 'RET30') !== false ||
                strpos($data['invoice_number'], 'RET25') !== false) {
                $isRetrocessionInvoice = true;
            }
        } elseif (isset($data['invoice_type']) && in_array($data['invoice_type'], ['retrocession_30', 'retrocession_25'])) {
            $isRetrocessionInvoice = true;
        }
        
        if ($isRentalInvoice) {
            // For rental invoices, use current month
            $month = date('n', $timestamp);
            $year = date('Y', $timestamp);
            return $frenchMonths[$month] . ' ' . $year;
        } elseif ($isRetrocessionInvoice) {
            // For retrocession invoices, use previous month
            $previousMonth = date('n', strtotime('-1 month', $timestamp));
            $previousYear = date('Y', strtotime('-1 month', $timestamp));
            return $frenchMonths[$previousMonth] . ' ' . $previousYear;
        } else {
            // For other invoices (including user invoices), use current month
            $month = date('n', $timestamp);
            $year = date('Y', $timestamp);
            return $frenchMonths[$month] . ' ' . $year;
        }
    }
    
    /**
     * Get invoice templates via API (public endpoint)
     */
    public function getInvoiceTemplatesApi()
    {
        try {
            $type = $_GET['type'] ?? '';
            
            // Use the existing private method to get templates
            $templates = $this->getInvoiceTemplates($type);
            
            Flight::json([
                'success' => true,
                'templates' => $templates ?: []
            ]);
            
        } catch (Exception $e) {
            Flight::json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get invoice template details via API
     */
    public function getInvoiceTemplateDetails($id)
    {
        try {
            $db = Flight::db();
            $stmt = $db->prepare("
                SELECT * FROM invoice_templates 
                WHERE id = :id 
                AND is_active = TRUE
            ");
            $stmt->execute([':id' => $id]);
            $template = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            Flight::json([
                'success' => true,
                'template' => $template ?: null
            ]);
            
        } catch (Exception $e) {
            Flight::json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
}