<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Recreating Invoice Items for Invoice 271</h2>";
    
    // Get invoice details
    $invoiceSql = "SELECT i.*, u.first_name, u.last_name 
                   FROM invoices i
                   LEFT JOIN users u ON i.user_id = u.id
                   WHERE i.id = :id";
    
    $invoiceStmt = $db->prepare($invoiceSql);
    $invoiceStmt->execute(['id' => $invoiceId]);
    $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>User: {$invoice['first_name']} {$invoice['last_name']}</li>";
    echo "<li>Total: €" . number_format($invoice['total'], 2) . "</li>";
    echo "<li>Subtotal: €" . number_format($invoice['subtotal'], 2) . "</li>";
    echo "<li>VAT: €" . number_format($invoice['vat_amount'], 2) . "</li>";
    echo "</ul>";
    
    // Check for existing items
    $checkItemsSql = "SELECT COUNT(*) as count FROM invoice_items WHERE invoice_id = :invoice_id";
    $checkStmt = $db->prepare($checkItemsSql);
    $checkStmt->execute(['invoice_id' => $invoiceId]);
    $itemCount = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($itemCount > 0) {
        echo "<p style='color: orange;'>Invoice already has $itemCount item(s). Delete them first if you want to recreate.</p>";
        
        if (isset($_GET['delete_existing'])) {
            $deleteSql = "DELETE FROM invoice_items WHERE invoice_id = :invoice_id";
            $deleteStmt = $db->prepare($deleteSql);
            $deleteStmt->execute(['invoice_id' => $invoiceId]);
            echo "<p style='color: green;'>Deleted existing items.</p>";
        } else {
            echo "<p><a href='?delete_existing=1'>Click here to delete existing items and recreate</a></p>";
            exit;
        }
    }
    
    // Based on the HTML you showed, we need to recreate these lines:
    // 1. RÉTROCESSION 20.00% - Base: €6,575.90, Total: €1,315.18 (no VAT)
    // 2. FRAIS SECRÉTARIAT 5.00% - Base: €6,575.90, Total: €328.79, VAT: 17%
    
    // The base amount appears to be €6,575.90
    $baseAmount = 6575.90;
    
    echo "<h3>Creating Invoice Items...</h3>";
    
    // Create retrocession line (for Rémi Heine, without "CNS")
    $retrocessionAmount = $baseAmount * 0.20; // 20% of base
    
    $insertRetro = "INSERT INTO invoice_items 
                    (invoice_id, description, quantity, unit_price, vat_rate, vat_amount, total_amount, cns_amount, retrocession_data) 
                    VALUES 
                    (:invoice_id, :description, 1, :unit_price, 0, 0, :total_amount, :cns_amount, :retrocession_data)";
    
    $retrocessionData = json_encode([
        'base_amount' => $baseAmount,
        'percentage' => 20,
        'type' => 'cns'
    ]);
    
    $stmt = $db->prepare($insertRetro);
    $stmt->execute([
        'invoice_id' => $invoiceId,
        'description' => 'RÉTROCESSION 20.00%',  // Without CNS for Rémi Heine
        'unit_price' => $retrocessionAmount,
        'total_amount' => $retrocessionAmount,
        'cns_amount' => $baseAmount,
        'retrocession_data' => $retrocessionData
    ]);
    
    echo "<p>✓ Created RÉTROCESSION line: €" . number_format($retrocessionAmount, 2) . "</p>";
    
    // Create secretary fees line
    $secretaryBase = $baseAmount * 0.05; // 5% of base
    $secretaryVat = $secretaryBase * 0.17; // 17% VAT
    $secretaryTotal = $secretaryBase + $secretaryVat;
    
    $insertSecretary = "INSERT INTO invoice_items 
                        (invoice_id, description, quantity, unit_price, vat_rate, vat_amount, total_amount, secretary_amount, retrocession_data) 
                        VALUES 
                        (:invoice_id, :description, 1, :unit_price, 17, :vat_amount, :total_amount, :secretary_amount, :retrocession_data)";
    
    $secretaryData = json_encode([
        'base_amount' => $baseAmount,
        'percentage' => 5,
        'type' => 'secretary'
    ]);
    
    $stmt = $db->prepare($insertSecretary);
    $stmt->execute([
        'invoice_id' => $invoiceId,
        'description' => 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL 5.00%',
        'unit_price' => $secretaryBase,
        'vat_amount' => $secretaryVat,
        'total_amount' => $secretaryTotal,
        'secretary_amount' => $baseAmount,
        'retrocession_data' => $secretaryData
    ]);
    
    echo "<p>✓ Created Secretary fees line: €" . number_format($secretaryTotal, 2) . " (Base: €" . number_format($secretaryBase, 2) . " + VAT: €" . number_format($secretaryVat, 2) . ")</p>";
    
    // Calculate totals
    $calculatedSubtotal = $retrocessionAmount + $secretaryBase;
    $calculatedVat = $secretaryVat;
    $calculatedTotal = $retrocessionAmount + $secretaryTotal;
    
    echo "<h3>Summary:</h3>";
    echo "<ul>";
    echo "<li>Base Amount: €" . number_format($baseAmount, 2) . "</li>";
    echo "<li>Retrocession (20%): €" . number_format($retrocessionAmount, 2) . "</li>";
    echo "<li>Secretary (5% + VAT): €" . number_format($secretaryTotal, 2) . "</li>";
    echo "<li>Calculated Subtotal: €" . number_format($calculatedSubtotal, 2) . "</li>";
    echo "<li>Calculated VAT: €" . number_format($calculatedVat, 2) . "</li>";
    echo "<li>Calculated Total: €" . number_format($calculatedTotal, 2) . "</li>";
    echo "</ul>";
    
    // Verify the items were created
    $verifySql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id ORDER BY id";
    $verifyStmt = $db->prepare($verifySql);
    $verifyStmt->execute(['invoice_id' => $invoiceId]);
    $items = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Created Invoice Items:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Description</th><th>Unit Price</th><th>VAT Rate</th><th>VAT Amount</th><th>Total</th></tr>";
    foreach ($items as $item) {
        echo "<tr>";
        echo "<td>{$item['description']}</td>";
        echo "<td>€" . number_format($item['unit_price'], 2) . "</td>";
        echo "<td>{$item['vat_rate']}%</td>";
        echo "<td>€" . number_format($item['vat_amount'], 2) . "</td>";
        echo "<td>€" . number_format($item['total_amount'], 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3 style='color: green;'>✓ Invoice items have been successfully recreated!</h3>";
    echo "<p>Note: For Rémi Heine, the retrocession line shows 'RÉTROCESSION' without 'CNS'.</p>";
    
    echo "<p><a href='/fit/public/invoices/$invoiceId' class='btn' style='background: blue; color: white; padding: 10px; text-decoration: none;'>View Updated Invoice</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>