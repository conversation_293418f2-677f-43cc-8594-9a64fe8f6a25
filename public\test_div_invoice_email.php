<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
require_once dirname(__DIR__) . '/app/config/bootstrap.php';
use App\Models\Invoice;
use App\Models\User;
use App\Services\EmailService;

$db = Flight::db();

echo "<h2>Testing DIV Invoice Email for Frank</h2>";

// First, ensure email templates exist
$stmt = $db->query("SELECT COUNT(*) as count FROM email_templates WHERE is_active = 1");
$result = $stmt->fetch(PDO::FETCH_ASSOC);
echo "<p>Active email templates: " . $result['count'] . "</p>";

if ($result['count'] == 0) {
    echo "<p style='color: red;'>No email templates found! Please run <a href='/fit/public/initialize_email_templates.php'>initialize_email_templates.php</a> first.</p>";
    exit;
}

// Get user Frank
$stmt = $db->prepare("SELECT * FROM users WHERE id = 1");
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    echo "<p style='color: red;'>User Frank (ID 1) not found!</p>";
    exit;
}

echo "<p>User: " . $user['first_name'] . " " . $user['last_name'] . " (Email: " . $user['email'] . ")</p>";

// Check for an existing draft invoice we can use
$stmt = $db->prepare("
    SELECT id, invoice_number 
    FROM invoices 
    WHERE user_id = 1 
    AND status = 'draft' 
    AND document_type_id = 1 
    AND invoice_type_id = 4
    ORDER BY id DESC 
    LIMIT 1
");
$stmt->execute();
$existingInvoice = $stmt->fetch(PDO::FETCH_ASSOC);

if ($existingInvoice) {
    echo "<p>Found existing draft invoice: " . $existingInvoice['invoice_number'] . "</p>";
    $invoiceId = $existingInvoice['id'];
} else {
    echo "<p>Creating new test invoice...</p>";
    
    // Create a new invoice
    $invoice = new Invoice();
    $invoiceData = [
        'document_type_id' => 1,
        'invoice_type_id' => 4, // DIV
        'invoice_number' => $invoice->suggestDocumentNumber(1, 4),
        'issue_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+30 days')),
        'status' => 'draft',
        'user_id' => 1,
        'subject' => 'LOYER + CHARGES',
        'period' => date('F Y'),
        'notes' => '',
        'internal_notes' => 'Test invoice for email',
        'created_by' => 1,
        'payment_term_id' => 1
    ];
    
    $invoiceId = $invoice->create($invoiceData);
    
    if (!$invoiceId) {
        echo "<p style='color: red;'>Failed to create invoice!</p>";
        exit;
    }
    
    // Add items
    $items = [
        [
            'description' => 'Loyer mensuel',
            'quantity' => 1,
            'unit_price' => 100,
            'vat_rate' => 17,
            'vat_amount' => 17,
            'total' => 117
        ]
    ];
    
    $invoice->saveItems($invoiceId, $items);
    $invoice->calculateTotals($invoiceId);
    
    echo "<p>✓ Created invoice ID: $invoiceId</p>";
}

// Test email sending
echo "<h3>Testing Email Send...</h3>";

$emailService = new EmailService();
$result = $emailService->sendInvoiceEmail($invoiceId);

if ($result['success']) {
    echo "<p style='color: green;'>✓ Email sent successfully!</p>";
    echo "<p>Subject: " . htmlspecialchars($result['subject'] ?? 'N/A') . "</p>";
    echo "<p>Message: " . htmlspecialchars($result['message'] ?? '') . "</p>";
    
    // Check if email was logged
    $stmt = $db->prepare("
        SELECT * FROM email_logs 
        WHERE invoice_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$invoiceId]);
    $log = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($log) {
        echo "<h4>Email Log:</h4>";
        echo "<ul>";
        echo "<li>To: " . htmlspecialchars($log['recipient_email']) . "</li>";
        echo "<li>Subject: " . htmlspecialchars($log['subject']) . "</li>";
        echo "<li>Status: " . $log['status'] . "</li>";
        echo "<li>Sent at: " . $log['sent_at'] . "</li>";
        echo "</ul>";
    }
    
    echo "<p><strong>Check Mailhog at <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a> to see the email.</strong></p>";
} else {
    echo "<p style='color: red;'>✗ Email failed: " . htmlspecialchars($result['message']) . "</p>";
    
    // Check PHP error log
    $errorLog = @file_get_contents('/mnt/c/wamp64/logs/php_error.log');
    if ($errorLog) {
        $lines = explode("\n", $errorLog);
        $recent = array_slice($lines, -10);
        echo "<h4>Recent PHP Errors:</h4>";
        echo "<pre style='background: #f0f0f0; padding: 10px;'>";
        foreach ($recent as $line) {
            if (strpos($line, 'EmailService') !== false) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>";
    }
}

echo "<hr>";
echo "<p><a href='/fit/public/invoices'>Back to Invoices</a></p>";