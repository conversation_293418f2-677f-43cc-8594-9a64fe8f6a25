<?php
// Clear all caches
$cacheDir = dirname(__DIR__) . '/storage/cache';
if (is_dir($cacheDir)) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($cacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($files as $fileinfo) {
        $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
        @$todo($fileinfo->getRealPath());
    }
}

echo "<h2>✅ Cache Cleared Successfully</h2>";

echo "<div style='background-color: #e8f5e9; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>LOY Invoice Fix Summary:</h3>";
echo "<ol>";
echo "<li>✅ Fixed VAT rate selection for all lines</li>";
echo "<li>✅ Updated calculateTotals() to handle LOY invoices correctly</li>";
echo "<li>✅ LOY invoices now show simple total without VAT calculation</li>";
echo "<li>✅ Secretary lines only appear when values > 0</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background-color: #f0f0f0; padding: 20px; border-radius: 10px;'>";
echo "<h3>Expected Behavior:</h3>";
echo "<h4>Pierre Louis (Simple LOY):</h4>";
echo "<ul>";
echo "<li>Loyer mensuel: €180.00 (0% VAT)</li>";
echo "<li>Charges location: €40.00 (0% VAT)</li>";
echo "<li><strong>Total: €220.00</strong></li>";
echo "</ul>";

echo "<h4>Bernard Heens (Complex LOY with Secretary):</h4>";
echo "<ul>";
echo "<li>Loyer mensuel: €500.00 (0% VAT)</li>";
echo "<li>Secrétariat TVAC 17%: €1,170.00 (17% VAT)</li>";
echo "<li>Secrétariat HTVA: €1,000.00 (0% VAT)</li>";
echo "<li>TVA 17%: €170.00 (17% VAT)</li>";
echo "<li><strong>Total: €2,840.00</strong></li>";
echo "</ul>";
echo "</div>";

echo "<div style='background-color: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>Test Now:</h3>";
echo "<p><a href='/fit/public/invoices/create?type=loyer' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>Create LOY Invoice</a></p>";
echo "<ol>";
echo "<li>Select <strong>Bernard Heens</strong></li>";
echo "<li>Verify the total shows <strong>€2,840.00</strong> (not €3,067.80)</li>";
echo "<li>Check browser console for debugging messages</li>";
echo "</ol>";
echo "</div>";

echo "<script>";
echo "console.log('Cache cleared, ready to test LOY invoice');";
echo "</script>";
?>