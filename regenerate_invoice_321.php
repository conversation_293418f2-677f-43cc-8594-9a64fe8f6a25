<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Regenerating Invoice with Updated Format</h2>\n";
    
    // Delete invoice 321
    $pdo->exec("DELETE FROM retrocession_data_entry WHERE invoice_id = 321");
    $pdo->exec("DELETE FROM invoice_lines WHERE invoice_id = 321");
    $pdo->exec("DELETE FROM user_generated_invoices WHERE invoice_id = 321");
    $pdo->exec("DELETE FROM invoices WHERE id = 321");
    
    echo "✓ Deleted invoice #321\n\n";
    
    echo "<a href='test_retrocession_generation.php?user_id=1&month=7&year=2025'>Generate invoice with new format</a>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}