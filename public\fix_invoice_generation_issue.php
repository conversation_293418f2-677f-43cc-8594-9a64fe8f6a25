<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Invoice Generation Issue</h2>";
    
    // Check what codes exist
    echo "<h3>Current Invoice Type Codes:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code LIKE '%ret%'
        ORDER BY id
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($types as $type) {
        $nameData = json_decode($type['name'], true);
        $displayName = is_array($nameData) ? ($nameData['fr'] ?? $type['name']) : $type['name'];
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td><strong>{$type['code']}</strong></td>";
        echo "<td>{$type['prefix']}</td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Issue Analysis:</h3>";
    echo "<p>The UnifiedInvoiceGenerator is looking for codes 'ret25' or 'ret30', but we have 'ret2' and 'ret3'.</p>";
    
    echo "<h3>Solution Options:</h3>";
    echo "<ol>";
    echo "<li><strong>Option 1:</strong> Update the codes from 'ret2' → 'ret25' and 'ret3' → 'ret30'</li>";
    echo "<li><strong>Option 2:</strong> Update UnifiedInvoiceGenerator to use 'ret2' and 'ret3' codes</li>";
    echo "</ol>";
    
    // Try to update the codes
    echo "<h3>Attempting to update codes:</h3>";
    
    try {
        $db->beginTransaction();
        
        // Update ret2 to ret25
        $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret25' WHERE id = 37");
        $result1 = $stmt->execute();
        $rows1 = $stmt->rowCount();
        
        // Update ret3 to ret30
        $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret30' WHERE id = 38");
        $result2 = $stmt->execute();
        $rows2 = $stmt->rowCount();
        
        if ($rows1 > 0 || $rows2 > 0) {
            $db->commit();
            echo "<p style='color: green;'>✓ Successfully updated codes:</p>";
            echo "<ul>";
            if ($rows1 > 0) echo "<li>ret2 → ret25</li>";
            if ($rows2 > 0) echo "<li>ret3 → ret30</li>";
            echo "</ul>";
        } else {
            $db->rollBack();
            echo "<p style='color: orange;'>No changes made - codes may already be correct or update failed.</p>";
        }
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<p style='color: red;'>Could not update codes: " . $e->getMessage() . "</p>";
        echo "<p>This might be due to a unique constraint on the code column.</p>";
    }
    
    // Verify final state
    echo "<h3>Final State:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret2', 'ret3', 'ret25', 'ret30')
        ORDER BY id
    ");
    $finalTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($finalTypes as $type) {
        $nameData = json_decode($type['name'], true);
        $displayName = is_array($nameData) ? ($nameData['fr'] ?? $type['name']) : $type['name'];
        
        $rowStyle = '';
        if ($type['code'] == 'ret25' || ($type['code'] == 'ret2' && $type['prefix'] == 'FAC-RET25')) {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($type['code'] == 'ret30' || ($type['code'] == 'ret3' && $type['prefix'] == 'FAC-RET30')) {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$type['id']}</td>";
        echo "<td><strong>{$type['code']}</strong></td>";
        echo "<td>{$type['prefix']}</td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Alternative solution
    echo "<h3>Alternative Solution:</h3>";
    echo "<p>If the codes cannot be updated, we need to modify the UnifiedInvoiceGenerator.php to handle 'ret2' and 'ret3' codes.</p>";
    
    // Check Frank's settings
    echo "<h3>Frank's Settings:</h3>";
    $stmt = $db->prepare("
        SELECT u.id, u.first_name, u.last_name, urs.secretary_value
        FROM users u
        LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
        WHERE u.id = 1
        ORDER BY urs.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $frank = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($frank) {
        echo "<p>User: {$frank['first_name']} {$frank['last_name']}</p>";
        echo "<p>Secretary Fee: {$frank['secretary_value']}%</p>";
        
        $expectedCode = ($frank['secretary_value'] == 5) ? 'ret25 or ret2' : 'ret30 or ret3';
        echo "<p>Expected code for generation: <strong>$expectedCode</strong></p>";
    }
    
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Next Steps:</h3>";
    echo "<p>If invoice generation still fails, we need to update the UnifiedInvoiceGenerator.php to handle the current codes.</p>";
    echo "<p><a href='/fit/public/fix_unified_generator.php' style='color: #856404; font-weight: bold;'>→ Fix UnifiedInvoiceGenerator</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}