<?php
echo "<h2>Fix for UnifiedInvoiceGenerator</h2>";

echo "<p>The issue is in /app/services/UnifiedInvoiceGenerator.php</p>";

echo "<h3>Current Issue:</h3>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
echo "// Current code in getInvoiceTypeConfig() around line 835:
if (\$secretaryValue == 5) {
    \$code = 'RET25';  // Looking for 'ret25' but we have 'ret2'
} else {
    \$code = 'RET30';  // Looking for 'ret30' but we have 'ret3'
}";
echo "</pre>";

echo "<h3>Required Fix:</h3>";
echo "<pre style='background: #e8f5e9; padding: 10px; border: 1px solid #4caf50;'>";
echo "// Update to:
if (\$secretaryValue == 5) {
    \$code = 'RET2';   // Use 'ret2' which exists
} else {
    \$code = 'RET3';   // Use 'ret3' which exists
}";
echo "</pre>";

echo "<h3>Full Fix Location:</h3>";
echo "<p>In <strong>/app/services/UnifiedInvoiceGenerator.php</strong>, find the getInvoiceTypeConfig() method (around line 810-850)</p>";
echo "<p>Look for this section:</p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
echo "// For retrocession invoices, determine if RET25 or RET30 based on secretary percentage
if (\$this->invoiceType === 'RET' && \$code === 'RET') {
    \$stmt = \$this->db->prepare(\"
        SELECT secretary_value 
        FROM user_retrocession_settings 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC LIMIT 1
    \");
    \$stmt->execute(['user_id' => \$this->userId]);
    \$settings = \$stmt->fetch(PDO::FETCH_ASSOC);
    
    \$secretaryValue = \$settings['secretary_value'] ?? 10;
    
    if (\$secretaryValue == 5) {
        \$code = 'RET25';  // <-- CHANGE THIS TO 'RET2'
    } else {
        \$code = 'RET30';  // <-- CHANGE THIS TO 'RET3'
    }
}";
echo "</pre>";

echo "<h3>Alternative SQL Fix:</h3>";
echo "<p>If you prefer to keep the code as-is, you can create aliases in the database:</p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
echo "-- Create duplicate entries with ret25/ret30 codes
INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
SELECT 'ret25', prefix, name, color, is_active FROM config_invoice_types WHERE code = 'ret2';

INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
SELECT 'ret30', prefix, name, color, is_active FROM config_invoice_types WHERE code = 'ret3';";
echo "</pre>";

echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3 style='color: #155724; margin-top: 0;'>Recommended Action:</h3>";
echo "<p>Edit <strong>/app/services/UnifiedInvoiceGenerator.php</strong> and change:</p>";
echo "<ul>";
echo "<li>Line ~835: <code>\$code = 'RET25';</code> → <code>\$code = 'RET2';</code></li>";
echo "<li>Line ~837: <code>\$code = 'RET30';</code> → <code>\$code = 'RET3';</code></li>";
echo "</ul>";
echo "<p>This will fix the foreign key constraint error when generating retrocession invoices.</p>";
echo "</div>";