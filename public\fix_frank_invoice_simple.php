<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing <PERSON>'s Invoice #336</h2>";
    
    // Get ret25 type
    $stmt = $db->prepare("SELECT id, prefix FROM config_invoice_types WHERE code = 'ret25'");
    $stmt->execute();
    $ret25Type = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$ret25Type) {
        echo "<p style='color: red;'>Error: ret25 type not found!</p>";
        echo "<p><a href='/fit/public/create_ret_types_simple.php'>Create RET types first</a></p>";
        exit;
    }
    
    // Get <PERSON>'s client ID
    $stmt = $db->prepare("
        SELECT c.id as client_id, c.name
        FROM clients c
        JOIN users u ON u.id = c.user_id
        WHERE u.id = 1
    ");
    $stmt->execute();
    $frankClient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$frankClient) {
        echo "<p style='color: red;'>Error: Frank's client record not found!</p>";
        exit;
    }
    
    echo "<p>Found Frank's client: {$frankClient['name']} (ID: {$frankClient['client_id']})</p>";
    echo "<p>Will use ret25 type (ID: {$ret25Type['id']}, Prefix: {$ret25Type['prefix']})</p>";
    
    // Update the invoice
    $stmt = $db->prepare("
        UPDATE invoices 
        SET type_id = :type_id,
            client_id = :client_id,
            invoice_number = :invoice_number
        WHERE id = 336
    ");
    
    $newInvoiceNumber = 'FAC-RET25-2025-0200';
    
    $stmt->execute([
        ':type_id' => $ret25Type['id'],
        ':client_id' => $frankClient['client_id'],
        ':invoice_number' => $newInvoiceNumber
    ]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Successfully updated invoice #336</p>";
        echo "<ul>";
        echo "<li>Type ID: {$ret25Type['id']} (ret25)</li>";
        echo "<li>Client ID: {$frankClient['client_id']}</li>";
        echo "<li>Invoice Number: {$newInvoiceNumber}</li>";
        echo "</ul>";
        
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>Frank's invoice has been updated to use the correct FAC-RET25 prefix.</p>";
        echo "<p><a href='/fit/public/invoices/336' style='color: #155724; font-weight: bold;'>→ View the updated invoice</a></p>";
        echo "<p><a href='/fit/public/invoices' style='color: #155724; font-weight: bold;'>→ Return to invoice list</a></p>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>No changes made - invoice may already be updated.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}