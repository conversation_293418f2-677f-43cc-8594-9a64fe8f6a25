<?php
// Web version of duplicate invoice lines checker
require_once __DIR__ . '/../vendor/autoload.php';

try {
    // Load environment
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
    $dotenv->load();
    
    // Database connection - always use .env values
    $host = $_ENV['DB_HOST'];
    $dbname = $_ENV['DB_DATABASE'];
    $username = $_ENV['DB_USERNAME'];
    $password = $_ENV['DB_PASSWORD'];
    
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Check Duplicate Invoice Lines</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2 { color: #333; }
        .success { background: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; margin: 10px 0; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th { background: #007bff; color: white; padding: 10px; text-align: left; }
        td { padding: 8px; border-bottom: 1px solid #ddd; }
        tr:hover { background: #f5f5f5; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-box { background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; font-size: 14px; }
    </style>
</head>
<body>
<div class="container">
    <h1>Check Duplicate Invoice Lines</h1>
    
    <?php
    // 1. Overall statistics
    $stmt = $db->query("SELECT COUNT(*) as total FROM invoices");
    $totalInvoices = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM invoice_lines");
    $totalLines = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $avgLines = $totalInvoices > 0 ? round($totalLines / $totalInvoices, 2) : 0;
    ?>
    
    <div class="stats">
        <div class="stat-box">
            <div class="stat-value"><?php echo number_format($totalInvoices); ?></div>
            <div class="stat-label">Total Invoices</div>
        </div>
        <div class="stat-box">
            <div class="stat-value"><?php echo number_format($totalLines); ?></div>
            <div class="stat-label">Total Invoice Lines</div>
        </div>
        <div class="stat-box">
            <div class="stat-value"><?php echo $avgLines; ?></div>
            <div class="stat-label">Avg Lines per Invoice</div>
        </div>
    </div>
    
    <?php
    // 2. Check for exact duplicates
    $stmt = $db->prepare("
        SELECT 
            invoice_id, 
            description, 
            quantity, 
            unit_price, 
            vat_rate,
            COUNT(*) as count
        FROM invoice_lines
        GROUP BY invoice_id, description, quantity, unit_price, vat_rate
        HAVING count > 1
        ORDER BY count DESC, invoice_id
        LIMIT 20
    ");
    $stmt->execute();
    $exactDuplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    ?>
    
    <h2>Exact Duplicate Lines</h2>
    <?php if (empty($exactDuplicates)): ?>
        <div class="success">✓ No exact duplicate lines found.</div>
    <?php else: ?>
        <div class="error">⚠️ Found duplicate lines in the database!</div>
        <table>
            <tr>
                <th>Invoice ID</th>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>VAT Rate</th>
                <th>Count</th>
            </tr>
            <?php foreach ($exactDuplicates as $dup): ?>
            <tr>
                <td><?php echo $dup['invoice_id']; ?></td>
                <td><?php echo htmlspecialchars($dup['description']); ?></td>
                <td><?php echo $dup['quantity']; ?></td>
                <td><?php echo number_format($dup['unit_price'], 2); ?>€</td>
                <td><?php echo $dup['vat_rate']; ?>%</td>
                <td style="background: #ffcccc; font-weight: bold;"><?php echo $dup['count']; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    <?php endif; ?>
    
    <?php
    // 3. Summary of affected invoices
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.status,
            i.created_at,
            COUNT(il.id) as total_lines,
            COUNT(DISTINCT CONCAT(il.description, il.quantity, il.unit_price, il.vat_rate)) as unique_lines
        FROM invoices i
        JOIN invoice_lines il ON i.id = il.invoice_id
        GROUP BY i.id
        HAVING total_lines > unique_lines
        ORDER BY i.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $affectedInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    ?>
    
    <h2>Invoices with Duplicate Lines</h2>
    <?php if (empty($affectedInvoices)): ?>
        <div class="success">✓ No invoices with duplicate lines found.</div>
    <?php else: ?>
        <div class="warning">Found <?php echo count($affectedInvoices); ?> invoices with duplicate lines</div>
        <table>
            <tr>
                <th>Invoice Number</th>
                <th>Status</th>
                <th>Created Date</th>
                <th>Total Lines</th>
                <th>Unique Lines</th>
                <th>Duplicates</th>
                <th>Action</th>
            </tr>
            <?php foreach ($affectedInvoices as $inv): ?>
            <tr>
                <td><?php echo htmlspecialchars($inv['invoice_number']); ?></td>
                <td><?php echo $inv['status']; ?></td>
                <td><?php echo date('Y-m-d', strtotime($inv['created_at'])); ?></td>
                <td><?php echo $inv['total_lines']; ?></td>
                <td><?php echo $inv['unique_lines']; ?></td>
                <td style="background: #ffcccc;"><?php echo $inv['total_lines'] - $inv['unique_lines']; ?></td>
                <td>
                    <a href="?invoice_id=<?php echo $inv['id']; ?>">View Details</a>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
    <?php endif; ?>
    
    <?php
    // 4. Check for orphaned lines
    $stmt = $db->prepare("
        SELECT COUNT(*) as orphaned_count
        FROM invoice_lines il
        LEFT JOIN invoices i ON il.invoice_id = i.id
        WHERE i.id IS NULL
    ");
    $stmt->execute();
    $orphaned = $stmt->fetch(PDO::FETCH_ASSOC);
    ?>
    
    <h2>Orphaned Invoice Lines</h2>
    <?php if ($orphaned['orphaned_count'] == 0): ?>
        <div class="success">✓ No orphaned invoice lines found.</div>
    <?php else: ?>
        <div class="error">⚠️ Found <?php echo $orphaned['orphaned_count']; ?> orphaned invoice lines!</div>
    <?php endif; ?>
    
    <?php
    // 5. Show details for specific invoice if requested
    if (isset($_GET['invoice_id'])) {
        $invoiceId = intval($_GET['invoice_id']);
        
        $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "<h2>Invoice Details: " . htmlspecialchars($invoice['invoice_number']) . "</h2>";
            
            $stmt = $db->prepare("
                SELECT * FROM invoice_lines 
                WHERE invoice_id = ? 
                ORDER BY sort_order, id
            ");
            $stmt->execute([$invoiceId]);
            $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT</th><th>Total</th><th>Sort Order</th></tr>";
            
            foreach ($lines as $line) {
                $total = $line['quantity'] * $line['unit_price'] * (1 + $line['vat_rate'] / 100);
                echo "<tr>";
                echo "<td>" . $line['id'] . "</td>";
                echo "<td>" . htmlspecialchars($line['description']) . "</td>";
                echo "<td>" . $line['quantity'] . "</td>";
                echo "<td>" . number_format($line['unit_price'], 2) . "€</td>";
                echo "<td>" . $line['vat_rate'] . "%</td>";
                echo "<td>" . number_format($total, 2) . "€</td>";
                echo "<td>" . ($line['sort_order'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo '<div class="info"><a href="' . $_SERVER['PHP_SELF'] . '">← Back to summary</a></div>';
        }
    }
    ?>
    
    <div class="info">
        <strong>Note:</strong> This tool checks for duplicate invoice lines based on description, quantity, unit price, and VAT rate. 
        Duplicates may be intentional (e.g., same service provided multiple times) or accidental.
    </div>
</div>
</body>
</html>