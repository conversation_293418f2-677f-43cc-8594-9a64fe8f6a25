<?php
// Debug user groups for user 14

require_once '../app/config/bootstrap.php';

$userId = 14;

// Get user with groups
$user = \App\Models\User::getWithGroups($userId);

echo "<h1>Debug User Groups for User ID: $userId</h1>";

echo "<h2>User Data:</h2>";
echo "<pre>";
print_r($user);
echo "</pre>";

echo "<h2>Groups Array:</h2>";
if (isset($user['groups'])) {
    echo "<pre>";
    print_r($user['groups']);
    echo "</pre>";
    
    echo "<h3>Group IDs:</h3>";
    $groupIds = array_column($user['groups'], 'id');
    echo "<pre>";
    print_r($groupIds);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>No groups found in user data</p>";
}

// Check what the controller would pass
echo "<h2>What Controller Passes:</h2>";
echo "<p>userGroupIds = array_column(\$user['groups'], 'id');</p>";
echo "<p>Result: ";
if (isset($user['groups'])) {
    $userGroupIds = array_column($user['groups'], 'id');
    echo "<pre>" . print_r($userGroupIds, true) . "</pre>";
} else {
    echo "Empty array (no groups)";
}
echo "</p>";

// Test the Twig condition
echo "<h2>Testing Twig 'in' operator:</h2>";
if (isset($userGroupIds) && is_array($userGroupIds)) {
    foreach ([1, 24, 4] as $testId) {
        $inArray = in_array($testId, $userGroupIds);
        echo "<p>Is $testId in userGroupIds? " . ($inArray ? "YES" : "NO") . "</p>";
    }
}

// Direct database check
echo "<h2>Direct Database Check:</h2>";
$db = \Flight::db();
$stmt = $db->prepare("
    SELECT ugm.*, ug.name as group_name 
    FROM user_group_members ugm
    JOIN user_groups ug ON ugm.group_id = ug.id
    WHERE ugm.user_id = ?
");
$stmt->execute([$userId]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<pre>";
print_r($results);
echo "</pre>";
?>