# Fit360 AdminDesk - Flight PHP Project

# Dependencies
/vendor/
composer.lock

# Environment files
.env
.env.local
.env.production
.env.staging
.env.testing

# IDE files
.vscode/
.idea/
.claude/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
bash.exe.stackdump

# Logs and cache
/storage/logs/*
/storage/cache/*
/cache/
*.log

# Development and debug files
/public/debug-*
/public/test-*
/public/check-*
/public/fix-*
/public/apply-*
/public/run-*
/public/setup-*
/public/prepare-*
/public/skip-*
/public/add-*
/public/phpinfo.php
/public/clear-session.php
/public/reset_password.php
/public/send-invoice-test.php
/public/simple_invoice_pdf.php
/public/download_*
/public/view_*
/public/show_*
/public/invoice-*
/public/CLIENT_*
/public/INVOICE_*
/public/QUICK_FIX.md

# JavaScript debug files
/public/js/*-debug.js
/public/js/*-fix*.js
/public/js/client-form-*.js
/public/js/invoice-*-fix.js
/public/js/fix-*.js
/public/console-*.js

# Test files in public directory
/public/test-*.php
/public/test-*.html

# Development PHP files
*.php.backup
*-backup.php
*-debug.php
*-test.php
*-fix.php
debug-*.php
test-*.php
check-*.php
fix-*.php
apply-*.php
run-*.php
setup-*.php
prepare-*.php
skip-*.php
add-*.php
migrate.php
sync-*.php
update-*.php

# Development markdown files
*-fixes-report.md
*-fix-summary.md
*CLEANUP*.md
*ERRORS*.md
*INTEGRATION*.md
*TRANSLATION*.md
*USERNAME*.md
*USER_*.md
*SESSION_SAVE*.md

# Backup and temporary files
*.bak
*.backup
*.tmp
*.temp
*~
*.twig.backup*

# Database files
*.sql
/database/check_*.sql

# Database backups (exclude from git but keep structure)
/storage/backups/*.sql
/storage/backups/*.gz
!/storage/backups/.gitkeep
/database/migrations/000_*.sql
/database/migrations/050_*.sql
/database/migrations/051_*.sql
/database/migrations/052_*.sql
/database/migrations/053_*.sql
/database/migrations/054_*.sql
/database/migrations/055_*.sql
/database/migrations/056_*.sql
/database/migrations/057_*.sql
/database/migrations/060_*.sql
/database/migrations/061_*.sql
/database/migrations/062_*.sql
/database/migrations/063_*.sql
/database/migrations/066_*.sql
/database/migrations/067_*.sql
/database/migrations/068_*.sql
/database/migrations/069_*.sql
/database/migrations/070_*.sql
/database/migrations/optimize_*.sql

# Test files
/tests/add-*.php
/tests/bootstrap-test.php
/tests/check-*.php
/tests/setup-*.php
/tests/test-runner.php
/tests/CategoryCrudTest.php
/tests/InvoiceCalculationTest.php
/tests/MoneyHelperTest.php
/tests/RetrocessionCalculationTest.php
/tests/run-*.php

# Development views and assets
/app/views/*-backup.twig
/app/views/*-improved.twig
/app/views/*-optimized.twig
/app/views/dashboard-fresh.twig
/public/assets/optimize-*.php

# Development models and controllers
/app/Models/InvoiceOptimized.php
/app/Models/SalesInvoice.php
/app/Models/SalesInvoiceLine.php
/app/Models/SalesPayment.php
/app/Models/UserRetrocessionSetting.php
/app/Models/DocumentTypeColumnConfig.php
/app/Core/OptimizedController.php
/app/controllers/*_optimized.php
/app/controllers/PerformanceController.php
/app/controllers/PosController.php
/app/services/*Improved.php
/app/services/ConfigCacheService.php

# Development helpers
/app/helpers/column_helper.php
/app/helpers/MoneyHelper.php

# Development modules and config
/app/config/modules/
/app/modules/products/

# Development language files
/app/lang/*/pos.php
/app/lang/*/products.php

# Development views directories
/app/views/pos/
/app/views/config/invoice-items-columns-modern.twig
/app/views/config/table-columns-enhanced.twig
/app/views/users/financial-obligations-modern.twig
/app/views/users/group-members-modern.twig

# Node.js (if using)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# User uploads
/public/uploads/

# Test coverage
/coverage/
/.phpunit.result.cache
