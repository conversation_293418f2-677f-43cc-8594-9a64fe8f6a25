<?php
/**
 * Check Invoice Creation Issues
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

echo "<h1>Check Invoice Creation</h1>";
echo "<pre>";

$db = Flight::db();

// Check last few invoices
echo "Recent invoices:\n";
$stmt = $db->query("
    SELECT 
        i.id,
        i.invoice_number,
        i.status,
        i.total,
        i.created_at,
        CASE 
            WHEN i.client_id IS NOT NULL THEN CONCAT('Client: ', c.name)
            WHEN i.user_id IS NOT NULL THEN CONCAT('User: ', u.first_name, ' ', u.last_name)
            ELSE 'No billable'
        END as billable
    FROM invoices i
    LEFT JOIN clients c ON i.client_id = c.id
    LEFT JOIN users u ON i.user_id = u.id
    ORDER BY i.id DESC
    LIMIT 10
");

$invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($invoices as $inv) {
    echo "\n";
    echo "ID: {$inv['id']}\n";
    echo "Number: {$inv['invoice_number']}\n";
    echo "Status: {$inv['status']}\n";
    echo "Total: {$inv['total']}\n";
    echo "Billable: {$inv['billable']}\n";
    echo "Created: {$inv['created_at']}\n";
}

// Check for validation rules
echo "\n\nChecking required fields configuration:\n";

// Check if payment_term_id is required
$stmt = $db->query("DESCRIBE invoices payment_term_id");
$col = $stmt->fetch(PDO::FETCH_ASSOC);
echo "payment_term_id nullable: " . ($col['Null'] === 'YES' ? 'Yes' : 'No') . "\n";

// Check payment terms
echo "\nAvailable payment terms:\n";
$stmt = $db->query("SELECT id, name, days FROM config_payment_terms WHERE is_active = 1");
$terms = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($terms as $term) {
    echo "  ID: {$term['id']} - {$term['name']} ({$term['days']} days)\n";
}

// Check for any recent errors in PHP error log
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    echo "\n\nRecent PHP errors:\n";
    $errors = file($errorLog);
    $recentErrors = array_slice($errors, -10);
    foreach ($recentErrors as $error) {
        if (stripos($error, 'invoice') !== false || stripos($error, 'validation') !== false) {
            echo $error;
        }
    }
}

// Test invoice creation
if (isset($_GET['test'])) {
    echo "\n\nTesting invoice creation...\n";
    
    // Get a test user
    $stmt = $db->query("SELECT id, first_name, last_name FROM users WHERE is_active = 1 LIMIT 1");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get payment term
    $stmt = $db->query("SELECT id FROM config_payment_terms WHERE is_active = 1 LIMIT 1");
    $paymentTerm = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && $paymentTerm) {
        $invoice = new Invoice();
        
        $data = [
            'invoice_type_id' => 1, // Standard invoice
            'document_type_id' => 1,
            'user_id' => $user['id'],
            'invoice_number' => 'TEST-' . date('Y-m-d-His'),
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'payment_term_id' => $paymentTerm['id'],
            'status' => 'draft',
            'subtotal' => 100,
            'total' => 100,
            'items' => [
                [
                    'description' => 'Test item',
                    'quantity' => 1,
                    'unit_price' => 100,
                    'total' => 100
                ]
            ]
        ];
        
        try {
            $result = $invoice->create($data);
            if ($result) {
                echo "✓ Test invoice created successfully! ID: " . $invoice->id . "\n";
                
                // Delete it
                $db->exec("DELETE FROM invoices WHERE id = " . $invoice->id);
                echo "✓ Test invoice deleted\n";
            } else {
                echo "✗ Failed to create test invoice\n";
                echo "Validation errors: " . json_encode($invoice->getErrors()) . "\n";
            }
        } catch (Exception $e) {
            echo "✗ Exception: " . $e->getMessage() . "\n";
            echo "Trace:\n" . $e->getTraceAsString() . "\n";
        }
    } else {
        echo "Cannot test - no active user or payment term found\n";
    }
} else {
    echo "\n\n<a href='?test=1'>Click here to test invoice creation</a>";
}

echo "</pre>";
echo "<hr>";
echo '<p><a href="/fit/public/invoices/create">Back to Create Invoice</a></p>';
?>