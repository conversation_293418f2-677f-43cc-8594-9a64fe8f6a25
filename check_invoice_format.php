<?php
// Simple script to check invoice format
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$pdo = new PDO(
    'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
    $_ENV['DB_USERNAME'],
    $_ENV['DB_PASSWORD']
);

// Check invoice 252 lines
echo "Invoice #252 Lines:\n";
$stmt = $pdo->query("SELECT description, quantity, unit_price, vat_rate, line_total FROM invoice_lines WHERE invoice_id = 252 ORDER BY sort_order");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "- {$row['description']} | Qty: {$row['quantity']} | Unit: {$row['unit_price']} | VAT: {$row['vat_rate']}% | Total: {$row['line_total']}\n";
}

echo "\nInvoice #321 (Generated) Lines:\n";
$stmt = $pdo->query("SELECT description, quantity, unit_price, vat_rate, line_total FROM invoice_lines WHERE invoice_id = 321 ORDER BY sort_order");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "- {$row['description']} | Qty: {$row['quantity']} | Unit: {$row['unit_price']} | VAT: {$row['vat_rate']}% | Total: {$row['line_total']}\n";
}

// Check the retrocession amounts
echo "\nRetrocession Data:\n";
$stmt = $pdo->query("
    SELECT r.*, u.first_name, u.last_name 
    FROM retrocession_data_entry r
    JOIN clients c ON r.practitioner_id = c.id
    LEFT JOIN users u ON (c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name)
    WHERE r.invoice_id IN (252, 321)
");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "\nInvoice #{$row['invoice_id']} ({$row['first_name']} {$row['last_name']}):\n";
    echo "- CNS Base: {$row['cns_amount']} | Patient Base: {$row['patient_amount']}\n";
}