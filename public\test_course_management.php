<?php
// Test course management functionality

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Test Course Management</h1>";

// 1. Get coaches from group 24
echo "<h2>1. Coaches in Group 24:</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 24
    AND u.is_active = 1
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Courses</th><th>Edit Link</th></tr>";
foreach ($coaches as $coach) {
    // Get courses for this coach
    $courseStmt = $db->prepare("
        SELECT course_name, hourly_rate, vat_rate, is_active
        FROM user_courses 
        WHERE user_id = :user_id
        ORDER BY display_order, course_name
    ");
    $courseStmt->execute(['user_id' => $coach['id']]);
    $courses = $courseStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $courseList = [];
    foreach ($courses as $course) {
        $status = $course['is_active'] ? '✅' : '❌';
        $courseList[] = sprintf(
            "%s (€%.2f TTC, %s%%VAT) %s",
            $course['course_name'],
            $course['hourly_rate'],
            $course['vat_rate'],
            $status
        );
    }
    
    echo "<tr>";
    echo "<td>{$coach['id']}</td>";
    echo "<td>{$coach['name']}</td>";
    echo "<td>{$coach['username']}</td>";
    echo "<td>" . (empty($courseList) ? '<em style="color: red;">No courses</em>' : implode('<br>', $courseList)) . "</td>";
    echo "<td><a href='/fit/public/users/{$coach['id']}/edit' target='_blank'>Edit User</a></td>";
    echo "</tr>";
}
echo "</table>";

// 2. Test adding a course programmatically
echo "<h2>2. Test Course CRUD Operations:</h2>";

// Pick the first coach without courses for testing
$testCoachId = null;
foreach ($coaches as $coach) {
    $stmt = $db->prepare("SELECT COUNT(*) FROM user_courses WHERE user_id = ?");
    $stmt->execute([$coach['id']]);
    if ($stmt->fetchColumn() == 0) {
        $testCoachId = $coach['id'];
        echo "<p>Using coach: {$coach['name']} (ID: {$coach['id']}) for testing</p>";
        break;
    }
}

if ($testCoachId) {
    // Add a test course
    echo "<h3>Adding test course...</h3>";
    try {
        $stmt = $db->prepare("
            INSERT INTO user_courses (user_id, course_name, hourly_rate, vat_rate, is_active, display_order, created_at, updated_at)
            VALUES (:user_id, :course_name, :hourly_rate, :vat_rate, :is_active, :display_order, NOW(), NOW())
        ");
        $stmt->execute([
            'user_id' => $testCoachId,
            'course_name' => 'Test Course - Yoga',
            'hourly_rate' => 75.00,
            'vat_rate' => 16.00,
            'is_active' => 1,
            'display_order' => 1
        ]);
        echo "<p style='color: green;'>✅ Course added successfully!</p>";
        
        // Verify it was added
        $stmt = $db->prepare("SELECT * FROM user_courses WHERE user_id = ? AND course_name = 'Test Course - Yoga'");
        $stmt->execute([$testCoachId]);
        $course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<pre>";
        print_r($course);
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error adding course: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ All coaches already have courses. No test performed.</p>";
}

// 3. List available routes
echo "<h2>3. Available Course Management Routes:</h2>";
echo "<ul>";
echo "<li>GET /users/{id}/courses - Get all courses for a user</li>";
echo "<li>POST /users/{id}/courses - Add a new course</li>";
echo "<li>POST /users/{userId}/courses/{courseId} - Update a course</li>";
echo "<li>DELETE /users/{userId}/courses/{courseId} - Delete a course</li>";
echo "<li>POST /users/{id}/courses/order - Update course display order</li>";
echo "</ul>";

// 4. Direct link to edit a coach
if (!empty($coaches)) {
    echo "<h2>4. Quick Links:</h2>";
    echo "<p>Edit a coach to manage their courses:</p>";
    echo "<ul>";
    foreach (array_slice($coaches, 0, 3) as $coach) {
        echo "<li><a href='/fit/public/users/{$coach['id']}/edit' target='_blank'>{$coach['name']}</a></li>";
    }
    echo "</ul>";
}

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #e9e9e9; font-weight: bold; }
td, th { padding: 5px 8px; border: 1px solid #ddd; }
h2 { color: #333; margin-top: 30px; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
</style>

<hr>
<p><a href='/fit/public/'>Back to Home</a></p>