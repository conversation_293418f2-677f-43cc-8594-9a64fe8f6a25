<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Auto-fill Test</h2>";
    
    // Check LOY invoice type configuration
    echo "<h3>1. LOY Invoice Type Configuration:</h3>";
    $sql = "SELECT * FROM invoice_types WHERE prefix = 'LOY' OR code = 'LOY'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $loyType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($loyType) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Property</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$loyType['id']}</td></tr>";
        echo "<tr><td>Name</td><td>{$loyType['name']}</td></tr>";
        echo "<tr><td>Prefix</td><td><strong>{$loyType['prefix']}</strong></td></tr>";
        echo "<tr><td>Code</td><td>{$loyType['code']}</td></tr>";
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ LOY invoice type not found!</p>";
    }
    
    // Check latest LOY invoice number
    echo "<h3>2. Latest LOY Invoice Number:</h3>";
    $sql = "SELECT invoice_number FROM invoices 
            WHERE invoice_number LIKE '%LOY%' 
            ORDER BY id DESC LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $latestInvoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($latestInvoice) {
        echo "<p>Latest LOY invoice: <strong>{$latestInvoice['invoice_number']}</strong></p>";
        
        // Extract number and predict next
        if (preg_match('/(\d{4})$/', $latestInvoice['invoice_number'], $matches)) {
            $currentNum = intval($matches[1]);
            $nextNum = str_pad($currentNum + 1, 4, '0', STR_PAD_LEFT);
            echo "<p>Next expected number: <strong>FAC-LOY-2025-{$nextNum}</strong></p>";
        }
    } else {
        echo "<p>No LOY invoices found. First number would be: <strong>FAC-LOY-2025-0001</strong></p>";
    }
    
    // Check invoice number sequence
    echo "<h3>3. LOY Invoice Number Sequence:</h3>";
    $sql = "SELECT * FROM invoice_number_sequences 
            WHERE prefix = 'LOY' AND year = YEAR(CURDATE())";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sequence) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Current Number</th><th>Pattern</th><th>Last Updated</th></tr>";
        echo "<tr>";
        echo "<td><strong>{$sequence['current_number']}</strong></td>";
        echo "<td>{$sequence['number_pattern']}</td>";
        echo "<td>{$sequence['updated_at']}</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No sequence found for LOY in current year</p>";
    }
    
    // Show expected behavior
    echo "<h3>4. Expected Auto-fill Behavior:</h3>";
    
    $currentMonth = date('n');
    $currentYear = date('Y');
    $previousMonth = $currentMonth - 1;
    $previousYear = $currentYear;
    
    if ($previousMonth < 1) {
        $previousMonth = 12;
        $previousYear--;
    }
    
    $frenchMonths = [
        1 => 'JANVIER', 2 => 'FÉVRIER', 3 => 'MARS', 4 => 'AVRIL',
        5 => 'MAI', 6 => 'JUIN', 7 => 'JUILLET', 8 => 'AOÛT',
        9 => 'SEPTEMBRE', 10 => 'OCTOBRE', 11 => 'NOVEMBRE', 12 => 'DÉCEMBRE'
    ];
    
    $expectedPeriod = $frenchMonths[$previousMonth] . ' ' . $previousYear;
    
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<p>When LOY invoice type is selected:</p>";
    echo "<ul>";
    echo "<li><strong>Subject/Objet:</strong> LOYER + CHARGES</li>";
    echo "<li><strong>Period/Période:</strong> {$expectedPeriod}</li>";
    echo "<li><strong>Invoice Number:</strong> Continues sequence (next available number)</li>";
    echo "<li><strong>User Selection:</strong> Only Medical Staff and Managers group members</li>";
    echo "<li><strong>Price Type:</strong> TTC (Tax Included)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>5. Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='/fit/public/invoices/create'>Create Invoice</a></li>";
    echo "<li>Select <strong>LOY</strong> invoice type from dropdown</li>";
    echo "<li>Verify that:</li>";
    echo "<ul>";
    echo "<li>Subject field shows: <strong>LOYER + CHARGES</strong></li>";
    echo "<li>Period field shows: <strong>{$expectedPeriod}</strong></li>";
    echo "<li>Invoice number continues the sequence</li>";
    echo "<li>User dropdown only shows Medical/Managers members</li>";
    echo "</ul>";
    echo "</ol>";
    
    echo "<h3>6. Implementation Status:</h3>";
    echo "<div style='background-color: #e8f5e9; padding: 10px; border-radius: 5px;'>";
    echo "<p><strong>✅ Completed Updates:</strong></p>";
    echo "<ul>";
    echo "<li>updateRentalPeriod() - LOY now uses previous month</li>";
    echo "<li>handleInvoiceTypeChange() - LOY type code detection added</li>";
    echo "<li>User filtering - Only Medical/Managers groups shown</li>";
    echo "<li>TTC calculation - Already configured for LOY</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>