<?php
/**
 * Check email sending status for any invoice
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$invoiceNumber = $_GET['invoice'] ?? '';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Check Invoice Email Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .search-form { background: #e9ecef; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .search-form input[type="text"] { padding: 8px; width: 300px; font-size: 16px; }
        .search-form input[type="submit"] { padding: 8px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .search-form input[type="submit"]:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Check Invoice Email Status</h1>
    
    <div class="search-form">
        <form method="get">
            <label for="invoice">Invoice Number:</label>
            <input type="text" id="invoice" name="invoice" value="<?php echo htmlspecialchars($invoiceNumber); ?>" placeholder="e.g., FAC-DIV-2025-0190">
            <input type="submit" value="Check Status">
        </form>
    </div>
    
    <?php
    if ($invoiceNumber) {
        try {
            $db = Flight::db();
            
            // 1. Find the invoice
            echo "<h2>1. Invoice Details</h2>";
            $stmt = $db->prepare("
                SELECT 
                    i.*,
                    c.name as client_name,
                    c.email as client_email,
                    u.first_name,
                    u.last_name,
                    u.email as user_email,
                    u.billing_email as user_billing_email,
                    it.name as invoice_type_name
                FROM invoices i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN users u ON i.user_id = u.id
                LEFT JOIN invoice_types it ON i.invoice_type = it.code
                WHERE i.invoice_number = :invoice_number
            ");
            $stmt->execute([':invoice_number' => $invoiceNumber]);
            $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$invoice) {
                echo "<p class='error'>❌ Invoice '{$invoiceNumber}' not found!</p>";
                
                // Show recent invoices
                echo "<h3>Recent Invoices:</h3>";
                $stmt = $db->query("
                    SELECT id, invoice_number, invoice_type, status, total, created_at
                    FROM invoices 
                    ORDER BY id DESC 
                    LIMIT 20
                ");
                $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if ($recent) {
                    echo "<table>";
                    echo "<tr><th>ID</th><th>Invoice Number</th><th>Type</th><th>Status</th><th>Total</th><th>Created</th></tr>";
                    foreach ($recent as $inv) {
                        echo "<tr>";
                        echo "<td>{$inv['id']}</td>";
                        echo "<td><a href='?invoice=" . urlencode($inv['invoice_number']) . "'>{$inv['invoice_number']}</a></td>";
                        echo "<td>{$inv['invoice_type']}</td>";
                        echo "<td>{$inv['status']}</td>";
                        echo "<td>" . number_format($inv['total'] ?? 0, 2) . " €</td>";
                        echo "<td>{$inv['created_at']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                // Display invoice details
                echo "<div class='info'>";
                echo "<p class='success'>✓ Invoice found!</p>";
                echo "<table>";
                echo "<tr><th>Field</th><th>Value</th></tr>";
                echo "<tr><td>ID</td><td>{$invoice['id']}</td></tr>";
                echo "<tr><td>Number</td><td><strong>{$invoice['invoice_number']}</strong></td></tr>";
                echo "<tr><td>Type</td><td>{$invoice['invoice_type']} - {$invoice['invoice_type_name']}</td></tr>";
                echo "<tr><td>Status</td><td><strong>{$invoice['status']}</strong></td></tr>";
                echo "<tr><td>Total</td><td>" . number_format($invoice['total'] ?? 0, 2) . " €</td></tr>";
                echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
                echo "<tr><td>Due Date</td><td>{$invoice['due_date']}</td></tr>";
                echo "<tr><td>Sent At</td><td>" . ($invoice['sent_at'] ?: '<span class="error">Not sent</span>') . "</td></tr>";
                echo "<tr><td>Created</td><td>{$invoice['created_at']}</td></tr>";
                echo "</table>";
                
                echo "<h3>Recipient Information:</h3>";
                if ($invoice['client_id']) {
                    echo "<p>Client: {$invoice['client_name']}<br>";
                    echo "Email: " . ($invoice['client_email'] ?: '<span class="error">No email</span>') . "</p>";
                } elseif ($invoice['user_id']) {
                    echo "<p>User: {$invoice['first_name']} {$invoice['last_name']}<br>";
                    echo "Email: " . ($invoice['user_email'] ?: '<span class="error">No email</span>') . "<br>";
                    echo "Billing Email: " . ($invoice['user_billing_email'] ?: '<span class="warning">Not set</span>') . "</p>";
                } else {
                    echo "<p class='error'>No recipient found!</p>";
                }
                echo "</div>";
                
                // 2. Check email logs
                echo "<h2>2. Email Logs</h2>";
                
                // Check if table exists
                $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
                if (!$stmt->fetch()) {
                    echo "<p class='error'>❌ email_logs table does not exist!</p>";
                    echo "<p>The email logging feature needs to be set up.</p>";
                } else {
                    // Get email logs
                    $stmt = $db->prepare("
                        SELECT * FROM email_logs 
                        WHERE invoice_id = :invoice_id
                        ORDER BY created_at DESC
                    ");
                    $stmt->execute([':invoice_id' => $invoice['id']]);
                    $emailLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (empty($emailLogs)) {
                        echo "<p class='error'>❌ No email logs found for this invoice.</p>";
                        echo "<div class='info'>";
                        echo "<p>This means the invoice has never been emailed (or email logging was not active when it was sent).</p>";
                        echo "</div>";
                    } else {
                        echo "<p class='success'>✓ Found " . count($emailLogs) . " email log(s)</p>";
                        
                        echo "<table>";
                        echo "<tr><th>Date</th><th>To</th><th>Status</th><th>Subject</th><th>Error</th></tr>";
                        foreach ($emailLogs as $log) {
                            $statusClass = $log['status'] === 'sent' ? 'success' : 'error';
                            echo "<tr>";
                            echo "<td>" . date('Y-m-d H:i', strtotime($log['created_at'])) . "</td>";
                            echo "<td>{$log['recipient_email']}</td>";
                            echo "<td class='$statusClass'><strong>{$log['status']}</strong></td>";
                            echo "<td>" . htmlspecialchars(substr($log['subject'], 0, 50)) . "...</td>";
                            echo "<td>" . ($log['error_message'] ? htmlspecialchars(substr($log['error_message'], 0, 50)) . '...' : '-') . "</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                }
                
                // 3. Summary
                echo "<h2>Summary</h2>";
                echo "<div class='info'>";
                if (!empty($emailLogs)) {
                    $successfulSends = array_filter($emailLogs, function($log) { return $log['status'] === 'sent'; });
                    if ($successfulSends) {
                        $lastSuccess = reset($successfulSends);
                        echo "<p class='success'>✅ Invoice WAS SENT successfully</p>";
                        echo "<p>Last sent on: " . date('Y-m-d H:i', strtotime($lastSuccess['sent_at'])) . " to {$lastSuccess['recipient_email']}</p>";
                    } else {
                        echo "<p class='error'>❌ All email attempts FAILED</p>";
                        $lastAttempt = $emailLogs[0];
                        echo "<p>Last attempt: " . date('Y-m-d H:i', strtotime($lastAttempt['created_at'])) . "</p>";
                        echo "<p>Error: " . htmlspecialchars($lastAttempt['error_message']) . "</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠️ No email history found - invoice appears to have NEVER BEEN SENT</p>";
                }
                
                echo "<h3>Actions:</h3>";
                echo "<ul>";
                echo "<li><a href='/fit/public/invoices/{$invoice['id']}'>View Invoice</a></li>";
                echo "<li><a href='/fit/public/invoices/{$invoice['id']}/pdf' target='_blank'>Download PDF</a></li>";
                echo "<li><a href='/fit/public/send_invoice_email.php?id={$invoice['id']}'>Send/Resend Email</a></li>";
                echo "</ul>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    } else {
        // Show some statistics
        try {
            $db = Flight::db();
            
            echo "<h2>Email Statistics</h2>";
            
            // Check if email_logs table exists
            $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
            if ($stmt->fetch()) {
                // Get statistics
                $stats = $db->query("
                    SELECT 
                        COUNT(*) as total_emails,
                        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_count,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
                        MAX(created_at) as last_email_date
                    FROM email_logs
                    WHERE invoice_id IS NOT NULL
                ")->fetch(PDO::FETCH_ASSOC);
                
                echo "<div class='info'>";
                echo "<p><strong>Total Invoice Emails:</strong> {$stats['total_emails']}</p>";
                echo "<p><strong>Successfully Sent:</strong> <span class='success'>{$stats['sent_count']}</span></p>";
                echo "<p><strong>Failed:</strong> <span class='error'>{$stats['failed_count']}</span></p>";
                if ($stats['last_email_date']) {
                    echo "<p><strong>Last Email:</strong> " . date('Y-m-d H:i', strtotime($stats['last_email_date'])) . "</p>";
                }
                echo "</div>";
                
                // Recent email activity
                echo "<h3>Recent Email Activity</h3>";
                $recent = $db->query("
                    SELECT 
                        el.*,
                        i.invoice_number
                    FROM email_logs el
                    JOIN invoices i ON el.invoice_id = i.id
                    ORDER BY el.created_at DESC
                    LIMIT 10
                ")->fetchAll(PDO::FETCH_ASSOC);
                
                if ($recent) {
                    echo "<table>";
                    echo "<tr><th>Date</th><th>Invoice</th><th>To</th><th>Status</th></tr>";
                    foreach ($recent as $log) {
                        $statusClass = $log['status'] === 'sent' ? 'success' : 'error';
                        echo "<tr>";
                        echo "<td>" . date('Y-m-d H:i', strtotime($log['created_at'])) . "</td>";
                        echo "<td><a href='?invoice=" . urlencode($log['invoice_number']) . "'>{$log['invoice_number']}</a></td>";
                        echo "<td>{$log['recipient_email']}</td>";
                        echo "<td class='$statusClass'>{$log['status']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<div class='warning'>";
                echo "<p>Email logging is not set up. Email history is not being tracked.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            // Ignore errors when no invoice is specified
        }
    }
    ?>
    
    <hr>
    <p><a href="/fit/public/">Back to Dashboard</a></p>
</body>
</html>