<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fix June 2025 Retrocession Entry</h2>\n";
    
    // Check the June entry
    $stmt = $pdo->prepare("
        SELECT * FROM retrocession_data_entry 
        WHERE id = 2
    ");
    $stmt->execute();
    $entry = $stmt->fetch();
    
    if ($entry) {
        echo "<h3>June 2025 Entry Details:</h3>\n";
        echo "<table border='1'>\n";
        echo "<tr><th>Field</th><th>Value</th></tr>\n";
        echo "<tr><td>ID</td><td>{$entry['id']}</td></tr>\n";
        echo "<tr><td>Practitioner ID</td><td>{$entry['practitioner_id']}</td></tr>\n";
        echo "<tr><td>Period</td><td>{$entry['period_month']}/{$entry['period_year']}</td></tr>\n";
        echo "<tr><td>CNS Amount</td><td>{$entry['cns_amount']}€</td></tr>\n";
        echo "<tr><td>Patient Amount</td><td>{$entry['patient_amount']}€</td></tr>\n";
        echo "<tr><td>Status</td><td>{$entry['status']}</td></tr>\n";
        echo "<tr><td>Invoice ID</td><td>" . ($entry['invoice_id'] ?: 'NULL') . "</td></tr>\n";
        echo "</table>\n";
        
        echo "\n<p>This entry is marked as 'invoiced' but has no invoice_id. This needs to be fixed.</p>\n";
        
        echo "<h3>Options:</h3>\n";
        echo "<p><a href='?action=reset_status'>Reset status to 'confirmed'</a> - This will allow invoice generation</p>\n";
        echo "<p><a href='?action=delete_entry'>Delete this entry</a> - Remove it completely</p>\n";
    }
    
    // Handle actions
    if (isset($_GET['action'])) {
        if ($_GET['action'] == 'reset_status') {
            $pdo->exec("UPDATE retrocession_data_entry SET status = 'confirmed', invoice_id = NULL WHERE id = 2");
            echo "\n<p style='color: green;'>✓ Reset status to 'confirmed'</p>\n";
        } elseif ($_GET['action'] == 'delete_entry') {
            $pdo->exec("DELETE FROM retrocession_data_entry WHERE id = 2");
            echo "\n<p style='color: green;'>✓ Deleted the entry</p>\n";
        }
        echo "<p><a href='fix_june_retrocession.php'>Refresh</a></p>\n";
    }
    
    echo "\n<hr>\n";
    echo "<p><strong>Important:</strong> The retrocession system works as follows:</p>\n";
    echo "<ul>\n";
    echo "<li>To generate a <strong>July 2025</strong> retrocession invoice, you need <strong>June 2025</strong> data</li>\n";
    echo "<li>The June data should be in <code>user_monthly_retrocession_amounts</code> table</li>\n";
    echo "<li>The system will create an entry in <code>retrocession_data_entry</code> when generating the invoice</li>\n";
    echo "</ul>\n";
    
    echo "\n<p><strong>Next Steps:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Fix or delete the June retrocession entry above</li>\n";
    echo "<li>Make sure June 2025 amounts exist in user_monthly_retrocession_amounts</li>\n";
    echo "<li><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation'>Return to Bulk Generation</a></li>\n";
    echo "<li>Generate July 2025 invoice (which will use June 2025 data)</li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}