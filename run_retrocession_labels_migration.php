<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Running Retrocession Labels Migration</h2>";
    
    // Check if columns already exist
    $checkSql = "SHOW COLUMNS FROM user_retrocession_settings LIKE 'cns_label'";
    $checkStmt = $db->prepare($checkSql);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() > 0) {
        echo "<p style='color: orange;'>Column 'cns_label' already exists. Migration may have already been run.</p>";
        echo "<p>Do you want to proceed anyway? This might cause errors if columns already exist.</p>";
        echo "<p><a href='?force=1'>Yes, proceed anyway</a> | <a href='/fit/public/'>No, go back</a></p>";
        
        if (!isset($_GET['force'])) {
            exit;
        }
    }
    
    echo "<h3>Adding custom label columns...</h3>";
    
    try {
        // Add columns one by one to handle partial failures
        $columns = [
            'cns_label' => "ALTER TABLE user_retrocession_settings ADD COLUMN cns_label VARCHAR(255) DEFAULT 'RÉTROCESSION CNS' AFTER cns_value",
            'patient_label' => "ALTER TABLE user_retrocession_settings ADD COLUMN patient_label VARCHAR(255) DEFAULT 'RÉTROCESSION PATIENTS' AFTER patient_value",
            'secretary_label' => "ALTER TABLE user_retrocession_settings ADD COLUMN secretary_label VARCHAR(255) DEFAULT 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL' AFTER secretary_value"
        ];
        
        foreach ($columns as $column => $sql) {
            try {
                $db->exec($sql);
                echo "<p style='color: green;'>✓ Added column: $column</p>";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate column') !== false) {
                    echo "<p style='color: orange;'>⚠ Column $column already exists</p>";
                } else {
                    throw $e;
                }
            }
        }
        
        // Update existing records with default values
        echo "<h3>Updating existing records with default values...</h3>";
        $updateSql = "UPDATE user_retrocession_settings 
                     SET cns_label = COALESCE(cns_label, 'RÉTROCESSION CNS'),
                         patient_label = COALESCE(patient_label, 'RÉTROCESSION PATIENTS'),
                         secretary_label = COALESCE(secretary_label, 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL')
                     WHERE cns_label IS NULL OR patient_label IS NULL OR secretary_label IS NULL";
        
        $updateStmt = $db->prepare($updateSql);
        $updateStmt->execute();
        $updatedCount = $updateStmt->rowCount();
        
        echo "<p>Updated $updatedCount record(s) with default labels</p>";
        
        // Special update for Rémi Heine
        echo "<h3>Applying special label for Rémi Heine...</h3>";
        $specialSql = "UPDATE user_retrocession_settings urs
                      JOIN users u ON urs.user_id = u.id
                      SET urs.cns_label = 'RÉTROCESSION'
                      WHERE u.first_name = 'Rémi' AND u.last_name = 'Heine'";
        
        $specialStmt = $db->prepare($specialSql);
        $specialStmt->execute();
        $specialCount = $specialStmt->rowCount();
        
        if ($specialCount > 0) {
            echo "<p style='color: green;'>✓ Updated $specialCount retrocession setting(s) for Rémi Heine</p>";
        } else {
            echo "<p>No retrocession settings found for Rémi Heine (this is normal if not set up yet)</p>";
        }
        
        echo "<h3 style='color: green;'>Migration completed successfully!</h3>";
        
        // Show current settings
        echo "<h3>Current Retrocession Settings with Labels:</h3>";
        $listSql = "SELECT urs.*, u.first_name, u.last_name 
                   FROM user_retrocession_settings urs
                   JOIN users u ON urs.user_id = u.id
                   WHERE urs.is_active = 1
                   ORDER BY u.last_name, u.first_name";
        
        $listStmt = $db->prepare($listSql);
        $listStmt->execute();
        $settings = $listStmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($settings)) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>User</th><th>CNS Label</th><th>Patient Label</th><th>Secretary Label</th></tr>";
            foreach ($settings as $setting) {
                echo "<tr>";
                echo "<td>{$setting['first_name']} {$setting['last_name']}</td>";
                echo "<td>{$setting['cns_label']}</td>";
                echo "<td>{$setting['patient_label']}</td>";
                echo "<td>{$setting['secretary_label']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No active retrocession settings found</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Migration error: " . $e->getMessage() . "</p>";
    }
    
    echo "<p><a href='/fit/public/users'>Go to Users Management</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database connection error: " . $e->getMessage() . "</p>";
}
?>