<?php
// Simple Invoice Columns Check - No Bootstrap Required
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value, '"\'');
        $_ENV[$name] = $value;
    }
}

// Database configuration from .env
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

// Initialize output
header('Content-Type: text/html; charset=UTF-8');

echo "<h1>Invoice Table Structure Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin-top: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .highlight { background-color: #ffffcc; }
    .error { color: red; }
    .success { color: green; }
    pre { background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>";

try {
    // Connect to database
    $dsn = "mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4";
    $db = new PDO($dsn, $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='success'>✓ Connected to database successfully</p>";
    
    // Check table columns
    echo "<h2>Invoice Table Columns</h2>";
    $stmt = $db->prepare("SHOW COLUMNS FROM invoices");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $patientFields = [];
    $billableFields = [];
    $relationFields = [];
    
    foreach ($columns as $column) {
        $highlight = '';
        $fieldName = $column['Field'];
        
        if (strpos($fieldName, 'patient') !== false) {
            $highlight = 'highlight';
            $patientFields[] = $fieldName;
        }
        if (strpos($fieldName, 'billable') !== false) {
            $highlight = 'highlight';
            $billableFields[] = $fieldName;
        }
        if (in_array($fieldName, ['client_id', 'user_id'])) {
            $highlight = 'highlight';
            $relationFields[] = $fieldName;
        }
        
        echo "<tr class='$highlight'>";
        echo "<td>$fieldName</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Summary
    echo "<h2>Analysis Summary</h2>";
    echo "<ul>";
    echo "<li>Total columns: <strong>" . count($columns) . "</strong></li>";
    echo "<li>Patient-related fields: " . (empty($patientFields) ? "<span class='error'>None found</span>" : "<span class='success'>" . implode(', ', $patientFields) . "</span>") . "</li>";
    echo "<li>Billable-related fields: " . (empty($billableFields) ? "<span class='error'>None found</span>" : "<span class='success'>" . implode(', ', $billableFields) . "</span>") . "</li>";
    echo "<li>Relation fields: " . (empty($relationFields) ? "<span class='error'>None found</span>" : "<span class='success'>" . implode(', ', $relationFields) . "</span>") . "</li>";
    echo "</ul>";
    
    // Check invoice 246
    echo "<h2>Invoice #246 Data</h2>";
    $stmt = $db->prepare("SELECT * FROM invoices WHERE id = 246");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<p class='success'>✓ Invoice found</p>";
        echo "<table>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        
        // Show key fields
        $keyFields = ['id', 'invoice_number', 'client_id', 'user_id'];
        foreach ($patientFields as $field) {
            $keyFields[] = $field;
        }
        foreach ($billableFields as $field) {
            $keyFields[] = $field;
        }
        
        foreach ($invoice as $key => $value) {
            if (in_array($key, $keyFields) || strpos($key, 'patient') !== false || strpos($key, 'billable') !== false) {
                $highlight = 'highlight';
            } else {
                $highlight = '';
            }
            echo "<tr class='$highlight'>";
            echo "<td>$key</td>";
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>✗ Invoice #246 not found</p>";
    }
    
    // Show the problematic code
    echo "<h2>Code Analysis</h2>";
    echo "<p>The error occurs because the code tries to access:</p>";
    echo "<pre>\$duplicateInvoice['patient_id']</pre>";
    echo "<p>But based on the table structure above, the 'patient_id' field " . 
         (in_array('patient_id', array_column($columns, 'Field')) ? 
          "<span class='success'>EXISTS</span>" : 
          "<span class='error'>DOES NOT EXIST</span>") . 
         " in the invoices table.</p>";
    
    // Recommendation
    echo "<h2>Recommendation</h2>";
    if (!in_array('patient_id', array_column($columns, 'Field'))) {
        echo "<div style='background-color: #ffe6e6; padding: 15px; border: 1px solid #ff9999; border-radius: 5px;'>";
        echo "<p><strong>The 'patient_id' field does not exist in the invoices table.</strong></p>";
        echo "<p>The duplication code should be updated to handle this properly. The fix has already been applied using null coalescing operators.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>Database Connection Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/fit/public/invoices/create?duplicate=246'>Try duplication again</a> | ";
echo "<a href='/fit/public/'>Back to Application</a></p>";
?>