<?php
// Fix Invoice by Invoice Number - Course-Specific Pricing
// Search by invoice number FAC-LOC-2025-0196

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Find Invoice FAC-LOC-2025-0196</h1>";
    
    // Search for the invoice by number
    $stmt = $db->prepare("
        SELECT i.*, u.first_name, u.last_name, u.username 
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        WHERE i.invoice_number = 'FAC-LOC-2025-0196'
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice FAC-LOC-2025-0196 not found!</p>";
        
        // Search for similar invoice numbers
        $stmt = $db->prepare("
            SELECT i.id, i.invoice_number, i.total, i.status, u.first_name, u.last_name
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.invoice_number LIKE '%0196%' OR i.invoice_number LIKE '%FAC-LOC-2025%'
            ORDER BY i.id DESC
        ");
        $stmt->execute();
        $similar = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($similar)) {
            echo "<h2>Similar Invoice Numbers Found:</h2>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Status</th><th>Action</th></tr>";
            
            foreach ($similar as $inv) {
                echo "<tr>";
                echo "<td>{$inv['id']}</td>";
                echo "<td>{$inv['invoice_number']}</td>";
                echo "<td>{$inv['first_name']} {$inv['last_name']}</td>";
                echo "<td>€ " . number_format($inv['total'], 2) . "</td>";
                echo "<td>{$inv['status']}</td>";
                echo "<td><a href='?fix_id={$inv['id']}'>Fix This Invoice</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Also search for recent invoices
        $stmt = $db->prepare("
            SELECT i.id, i.invoice_number, i.total, i.status, u.first_name, u.last_name
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.invoice_number LIKE 'FAC-LOC-2025%'
            ORDER BY i.id DESC
            LIMIT 10
        ");
        $stmt->execute();
        $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($recent)) {
            echo "<h2>Recent FAC-LOC-2025 Invoices:</h2>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Status</th><th>Action</th></tr>";
            
            foreach ($recent as $inv) {
                echo "<tr>";
                echo "<td>{$inv['id']}</td>";
                echo "<td>{$inv['invoice_number']}</td>";
                echo "<td>{$inv['first_name']} {$inv['last_name']}</td>";
                echo "<td>€ " . number_format($inv['total'], 2) . "</td>";
                echo "<td>{$inv['status']}</td>";
                echo "<td><a href='?fix_id={$inv['id']}'>Fix This Invoice</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        exit;
    }
    
    // If we have a fix_id parameter, use that instead
    if (isset($_GET['fix_id'])) {
        $stmt = $db->prepare("
            SELECT i.*, u.first_name, u.last_name, u.username 
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.id = ?
        ");
        $stmt->execute([$_GET['fix_id']]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    $invoiceId = $invoice['id'];
    $targetTotal = 930.00;
    
    echo "<h2>Invoice {$invoice['invoice_number']} Details</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>ID</td><td>{$invoice['id']}</td></tr>";
    echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
    echo "<tr><td>Client</td><td>{$invoice['first_name']} {$invoice['last_name']} ({$invoice['username']})</td></tr>";
    echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
    echo "<tr><td>Current Total</td><td><strong style='color: red;'>€ " . number_format($invoice['total'], 2) . "</strong></td></tr>";
    echo "<tr><td>Target Total</td><td><strong style='color: green;'>€ " . number_format($targetTotal, 2) . "</strong></td></tr>";
    echo "<tr><td>Missing Amount</td><td><strong>€ " . number_format($targetTotal - $invoice['total'], 2) . "</strong></td></tr>";
    echo "</table>";
    
    // Get user's courses
    $stmt = $db->prepare("
        SELECT * FROM user_courses 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY display_order ASC
    ");
    $stmt->execute([$invoice['client_id']]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>User's Configured Courses</h2>";
    if (!empty($courses)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Course Name</th><th>Hourly Rate (TTC)</th><th>VAT Rate</th><th>Status</th></tr>";
        
        foreach ($courses as $course) {
            $status = $course['is_active'] ? 'Active' : 'Inactive';
            echo "<tr>";
            echo "<td>{$course['course_name']}</td>";
            echo "<td>€ " . number_format($course['hourly_rate'], 2) . "</td>";
            echo "<td>{$course['vat_rate']}%</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No courses configured for this user</p>";
    }
    
    // Get current invoice lines
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute([$invoiceId]);
    $currentLines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Invoice Lines</h2>";
    if (!empty($currentLines)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Line Type</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
        
        $totalFromLines = 0;
        foreach ($currentLines as $line) {
            $totalFromLines += $line['line_total'];
            echo "<tr>";
            echo "<td>{$line['line_type']}</td>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>€ " . number_format($line['unit_price'], 2) . "</td>";
            echo "<td>{$line['vat_rate']}%</td>";
            echo "<td>€ " . number_format($line['line_total'], 2) . "</td>";
            echo "</tr>";
        }
        
        echo "<tr style='font-weight: bold; background: #f0f0f0;'>";
        echo "<td colspan='5'>TOTAL FROM LINES</td>";
        echo "<td>€ " . number_format($totalFromLines, 2) . "</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No invoice lines found!</p>";
    }
    
    // Fix options
    echo "<h2>Fix Options</h2>";
    
    if (!isset($_GET['action'])) {
        echo "<div style='margin: 20px 0;'>";
        echo "<p><strong>Choose how to fix invoice {$invoice['invoice_number']}:</strong></p>";
        
        $missingAmount = $targetTotal - $invoice['total'];
        
        echo "<p><strong>Option 1:</strong> Add missing amount as patient retrocession (€" . number_format($missingAmount, 2) . ")</p>";
        echo "<a href='?fix_id=$invoiceId&action=add_patient_part' style='background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Add Patient Part</a>";
        
        echo "<p><strong>Option 2:</strong> Simple total adjustment to 930.00€</p>";
        echo "<a href='?fix_id=$invoiceId&action=simple_adjust' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Simple Adjustment</a>";
        
        echo "<p><strong>Option 3:</strong> Custom amount</p>";
        echo "<form method='get' style='display: inline;'>";
        echo "<input type='hidden' name='fix_id' value='$invoiceId'>";
        echo "<input type='hidden' name='action' value='add_custom_amount'>";
        echo "Amount: <input type='number' name='custom_amount' step='0.01' value='" . number_format($missingAmount, 2) . "'> ";
        echo "Description: <input type='text' name='custom_description' value='Correction montant'> ";
        echo "<input type='submit' value='Add Custom Amount'>";
        echo "</form>";
        echo "</div>";
    }
    
    // Handle fix actions
    if (isset($_GET['action'])) {
        $action = $_GET['action'];
        
        try {
            $db->beginTransaction();
            
            switch ($action) {
                case 'add_patient_part':
                    $missingAmount = $targetTotal - $invoice['total'];
                    
                    // Add patient retrocession line
                    $stmt = $db->prepare("
                        INSERT INTO invoice_lines (
                            invoice_id, line_type, description, quantity, unit_price, 
                            vat_rate, line_total, sort_order
                        ) VALUES (?, 'patient_part', 'Part Patient', 1, ?, 0, ?, 10)
                    ");
                    $stmt->execute([$invoiceId, $missingAmount, $missingAmount]);
                    
                    // Update totals
                    $stmt = $db->prepare("
                        UPDATE invoices 
                        SET total = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$targetTotal, $invoiceId]);
                    
                    echo "<p style='color: green;'>✅ Added patient retrocession line: € " . number_format($missingAmount, 2) . "</p>";
                    break;
                    
                case 'add_custom_amount':
                    $customAmount = $_GET['custom_amount'] ?? 0;
                    $customDescription = $_GET['custom_description'] ?? 'Correction montant';
                    
                    // Add custom line
                    $stmt = $db->prepare("
                        INSERT INTO invoice_lines (
                            invoice_id, line_type, description, quantity, unit_price, 
                            vat_rate, line_total, sort_order
                        ) VALUES (?, 'adjustment', ?, 1, ?, 0, ?, 10)
                    ");
                    $stmt->execute([$invoiceId, $customDescription, $customAmount, $customAmount]);
                    
                    // Update totals
                    $newTotal = $invoice['total'] + $customAmount;
                    $stmt = $db->prepare("
                        UPDATE invoices 
                        SET total = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$newTotal, $invoiceId]);
                    
                    echo "<p style='color: green;'>✅ Added custom amount: € " . number_format($customAmount, 2) . " ($customDescription)</p>";
                    break;
                    
                case 'simple_adjust':
                    $stmt = $db->prepare("
                        UPDATE invoices 
                        SET total = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$targetTotal, $invoiceId]);
                    
                    echo "<p style='color: green;'>✅ Set total to € " . number_format($targetTotal, 2) . "</p>";
                    break;
            }
            
            $db->commit();
            echo "<p><strong>Invoice {$invoice['invoice_number']} has been updated!</strong></p>";
            echo "<p><a href='http://localhost/fit/public/invoices/$invoiceId' target='_blank'>View Updated Invoice</a></p>";
            
        } catch (Exception $e) {
            $db->rollBack();
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
form { margin: 10px 0; }
input[type="number"], input[type="text"] { padding: 5px; margin: 2px; }
</style>