<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\CatalogItem;
use App\Models\CatalogCategory;
use App\Models\ConfigVatRate;
use App\Models\StockMovement;
use Flight;

class ProductController extends Controller
{
    /**
     * List all products
     */
    public function index()
    {
        $page = (int) (Flight::request()->query->page ?? 1);
        $perPage = (int) (Flight::request()->query->per_page ?? 25);
        $search = Flight::request()->query->search ?? '';
        $category = Flight::request()->query->category ?? '';
        $type = Flight::request()->query->type ?? '';
        $stockStatus = Flight::request()->query->stock_status ?? '';
        
        // Build query
        $db = CatalogItem::db();
        $where = [];
        $params = [];
        
        // Base query
        $sql = "SELECT ci.*, cc.name as category_name, cc.icon as category_icon, 
                cvr.rate as vat_rate, cvr.name as vat_name
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE 1=1";
        
        // Apply filters
        if ($search) {
            $sql .= " AND (ci.code LIKE :search OR ci.name LIKE :search2 OR ci.description LIKE :search3)";
            $params['search'] = "%{$search}%";
            $params['search2'] = "%{$search}%";
            $params['search3'] = "%{$search}%";
        }
        
        if ($category) {
            $sql .= " AND ci.category_id = :category";
            $params['category'] = $category;
        }
        
        if ($type) {
            $sql .= " AND ci.item_type = :type";
            $params['type'] = $type;
        }
        
        if ($stockStatus) {
            switch ($stockStatus) {
                case 'low':
                    $sql .= " AND ci.is_stockable = 1 AND ci.current_stock <= ci.low_stock_alert AND ci.current_stock > 0";
                    break;
                case 'out':
                    $sql .= " AND ci.is_stockable = 1 AND ci.current_stock <= 0";
                    break;
                case 'in':
                    $sql .= " AND ci.is_stockable = 1 AND ci.current_stock > ci.low_stock_alert";
                    break;
            }
        }
        
        // Count total records
        $countSql = "SELECT COUNT(*) as total FROM ($sql) as count_query";
        $countStmt = $db->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch(\PDO::FETCH_ASSOC)['total'];
        
        // Add pagination
        $sql .= " ORDER BY ci.name ASC";
        $offset = ($page - 1) * $perPage;
        $sql .= " LIMIT :limit OFFSET :offset";
        $params['limit'] = $perPage;
        $params['offset'] = $offset;
        
        // Execute query
        $stmt = $db->prepare($sql);
        foreach ($params as $key => $value) {
            if ($key === 'limit' || $key === 'offset') {
                $stmt->bindValue($key, $value, \PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }
        $stmt->execute();
        
        // Build results
        $products = [];
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $product = new CatalogItem();
            $product->fill($row);
            
            // Parse category name if it's JSON
            $categoryName = $row['category_name'];
            if ($categoryName && strpos($categoryName, '{') === 0) {
                $names = json_decode($categoryName, true);
                if ($names) {
                    // Use French as default, fallback to English, then any available
                    $categoryName = $names['fr'] ?? $names['en'] ?? reset($names);
                }
            }
            
            // Add loaded relationships
            $product->category = (object) [
                'id' => $row['category_id'],
                'name' => $categoryName,
                'icon' => $row['category_icon']
            ];
            $product->vatRate = (object) [
                'id' => $row['vat_rate_id'],
                'rate' => $row['vat_rate'],
                'name' => $row['vat_name']
            ];
            
            $products[] = $product;
        }
        
        // Pagination data
        $totalPages = ceil($total / $perPage);
        $pagination = [
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => $totalPages,
            'has_pages' => $totalPages > 1
        ];
        
        // Get categories for filter
        $categorySql = "SELECT * FROM catalog_categories WHERE is_active = 1 ORDER BY sort_order ASC";
        $categoryStmt = $db->query($categorySql);
        $categories = [];
        while ($row = $categoryStmt->fetch(\PDO::FETCH_ASSOC)) {
            $cat = new CatalogCategory();
            $cat->fill($row);
            $categories[] = $cat;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/index-' . $template;
        
        $this->render($viewName, [
            'products' => $products,
            'pagination' => $pagination,
            'categories' => $categories,
            'filters' => [
                'search' => $search,
                'category' => $category,
                'type' => $type,
                'stock_status' => $stockStatus
            ]
        ]);
    }
    
    /**
     * Show create form
     */
    public function create()
    {
        $categories = CatalogCategory::all();
        $vatRates = ConfigVatRate::where('is_active', '=', 1)->get();
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/create-' . $template;
        
        $this->render($viewName, [
            'categories' => $categories,
            'vatRates' => $vatRates,
            'defaultCode' => CatalogItem::generateCode()
        ]);
    }
    
    /**
     * Store new product
     */
    public function store()
    {
        // Get data from POST since it's sent as FormData
        $data = $_POST;
        
        // Validate
        $errors = $this->validateProduct($data);
        if (!empty($errors)) {
            Flight::json(['success' => false, 'errors' => $errors], 422);
            return;
        }
        
        try {
            // Create product
            $product = CatalogItem::create([
                'code' => $data['code'],
                'name' => $data['name'],
                'category_id' => $data['category_id'],
                'item_type' => $data['item_type'],
                'description' => $data['description'] ?? null,
                'unit_price' => $data['unit_price'],
                'vat_rate_id' => $data['vat_rate_id'],
                'is_stockable' => isset($data['is_stockable']) ? 1 : 0,
                'current_stock' => $data['current_stock'] ?? 0,
                'low_stock_alert' => $data['low_stock_alert'] ?? 5,
                'quick_sale_button' => isset($data['quick_sale_button']) ? 1 : 0,
                'button_color' => $data['button_color'] ?? '#007bff',
                'button_order' => $data['button_order'] ?? 0,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            // If initial stock provided, create stock movement
            if ($product->is_stockable && $product->current_stock > 0) {
                StockMovement::create([
                    'item_id' => $product->id,
                    'movement_type' => 'adjustment',
                    'quantity' => $product->current_stock,
                    'notes' => 'Initial stock',
                    'created_by' => $_SESSION['user_id'] ?? null
                ]);
            }
            
            // Remove flash message since it might not be available
            Flight::json(['success' => true, 'redirect' => $this->url('/products/' . $product->id)]);
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error creating product: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Show product details
     */
    public function show($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::halt(404, 'Product not found');
            return;
        }
        $product->loadCategory();
        $product->loadVatRate();
        
        // Get recent stock movements
        $db = StockMovement::db();
        $stmt = $db->prepare("
            SELECT sm.*, CONCAT(u.first_name, ' ', u.last_name) as user_name
            FROM stock_movements sm
            LEFT JOIN users u ON sm.created_by = u.id
            WHERE sm.item_id = :item_id
            ORDER BY sm.created_at DESC
            LIMIT 10
        ");
        $stmt->execute(['item_id' => $id]);
        $stockMovements = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Get sales history
        try {
            $stmt = $db->prepare("
                SELECT sil.*, si.invoice_number, si.issue_date, c.name as client_name
                FROM sales_invoice_lines sil
                JOIN sales_invoices si ON sil.invoice_id = si.id
                JOIN clients c ON si.client_id = c.id
                WHERE sil.item_id = :item_id
                ORDER BY si.issue_date DESC
                LIMIT 10
            ");
            $stmt->execute(['item_id' => $id]);
            $salesHistory = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            // If there's an error (e.g., no sales yet), just set empty array
            $salesHistory = [];
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/show-' . $template;
        
        $this->render($viewName, [
            'product' => $product,
            'stockMovements' => $stockMovements,
            'salesHistory' => $salesHistory
        ]);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::halt(404, 'Product not found');
            return;
        }
        $categories = CatalogCategory::all();
        $vatRates = ConfigVatRate::where('is_active', '=', 1)->get();
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/edit-' . $template;
        
        $this->render($viewName, [
            'product' => $product,
            'categories' => $categories,
            'vatRates' => $vatRates
        ]);
    }
    
    /**
     * Update product
     */
    public function update($id)
    {
        // Force JSON response header
        header('Content-Type: application/json');
        
        $product = CatalogItem::find($id);
        if (!$product) {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }
        
        // Get data from POST since it's sent as FormData
        $data = $_POST;
        
        // Validate
        $errors = $this->validateProduct($data, $id);
        if (!empty($errors)) {
            echo json_encode(['success' => false, 'errors' => $errors]);
            exit;
        }
        
        try {
            // Update product
            $product->update([
                'code' => $data['code'],
                'name' => $data['name'],
                'category_id' => $data['category_id'],
                'item_type' => $data['item_type'],
                'description' => $data['description'] ?? null,
                'unit_price' => $data['unit_price'],
                'vat_rate_id' => $data['vat_rate_id'],
                'is_stockable' => isset($data['is_stockable']) ? 1 : 0,
                'low_stock_alert' => $data['low_stock_alert'] ?? 5,
                'quick_sale_button' => isset($data['quick_sale_button']) ? 1 : 0,
                'button_color' => $data['button_color'] ?? '#007bff',
                'button_order' => $data['button_order'] ?? 0,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            // Remove flash message since it might not be available
            echo json_encode(['success' => true, 'redirect' => $this->url('/products/' . $product->id)]);
            exit;
            
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error updating product: ' . $e->getMessage()]);
            exit;
        }
    }
    
    /**
     * Adjust stock
     */
    public function adjustStock($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::json(['success' => false, 'message' => 'Product not found'], 404);
            return;
        }
        
        if (!$product->is_stockable) {
            Flight::json(['success' => false, 'message' => 'This product is not stockable'], 400);
            return;
        }
        
        // Get data from POST since it's sent as FormData
        $data = $_POST;
        
        // Validate
        if (!isset($data['quantity']) || !is_numeric($data['quantity'])) {
            Flight::json(['success' => false, 'message' => 'Invalid quantity'], 422);
            return;
        }
        
        try {
            $movement = $product->adjustStock(
                $data['quantity'],
                $data['type'] ?? 'adjustment',
                null,
                $_SESSION['user_id'] ?? null
            );
            
            if (isset($data['notes'])) {
                $movement->update(['notes' => $data['notes']]);
            }
            
            Flight::json([
                'success' => true,
                'current_stock' => $product->current_stock,
                'message' => 'Stock adjusted successfully'
            ]);
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error adjusting stock: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Delete product
     */
    public function destroy($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::json(['success' => false, 'message' => 'Product not found'], 404);
            return;
        }
        
        // Check if product has been used
        $db = CatalogItem::db();
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM sales_invoice_lines WHERE item_id = :id");
        $stmt->execute(['id' => $id]);
        $hasHistory = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
        
        if ($hasHistory) {
            // Soft delete by deactivating
            $product->update(['is_active' => 0]);
            $message = 'Product deactivated (has sales history)';
        } else {
            // Hard delete
            $product->delete();
            $message = 'Product deleted successfully';
        }
        
        // Remove flash message since it might not be available
        Flight::json(['success' => true, 'redirect' => $this->url('/products')]);
    }
    
    /**
     * Quick sale items API
     */
    public function quickSaleItems()
    {
        $items = CatalogItem::getQuickSaleItems();
        
        $result = [];
        foreach ($items as $item) {
            $item->loadVatRate();
            $result[] = [
                'id' => $item->id,
                'code' => $item->code,
                'name' => $item->name,
                'price' => $item->unit_price,
                'price_with_vat' => $item->getPriceWithVat(),
                'vat_rate' => $item->vatRate ? $item->vatRate->rate : 0,
                'color' => $item->button_color,
                'in_stock' => $item->isInStock(),
                'current_stock' => $item->current_stock
            ];
        }
        
        Flight::json(['success' => true, 'items' => $result]);
    }
    
    /**
     * Search products API
     */
    public function search()
    {
        $term = Flight::request()->query->term ?? '';
        
        if (strlen($term) < 2) {
            Flight::json(['success' => true, 'items' => []]);
            return;
        }
        
        $db = CatalogItem::db();
        $sql = "SELECT ci.*, cc.name as category_name, cvr.rate as vat_rate
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE ci.is_active = 1
                AND (ci.code LIKE :term OR ci.name LIKE :term2 OR ci.description LIKE :term3)
                LIMIT 20";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            'term' => "%{$term}%",
            'term2' => "%{$term}%",
            'term3' => "%{$term}%"
        ]);
        
        $items = [];
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $item = new CatalogItem();
            $item->fill($row);
            
            $items[] = [
                'id' => $item->id,
                'code' => $item->code,
                'name' => $item->name,
                'category' => $row['category_name'],
                'price' => $item->unit_price,
                'price_formatted' => $item->formatPrice(),
                'vat_rate' => $row['vat_rate'],
                'in_stock' => $item->isInStock(),
                'stock_status' => $item->getStockStatusLabel()
            ];
        }
        
        Flight::json(['success' => true, 'items' => $items]);
    }
    
    /**
     * Validate product data
     */
    private function validateProduct($data, $id = null)
    {
        $errors = [];
        
        // Required fields
        if (empty($data['code'])) {
            $errors['code'] = 'Product code is required';
        } else {
            // Check uniqueness
            $db = CatalogItem::db();
            $sql = "SELECT COUNT(*) as count FROM catalog_items WHERE code = :code";
            $params = ['code' => $data['code']];
            
            if ($id) {
                $sql .= " AND id != :id";
                $params['id'] = $id;
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            if ($stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0) {
                $errors['code'] = 'Product code already exists';
            }
        }
        
        if (empty($data['name'])) {
            $errors['name'] = 'Product name is required';
        }
        
        if (empty($data['category_id'])) {
            $errors['category_id'] = 'Category is required';
        }
        
        if (empty($data['item_type'])) {
            $errors['item_type'] = 'Item type is required';
        }
        
        if (!isset($data['unit_price']) || !is_numeric($data['unit_price']) || $data['unit_price'] < 0) {
            $errors['unit_price'] = 'Valid unit price is required';
        }
        
        if (empty($data['vat_rate_id'])) {
            $errors['vat_rate_id'] = 'VAT rate is required';
        }
        
        return $errors;
    }
    
    /**
     * Quick create product from invoice line
     */
    public function quickCreate()
    {
        // Get data from POST
        $data = Flight::request()->data->getData();
        
        // Basic validation
        $errors = [];
        
        if (empty($data['code'])) {
            $errors['code'] = __('validation.required');
        } else {
            // Check if code already exists
            $db = CatalogItem::db();
            $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
            $stmt->execute(['code' => $data['code']]);
            $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
            if ($existing) {
                $errors['code'] = __('products.code_exists');
            }
        }
        
        if (empty($data['name'])) {
            $errors['name'] = __('validation.required');
        }
        
        if (empty($data['category_id'])) {
            $errors['category_id'] = __('validation.required');
        }
        
        if (empty($data['unit_price']) || $data['unit_price'] < 0) {
            $errors['unit_price'] = __('validation.required');
        }
        
        if (!empty($errors)) {
            Flight::json(['success' => false, 'errors' => $errors], 422);
            return;
        }
        
        try {
            // Create product with minimal data
            $product = CatalogItem::create([
                'code' => $data['code'],
                'name' => $data['name'],
                'category_id' => $data['category_id'],
                'item_type' => $data['item_type'] ?? 'service',
                'unit_price' => $data['unit_price'],
                'vat_rate_id' => $data['vat_rate_id'] ?? 1,
                'is_stockable' => $data['is_stockable'] ?? 0,
                'is_active' => 1
            ]);
            
            Flight::json([
                'success' => true,
                'product' => [
                    'id' => $product->id,
                    'code' => $product->code,
                    'name' => $product->name,
                    'price' => $product->unit_price
                ]
            ]);
            
        } catch (\Exception $e) {
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Check if product code exists and suggest alternatives
     */
    public function checkCode()
    {
        try {
            // Force JSON response header
            header('Content-Type: application/json');
            
            $code = Flight::request()->query->code ?? '';
            
            if (empty($code)) {
                Flight::json(['exists' => false, 'suggestions' => []]);
                return;
            }
            
            // Check if code exists
            $db = CatalogItem::db();
            $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
            $stmt->execute(['code' => $code]);
            $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$existing) {
                Flight::json(['exists' => false, 'suggestions' => []]);
                return;
            }
            
            // Generate alternative suggestions
            $baseCode = preg_replace('/\d+$/', '', $code); // Remove trailing numbers
            $suggestions = [];
            
            // Try different suffixes
            for ($i = 1; $i <= 5; $i++) {
                $newCode = $baseCode . str_pad((int)substr($code, -3) + $i, 3, '0', STR_PAD_LEFT);
                $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
                $stmt->execute(['code' => $newCode]);
                if (!$stmt->fetch(\PDO::FETCH_ASSOC)) {
                    $suggestions[] = $newCode;
                    if (count($suggestions) >= 3) break;
                }
            }
            
            // If still need suggestions, try with current timestamp
            if (count($suggestions) < 3) {
                $timestamp = time();
                $suggestions[] = $baseCode . substr($timestamp, -3);
                $suggestions[] = $baseCode . substr($timestamp, -4, 3);
            }
            
            Flight::json([
                'exists' => true,
                'suggestions' => array_unique($suggestions)
            ]);
        } catch (\Exception $e) {
            // Always return JSON on error
            Flight::json([
                'error' => true,
                'message' => 'Error checking code',
                'exists' => false,
                'suggestions' => []
            ]);
        }
    }
    
    /**
     * Bulk delete products
     */
    public function bulkDelete()
    {
        try {
            // Get JSON data
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($data['ids']) || !is_array($data['ids']) || empty($data['ids'])) {
                Flight::json(['success' => false, 'message' => 'No products selected'], 400);
                return;
            }
            
            $db = CatalogItem::db();
            $db->beginTransaction();
            
            try {
                $deletedCount = 0;
                $deactivatedCount = 0;
                
                foreach ($data['ids'] as $id) {
                    $product = CatalogItem::find($id);
                    if (!$product) continue;
                    
                    // Check if product has been used in invoices
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoice_lines WHERE item_id = :id");
                    $stmt->execute(['id' => $id]);
                    $hasHistory = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
                    
                    if ($hasHistory) {
                        // Soft delete by deactivating
                        $product->update(['is_active' => 0]);
                        $deactivatedCount++;
                    } else {
                        // Hard delete
                        $product->delete();
                        $deletedCount++;
                    }
                }
                
                $db->commit();
                
                $message = '';
                if ($deletedCount > 0) {
                    $message .= "$deletedCount products deleted. ";
                }
                if ($deactivatedCount > 0) {
                    $message .= "$deactivatedCount products deactivated (have sales history).";
                }
                
                Flight::json(['success' => true, 'message' => $message]);
                
            } catch (\Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error deleting products: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Export products
     */
    public function export()
    {
        try {
            $ids = Flight::request()->data->ids ?? [];
            
            if (empty($ids)) {
                Flight::halt(400, 'No products selected');
                return;
            }
            
            // Get products
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $db = CatalogItem::db();
            $sql = "
                SELECT ci.*, cc.name as category_name, cvr.rate as vat_rate
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE ci.id IN ($placeholders)
            ";
            $stmt = $db->prepare($sql);
            $stmt->execute($ids);
            
            // Set headers for CSV download with UTF-8 encoding
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename="products_export_' . date('Y-m-d_His') . '.csv"');
            
            // Open output stream
            $output = fopen('php://output', 'w');
            
            // Write UTF-8 BOM to ensure Excel recognizes UTF-8 encoding
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Write CSV headers
            fputcsv($output, [
                'Code',
                'Name',
                'Category',
                'Type',
                'Description',
                'Unit Price',
                'VAT Rate',
                'Stock',
                'Status'
            ]);
            
            // Write data
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                // Parse category name if JSON
                $categoryName = $row['category_name'];
                if ($categoryName && strpos($categoryName, '{') === 0) {
                    $names = json_decode($categoryName, true);
                    if ($names) {
                        $categoryName = $names[$_SESSION['user_language'] ?? 'fr'] ?? $names['fr'] ?? $names['en'] ?? reset($names);
                    }
                }
                
                fputcsv($output, [
                    $row['code'],
                    $row['name'],
                    $categoryName,
                    $row['item_type'],
                    $row['description'],
                    $row['unit_price'],
                    $row['vat_rate'] . '%',
                    $row['is_stockable'] ? $row['current_stock'] : 'N/A',
                    $row['is_active'] ? 'Active' : 'Inactive'
                ]);
            }
            
            fclose($output);
            exit;
            
        } catch (\Exception $e) {
            Flight::halt(500, 'Error exporting products: ' . $e->getMessage());
        }
    }
}