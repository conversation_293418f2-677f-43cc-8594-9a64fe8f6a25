<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== DEBUGGING INVOICE FORM ISSUES ===\n\n";
    
    // Check if the test invoice shows up
    echo "1. Checking if test invoice exists:\n";
    $stmt = $pdo->query("SELECT * FROM invoices ORDER BY id DESC LIMIT 5");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invoices) > 0) {
        echo "Found " . count($invoices) . " invoice(s):\n";
        foreach ($invoices as $inv) {
            echo "  - ID: {$inv['id']}, Number: {$inv['invoice_number']}, Status: {$inv['status']}, Total: {$inv['total']}\n";
        }
    } else {
        echo "No invoices found!\n";
    }
    
    // Check invoice_lines table
    echo "\n2. Checking invoice_lines table:\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'invoice_lines'");
    if ($stmt->fetch()) {
        echo "✓ invoice_lines table exists\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM invoice_lines");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "  Total invoice lines: {$result['count']}\n";
        
        // Check if test invoice has lines
        if (count($invoices) > 0) {
            $testInvoiceId = $invoices[0]['id'];
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM invoice_lines WHERE invoice_id = ?");
            $stmt->execute([$testInvoiceId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "  Lines for invoice #{$testInvoiceId}: {$result['count']}\n";
        }
    } else {
        echo "❌ invoice_lines table NOT FOUND!\n";
        echo "  This might be why invoice creation fails - the form expects to add invoice lines\n";
    }
    
    // Check invoice_items table (alternative name)
    $stmt = $pdo->query("SHOW TABLES LIKE 'invoice_items'");
    if ($stmt->fetch()) {
        echo "\n✓ invoice_items table exists (alternative to invoice_lines)\n";
    }
    
    // Check for any error logs
    echo "\n3. Common issues that prevent invoice creation:\n";
    echo "  - Missing invoice lines/items (at least one line item is usually required)\n";
    echo "  - Missing required fields (client/user selection)\n";
    echo "  - CSRF token validation failure\n";
    echo "  - JavaScript errors preventing form submission\n";
    
    // Check the form requirements
    echo "\n4. Invoice form typically requires:\n";
    echo "  ✓ Document type (you selected Invoice)\n";
    echo "  ✓ Invoice number (FAC-2025-0186)\n";
    echo "  ✓ Issue date\n";
    echo "  ✓ Due date (usually auto-calculated)\n";
    echo "  ✓ Client or User selection\n";
    echo "  ✓ At least one line item with description and amount\n";
    
    // Suggest next steps
    echo "\n5. To successfully create an invoice:\n";
    echo "  1. Go to http://localhost/fit/public/invoices/create\n";
    echo "  2. Select Document Type: Invoice\n";
    echo "  3. Click generate number button (should show FAC-2025-0186)\n";
    echo "  4. Select a client or user to bill to\n";
    echo "  5. Add at least one line item:\n";
    echo "     - Description: e.g., 'Service'\n";
    echo "     - Quantity: 1\n";
    echo "     - Unit Price: 100\n";
    echo "     - VAT Rate: 17%\n";
    echo "  6. Click 'Save Draft' or 'Save and Send'\n";
    
    // Delete test invoice if needed
    echo "\n6. Cleanup options:\n";
    if (count($invoices) > 0 && strpos($invoices[0]['invoice_number'], 'TEST') !== false) {
        echo "  Found test invoice. To delete it, run:\n";
        echo "  DELETE FROM invoices WHERE invoice_number LIKE 'TEST%';\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}