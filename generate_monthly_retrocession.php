<?php
/**
 * Generate monthly retrocession entries
 * Usage: php generate_monthly_retrocession.php [month] [year]
 * If no parameters provided, generates for current month
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Services\RetrocessionAutoGenerator;

// Get command line arguments - default to previous month
if (isset($argv[1])) {
    $month = intval($argv[1]);
    $year = isset($argv[2]) ? intval($argv[2]) : date('Y');
} else {
    // Default to previous month
    $month = date('n') - 1;
    $year = date('Y');
    
    if ($month == 0) {
        $month = 12;
        $year--;
    }
}

// Validate month
if ($month < 1 || $month > 12) {
    echo "Error: Invalid month. Must be between 1 and 12.\n";
    exit(1);
}

// Validate year
if ($year < 2020 || $year > 2050) {
    echo "Error: Invalid year. Must be between 2020 and 2050.\n";
    exit(1);
}

echo "Generating retrocession entries for " . date('F Y', strtotime("$year-$month-01")) . "...\n\n";

try {
    $generator = new RetrocessionAutoGenerator();
    $results = $generator->generateMonthlyEntries($month, $year);
    
    echo "Results:\n";
    echo "- Entries created: " . $results['success'] . "\n";
    echo "- Entries skipped (already exist): " . $results['skipped'] . "\n";
    
    if (!empty($results['errors'])) {
        echo "\nErrors encountered:\n";
        foreach ($results['errors'] as $error) {
            echo "  - " . $error . "\n";
        }
    }
    
    echo "\nDone!\n";
    
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}