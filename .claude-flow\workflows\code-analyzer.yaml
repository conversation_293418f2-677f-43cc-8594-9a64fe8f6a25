name: Code Analyzer and Optimizer
description: Analyzes code quality, finds bugs, suggests improvements, and can refactor code automatically
version: 1.0.0
author: Claude-Flow

inputs:
  - name: target
    type: string
    description: File, directory, or module to analyze (e.g., app/controllers/, UserController.php)
    required: false
    default: "app/"
    
  - name: analysis_type
    type: string
    description: Type of analysis (security, performance, quality, bugs, all)
    required: false
    default: "all"
    
  - name: fix_issues
    type: boolean
    description: Automatically fix found issues where possible
    required: false
    default: false
    
  - name: complexity_threshold
    type: number
    description: Cyclomatic complexity threshold for flagging complex methods
    required: false
    default: 10

env:
  app_root: "."
  phpstan_level: "${PHPSTAN_LEVEL:-5}"

steps:
  - name: Install Analysis Tools
    type: shell
    command: |
      echo "=== Setting Up Code Analysis Tools ==="
      
      # Check if analysis tools are installed
      if [ ! -f "vendor/bin/phpstan" ]; then
        echo "Installing PHPStan..."
        composer require --dev phpstan/phpstan
      fi
      
      if [ ! -f "vendor/bin/phpcs" ]; then
        echo "Installing PHP CodeSniffer..."
        composer require --dev squizlabs/php_codesniffer
      fi
      
      echo "Analysis tools ready"

  - name: Static Code Analysis
    type: shell
    command: |
      echo -e "\n=== Running Static Analysis ==="
      
      # Create PHPStan config if not exists
      if [ ! -f "phpstan.neon" ]; then
        cat > phpstan.neon << 'EOF'
      parameters:
        level: ${env.phpstan_level}
        paths:
          - ${inputs.target}
        excludePaths:
          - vendor
          - storage
        treatPhpDocTypesAsCertain: false
      EOF
      fi
      
      # Run PHPStan
      echo -e "\nRunning PHPStan analysis..."
      vendor/bin/phpstan analyze --error-format=json > phpstan_results.json || true
      
      # Parse and display results
      php -r "
      \$results = json_decode(file_get_contents('phpstan_results.json'), true);
      
      if (isset(\$results['errors']) && !empty(\$results['errors'])) {
          echo \"\\nFound \" . count(\$results['errors']) . \" potential issues:\\n\\n\";
          
          \$byFile = [];
          foreach (\$results['errors'] as \$error) {
              \$byFile[\$error['file']][] = \$error;
          }
          
          foreach (\$byFile as \$file => \$errors) {
              echo \"File: \$file\\n\";
              foreach (\$errors as \$error) {
                  echo \"  Line \" . \$error['line'] . \": \" . \$error['message'] . \"\\n\";
              }
              echo \"\\n\";
          }
      } else {
          echo \"✓ No issues found by static analysis\\n\";
      }
      "

  - name: Security Analysis
    type: conditional
    condition: "{{ inputs.analysis_type == 'all' || inputs.analysis_type == 'security' }}"
    steps:
      - name: Check Security Issues
        type: shell
        command: |
          php -r "
          echo \"\\n=== Security Analysis ===\\n\";
          
          \$target = '${inputs.target}';
          \$issues = [];
          
          // Define security patterns to check
          \$securityPatterns = [
              'sql_injection' => [
                  'pattern' => '/\\\$_(GET|POST|REQUEST)\\[[\'\"](.*?)[\'\"]\].*?(query|prepare|exec)/',
                  'message' => 'Potential SQL injection - unsanitized user input in query'
              ],
              'xss' => [
                  'pattern' => '/echo\\s+\\\$_(GET|POST|REQUEST)/',
                  'message' => 'Potential XSS - unescaped output of user input'
              ],
              'file_inclusion' => [
                  'pattern' => '/(include|require)(_once)?\\s*\\(?\\s*\\\$/',
                  'message' => 'Potential file inclusion vulnerability'
              ],
              'weak_comparison' => [
                  'pattern' => '/if\\s*\\(\\s*\\\$\\w+\\s*==\\s*[\'\"](admin|true|1)[\'\"]/i',
                  'message' => 'Weak comparison - use === for security checks'
              ],
              'hardcoded_creds' => [
                  'pattern' => '/password[\'\"\\s]*[=:][\'\"\\s]*(\\w{6,})/',
                  'message' => 'Potential hardcoded credentials'
              ],
              'unvalidated_redirect' => [
                  'pattern' => '/header\\s*\\([\'\"](Location|location):\\s*[\'\"]\s*\.\s*\\\$/',
                  'message' => 'Potential open redirect vulnerability'
              ]
          ];
          
          // Scan files
          \$files = [];
          if (is_file(\$target)) {
              \$files[] = \$target;
          } else {
              \$iterator = new RecursiveIteratorIterator(
                  new RecursiveDirectoryIterator(\$target)
              );
              
              foreach (\$iterator as \$file) {
                  if (\$file->isFile() && \$file->getExtension() === 'php') {
                      \$files[] = \$file->getPathname();
                  }
              }
          }
          
          foreach (\$files as \$file) {
              \$content = file_get_contents(\$file);
              \$lines = explode(\"\\n\", \$content);
              
              foreach (\$securityPatterns as \$type => \$check) {
                  foreach (\$lines as \$lineNum => \$line) {
                      if (preg_match(\$check['pattern'], \$line)) {
                          \$issues[] = [
                              'type' => \$type,
                              'file' => \$file,
                              'line' => \$lineNum + 1,
                              'message' => \$check['message'],
                              'code' => trim(\$line)
                          ];
                      }
                  }
              }
          }
          
          if (!empty(\$issues)) {
              echo \"Found \" . count(\$issues) . \" security concerns:\\n\\n\";
              
              foreach (\$issues as \$issue) {
                  echo \"⚠️  \" . \$issue['message'] . \"\\n\";
                  echo \"   File: \" . \$issue['file'] . \":\" . \$issue['line'] . \"\\n\";
                  echo \"   Code: \" . \$issue['code'] . \"\\n\\n\";
              }
              
              file_put_contents('security_issues.json', json_encode(\$issues, JSON_PRETTY_PRINT));
          } else {
              echo \"✓ No obvious security issues found\\n\";
          }
          "

  - name: Performance Analysis
    type: conditional
    condition: "{{ inputs.analysis_type == 'all' || inputs.analysis_type == 'performance' }}"
    steps:
      - name: Check Performance Issues
        type: shell
        command: |
          php -r "
          echo \"\\n=== Performance Analysis ===\\n\";
          
          \$target = '${inputs.target}';
          \$issues = [];
          
          // Performance antipatterns
          \$perfPatterns = [
              'n_plus_one' => [
                  'pattern' => '/foreach.*?\\{[^}]*?(query|find|findBy|select)/',
                  'message' => 'Potential N+1 query problem in loop'
              ],
              'no_index' => [
                  'pattern' => '/WHERE\\s+\\w+\\s*(!?=|LIKE).*ORDER\\s+BY/i',
                  'message' => 'Query may need index for WHERE + ORDER BY'
              ],
              'select_all' => [
                  'pattern' => '/SELECT\\s+\\*\\s+FROM/i',
                  'message' => 'SELECT * can be inefficient - specify needed columns'
              ],
              'no_limit' => [
                  'pattern' => '/SELECT.*FROM(?!.*LIMIT)/i',
                  'message' => 'Query without LIMIT can return too many rows'
              ],
              'file_get_contents_loop' => [
                  'pattern' => '/foreach.*?\\{[^}]*?file_get_contents/',
                  'message' => 'file_get_contents in loop - consider batch processing'
              ]
          ];
          
          // Scan for performance issues
          \$files = is_file(\$target) ? [\$target] : glob(\$target . '/**/*.php');
          
          foreach (\$files as \$file) {
              if (!is_file(\$file)) continue;
              
              \$content = file_get_contents(\$file);
              \$lines = explode(\"\\n\", \$content);
              
              foreach (\$perfPatterns as \$type => \$check) {
                  // Multi-line pattern matching
                  if (preg_match_all(\$check['pattern'], \$content, \$matches, PREG_OFFSET_CAPTURE)) {
                      foreach (\$matches[0] as \$match) {
                          \$lineNum = substr_count(substr(\$content, 0, \$match[1]), \"\\n\") + 1;
                          
                          \$issues[] = [
                              'type' => \$type,
                              'file' => \$file,
                              'line' => \$lineNum,
                              'message' => \$check['message']
                          ];
                      }
                  }
              }
          }
          
          if (!empty(\$issues)) {
              echo \"Found \" . count(\$issues) . \" performance concerns:\\n\\n\";
              
              \$grouped = [];
              foreach (\$issues as \$issue) {
                  \$grouped[\$issue['type']][] = \$issue;
              }
              
              foreach (\$grouped as \$type => \$typeIssues) {
                  echo strtoupper(str_replace('_', ' ', \$type)) . \":\\n\";
                  foreach (\$typeIssues as \$issue) {
                      echo \"  - \" . basename(\$issue['file']) . \":\" . \$issue['line'] . \"\\n\";
                  }
                  echo \"\\n\";
              }
          } else {
              echo \"✓ No obvious performance issues found\\n\";
          }
          "

  - name: Code Complexity Analysis
    type: shell
    command: |
      php -r "
      echo \"\\n=== Code Complexity Analysis ===\\n\";
      
      \$target = '${inputs.target}';
      \$threshold = ${inputs.complexity_threshold};
      
      // Simple cyclomatic complexity calculator
      function calculateComplexity(\$code) {
          \$complexity = 1;
          
          // Count decision points
          \$patterns = [
              '/\\bif\\s*\\(/',
              '/\\belseif\\s*\\(/',
              '/\\bfor\\s*\\(/',
              '/\\bforeach\\s*\\(/',
              '/\\bwhile\\s*\\(/',
              '/\\bcase\\s+/',
              '/\\bcatch\\s*\\(/',
              '/\\?.*:/', // ternary
              '/&&/',
              '/\\|\\|/'
          ];
          
          foreach (\$patterns as \$pattern) {
              \$complexity += preg_match_all(\$pattern, \$code, \$matches);
          }
          
          return \$complexity;
      }
      
      \$files = is_file(\$target) ? [\$target] : glob(\$target . '/**/*.php');
      \$complexMethods = [];
      
      foreach (\$files as \$file) {
          if (!is_file(\$file)) continue;
          
          \$content = file_get_contents(\$file);
          
          // Find all methods
          preg_match_all(
              '/function\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{/i',
              \$content,
              \$matches,
              PREG_OFFSET_CAPTURE
          );
          
          foreach (\$matches[1] as \$i => \$match) {
              \$methodName = \$match[0];
              \$startPos = \$matches[0][\$i][1];
              
              // Extract method body (simplified)
              \$braceCount = 0;
              \$inMethod = false;
              \$methodBody = '';
              
              for (\$j = \$startPos; \$j < strlen(\$content); \$j++) {
                  if (\$content[\$j] === '{') {
                      \$braceCount++;
                      \$inMethod = true;
                  } elseif (\$content[\$j] === '}') {
                      \$braceCount--;
                  }
                  
                  if (\$inMethod) {
                      \$methodBody .= \$content[\$j];
                  }
                  
                  if (\$inMethod && \$braceCount === 0) {
                      break;
                  }
              }
              
              \$complexity = calculateComplexity(\$methodBody);
              
              if (\$complexity > \$threshold) {
                  \$lineNum = substr_count(substr(\$content, 0, \$startPos), \"\\n\") + 1;
                  
                  \$complexMethods[] = [
                      'file' => \$file,
                      'method' => \$methodName,
                      'complexity' => \$complexity,
                      'line' => \$lineNum
                  ];
              }
          }
      }
      
      if (!empty(\$complexMethods)) {
          echo \"Found \" . count(\$complexMethods) . \" complex methods (threshold: \$threshold):\\n\\n\";
          
          usort(\$complexMethods, function(\$a, \$b) {
              return \$b['complexity'] - \$a['complexity'];
          });
          
          foreach (\$complexMethods as \$method) {
              echo sprintf(
                  \"Complexity %d: %s::%s() at line %d\\n\",
                  \$method['complexity'],
                  basename(\$method['file'], '.php'),
                  \$method['method'],
                  \$method['line']
              );
          }
          
          echo \"\\nConsider refactoring these methods to reduce complexity\\n\";
      } else {
          echo \"✓ All methods are within complexity threshold\\n\";
      }
      "

  - name: Code Quality Metrics
    type: shell
    command: |
      php -r "
      echo \"\\n=== Code Quality Metrics ===\\n\";
      
      \$target = '${inputs.target}';
      \$metrics = [
          'total_files' => 0,
          'total_lines' => 0,
          'code_lines' => 0,
          'comment_lines' => 0,
          'blank_lines' => 0,
          'classes' => 0,
          'methods' => 0,
          'functions' => 0
      ];
      
      \$files = is_file(\$target) ? [\$target] : glob(\$target . '/**/*.php');
      
      foreach (\$files as \$file) {
          if (!is_file(\$file)) continue;
          
          \$metrics['total_files']++;
          \$lines = file(\$file, FILE_IGNORE_NEW_LINES);
          \$metrics['total_lines'] += count(\$lines);
          
          foreach (\$lines as \$line) {
              \$trimmed = trim(\$line);
              
              if (empty(\$trimmed)) {
                  \$metrics['blank_lines']++;
              } elseif (strpos(\$trimmed, '//') === 0 || strpos(\$trimmed, '/*') === 0 || strpos(\$trimmed, '*') === 0) {
                  \$metrics['comment_lines']++;
              } else {
                  \$metrics['code_lines']++;
              }
          }
          
          \$content = file_get_contents(\$file);
          \$metrics['classes'] += preg_match_all('/\\bclass\\s+\\w+/i', \$content, \$m);
          \$metrics['methods'] += preg_match_all('/\\bfunction\\s+\\w+\\s*\\(/i', \$content, \$m);
      }
      
      echo \"Files analyzed: \" . \$metrics['total_files'] . \"\\n\";
      echo \"Total lines: \" . number_format(\$metrics['total_lines']) . \"\\n\";
      echo \"Code lines: \" . number_format(\$metrics['code_lines']) . \" (\" . round(\$metrics['code_lines'] / \$metrics['total_lines'] * 100, 1) . \"%)\\n\";
      echo \"Comment lines: \" . number_format(\$metrics['comment_lines']) . \" (\" . round(\$metrics['comment_lines'] / \$metrics['total_lines'] * 100, 1) . \"%)\\n\";
      echo \"Blank lines: \" . number_format(\$metrics['blank_lines']) . \" (\" . round(\$metrics['blank_lines'] / \$metrics['total_lines'] * 100, 1) . \"%)\\n\";
      echo \"\\nClasses: \" . \$metrics['classes'] . \"\\n\";
      echo \"Methods/Functions: \" . \$metrics['methods'] . \"\\n\";
      
      if (\$metrics['classes'] > 0) {
          echo \"Avg methods per class: \" . round(\$metrics['methods'] / \$metrics['classes'], 1) . \"\\n\";
      }
      "

  - name: Generate Improvement Suggestions
    type: shell
    command: |
      php -r "
      echo \"\\n=== Code Improvement Suggestions ===\\n\";
      
      \$suggestions = [];
      
      // Load all analysis results
      \$hasSecurityIssues = file_exists('security_issues.json');
      \$hasStaticIssues = file_exists('phpstan_results.json');
      
      if (\$hasSecurityIssues) {
          \$securityIssues = json_decode(file_get_contents('security_issues.json'), true);
          
          \$suggestions[] = [
              'priority' => 'HIGH',
              'category' => 'Security',
              'suggestion' => 'Fix ' . count(\$securityIssues) . ' security vulnerabilities',
              'details' => 'Run workflow with --fix_issues=true to auto-fix some issues'
          ];
      }
      
      \$suggestions[] = [
          'priority' => 'MEDIUM',
          'category' => 'Testing',
          'suggestion' => 'Add unit tests for critical business logic',
          'details' => 'Focus on Invoice, Retrocession, and Payment models'
      ];
      
      \$suggestions[] = [
          'priority' => 'LOW',
          'category' => 'Documentation',
          'suggestion' => 'Add PHPDoc comments to public methods',
          'details' => 'Improves IDE support and code understanding'
      ];
      
      foreach (\$suggestions as \$s) {
          echo \"[\" . \$s['priority'] . \"] \" . \$s['category'] . \": \" . \$s['suggestion'] . \"\\n\";
          echo \"    \" . \$s['details'] . \"\\n\\n\";
      }
      
      // Save comprehensive report
      \$report = [
          'timestamp' => date('c'),
          'target' => '${inputs.target}',
          'analysis_type' => '${inputs.analysis_type}',
          'suggestions' => \$suggestions
      ];
      
      \$reportFile = 'storage/logs/code-analysis-' . date('Y-m-d-His') . '.json';
      file_put_contents(\$reportFile, json_encode(\$report, JSON_PRETTY_PRINT));
      
      echo \"Full analysis report saved to: \$reportFile\\n\";
      
      // Cleanup
      @unlink('security_issues.json');
      @unlink('phpstan_results.json');
      "

  - name: Auto Fix Issues
    type: conditional
    condition: "{{ inputs.fix_issues }}"
    steps:
      - name: Apply Fixes
        type: shell
        command: |
          echo -e "\n=== Applying Automated Fixes ==="
          
          # Run PHP Code Beautifier and Fixer
          if [ -f "vendor/bin/phpcbf" ]; then
            echo "Running PHP Code Beautifier..."
            vendor/bin/phpcbf ${inputs.target} --standard=PSR12 || true
          fi
          
          # Clear cache after fixes
          if [ -f "clear-cache.php" ]; then
            php clear-cache.php
          fi
          
          echo "Automated fixes applied where possible"

on_error:
  - name: Log Analysis Error
    type: shell
    command: |
      echo "Code analysis error: {{ error_message }}" >> storage/logs/code-analysis-errors.log

on_success:
  - name: Log Success
    type: shell
    command: |
      echo "Code analysis completed successfully at $(date)" >> storage/logs/code-analysis.log