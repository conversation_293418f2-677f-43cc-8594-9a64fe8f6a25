<?php
/**
 * Copy Retrocession Amounts to Different Years
 * 
 * This script helps copy retrocession amounts from one year to another,
 * useful after adding year support to the table.
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage() . "\n");
}

// Parse command line arguments
$options = getopt('', ['from:', 'to:', 'user:', 'confirm']);

$fromYear = $options['from'] ?? null;
$toYear = $options['to'] ?? null;
$userId = $options['user'] ?? null;
$confirm = isset($options['confirm']);

if (!$fromYear || !$toYear) {
    echo "Usage: php copy_retrocession_amounts_to_year.php --from=2025 --to=2026 [--user=123] [--confirm]\n";
    echo "\nOptions:\n";
    echo "  --from    Source year to copy from\n";
    echo "  --to      Target year to copy to\n";
    echo "  --user    Optional: Copy for specific user only\n";
    echo "  --confirm Execute the copy (without this, it's a dry run)\n";
    exit(1);
}

echo "=== Copy Retrocession Amounts ===\n";
echo "From year: $fromYear\n";
echo "To year: $toYear\n";
if ($userId) {
    echo "User ID: $userId\n";
}
echo "Mode: " . ($confirm ? "EXECUTE" : "DRY RUN") . "\n\n";

// Check if year column exists
$stmt = $pdo->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
if ($stmt->rowCount() == 0) {
    die("Error: Year column does not exist. Please run migration 104 first.\n");
}

// Build query to check existing data
$sql = "SELECT 
    u.id,
    u.first_name,
    u.last_name,
    uma.month,
    uma.cns_amount,
    uma.patient_amount
FROM user_monthly_retrocession_amounts uma
JOIN users u ON u.id = uma.user_id
WHERE uma.year = :from_year";

$params = ['from_year' => $fromYear];

if ($userId) {
    $sql .= " AND u.id = :user_id";
    $params['user_id'] = $userId;
}

$sql .= " ORDER BY u.id, uma.month";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$sourceData = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($sourceData)) {
    die("No data found for year $fromYear\n");
}

echo "Found " . count($sourceData) . " records to copy\n\n";

// Check for existing data in target year
$sql = "SELECT COUNT(*) FROM user_monthly_retrocession_amounts WHERE year = :year";
if ($userId) {
    $sql .= " AND user_id = :user_id";
}
$stmt = $pdo->prepare($sql);
$params = ['year' => $toYear];
if ($userId) {
    $params['user_id'] = $userId;
}
$stmt->execute($params);
$existingCount = $stmt->fetchColumn();

if ($existingCount > 0) {
    echo "WARNING: Found $existingCount existing records for year $toYear\n";
    echo "These will be skipped (duplicate key protection)\n\n";
}

// Display sample of data to be copied
echo "Sample data to be copied:\n";
echo str_repeat('-', 80) . "\n";
echo sprintf("%-20s | %5s | %12s | %12s\n", "User", "Month", "CNS Amount", "Patient Amt");
echo str_repeat('-', 80) . "\n";

$count = 0;
foreach ($sourceData as $row) {
    if ($count < 5 || $count >= count($sourceData) - 2) {
        printf("%-20s | %5d | %12.2f | %12.2f\n",
            substr($row['first_name'] . ' ' . $row['last_name'], 0, 20),
            $row['month'],
            $row['cns_amount'],
            $row['patient_amount']
        );
    } elseif ($count == 5) {
        echo "... (" . (count($sourceData) - 7) . " more records) ...\n";
    }
    $count++;
}
echo str_repeat('-', 80) . "\n\n";

if (!$confirm) {
    echo "This is a DRY RUN. To execute, add --confirm flag\n";
    exit(0);
}

// Execute the copy
echo "Copying data...\n";

$pdo->beginTransaction();

try {
    $insertStmt = $pdo->prepare("
        INSERT IGNORE INTO user_monthly_retrocession_amounts 
        (user_id, month, year, cns_amount, patient_amount)
        VALUES (:user_id, :month, :year, :cns_amount, :patient_amount)
    ");
    
    $copied = 0;
    foreach ($sourceData as $row) {
        $result = $insertStmt->execute([
            'user_id' => $row['id'],
            'month' => $row['month'],
            'year' => $toYear,
            'cns_amount' => $row['cns_amount'],
            'patient_amount' => $row['patient_amount']
        ]);
        if ($insertStmt->rowCount() > 0) {
            $copied++;
        }
    }
    
    $pdo->commit();
    
    echo "\n✓ Successfully copied $copied records from $fromYear to $toYear\n";
    
    if ($copied < count($sourceData)) {
        echo "ℹ " . (count($sourceData) - $copied) . " records were skipped (already existed)\n";
    }
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "\n✗ Error during copy: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nDone!\n";
?>