<!DOCTYPE html>
<html lang="{{ app_locale|default('en') }}" data-bs-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('auth.login') }} - {{ app_name|default('Fit360 AdminDesk') }}</title>
    <meta name="description" content="{{ __('auth.login_description') }}">
    <link rel="icon" type="image/x-icon" href="{{ base_url }}/favicon.ico">
    
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Modern Theme CSS -->
    <link rel="stylesheet" href="{{ base_url }}/assets/css/modern-theme.css">
    
    <style>
        /* Reset any global styles that might interfere */
        html {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        
        body.login-page {
            height: 100%;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-direction: column !important;
            font-size: 16px !important; /* Override mobile-responsive.css */
            line-height: 1.5 !important; /* Override mobile-responsive.css */
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-floating > .form-control:focus ~ label {
            color: #667eea;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
        }
        
        /* Login wrapper for proper centering */
        .login-wrapper {
            width: 100%;
            max-width: 400px;
            padding: 0 15px;
        }
        
        /* Mobile responsiveness for login */
        @media (max-width: 767px) {
            .login-card {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
            
            .login-header, .login-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body class="login-page">
    <div class="login-wrapper">
        <div class="login-card animate__animated animate__fadeIn">
                    <div class="login-header">
                        <i class="bi bi-heart-pulse-fill fs-1 mb-3"></i>
                        <h3 class="mb-0">{{ app_name|default('Fit360 AdminDesk') }}</h3>
                        <p class="mb-0 opacity-75">{{ __('auth.login_subtitle') }}</p>
                    </div>
                    
                    <div class="login-body">
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endif %}
                        
                        {% if success %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ success }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endif %}
                        
                        <form method="POST" action="{{ base_url }}/login">
                            {{ csrf_field() }}
                            
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="{{ __('auth.username_or_email') }}" required autofocus>
                                <label for="username">{{ __('auth.username_or_email') }}</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="{{ __('auth.password') }}" required>
                                <label for="password">{{ __('auth.password') }}</label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    {{ __('auth.remember_me') }}
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                {{ __('auth.login') }}
                            </button>
                            
                            <div class="text-center">
                                <a href="{{ base_url }}/forgot-password" class="text-decoration-none">
                                    {{ __('auth.forgot_password') }}
                                </a>
                            </div>
                        </form>
                    </div>
        </div>
        
        <p class="text-center text-white mt-4">
            &copy; {{ "now"|date("Y") }} {{ app_name|default('Fit360 AdminDesk') }}. {{ __('common.all_rights_reserved') }}
        </p>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>