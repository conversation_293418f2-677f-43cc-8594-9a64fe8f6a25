<?php
session_start();
require_once 'vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Initialize Flight's database connection before bootstrap
Flight::register('db', 'PDO', [
    'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
    $_ENV['DB_USERNAME'],
    $_ENV['DB_PASSWORD'],
    [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]
]);

require_once 'app/config/bootstrap.php';

// Simulate logged in admin user
$_SESSION['user_id'] = 1; // Admin user

// Test parameters
$userId = $_GET['user_id'] ?? 1; // Frank <PERSON> by default
$month = $_GET['month'] ?? 7; // July
$year = $_GET['year'] ?? 2025;

echo "<h2>Testing Retrocession Generation</h2>\n";
echo "User ID: $userId, Month: $month, Year: $year\n\n";

try {
    // Initialize the generator
    echo "1. Initializing UnifiedInvoiceGenerator...\n";
    $generator = new \App\Services\UnifiedInvoiceGenerator(
        $userId,
        'RET',
        $month,
        $year,
        $_SESSION['user_id']
    );
    
    echo "2. Calling generate() method...\n";
    $result = $generator->generate();
    
    echo "\n<h3>Result:</h3>\n";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    if (!$result['success']) {
        echo "\n<h3>Debugging the failure:</h3>\n";
        
        // Check if invoice already exists
        $db = Flight::db();
        
        // First, let's see if we need to get client_id differently
        echo "\n<h4>Finding client for user:</h4>\n";
        $stmt = $db->prepare("
            SELECT u.*, c.id as client_id, c.name as client_name, c.is_practitioner
            FROM users u
            LEFT JOIN clients c ON c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name
            WHERE u.id = :user_id
        ");
        $stmt->execute(['user_id' => $userId]);
        $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "User: {$userInfo['first_name']} {$userInfo['last_name']}\n";
        echo "Client ID: " . ($userInfo['client_id'] ?? 'NOT FOUND') . "\n";
        echo "Is Practitioner: " . ($userInfo['is_practitioner'] ? 'Yes' : 'No') . "\n";
        
        // Check for existing invoice
        echo "\n<h4>Checking for existing invoice:</h4>\n";
        $stmt = $db->prepare("
            SELECT ugi.*, i.invoice_number 
            FROM user_generated_invoices ugi
            LEFT JOIN invoices i ON i.id = ugi.invoice_id
            WHERE ugi.user_id = :user_id 
            AND ugi.invoice_type = 'RET'
            AND ugi.period_month = :month
            AND ugi.period_year = :year
        ");
        $stmt->execute([
            'user_id' => $userId,
            'month' => $month,
            'year' => $year
        ]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($existing) {
            echo "Found existing invoice: #{$existing['invoice_id']} - {$existing['invoice_number']}\n";
        } else {
            echo "No existing invoice found\n";
        }
        
        // Check monthly amounts
        echo "\n<h4>Checking monthly amounts:</h4>\n";
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts
            WHERE user_id = :user_id AND month = :month AND year = :year
        ");
        $stmt->execute([
            'user_id' => $userId,
            'month' => $month,
            'year' => $year
        ]);
        $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($amounts) {
            echo "CNS Amount: {$amounts['cns_amount']}€\n";
            echo "Patient Amount: {$amounts['patient_amount']}€\n";
        } else {
            echo "No amounts found for this month/year\n";
        }
        
        // Check retrocession settings
        echo "\n<h4>Checking retrocession settings:</h4>\n";
        $stmt = $db->prepare("
            SELECT * FROM user_retrocession_settings
            WHERE user_id = :user_id
            ORDER BY id DESC LIMIT 1
        ");
        $stmt->execute(['user_id' => $userId]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($settings) {
            echo "CNS: {$settings['cns_value']}{$settings['cns_type']}\n";
            echo "Patient: {$settings['patient_value']}{$settings['patient_type']}\n";
        } else {
            echo "No retrocession settings found\n";
        }
    }
    
} catch (Exception $e) {
    echo "\n<h3>ERROR:</h3>\n";
    echo "Message: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\n<h4>Stack trace:</h4>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "\n\n<p>Test different users:</p>";
echo "<a href='?user_id=1&month=7&year=2025'>Frank Huet (July 2025)</a> | ";
echo "<a href='?user_id=10&month=7&year=2025'>Ismail Lakouar (July 2025)</a> | ";
echo "<a href='?user_id=12&month=7&year=2025'>Lyse Henry (July 2025)</a> | ";
echo "<a href='?user_id=18&month=7&year=2025'>Rémi Heine (July 2025)</a>";