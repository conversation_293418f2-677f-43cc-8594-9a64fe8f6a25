<?php
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Invoice Save Issue</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .debug-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            background: #f9f9f9;
        }
        .error { color: red; font-weight: bold; }
        .success { color: green; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .code { 
            background: #333; 
            color: #fff; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
        }
        button:hover { background: #0056b3; }
        .fix-button {
            background: #28a745;
        }
        .fix-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Invoice Save Issue</h1>
    
    <div class="debug-section">
        <h2>Problem: "Save and Send" button not working</h2>
        <p>This tool will help identify why the invoice form isn't submitting when clicking "Enregistrer et envoyer".</p>
    </div>

    <div class="debug-section">
        <h2>Step 1: Open Invoice Form</h2>
        <p>First, open the invoice creation form in a new tab:</p>
        <a href="/fit/public/invoices/create?type=loyer" target="_blank">
            <button>Open LOY Invoice Form</button>
        </a>
    </div>

    <div class="debug-section">
        <h2>Step 2: Check Common Issues</h2>
        <p>Copy and run these commands in the browser console (F12) on the invoice page:</p>
        
        <h3>Check 1: Form and Action Field</h3>
        <div class="code">
// Check if form exists and action field conflict
const form = document.getElementById('invoiceForm');
const actionInputs = form ? form.querySelectorAll('input[name="action"]') : [];
console.log('Form found:', !!form);
console.log('Action inputs:', actionInputs.length);

// Check for the hidden action field
const hiddenAction = document.getElementById('formAction');
console.log('Hidden action field:', hiddenAction?.name, 'Value:', hiddenAction?.value);

// Check submit buttons
const submitBtns = document.querySelectorAll('button[form="invoiceForm"]');
submitBtns.forEach(btn => {
    console.log('Button:', btn.textContent.trim(), 'Name:', btn.name, 'Value:', btn.value);
});
        </div>

        <h3>Check 2: Required Fields</h3>
        <div class="code">
// Find all invalid required fields
const form = document.getElementById('invoiceForm');
const invalid = form.querySelectorAll(':invalid');
console.log('Invalid fields:', invalid.length);
invalid.forEach(field => {
    console.error('Invalid:', field.name || field.id, '- Reason:', field.validationMessage);
});
        </div>

        <h3>Check 3: Form Data</h3>
        <div class="code">
// See what data would be submitted
const form = document.getElementById('invoiceForm');
const formData = new FormData(form);
console.log('=== Form Data ===');
for (let [key, value] of formData.entries()) {
    if (key.includes('action')) {
        console.warn(key + ':', value);
    } else {
        console.log(key + ':', value);
    }
}
        </div>
    </div>

    <div class="debug-section">
        <h2>Step 3: Apply Fix</h2>
        <p class="warning">⚠️ The issue is likely a conflict between the hidden action field and button name="action"</p>
        
        <h3>Quick Fix (Run in console):</h3>
        <div class="code">
// Fix the action field conflict
const form = document.getElementById('invoiceForm');
const hiddenAction = document.getElementById('formAction');

// Remove the name attribute from hidden action to avoid conflict
if (hiddenAction) {
    hiddenAction.removeAttribute('name');
    console.log('✅ Removed name attribute from hidden action field');
}

// Update submit button handlers
const submitButtons = document.querySelectorAll('button[form="invoiceForm"]');
submitButtons.forEach(button => {
    // Remove existing listeners
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    // Add new listener
    newButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Create hidden input for action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = this.value;
        form.appendChild(actionInput);
        
        console.log('Submitting with action:', this.value);
        
        // Submit form
        if (form.checkValidity()) {
            form.submit();
        } else {
            form.reportValidity();
        }
    });
});

console.log('✅ Fix applied! Try clicking "Save and Send" now.');
        </div>
    </div>

    <div class="debug-section">
        <h2>Step 4: Permanent Fix</h2>
        <p>The permanent fix needs to be applied to the template file. The issue is:</p>
        <ol>
            <li>There's a hidden input: <code>&lt;input type="hidden" name="action" id="formAction" value="save"&gt;</code></li>
            <li>The buttons also have: <code>name="action" value="save_and_send"</code></li>
            <li>This creates a conflict where both values are submitted</li>
        </ol>
        
        <p><strong>Solution:</strong> Remove the hidden action field from the form since the buttons already provide the action value.</p>
    </div>

    <div class="debug-section">
        <h2>Test Submission</h2>
        <div class="code">
// Test form submission with save_and_send
const form = document.getElementById('invoiceForm');

// Temporarily add action for save_and_send
const tempAction = document.createElement('input');
tempAction.type = 'hidden';
tempAction.name = 'action';
tempAction.value = 'save_and_send';
form.appendChild(tempAction);

// Check validity
if (form.checkValidity()) {
    console.log('✅ Form is valid! Ready to submit with action: save_and_send');
    console.log('Run form.submit() to submit');
} else {
    console.error('❌ Form is invalid');
    form.reportValidity();
}
        </div>
    </div>
</body>
</html>