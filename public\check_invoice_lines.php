<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

$invoiceId = $_GET['id'] ?? null;

echo "=== CHECKING INVOICE LINES ===\n\n";

try {
    $db = Flight::db();
    
    if ($invoiceId) {
        // Check specific invoice
        echo "Checking invoice ID: $invoiceId\n\n";
        
        // Get invoice details
        $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "Invoice found:\n";
            echo "- Number: {$invoice['invoice_number']}\n";
            echo "- Status: {$invoice['status']}\n";
            echo "- Total: {$invoice['total']}\n";
            echo "- Created: {$invoice['created_at']}\n\n";
            
            // Get invoice lines
            echo "Invoice lines:\n";
            $stmt = $db->prepare("
                SELECT * FROM invoice_lines 
                WHERE invoice_id = ? 
                ORDER BY sort_order ASC, id ASC
            ");
            $stmt->execute([$invoiceId]);
            $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            if (count($lines) > 0) {
                foreach ($lines as $i => $line) {
                    echo sprintf("%d. %s - Qty: %.2f x %.2f€ = %.2f€ (VAT: %.2f%%)\n",
                        $i + 1,
                        $line['description'],
                        $line['quantity'],
                        $line['unit_price'],
                        $line['total'],
                        $line['vat_rate']
                    );
                }
                echo "\nTotal lines: " . count($lines) . "\n";
            } else {
                echo "⚠️  No lines found for this invoice!\n";
            }
        } else {
            echo "Invoice not found!\n";
        }
    } else {
        // Show recent invoices with line counts
        echo "Recent invoices with line counts:\n\n";
        $stmt = $db->query("
            SELECT i.id, i.invoice_number, i.status, i.total,
                   COUNT(il.id) as line_count
            FROM invoices i
            LEFT JOIN invoice_lines il ON i.id = il.invoice_id
            GROUP BY i.id
            ORDER BY i.created_at DESC
            LIMIT 10
        ");
        $invoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        foreach ($invoices as $inv) {
            echo sprintf("ID: %d | %s | Lines: %d | Total: %.2f€ | Status: %s\n",
                $inv['id'],
                $inv['invoice_number'],
                $inv['line_count'],
                $inv['total'],
                $inv['status']
            );
        }
        
        echo "\nTo check a specific invoice, add ?id=XXX to the URL\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}