<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Cleaning RET Types</h2>";
    
    // First, show what we have
    echo "<h3>Current RET-related types:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code LIKE 'ret%' OR prefix LIKE '%RET%' ORDER BY id");
    $existing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($existing as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['code']}</td>";
        echo "<td>{$row['prefix']}</td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Clean up - we want to keep only what we need
    echo "<h3>Cleaning up duplicates:</h3>";
    
    // Delete the problematic entries and start fresh
    $toDelete = [];
    foreach ($existing as $row) {
        if (in_array($row['code'], ['retr', 'ret3', 'ret2', 'retrocession_5', 'retrocession_10'])) {
            $toDelete[] = $row['id'];
        }
    }
    
    if (!empty($toDelete)) {
        $placeholders = implode(',', array_fill(0, count($toDelete), '?'));
        $stmt = $db->prepare("DELETE FROM config_invoice_types WHERE id IN ($placeholders)");
        $stmt->execute($toDelete);
        echo "<p style='color: orange;'>Deleted " . count($toDelete) . " duplicate/problematic entries</p>";
    }
    
    // Now create clean entries
    echo "<h3>Creating clean RET types:</h3>";
    
    // Check if we still have a base RET type
    $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = 'ret' AND prefix = 'RET'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
            VALUES ('ret', 'RET', :name, '#17a2b8', 1)
        ");
        $stmt->execute([
            'name' => json_encode(['fr' => 'Rétrocession', 'en' => 'Retrocession'])
        ]);
        echo "<p style='color: green;'>✓ Created base RET type</p>";
    }
    
    // Create RET25
    $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = 'ret25'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
            VALUES ('ret25', 'FAC-RET25', :name, '#17a2b8', 1)
        ");
        $stmt->execute([
            'name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%'])
        ]);
        echo "<p style='color: green;'>✓ Created RET25 type</p>";
    }
    
    // Create RET30
    $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = 'ret30'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
            VALUES ('ret30', 'FAC-RET30', :name, '#17a2b8', 1)
        ");
        $stmt->execute([
            'name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%'])
        ]);
        echo "<p style='color: green;'>✓ Created RET30 type</p>";
    }
    
    // Also ensure they exist in invoice_types table
    echo "<h3>Ensuring invoice_types table is synchronized:</h3>";
    
    $invoiceTypes = [
        ['code' => 'RET', 'name' => 'Rétrocession'],
        ['code' => 'RET25', 'name' => 'Rétrocession 5%'],
        ['code' => 'RET30', 'name' => 'Rétrocession 10%']
    ];
    
    foreach ($invoiceTypes as $type) {
        $stmt = $db->prepare("SELECT id FROM invoice_types WHERE code = :code");
        $stmt->execute(['code' => $type['code']]);
        if (!$stmt->fetch()) {
            $stmt = $db->prepare("
                INSERT INTO invoice_types (code, name, is_active)
                VALUES (:code, :name, 1)
            ");
            $stmt->execute($type);
            echo "<p style='color: green;'>✓ Created {$type['code']} in invoice_types</p>";
        } else {
            echo "<p>✓ {$type['code']} already exists in invoice_types</p>";
        }
    }
    
    // Show final state
    echo "<h3>Final configuration:</h3>";
    $stmt = $db->query("
        SELECT cit.*, it.id as invoice_type_id 
        FROM config_invoice_types cit
        LEFT JOIN invoice_types it ON it.code = UPPER(cit.code)
        WHERE cit.code IN ('ret', 'ret25', 'ret30') 
        ORDER BY cit.code
    ");
    $final = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Config Type</th><th>Code</th><th>Prefix</th><th>Name</th><th>Invoice Type ID</th></tr>";
    foreach ($final as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        echo "<tr>";
        echo "<td>config_invoice_types</td>";
        echo "<td>{$row['code']}</td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td>" . ($row['invoice_type_id'] ?: 'Not linked') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<p><strong>Configuration complete!</strong></p>";
    echo "<p>The system will now generate:</p>";
    echo "<ul>";
    echo "<li><strong>FAC-RET25-2025-XXXX</strong> for users with 5% secretary fee</li>";
    echo "<li><strong>FAC-RET30-2025-XXXX</strong> for users with 10% secretary fee</li>";
    echo "</ul>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession' class='btn btn-primary'>Test Bulk Generation</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}