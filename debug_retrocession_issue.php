<?php

// Debug script for retrocession invoice generation issues
require_once 'vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Retrocession Invoice Generation Debug</h2>\n\n";
    
    // 1. Check if table exists
    echo "<h3>1. Table Structure Check</h3>\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_monthly_retrocession_amounts'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Table 'user_monthly_retrocession_amounts' exists\n\n";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE user_monthly_retrocession_amounts");
        echo "Table structure:\n";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
        }
        echo "\n";
    } else {
        echo "✗ Table 'user_monthly_retrocession_amounts' does NOT exist!\n\n";
    }
    
    // 2. Check for data
    echo "<h3>2. Monthly Amounts Data</h3>\n";
    $stmt = $pdo->query("
        SELECT u.id, u.first_name, u.last_name, 
               COUNT(uma.id) as amount_count,
               SUM(uma.cns_amount) as total_cns,
               SUM(uma.patient_amount) as total_patient
        FROM users u
        LEFT JOIN user_monthly_retrocession_amounts uma ON u.id = uma.user_id
        WHERE 1=1
        GROUP BY u.id
        HAVING amount_count > 0
    ");
    
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (count($results) > 0) {
        echo "Users with monthly amounts configured:\n";
        foreach ($results as $row) {
            echo "  - User #{$row['id']}: {$row['first_name']} {$row['last_name']} - ";
            echo "{$row['amount_count']} months, Total CNS: {$row['total_cns']}€, Total Patient: {$row['total_patient']}€\n";
        }
        echo "\n";
    } else {
        echo "✗ No users have monthly amounts configured!\n\n";
    }
    
    // 3. Check retrocession settings
    echo "<h3>3. Retrocession Settings</h3>\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_retrocession_settings'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Table 'user_retrocession_settings' exists\n";
        
        $stmt = $pdo->query("
            SELECT u.id, u.first_name, u.last_name, 
                   urs.cns_value, urs.cns_type, urs.patient_value, urs.patient_type
            FROM users u
            INNER JOIN user_retrocession_settings urs ON u.id = urs.user_id
            WHERE 1=1
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (count($results) > 0) {
            echo "Users with retrocession settings:\n";
            foreach ($results as $row) {
                echo "  - User #{$row['id']}: {$row['first_name']} {$row['last_name']} - ";
                echo "CNS: {$row['cns_value']}{$row['cns_type']}, Patient: {$row['patient_value']}{$row['patient_type']}\n";
            }
        } else {
            echo "  No users have retrocession settings configured\n";
        }
        echo "\n";
    } else {
        echo "✗ Table 'user_retrocession_settings' does NOT exist!\n\n";
    }
    
    // 4. Check user_generated_invoices
    echo "<h3>4. Generated Invoices Tracking</h3>\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_generated_invoices'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Table 'user_generated_invoices' exists\n";
        
        // First check what columns exist
        $stmt = $pdo->query("DESCRIBE user_generated_invoices");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }
        
        $stmt = $pdo->query("
            SELECT invoice_type, COUNT(*) as count
            FROM user_generated_invoices
            GROUP BY invoice_type
        ");
        
        echo "Invoice generation summary:\n";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "  - Type {$row['invoice_type']}: {$row['count']} invoices\n";
        }
        echo "\n";
    } else {
        echo "✗ Table 'user_generated_invoices' does NOT exist!\n\n";
    }
    
    // 5. Check for missing client relationships
    echo "<h3>5. User-Client Relationships</h3>\n";
    $stmt = $pdo->query("
        SELECT u.id, u.first_name, u.last_name, u.client_id,
               c.name as client_name, c.is_practitioner
        FROM users u
        LEFT JOIN clients c ON u.client_id = c.id
        WHERE 1=1
        AND u.client_id IS NOT NULL
    ");
    
    $missing_practitioners = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        if ($row['client_id'] && !$row['is_practitioner']) {
            $missing_practitioners++;
            echo "⚠️ User #{$row['id']} {$row['first_name']} {$row['last_name']} - ";
            echo "Client #{$row['client_id']} is NOT marked as practitioner\n";
        }
    }
    
    if ($missing_practitioners == 0) {
        echo "✓ All users with client_id have practitioner clients\n";
    }
    echo "\n";
    
    // 6. Common error scenarios
    echo "<h3>6. Common Error Scenarios</h3>\n";
    
    // Check for users without client_id
    $stmt = $pdo->query("
        SELECT COUNT(*) FROM users 
        WHERE (client_id IS NULL OR client_id = 0)
    ");
    $count = $stmt->fetchColumn();
    if ($count > 0) {
        echo "⚠️ {$count} users have no client_id set\n";
    }
    
    // Check for year column
    $stmt = $pdo->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYear = $stmt->rowCount() > 0;
    echo "Year column in monthly amounts: " . ($hasYear ? "✓ Yes" : "✗ No") . "\n";
    
    // Check invoice types
    $stmt = $pdo->query("SELECT * FROM invoice_types WHERE code = 'RET'");
    $retType = $stmt->fetch();
    if ($retType) {
        echo "✓ Invoice type 'RET' exists (ID: {$retType['id']})\n";
    } else {
        echo "✗ Invoice type 'RET' does NOT exist!\n";
    }
    
    echo "\n<h3>Recommendations:</h3>\n";
    echo "1. Ensure users have monthly amounts configured (CNS/Patient)\n";
    echo "2. Verify users have client_id set and client is marked as practitioner\n";
    echo "3. Check that retrocession settings are configured\n";
    echo "4. Ensure invoice type 'RET' exists in invoice_types table\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}