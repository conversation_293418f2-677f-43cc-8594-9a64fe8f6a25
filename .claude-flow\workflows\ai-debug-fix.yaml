name: AI Debug and Fix
description: AI-powered debugging workflow that analyzes errors, suggests fixes, and can automatically implement solutions
version: 1.0.0
author: Claude-Flow

inputs:
  - name: error_message
    type: string
    description: Error message or issue description to debug
    required: false
    default: ""
    
  - name: file_path
    type: string
    description: Specific file to analyze (optional)
    required: false
    default: ""
    
  - name: auto_fix
    type: boolean
    description: Automatically apply suggested fixes
    required: false
    default: false
    
  - name: context_lines
    type: number
    description: Number of context lines to show around errors
    required: false
    default: 10
    
  - name: search_depth
    type: string
    description: How deep to search for related issues (shallow, medium, deep)
    required: false
    default: "medium"

env:
  app_root: "."
  ai_model: "${AI_MODEL:-gpt-4}"
  max_tokens: "${MAX_TOKENS:-4000}"

steps:
  - name: Collect Error Context
    type: shell
    command: |
      php -r "
      echo \"=== Collecting Error Context ===\\n\";
      
      \$errorMessage = '${inputs.error_message}';
      \$filePath = '${inputs.file_path}';
      \$contextData = [];
      
      // Check recent error logs
      \$errorLogs = [
          'storage/logs/error.log',
          'storage/logs/app.log',
          'storage/logs/php-errors.log'
      ];
      
      foreach (\$errorLogs as \$log) {
          if (file_exists(\$log)) {
              \$lines = file(\$log, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
              \$recentErrors = array_slice(\$lines, -20);
              
              if (!empty(\$recentErrors)) {
                  \$contextData['recent_errors'][\$log] = \$recentErrors;
              }
          }
      }
      
      // If error message provided, search for it in logs
      if (\$errorMessage) {
          echo \"Searching for error: \$errorMessage\\n\";
          
          foreach (\$errorLogs as \$log) {
              if (file_exists(\$log)) {
                  \$content = file_get_contents(\$log);
                  if (stripos(\$content, \$errorMessage) !== false) {
                      echo \"Found error in: \$log\\n\";
                      
                      // Extract relevant lines
                      \$lines = explode(\"\\n\", \$content);
                      foreach (\$lines as \$lineNum => \$line) {
                          if (stripos(\$line, \$errorMessage) !== false) {
                              \$start = max(0, \$lineNum - ${inputs.context_lines});
                              \$end = min(count(\$lines) - 1, \$lineNum + ${inputs.context_lines});
                              
                              \$contextData['error_context'][] = [
                                  'file' => \$log,
                                  'line' => \$lineNum + 1,
                                  'context' => array_slice(\$lines, \$start, \$end - \$start + 1)
                              ];
                          }
                      }
                  }
              }
          }
      }
      
      // Analyze specific file if provided
      if (\$filePath && file_exists(\$filePath)) {
          echo \"Analyzing file: \$filePath\\n\";
          \$contextData['target_file'] = [
              'path' => \$filePath,
              'size' => filesize(\$filePath),
              'modified' => date('Y-m-d H:i:s', filemtime(\$filePath))
          ];
      }
      
      file_put_contents('debug_context.json', json_encode(\$contextData, JSON_PRETTY_PRINT));
      echo \"Context data collected and saved to debug_context.json\\n\";
      "
    output: context_collection

  - name: Search for Related Code Issues
    type: shell
    command: |
      php -r "
      echo \"\\n=== Searching for Related Issues ===\\n\";
      
      \$errorMessage = '${inputs.error_message}';
      \$searchDepth = '${inputs.search_depth}';
      \$patterns = [];
      
      // Extract potential problem patterns from error
      if (\$errorMessage) {
          // Look for class names
          if (preg_match('/Class [\'\"\\\\\\\\]?([\\\\w\\\\\\\\]+)[\'\"\\\\s]/', \$errorMessage, \$matches)) {
              \$patterns['class'] = \$matches[1];
          }
          
          // Look for method names
          if (preg_match('/(->|::)([\\\\w]+)\\\\(/', \$errorMessage, \$matches)) {
              \$patterns['method'] = \$matches[2];
          }
          
          // Look for file paths
          if (preg_match('/in\\\\s+([^\\\\s:]+\\\\.php)/', \$errorMessage, \$matches)) {
              \$patterns['file'] = \$matches[1];
          }
          
          // Look for SQL errors
          if (preg_match('/SQL.*?:(.+?)at/is', \$errorMessage, \$matches)) {
              \$patterns['sql'] = trim(\$matches[1]);
          }
      }
      
      \$findings = [];
      
      // Search for patterns in codebase
      if (!empty(\$patterns)) {
          echo \"Found patterns to search: \" . json_encode(\$patterns) . \"\\n\";
          
          // Search in PHP files
          \$searchDirs = ['app/', 'public/'];
          if (\$searchDepth === 'deep') {
              \$searchDirs[] = 'vendor/';
          }
          
          foreach (\$searchDirs as \$dir) {
              if (is_dir(\$dir)) {
                  \$iterator = new RecursiveIteratorIterator(
                      new RecursiveDirectoryIterator(\$dir)
                  );
                  
                  foreach (\$iterator as \$file) {
                      if (\$file->isFile() && \$file->getExtension() === 'php') {
                          \$content = file_get_contents(\$file->getPathname());
                          
                          foreach (\$patterns as \$type => \$pattern) {
                              if (stripos(\$content, \$pattern) !== false) {
                                  \$findings[] = [
                                      'type' => \$type,
                                      'pattern' => \$pattern,
                                      'file' => \$file->getPathname(),
                                      'line' => \$this->findLineNumber(\$content, \$pattern)
                                  ];
                              }
                          }
                      }
                  }
              }
          }
      }
      
      echo \"Found \" . count(\$findings) . \" related code locations\\n\";
      file_put_contents('related_findings.json', json_encode(\$findings, JSON_PRETTY_PRINT));
      
      function findLineNumber(\$content, \$pattern) {
          \$lines = explode(\"\\n\", \$content);
          foreach (\$lines as \$num => \$line) {
              if (stripos(\$line, \$pattern) !== false) {
                  return \$num + 1;
              }
          }
          return 0;
      }
      "

  - name: Analyze Database Issues
    type: conditional
    condition: "{{ contains(inputs.error_message, 'SQL') || contains(inputs.error_message, 'database') || contains(inputs.error_message, 'PDO') }}"
    steps:
      - name: Check Database Structure
        type: shell
        command: |
          php -r "
          echo \"\\n=== Analyzing Database Issues ===\\n\";
          
          require_once 'vendor/autoload.php';
          \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
          \$dotenv->load();
          
          try {
              \$pdo = new PDO(
                  'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
                  \$_ENV['DB_USER'],
                  \$_ENV['DB_PASS']
              );
              
              \$errorMessage = '${inputs.error_message}';
              
              // Extract table name from error
              if (preg_match('/[\'` ](\\w+)[\'` ]/', \$errorMessage, \$matches)) {
                  \$tableName = \$matches[1];
                  echo \"Checking table: \$tableName\\n\";
                  
                  // Check if table exists
                  \$stmt = \$pdo->query(\"SHOW TABLES LIKE '\$tableName'\");
                  if (\$stmt->rowCount() === 0) {
                      echo \"⚠️ Table '\$tableName' does not exist!\\n\";
                      
                      // Look for similar tables
                      \$stmt = \$pdo->query(\"SHOW TABLES\");
                      \$tables = \$stmt->fetchAll(PDO::FETCH_COLUMN);
                      
                      \$similar = [];
                      foreach (\$tables as \$table) {
                          similar_text(\$tableName, \$table, \$percent);
                          if (\$percent > 70) {
                              \$similar[] = \$table;
                          }
                      }
                      
                      if (!empty(\$similar)) {
                          echo \"Did you mean: \" . implode(', ', \$similar) . \"?\\n\";
                      }
                  } else {
                      // Describe table structure
                      echo \"\\nTable structure for '\$tableName':\\n\";
                      \$stmt = \$pdo->query(\"DESCRIBE \$tableName\");
                      \$columns = \$stmt->fetchAll(PDO::FETCH_ASSOC);
                      
                      foreach (\$columns as \$col) {
                          echo sprintf(\"  %-20s %-15s %s\\n\", 
                              \$col['Field'], 
                              \$col['Type'], 
                              \$col['Null'] === 'NO' ? 'NOT NULL' : 'NULL'
                          );
                      }
                  }
              }
              
              // Check for foreign key issues
              if (stripos(\$errorMessage, 'foreign key') !== false) {
                  echo \"\\n=== Foreign Key Analysis ===\\n\";
                  \$stmt = \$pdo->query(\"
                      SELECT 
                          CONSTRAINT_NAME,
                          TABLE_NAME,
                          COLUMN_NAME,
                          REFERENCED_TABLE_NAME,
                          REFERENCED_COLUMN_NAME
                      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                      WHERE REFERENCED_TABLE_SCHEMA = '\" . \$_ENV['DB_NAME'] . \"'
                          AND TABLE_NAME = '\$tableName'
                  \");
                  
                  \$fks = \$stmt->fetchAll(PDO::FETCH_ASSOC);
                  foreach (\$fks as \$fk) {
                      echo \"FK: \" . \$fk['CONSTRAINT_NAME'] . \" - \";
                      echo \$fk['TABLE_NAME'] . \".\" . \$fk['COLUMN_NAME'] . \" -> \";
                      echo \$fk['REFERENCED_TABLE_NAME'] . \".\" . \$fk['REFERENCED_COLUMN_NAME'] . \"\\n\";
                  }
              }
              
          } catch (Exception \$e) {
              echo \"Database analysis failed: \" . \$e->getMessage() . \"\\n\";
          }
          "

  - name: Generate AI Analysis
    type: shell
    command: |
      echo -e "\n=== AI Analysis and Solution Generation ===\n"
      
      # Create analysis prompt
      cat << 'EOF' > debug_prompt.txt
      You are debugging a PHP application (Fit360 AdminDesk - health center billing system).
      
      Error Information:
      ${inputs.error_message}
      
      Context:
      - Framework: Flight PHP 3.x with Twig templates
      - Database: MySQL with PDO
      - PHP Version: 8.x
      
      Please analyze this error and provide:
      1. Root cause analysis
      2. Specific code fix suggestions
      3. Prevention recommendations
      4. Any database schema changes needed
      
      Format your response as JSON with these keys:
      {
        "root_cause": "explanation",
        "fixes": [
          {
            "file": "path/to/file.php",
            "line": 123,
            "old_code": "problematic code",
            "new_code": "fixed code",
            "explanation": "why this fixes it"
          }
        ],
        "prevention": ["recommendation 1", "recommendation 2"],
        "database_changes": ["SQL statement if needed"]
      }
      EOF
      
      # In a real implementation, this would call an AI API
      # For now, we'll generate pattern-based solutions
      php -r "
      \$error = '${inputs.error_message}';
      \$solutions = [];
      
      // Pattern-based solution generation
      if (stripos(\$error, 'Class') !== false && stripos(\$error, 'not found') !== false) {
          \$solutions['root_cause'] = 'Class autoloading issue or missing namespace declaration';
          \$solutions['fixes'][] = [
              'explanation' => 'Add proper namespace or check autoload configuration',
              'check_files' => ['composer.json', 'app/Core/bootstrap.php']
          ];
      }
      
      if (stripos(\$error, 'Undefined variable') !== false) {
          \$solutions['root_cause'] = 'Variable used before initialization';
          \$solutions['fixes'][] = [
              'explanation' => 'Initialize variable before use or check if isset()'
          ];
      }
      
      if (stripos(\$error, 'foreign key constraint') !== false) {
          \$solutions['root_cause'] = 'Referential integrity violation';
          \$solutions['fixes'][] = [
              'explanation' => 'Check if parent record exists before insert/update',
              'database_changes' => ['Consider CASCADE options or check data integrity']
          ];
      }
      
      echo json_encode(\$solutions, JSON_PRETTY_PRINT);
      " > ai_analysis.json

  - name: Implement Fixes
    type: conditional
    condition: "{{ inputs.auto_fix }}"
    steps:
      - name: Apply Code Fixes
        type: shell
        command: |
          php -r "
          echo \"\\n=== Applying Automated Fixes ===\\n\";
          
          \$analysis = json_decode(file_get_contents('ai_analysis.json'), true);
          \$applied = 0;
          
          if (isset(\$analysis['fixes'])) {
              foreach (\$analysis['fixes'] as \$fix) {
                  if (isset(\$fix['file']) && isset(\$fix['old_code']) && isset(\$fix['new_code'])) {
                      if (file_exists(\$fix['file'])) {
                          \$content = file_get_contents(\$fix['file']);
                          
                          // Create backup
                          \$backup = \$fix['file'] . '.backup-' . date('YmdHis');
                          file_put_contents(\$backup, \$content);
                          echo \"Created backup: \$backup\\n\";
                          
                          // Apply fix
                          \$newContent = str_replace(\$fix['old_code'], \$fix['new_code'], \$content);
                          
                          if (\$newContent !== \$content) {
                              file_put_contents(\$fix['file'], \$newContent);
                              echo \"✓ Applied fix to: \" . \$fix['file'] . \"\\n\";
                              \$applied++;
                          }
                      }
                  }
              }
          }
          
          echo \"\\nApplied \$applied fixes\\n\";
          
          // Run syntax check on modified files
          echo \"\\nRunning syntax check...\\n\";
          exec('find app -name \"*.php\" -exec php -l {} \\; 2>&1 | grep -v \"No syntax errors\"', \$output);
          
          if (empty(\$output)) {
              echo \"✓ All PHP files have valid syntax\\n\";
          } else {
              echo \"⚠️ Syntax errors found:\\n\";
              echo implode(\"\\n\", \$output);
          }
          "

  - name: Generate Fix Report
    type: shell
    command: |
      php -r "
      echo \"\\n=== Debug and Fix Report ===\\n\";
      echo \"Generated at: \" . date('Y-m-d H:i:s') . \"\\n\";
      echo \"Original Error: ${inputs.error_message}\\n\";
      
      // Load analysis results
      if (file_exists('ai_analysis.json')) {
          \$analysis = json_decode(file_get_contents('ai_analysis.json'), true);
          
          echo \"\\nRoot Cause:\\n\";
          echo \$analysis['root_cause'] ?? 'Could not determine root cause';
          echo \"\\n\";
          
          if (isset(\$analysis['fixes']) && !empty(\$analysis['fixes'])) {
              echo \"\\nSuggested Fixes:\\n\";
              foreach (\$analysis['fixes'] as \$i => \$fix) {
                  echo (\$i + 1) . \". \" . \$fix['explanation'] . \"\\n\";
              }
          }
          
          if (isset(\$analysis['prevention']) && !empty(\$analysis['prevention'])) {
              echo \"\\nPrevention Recommendations:\\n\";
              foreach (\$analysis['prevention'] as \$i => \$rec) {
                  echo (\$i + 1) . \". \$rec\\n\";
              }
          }
      }
      
      // Save comprehensive report
      \$reportData = [
          'timestamp' => date('c'),
          'error' => '${inputs.error_message}',
          'analysis' => \$analysis ?? [],
          'context' => json_decode(file_get_contents('debug_context.json'), true) ?? [],
          'findings' => json_decode(file_get_contents('related_findings.json'), true) ?? []
      ];
      
      \$reportFile = 'storage/logs/ai-debug-' . date('Y-m-d-His') . '.json';
      file_put_contents(\$reportFile, json_encode(\$reportData, JSON_PRETTY_PRINT));
      
      echo \"\\nFull report saved to: \$reportFile\\n\";
      
      // Cleanup temp files
      @unlink('debug_context.json');
      @unlink('related_findings.json');
      @unlink('ai_analysis.json');
      @unlink('debug_prompt.txt');
      "

  - name: Test After Fix
    type: conditional
    condition: "{{ inputs.auto_fix }}"
    steps:
      - name: Run Tests
        type: shell
        command: |
          echo -e "\n=== Running Tests After Fix ==="
          
          # Check if composer test exists
          if grep -q '"test"' composer.json; then
            echo "Running PHPUnit tests..."
            composer test || echo "Some tests failed - review the output"
          else
            echo "No test suite configured"
          fi
          
          # Clear cache after fixes
          if [ -f "clear-cache.php" ]; then
            echo -e "\nClearing cache..."
            php clear-cache.php
          fi

on_error:
  - name: Log Debug Error
    type: shell
    command: |
      echo "AI Debug workflow error: {{ error_message }}" >> storage/logs/ai-debug-errors.log
      echo "Debug process encountered issues - check the output above"

on_success:
  - name: Log Success
    type: shell
    command: |
      echo "AI Debug completed successfully at $(date)" >> storage/logs/ai-debug.log
      echo "Check the report for detailed analysis and recommendations"