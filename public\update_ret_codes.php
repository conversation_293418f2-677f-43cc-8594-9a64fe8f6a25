<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Updating RET Codes</h2>";
    
    // Show current state
    echo "<h3>Current state:</h3>";
    $stmt = $db->query("SELECT id, code, prefix FROM config_invoice_types WHERE code IN ('ret2', 'ret3')");
    $current = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Current Code</th><th>Prefix</th></tr>";
    foreach ($current as $row) {
        echo "<tr><td>{$row['id']}</td><td>{$row['code']}</td><td>{$row['prefix']}</td></tr>";
    }
    echo "</table>";
    
    // Update ret2 to ret25
    echo "<h3>Updating codes:</h3>";
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret25' WHERE id = 37 AND code = 'ret2'");
    $stmt->execute();
    echo "<p>Update ID 37: ret2 → ret25 - " . ($stmt->rowCount() > 0 ? "✓ Success" : "No change") . "</p>";
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret30' WHERE id = 38 AND code = 'ret3'");
    $stmt->execute();
    echo "<p>Update ID 38: ret3 → ret30 - " . ($stmt->rowCount() > 0 ? "✓ Success" : "No change") . "</p>";
    
    // Verify the updates
    echo "<h3>Final state:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30') 
        ORDER BY code
    ");
    $final = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    
    $ret25Found = false;
    $ret30Found = false;
    
    foreach ($final as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $rowStyle = '';
        if ($row['code'] == 'ret25') {
            $rowStyle = 'background-color: #d4edda;';
            $ret25Found = true;
        } elseif ($row['code'] == 'ret30') {
            $rowStyle = 'background-color: #cce5ff;';
            $ret30Found = true;
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($ret25Found && $ret30Found) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>The codes have been updated successfully:</p>";
        echo "<ul>";
        echo "<li>ret2 → ret25 (FAC-RET25)</li>";
        echo "<li>ret3 → ret30 (FAC-RET30)</li>";
        echo "</ul>";
        echo "<p><a href='/fit/public/fix_frank_invoice_simple.php' style='color: #155724; font-weight: bold;'>→ Now fix Frank's invoice</a></p>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>⚠️ Update may have failed. Please check the table above.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}