<?php
/**
 * Final Test - Send Invoice 279 Email
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Services\EmailService;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Invoice Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Final Test - Send Invoice FAC-DIV-2025-0190</h1>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $invoiceId = 279;
        $recipientEmail = '<EMAIL>';
        
        echo '<div class="info">';
        echo '<p>Sending invoice ' . $invoiceId . ' to ' . htmlspecialchars($recipientEmail) . '...</p>';
        echo '</div>';
        
        try {
            $emailService = new EmailService();
            $result = $emailService->sendInvoiceEmail($invoiceId, $recipientEmail);
            
            echo '<div class="info">';
            echo '<h2>Result:</h2>';
            echo '<pre>' . print_r($result, true) . '</pre>';
            echo '</div>';
            
            if (isset($result['success']) && $result['success']) {
                echo '<div class="success">';
                echo '<h2>✅ SUCCESS! Invoice email sent!</h2>';
                echo '<p>The email should now be visible in Mailhog.</p>';
                echo '<p><a href="http://localhost:8025" target="_blank" style="color: white;">Open Mailhog to view the email</a></p>';
                echo '</div>';
                
                // Check email log
                $db = Flight::db();
                $stmt = $db->prepare("
                    SELECT * FROM email_logs 
                    WHERE invoice_id = :id 
                    AND status = 'sent'
                    ORDER BY created_at DESC 
                    LIMIT 1
                ");
                $stmt->execute([':id' => $invoiceId]);
                $log = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($log) {
                    echo '<div class="info">';
                    echo '<h3>Email Log Entry:</h3>';
                    echo '<table>';
                    echo '<tr><td>Log ID:</td><td>' . $log['id'] . '</td></tr>';
                    echo '<tr><td>Status:</td><td>' . $log['status'] . '</td></tr>';
                    echo '<tr><td>Sent At:</td><td>' . $log['sent_at'] . '</td></tr>';
                    echo '<tr><td>Subject:</td><td>' . htmlspecialchars($log['subject']) . '</td></tr>';
                    echo '<tr><td>Recipient:</td><td>' . htmlspecialchars($log['recipient_email']) . '</td></tr>';
                    echo '</table>';
                    echo '</div>';
                }
            } else {
                echo '<div class="error">';
                echo '<h2>❌ Failed to send email</h2>';
                echo '<p>Error: ' . htmlspecialchars($result['message'] ?? 'Unknown error') . '</p>';
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">';
            echo '<h2>Exception occurred:</h2>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
            echo '</div>';
        }
        
        echo '<hr>';
        echo '<p><a href="">Try Again</a> | <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Email Status</a></p>';
        
    } else {
        ?>
        <div class="info">
            <h2>Ready to Send Invoice Email</h2>
            <p>This will attempt to send invoice FAC-DIV-2025-0190 to <EMAIL></p>
            <p>The SQL parameter issue has been fixed in the EmailService.</p>
            
            <form method="POST">
                <button type="submit">Send Invoice Email Now</button>
            </form>
        </div>
        
        <div style="margin-top: 20px;">
            <p>After clicking the button:</p>
            <ol>
                <li>The invoice email will be generated</li>
                <li>A PDF will be attached</li>
                <li>The email will be sent to Mailhog</li>
                <li>An email log entry will be created</li>
            </ol>
        </div>
        <?php
    }
    ?>
</body>
</html>