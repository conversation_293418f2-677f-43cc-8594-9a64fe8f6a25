<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking <PERSON><PERSON><PERSON>'s Retrocession Data</h2>";
    
    // Find <PERSON><PERSON><PERSON>'s user ID
    $stmt = $db->prepare("SELECT id, first_name, last_name FROM users WHERE first_name = '<PERSON><PERSON><PERSON>' AND last_name = 'He<PERSON>'");
    $stmt->execute();
    $remi = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$remi) {
        echo "<p style='color: red;'>Ré<PERSON> Heine not found in users table!</p>";
        exit;
    }
    
    echo "<p>Found Ré<PERSON> He<PERSON> - User ID: {$remi['id']}</p>";
    
    // Check retrocession settings
    echo "<h3>Retrocession Settings:</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_retrocession_settings 
        WHERE user_id = :user_id
        ORDER BY created_at DESC
        LIMIT 1
    ");
    $stmt->execute(['user_id' => $remi['id']]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<p>Secretary fee: {$settings['secretary_value']}%</p>";
    } else {
        echo "<p style='color: red;'>No retrocession settings found!</p>";
    }
    
    // Check monthly amounts
    echo "<h3>Monthly Retrocession Amounts:</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_monthly_retrocession_amounts 
        WHERE user_id = :user_id
        ORDER BY id
    ");
    $stmt->execute(['user_id' => $remi['id']]);
    $amounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($amounts)) {
        echo "<p style='color: red;'>No monthly amounts configured!</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Month</th><th>CNS Amount</th><th>Patient Amount</th></tr>";
        
        $months = ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                   'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        
        foreach ($amounts as $amount) {
            $monthName = $months[$amount['month']] ?? 'Unknown';
            echo "<tr>";
            echo "<td>$monthName ({$amount['month']})</td>";
            echo "<td>" . number_format($amount['cns_amount'], 2) . " €</td>";
            echo "<td>" . number_format($amount['patient_amount'], 2) . " €</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check retrocession_data_entry
    echo "<h3>Retrocession Data Entry (June 2025):</h3>";
    $stmt = $db->prepare("
        SELECT * FROM retrocession_data_entry 
        WHERE user_id = :user_id 
        AND year = 2025 
        AND month = 6
    ");
    $stmt->execute(['user_id' => $remi['id']]);
    $dataEntry = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($dataEntry) {
        echo "<p>Found entry for June 2025:</p>";
        echo "<ul>";
        echo "<li>ID: {$dataEntry['id']}</li>";
        echo "<li>Invoice ID: " . ($dataEntry['invoice_id'] ?? 'NULL') . "</li>";
        echo "<li>Created: {$dataEntry['created_at']}</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>No retrocession_data_entry found for June 2025!</p>";
    }
    
    // Current date info
    echo "<h3>Invoice Generation Info:</h3>";
    echo "<p>Current month: " . date('F Y') . "</p>";
    echo "<p>Generating for: June 2025 (previous month)</p>";
    
    // What's needed
    echo "<h3>Requirements for Invoice Generation:</h3>";
    echo "<ol>";
    echo "<li>Monthly amounts configured for month 6 (June) ✓</li>";
    echo "<li>Retrocession settings (secretary %) ✓</li>";
    echo "<li>Entry in retrocession_data_entry for June 2025 " . ($dataEntry ? "✓" : "✗ MISSING") . "</li>";
    echo "</ol>";
    
    if (!$dataEntry) {
        echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #856404; margin-top: 0;'>Solution:</h3>";
        echo "<p>The retrocession_data_entry record needs to be created for Rémi for June 2025.</p>";
        echo "<p>This should happen automatically when generating from the user profile page.</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}