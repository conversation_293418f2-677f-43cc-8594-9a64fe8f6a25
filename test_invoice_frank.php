<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use Flight;
use App\Models\Invoice;
use App\Models\User;
use App\Services\EmailService;

$db = Flight::db();

echo "Testing Invoice Creation for Frank (User ID 1)\n\n";

// First check if email templates exist
$stmt = $db->query("SELECT COUNT(*) as count FROM email_templates WHERE is_active = 1");
$result = $stmt->fetch(PDO::FETCH_ASSOC);
echo "Active email templates: " . $result['count'] . "\n";

if ($result['count'] == 0) {
    echo "\nNo email templates found. Creating default templates...\n";
    
    // Create a simple default template
    $stmt = $db->prepare("
        INSERT INTO email_templates (
            name, code, email_type, invoice_type,
            subject, body_html, body_text,
            is_active, priority, created_at
        ) VALUES (
            'Nouvelle facture - Standard',
            'new_invoice_standard',
            'new_invoice',
            NULL,
            'Facture {INVOICE_NUMBER} - Fit360',
            '<p>Bonjour {CLIENT_NAME},</p><p>Veuillez trouver ci-joint votre facture {INVOICE_NUMBER}.</p><p>Montant: {TOTAL_AMOUNT} €</p><p>Cordialement,<br>Fit360</p>',
            'Bonjour {CLIENT_NAME},\n\nVeuillez trouver ci-joint votre facture {INVOICE_NUMBER}.\n\nMontant: {TOTAL_AMOUNT} €\n\nCordialement,\nFit360',
            1,
            100,
            NOW()
        )
    ");
    
    if ($stmt->execute()) {
        echo "✓ Created default email template\n";
    } else {
        echo "✗ Failed to create email template\n";
    }
}

// Get user Frank
$stmt = $db->prepare("SELECT * FROM users WHERE id = 1");
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    echo "\nUser Frank (ID 1) not found!\n";
    exit;
}

echo "\nUser found: " . $user['first_name'] . " " . $user['last_name'] . " (Email: " . $user['email'] . ")\n";

// Check user's financial obligations
$stmt = $db->prepare("
    SELECT * FROM user_financial_obligations 
    WHERE user_id = 1 AND is_active = 1
    ORDER BY display_order, id
");
$stmt->execute();
$obligations = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "\nFinancial obligations for Frank:\n";
foreach ($obligations as $ob) {
    echo "- " . $ob['name'] . ": €" . $ob['amount'] . " (VAT: " . $ob['vat_rate'] . "%)\n";
}

// Create invoice data
$invoiceData = [
    'document_type_id' => 1, // Invoice
    'invoice_type_id' => 4, // DIV
    'invoice_number' => 'FAC-DIV-2025-0191',
    'issue_date' => date('Y-m-d'),
    'due_date' => date('Y-m-d', strtotime('+30 days')),
    'status' => 'draft',
    'user_id' => 1,
    'subject' => 'LOYER + CHARGES',
    'period' => 'JUILLET 2025',
    'notes' => '',
    'internal_notes' => 'Test invoice for Frank',
    'created_by' => 1,
    'payment_term_id' => 1
];

// Create invoice
$invoice = new Invoice();
$invoiceId = $invoice->create($invoiceData);

if ($invoiceId) {
    echo "\n✓ Invoice created with ID: $invoiceId\n";
    
    // Add items based on financial obligations
    $itemsData = [];
    foreach ($obligations as $ob) {
        $itemsData[] = [
            'description' => $ob['name'],
            'quantity' => 1,
            'unit_price' => $ob['amount'],
            'vat_rate' => $ob['vat_rate'],
            'vat_amount' => $ob['amount'] * $ob['vat_rate'] / 100,
            'total' => $ob['amount'] * (1 + $ob['vat_rate'] / 100)
        ];
    }
    
    if ($invoice->saveItems($invoiceId, $itemsData)) {
        echo "✓ Invoice items saved\n";
        
        // Calculate totals
        $invoice->calculateTotals($invoiceId);
        echo "✓ Invoice totals calculated\n";
        
        // Get the complete invoice data
        $completeInvoice = $invoice->getInvoiceWithDetails($invoiceId);
        echo "\nInvoice Summary:\n";
        echo "- Number: " . $completeInvoice['invoice_number'] . "\n";
        echo "- Total: €" . number_format($completeInvoice['total'], 2) . "\n";
        echo "- Status: " . $completeInvoice['status'] . "\n";
        
        // Test email sending
        echo "\nTesting email sending...\n";
        $emailService = new EmailService();
        $result = $emailService->sendInvoiceEmail($invoiceId);
        
        if ($result['success']) {
            echo "✓ Email sent successfully!\n";
            echo "Subject: " . ($result['subject'] ?? 'N/A') . "\n";
            
            // Update invoice status to sent
            $invoice->update($invoiceId, ['status' => 'sent']);
            echo "✓ Invoice marked as sent\n";
        } else {
            echo "✗ Email failed: " . $result['message'] . "\n";
        }
        
    } else {
        echo "✗ Failed to save invoice items\n";
    }
} else {
    echo "✗ Failed to create invoice\n";
}

echo "\nCheck Mailhog at http://localhost:8025 to see the email.\n";