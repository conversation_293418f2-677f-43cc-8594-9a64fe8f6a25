<?php
/**
 * DEBUG: Add debug output to track execution
 */
error_log('Dashboard Access: ' . $_SERVER['REQUEST_URI'] . ' at ' . date('Y-m-d H:i:s'));

// Uncomment the following lines to see debug output
// echo "DEBUG: index.php reached<br>";
// echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";
// echo "Session ID: " . session_id() . "<br>";
// die("DEBUG STOP");

/**
 * Fit360 AdminDesk - Healthcare Billing Management System
 * 
 * @package    Fit360
 * <AUTHOR> Team
 * @copyright  2025 Fit360
 * @license    Proprietary
 * @version    2.3.4
 * @link       http://localhost/fit/public/
 */

// Define the application path
define('APP_PATH', dirname(__DIR__));

// Check if vendor autoload exists
if (!file_exists(APP_PATH . '/vendor/autoload.php')) {
    die('Error: Vendor autoload not found. Please run "composer install" in the project root.');
}

// Load Composer autoloader
require APP_PATH . '/vendor/autoload.php';

// Load environment variables (in case bootstrap doesn't)
if (!getenv('DB_DATABASE') && file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Check if bootstrap file exists
if (!file_exists(APP_PATH . '/app/config/bootstrap.php')) {
    die('Error: Bootstrap file not found at /app/config/bootstrap.php');
}

// Start session early to check for dashboard route
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Special handling for dashboard route - BEFORE loading bootstrap
$requestUrl = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$basePath = '/fit/public';
$path = str_replace($basePath, '', $requestUrl);

// If it's the dashboard route and user is logged in, use standalone dashboard
if (($path === '/' || $path === '') && isset($_SESSION['user_id'])) {
    require __DIR__ . '/dashboard-standalone.php';
    exit;
}

// Load application bootstrap
require APP_PATH . '/app/config/bootstrap.php';

// Set error handler to catch all errors
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return;
    }
    error_log("Fit360 Error: [$severity] $message in $file:$line");
    throw new ErrorException($message, 0, $severity, $file, $line);
});

// Start the application
try {
    
    // Clean any existing output buffers to avoid conflicts
    while (ob_get_level() > 0) {
        ob_end_clean();
    }
    
    // The bootstrap file should have initialized Flight
    // Start Flight framework
    Flight::start();
} catch (Exception $e) {
    // Log error with full details
    error_log('Fit360 Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
    error_log('Stack trace: ' . $e->getTraceAsString());
    
    // Display error in development mode
    if (getenv('APP_ENV') === 'local' || getenv('APP_ENV') === 'development' || getenv('APP_DEBUG') === 'true') {
        echo '<h1>Application Error</h1>';
        echo '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
        echo '<p><strong>Type:</strong> ' . get_class($e) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        // Production error page
        http_response_code(500);
        echo '<h1>Application Error</h1>';
        echo '<p>An error occurred. Please contact the administrator.</p>';
    }
}