<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Monthly Retrocession Data Check</h2>\n";
    
    // Check <PERSON>'s data for different months
    echo "<h3>Frank <PERSON> (User ID: 1) Monthly Data:</h3>\n";
    
    $stmt = $pdo->prepare("
        SELECT month, year, cns_amount, patient_amount 
        FROM user_monthly_retrocession_amounts 
        WHERE user_id = 1 
        ORDER BY year DESC, month DESC
    ");
    $stmt->execute();
    
    echo "<table border='1'>\n";
    echo "<tr><th>Month</th><th>Year</th><th>CNS Amount</th><th>Patient Amount</th></tr>\n";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $monthName = date('F', mktime(0, 0, 0, $row['month'], 1));
        echo "<tr>";
        echo "<td>{$monthName} ({$row['month']})</td>";
        echo "<td>{$row['year']}</td>";
        echo "<td>{$row['cns_amount']}€</td>";
        echo "<td>{$row['patient_amount']}€</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "\n<h3>Invoice Generation Logic:</h3>\n";
    echo "- When generating a <strong>July 2025</strong> retrocession invoice:\n";
    echo "  → The system looks for <strong>June 2025</strong> data (previous month)\n";
    echo "  → This is because retrocession invoices are for services already provided\n";
    echo "\n";
    echo "- When generating a <strong>July 2025</strong> rent (loyer) invoice:\n";
    echo "  → The system looks for <strong>July 2025</strong> data (current month)\n";
    echo "  → This is because rent is billed in advance\n";
    
    // Check if June 2025 data exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM user_monthly_retrocession_amounts 
        WHERE user_id = 1 AND month = 6 AND year = 2025
    ");
    $stmt->execute();
    $june = $stmt->fetch();
    
    if ($june['count'] == 0) {
        echo "\n<h3 style='color: red;'>⚠️ Missing June 2025 Data</h3>\n";
        echo "Frank Huet has no data for June 2025, which is needed to generate July 2025 retrocession invoices.\n";
        echo "\n<h4>Solution:</h4>\n";
        echo "Add June 2025 data in the user profile's monthly amounts section.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}