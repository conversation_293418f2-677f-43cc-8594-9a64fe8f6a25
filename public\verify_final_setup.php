<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Final Setup Verification</h2>";
    
    // Check config_invoice_types
    echo "<h3>config_invoice_types table (what UnifiedInvoiceGenerator now uses):</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code LIKE '%ret%' OR prefix LIKE '%RET%'
        ORDER BY id
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Active</th></tr>";
    foreach ($types as $type) {
        $nameData = json_decode($type['name'], true);
        $displayName = is_array($nameData) ? ($nameData['fr'] ?? $type['name']) : $type['name'];
        
        $rowStyle = '';
        if ($type['code'] == 'ret2') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($type['code'] == 'ret3') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$type['id']}</td>";
        echo "<td><strong>{$type['code']}</strong></td>";
        echo "<td>{$type['prefix']}</td>";
        echo "<td>{$displayName}</td>";
        echo "<td>" . ($type['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check the config table
    echo "<h3>Config table check:</h3>";
    $stmt = $db->query("SELECT `key`, value FROM config WHERE `key` = 'ret_invoice_type'");
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        echo "<p>ret_invoice_type config: <strong>{$config['value']}</strong></p>";
    } else {
        echo "<p>No ret_invoice_type config found (will use 'RET' as fallback)</p>";
    }
    
    // Simulate what will happen for Frank
    echo "<h3>Simulation for Frank (5% secretary):</h3>";
    echo "<ol>";
    echo "<li>UnifiedInvoiceGenerator starts with code = 'RET' (from config or fallback)</li>";
    echo "<li>Detects RET invoice type and secretary_value = 5%</li>";
    echo "<li>Changes code to 'ret2'</li>";
    echo "<li>Looks for 'ret2' in config_invoice_types table</li>";
    echo "<li>Should find ID 37 with prefix 'FAC-RET25'</li>";
    echo "</ol>";
    
    // Test the query
    echo "<h3>Testing the exact query:</h3>";
    $code = 'ret2';
    $stmt = $db->prepare("
        SELECT * FROM config_invoice_types 
        WHERE code = :code AND is_active = 1
        LIMIT 1
    ");
    $stmt->execute(['code' => $code]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Query successful! Found:</p>";
        echo "<ul>";
        echo "<li>ID: {$result['id']}</li>";
        echo "<li>Code: {$result['code']}</li>";
        echo "<li>Prefix: {$result['prefix']}</li>";
        echo "</ul>";
        echo "<p>This ID ({$result['id']}) will be used for type_id in the invoice.</p>";
    } else {
        echo "<p style='color: red;'>✗ Query failed! No active 'ret2' type found.</p>";
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>Summary:</h3>";
    echo "<p>The setup looks correct. Invoice generation should now work with:</p>";
    echo "<ul>";
    echo "<li>Frank (5% secretary) → Uses 'ret2' → Gets ID 37 → Invoice prefix FAC-RET25</li>";
    echo "<li>Others (10% secretary) → Uses 'ret3' → Gets ID 38 → Invoice prefix FAC-RET30</li>";
    echo "</ul>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession' style='color: #155724; font-weight: bold;'>→ Try generating invoices again</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}