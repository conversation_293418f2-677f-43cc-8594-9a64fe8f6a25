<?php
// Debug jQuery loading issue

echo "<h1>Debug jQuery Loading</h1>";

// Check if jQuery is being loaded
?>
<!DOCTYPE html>
<html>
<head>
    <title>jQuery Debug</title>
</head>
<body>
    <h2>Test 1: Check if $ is defined inline</h2>
    <script>
        console.log('Test 1: typeof $ =', typeof $);
        console.log('Test 1: typeof jQuery =', typeof jQuery);
    </script>
    
    <h2>Test 2: Load jQuery</h2>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <h2>Test 3: Check after jQuery load</h2>
    <script>
        console.log('Test 3: typeof $ =', typeof $);
        console.log('Test 3: typeof jQuery =', typeof jQuery);
    </script>
    
    <h2>Test 4: Use jQuery</h2>
    <script>
        $(document).ready(function() {
            console.log('jQuery document ready works!');
            $('#result').text('jQuery is working!');
        });
    </script>
    
    <div id="result" style="color: green; font-weight: bold;"></div>
    
    <hr>
    <h2>Check Users Page</h2>
    <p>The error happens at line 1993 in the rendered HTML.</p>
    <p><a href="/fit/public/users" target="_blank">Open Users Page</a> and check browser console.</p>
    
    <h2>Possible Solutions:</h2>
    <ul>
        <li>Move all inline scripts that use jQuery to after jQuery is loaded</li>
        <li>Wrap jQuery-dependent code in a check: <code>if (typeof $ !== 'undefined')</code></li>
        <li>Use vanilla JavaScript instead of jQuery for early scripts</li>
    </ul>
</body>
</html>