<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Check the latest invoice
$stmt = $db->query("
    SELECT id, invoice_number 
    FROM invoices 
    WHERE invoice_number LIKE 'FAC-2025-%'
    ORDER BY id DESC 
    LIMIT 1
");
$invoice = $stmt->fetch(\PDO::FETCH_ASSOC);

if ($invoice) {
    echo "<h3>Checking invoice {$invoice['invoice_number']} (ID: {$invoice['id']})</h3>";
    
    // Get all lines
    $stmt = $db->prepare("
        SELECT * 
        FROM invoice_lines 
        WHERE invoice_id = ?
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute([$invoice['id']]);
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo "<pre>";
    echo "Found " . count($lines) . " invoice lines:\n\n";
    
    foreach ($lines as $i => $line) {
        echo "Line " . ($i + 1) . ":\n";
        echo "  ID: {$line['id']}\n";
        echo "  Description: {$line['description']}\n";
        echo "  Quantity: {$line['quantity']}\n";
        echo "  Unit Price: {$line['unit_price']}\n";
        echo "  VAT Rate: {$line['vat_rate']}%\n";
        echo "  Sort Order: " . ($line['sort_order'] ?? 'NULL') . "\n\n";
    }
    
    // Check for exact duplicates
    $descriptions = [];
    foreach ($lines as $line) {
        $key = $line['description'] . '|' . $line['quantity'] . '|' . $line['unit_price'];
        if (isset($descriptions[$key])) {
            echo "WARNING: Duplicate line found!\n";
            echo "  Description: {$line['description']}\n";
            echo "  This appears multiple times with same quantity and price\n\n";
        }
        $descriptions[$key] = true;
    }
    echo "</pre>";
} else {
    echo "No invoices found";
}