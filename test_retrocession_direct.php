<?php
/**
 * Direct test for retrocession generation
 */

// Mock the php://input for JSON data
$mockInput = json_encode([
    'month' => 7,
    'year' => 2025
]);

// Override file_get_contents for php://input
namespace {
    function file_get_contents($filename, $use_include_path = false, $context = null, $offset = -1, $maxlen = null) {
        global $mockInput;
        if ($filename === 'php://input') {
            return $mockInput;
        }
        return \file_get_contents($filename, $use_include_path, $context, $offset, $maxlen);
    }
}

namespace App\Controllers {
    
    // Load the application
    require_once __DIR__ . '/vendor/autoload.php';
    require_once __DIR__ . '/app/config/bootstrap.php';
    
    // Start session
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['language'] = 'fr';
    
    // Test directly
    try {
        $controller = new UserController();
        $request = new \App\Core\Request();
        $response = new \App\Core\Response();
        
        // Call the method
        $controller->generateRetrocession($request, $response, 1);
        
    } catch (\Exception $e) {
        echo "\nException: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . "\n";
        echo "Line: " . $e->getLine() . "\n";
        echo "Trace:\n" . $e->getTraceAsString() . "\n";
    }
}