<?php
/**
 * Firefox Add-on and Plugin Detection Script
 * Detects potential interference from browser extensions
 */

// Start output buffering for clean JSON response
ob_start();

// Set proper headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Get user agent and other request headers
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$allHeaders = getallheaders();

// Detection results
$detectionResults = [
    'timestamp' => date('Y-m-d H:i:s'),
    'user_agent' => $userAgent,
    'browser_info' => [],
    'addon_indicators' => [],
    'security_software' => [],
    'network_modifications' => [],
    'recommendations' => []
];

// Browser detection
function detectBrowser($userAgent) {
    $browsers = [
        'Firefox' => '/Firefox\/([0-9\.]+)/',
        'Chrome' => '/Chrome\/([0-9\.]+)/',
        'Safari' => '/Safari\/([0-9\.]+)/',
        'Edge' => '/Edge\/([0-9\.]+)/',
        'Internet Explorer' => '/MSIE ([0-9\.]+)/',
        'Opera' => '/Opera\/([0-9\.]+)/',
    ];
    
    foreach ($browsers as $browser => $pattern) {
        if (preg_match($pattern, $userAgent, $matches)) {
            return [
                'name' => $browser,
                'version' => $matches[1] ?? 'Unknown',
                'full_match' => $matches[0] ?? ''
            ];
        }
    }
    
    return ['name' => 'Unknown', 'version' => 'Unknown', 'full_match' => ''];
}

$detectionResults['browser_info'] = detectBrowser($userAgent);

// Common Firefox Add-on Detection
function detectFirefoxAddons($userAgent, $headers) {
    $indicators = [];
    
    // Check for common ad blockers in user agent
    $adBlockerPatterns = [
        '/AdBlock/',
        '/uBlock/',
        '/Ghostery/',
        '/Privacy Badger/',
        '/Disconnect/',
        '/NoScript/',
        '/Adguard/',
        '/ABP/', // AdBlock Plus
    ];
    
    foreach ($adBlockerPatterns as $pattern) {
        if (preg_match($pattern, $userAgent)) {
            $indicators[] = [
                'type' => 'Ad Blocker',
                'indicator' => 'User Agent Pattern',
                'pattern' => $pattern,
                'confidence' => 'Medium'
            ];
        }
    }
    
    // Check for modified headers that suggest add-ons
    $suspiciousHeaders = [
        'X-Adblock-Key',
        'X-Requested-With',
        'X-Privacy-Mode',
        'X-Ghostery-Version',
        'X-NoScript-Version',
        'X-Adguard-Filtered',
        'X-UBlock-Origin'
    ];
    
    foreach ($suspiciousHeaders as $header) {
        if (isset($headers[$header])) {
            $indicators[] = [
                'type' => 'Add-on Header',
                'indicator' => $header,
                'value' => $headers[$header],
                'confidence' => 'High'
            ];
        }
    }
    
    // Check for DNT (Do Not Track) header - often set by privacy extensions
    if (isset($headers['DNT']) && $headers['DNT'] === '1') {
        $indicators[] = [
            'type' => 'Privacy Setting',
            'indicator' => 'Do Not Track Enabled',
            'confidence' => 'Low'
        ];
    }
    
    // Check for Accept-Language modifications
    if (isset($headers['Accept-Language'])) {
        $acceptLang = $headers['Accept-Language'];
        // Some privacy extensions modify this header
        if (strpos($acceptLang, 'en-US,en;q=0.5') !== false) {
            $indicators[] = [
                'type' => 'Language Modification',
                'indicator' => 'Standardized Accept-Language',
                'confidence' => 'Low'
            ];
        }
    }
    
    return $indicators;
}

// Security Software Detection
function detectSecuritySoftware($userAgent, $headers) {
    $indicators = [];
    
    // Common security software patterns
    $securityPatterns = [
        'Avast' => '/Avast/',
        'AVG' => '/AVG/',
        'Kaspersky' => '/Kaspersky/',
        'Norton' => '/Norton/',
        'McAfee' => '/McAfee/',
        'Bitdefender' => '/Bitdefender/',
        'Malwarebytes' => '/Malwarebytes/',
        'Windows Defender' => '/Windows Defender/',
        'ESET' => '/ESET/',
        'Trend Micro' => '/Trend Micro/',
    ];
    
    foreach ($securityPatterns as $software => $pattern) {
        if (preg_match($pattern, $userAgent)) {
            $indicators[] = [
                'type' => 'Security Software',
                'name' => $software,
                'indicator' => 'User Agent Pattern',
                'confidence' => 'Medium'
            ];
        }
    }
    
    // Check for security-related headers
    $securityHeaders = [
        'X-Security-Scanner',
        'X-Antivirus',
        'X-Firewall',
        'X-Content-Filter',
        'X-Malware-Scanner'
    ];
    
    foreach ($securityHeaders as $header) {
        if (isset($headers[$header])) {
            $indicators[] = [
                'type' => 'Security Header',
                'indicator' => $header,
                'value' => $headers[$header],
                'confidence' => 'High'
            ];
        }
    }
    
    return $indicators;
}

// Network Modification Detection
function detectNetworkModifications($headers) {
    $indicators = [];
    
    // Check for proxy headers
    $proxyHeaders = [
        'X-Forwarded-For',
        'X-Real-IP',
        'X-Forwarded-Proto',
        'X-Forwarded-Host',
        'Via',
        'X-Proxy-ID',
        'X-Proxy-Authorization'
    ];
    
    foreach ($proxyHeaders as $header) {
        if (isset($headers[$header])) {
            $indicators[] = [
                'type' => 'Proxy/VPN',
                'indicator' => $header,
                'value' => $headers[$header],
                'confidence' => 'Medium'
            ];
        }
    }
    
    // Check for compression modifications
    if (isset($headers['Accept-Encoding'])) {
        $encoding = $headers['Accept-Encoding'];
        if (strpos($encoding, 'gzip') === false && strpos($encoding, 'deflate') === false) {
            $indicators[] = [
                'type' => 'Encoding Modification',
                'indicator' => 'Non-standard Accept-Encoding',
                'value' => $encoding,
                'confidence' => 'Low'
            ];
        }
    }
    
    return $indicators;
}

// Run detections
$detectionResults['addon_indicators'] = detectFirefoxAddons($userAgent, $allHeaders);
$detectionResults['security_software'] = detectSecuritySoftware($userAgent, $allHeaders);
$detectionResults['network_modifications'] = detectNetworkModifications($allHeaders);

// Generate recommendations
function generateRecommendations($results) {
    $recommendations = [];
    
    // Check if Firefox is detected
    if ($results['browser_info']['name'] === 'Firefox') {
        $recommendations[] = [
            'type' => 'Browser Specific',
            'message' => 'Firefox detected. Consider testing in private/incognito mode to bypass extensions.',
            'action' => 'Open private window (Ctrl+Shift+P) and test the invoice page.'
        ];
    }
    
    // Check for ad blockers
    $hasAdBlocker = false;
    foreach ($results['addon_indicators'] as $indicator) {
        if ($indicator['type'] === 'Ad Blocker') {
            $hasAdBlocker = true;
            break;
        }
    }
    
    if ($hasAdBlocker) {
        $recommendations[] = [
            'type' => 'Ad Blocker',
            'message' => 'Ad blocker detected. This may interfere with JavaScript execution.',
            'action' => 'Temporarily disable ad blocker for localhost or add to whitelist.'
        ];
    }
    
    // Check for security software
    if (!empty($results['security_software'])) {
        $recommendations[] = [
            'type' => 'Security Software',
            'message' => 'Security software detected. It may block JavaScript execution.',
            'action' => 'Add localhost to security software whitelist or temporarily disable web protection.'
        ];
    }
    
    // Check for network modifications
    if (!empty($results['network_modifications'])) {
        $recommendations[] = [
            'type' => 'Network Modification',
            'message' => 'Network modifications detected (proxy/VPN). May cause issues with local development.',
            'action' => 'Try disabling proxy/VPN for localhost testing.'
        ];
    }
    
    // General recommendations
    $recommendations[] = [
        'type' => 'General',
        'message' => 'Clear browser cache and cookies for localhost.',
        'action' => 'Press Ctrl+Shift+Delete, select "All time", clear all data.'
    ];
    
    $recommendations[] = [
        'type' => 'General',
        'message' => 'Test in different browser to isolate extension issues.',
        'action' => 'Try Chrome, Edge, or Safari to see if issue persists.'
    ];
    
    return $recommendations;
}

$detectionResults['recommendations'] = generateRecommendations($detectionResults);

// Add summary
$detectionResults['summary'] = [
    'total_indicators' => count($detectionResults['addon_indicators']) + 
                         count($detectionResults['security_software']) + 
                         count($detectionResults['network_modifications']),
    'browser' => $detectionResults['browser_info']['name'],
    'likely_interference' => count($detectionResults['addon_indicators']) > 0 || 
                            count($detectionResults['security_software']) > 0,
    'risk_level' => count($detectionResults['addon_indicators']) > 2 ? 'High' : 
                   (count($detectionResults['addon_indicators']) > 0 ? 'Medium' : 'Low')
];

// Add all request headers for debugging
$detectionResults['debug_info'] = [
    'all_headers' => $allHeaders,
    'server_vars' => [
        'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'HTTP_ACCEPT' => $_SERVER['HTTP_ACCEPT'] ?? '',
        'HTTP_ACCEPT_LANGUAGE' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
        'HTTP_ACCEPT_ENCODING' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
        'HTTP_CONNECTION' => $_SERVER['HTTP_CONNECTION'] ?? '',
        'HTTP_CACHE_CONTROL' => $_SERVER['HTTP_CACHE_CONTROL'] ?? '',
        'HTTP_DNT' => $_SERVER['HTTP_DNT'] ?? '',
        'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? '',
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? '',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? '',
    ]
];

// Clean output buffer and return JSON
ob_clean();
echo json_encode($detectionResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
exit;
?>