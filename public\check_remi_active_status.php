<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking <PERSON><PERSON><PERSON>'s Active Status Issue</h2>";
    
    // Check if is_active column exists
    echo "<h3>Checking table structure:</h3>";
    $stmt = $db->query("DESCRIBE user_monthly_retrocession_amounts");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasIsActive = false;
    foreach ($columns as $col) {
        if ($col['Field'] == 'is_active') {
            $hasIsActive = true;
            echo "<p>✓ is_active column exists</p>";
            break;
        }
    }
    
    if (!$hasIsActive) {
        echo "<p style='color: red;'>✗ is_active column does NOT exist</p>";
    }
    
    // Get <PERSON><PERSON><PERSON>'s data
    echo "<h3><PERSON><PERSON><PERSON>'s June 2025 data:</h3>";
    
    // Build query based on column existence
    if ($hasIsActive) {
        $query = "
            SELECT uma.*, u.first_name, u.last_name
            FROM user_monthly_retrocession_amounts uma
            JOIN users u ON u.id = uma.user_id
            WHERE u.first_name = 'Rémi' AND u.last_name = 'Heine'
            AND uma.month = 6 AND uma.year = 2025
        ";
    } else {
        $query = "
            SELECT uma.*, u.first_name, u.last_name
            FROM user_monthly_retrocession_amounts uma
            JOIN users u ON u.id = uma.user_id
            WHERE u.first_name = 'Rémi' AND u.last_name = 'Heine'
            AND uma.month = 6 AND uma.year = 2025
        ";
    }
    
    $stmt = $db->query($query);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($data) {
        echo "<table border='1' cellpadding='5'>";
        foreach ($data as $key => $value) {
            echo "<tr><td>$key</td><td>$value</td></tr>";
        }
        echo "</table>";
        
        if ($hasIsActive && isset($data['is_active'])) {
            if ($data['is_active'] == 0) {
                echo "<p style='color: red;'>⚠️ is_active is 0 - This is why bulk generation doesn't show Rémi!</p>";
                
                // Fix it
                echo "<h3>Fixing is_active status:</h3>";
                $stmt = $db->prepare("
                    UPDATE user_monthly_retrocession_amounts 
                    SET is_active = 1 
                    WHERE user_id = :user_id AND month = 6 AND year = 2025
                ");
                $stmt->execute(['user_id' => $data['user_id']]);
                echo "<p style='color: green;'>✓ Updated is_active to 1</p>";
            }
        }
    }
    
    // Show what bulk generation query would find
    echo "<h3>What bulk generation query finds:</h3>";
    
    $bulkQuery = "
        SELECT 
            u.id as user_id,
            CONCAT(u.first_name, ' ', u.last_name) as name,
            uma.cns_amount,
            uma.patient_amount" . 
            ($hasIsActive ? ", uma.is_active" : "") . "
        FROM users u
        INNER JOIN user_monthly_retrocession_amounts uma ON uma.user_id = u.id
        WHERE uma.month = 6 
        AND uma.year = 2025" .
        ($hasIsActive ? " AND uma.is_active = 1" : "") . "
        AND (uma.cns_amount > 0 OR uma.patient_amount > 0)
        AND u.first_name = 'Rémi' AND u.last_name = 'Heine'
    ";
    
    echo "<pre style='background: #f5f5f5; padding: 10px;'>$bulkQuery</pre>";
    
    $stmt = $db->query($bulkQuery);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Bulk generation WOULD find Rémi:</p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Bulk generation would NOT find Rémi</p>";
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>Summary:</h3>";
    if ($hasIsActive) {
        echo "<p>The issue was the is_active flag. It should now be fixed.</p>";
    } else {
        echo "<p>The table doesn't have an is_active column, so that's not the issue.</p>";
        echo "<p>The problem might be in the UnifiedInvoiceGenerator itself.</p>";
    }
    echo "<p>Try bulk generation again for Rémi.</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}