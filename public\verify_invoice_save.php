<?php
/**
 * Verify Invoice Save Status
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use Flight;

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Please login first.");
}

echo "<h1>Invoice Save Verification</h1>";
echo "<pre>";

try {
    $db = Flight::db();
    
    // Check last 10 invoices
    echo "=== Last 10 Invoices Created ===\n";
    $stmt = $db->query("
        SELECT id, invoice_number, status, total, created_at, 
               CASE 
                   WHEN client_id IS NOT NULL THEN CONCAT('Client #', client_id)
                   WHEN user_id IS NOT NULL THEN CONCAT('User #', user_id)
                   ELSE 'Unknown'
               END as billed_to
        FROM invoices 
        ORDER BY id DESC 
        LIMIT 10
    ");
    
    echo str_pad("ID", 6) . str_pad("Number", 20) . str_pad("Status", 10) . str_pad("Total", 12) . str_pad("Billed To", 15) . str_pad("Created", 20) . "\n";
    echo str_repeat("-", 83) . "\n";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo str_pad($row['id'], 6);
        echo str_pad($row['invoice_number'], 20);
        echo str_pad($row['status'], 10);
        echo str_pad('€' . number_format($row['total'], 2), 12);
        echo str_pad($row['billed_to'], 15);
        echo str_pad($row['created_at'], 20);
        echo "\n";
    }
    
    // Check email logs
    echo "\n=== Email Send Attempts (Last 10) ===\n";
    $tables = $db->query("SHOW TABLES LIKE 'email_logs'")->fetchAll();
    if (count($tables) > 0) {
        $stmt = $db->query("
            SELECT el.*, i.invoice_number 
            FROM email_logs el
            LEFT JOIN invoices i ON el.invoice_id = i.id
            ORDER BY el.id DESC 
            LIMIT 10
        ");
        
        echo str_pad("ID", 6) . str_pad("Invoice", 20) . str_pad("Status", 10) . str_pad("Recipient", 30) . str_pad("Date", 20) . "\n";
        echo str_repeat("-", 86) . "\n";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo str_pad($row['id'], 6);
            echo str_pad($row['invoice_number'] ?? 'N/A', 20);
            echo str_pad($row['status'], 10);
            echo str_pad(substr($row['recipient_email'], 0, 28), 30);
            echo str_pad($row['created_at'], 20);
            echo "\n";
            
            if ($row['error_message']) {
                echo "     Error: " . $row['error_message'] . "\n";
            }
        }
    } else {
        echo "Email logs table not found.\n";
    }
    
    // Check session flash messages
    echo "\n=== Current Session Flash Messages ===\n";
    if (isset($_SESSION['flash_messages'])) {
        foreach ($_SESSION['flash_messages'] as $msg) {
            echo $msg['type'] . ": " . $msg['message'] . "\n";
        }
    } else {
        echo "No flash messages in session.\n";
    }
    
    // Check for any stuck drafts
    echo "\n=== Draft Invoices (Last 5) ===\n";
    $stmt = $db->query("
        SELECT id, invoice_number, created_at, total
        FROM invoices 
        WHERE status = 'draft'
        ORDER BY id DESC 
        LIMIT 5
    ");
    
    $drafts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (count($drafts) > 0) {
        foreach ($drafts as $draft) {
            echo "ID: {$draft['id']} | {$draft['invoice_number']} | €{$draft['total']} | {$draft['created_at']}\n";
        }
    } else {
        echo "No draft invoices found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

echo "</pre>";

// Add quick test links
echo "<hr>";
echo "<h3>Quick Actions:</h3>";
echo "<ul>";
echo "<li><a href='/fit/public/invoices'>View All Invoices</a></li>";
echo "<li><a href='/fit/public/invoices/create'>Create New Invoice</a></li>";
echo "<li><a href='check_mailhog_status.php'>Check Email Status</a></li>";
echo "</ul>";

// Add JavaScript to check console
?>
<script>
console.log('=== Browser Console Check ===');
console.log('Session Storage:', window.sessionStorage);
console.log('Local Storage:', window.localStorage);

// Check for any stored form data
const formData = sessionStorage.getItem('invoiceFormData');
if (formData) {
    console.log('Stored form data found:', JSON.parse(formData));
}

// Check for redirect issues
console.log('Current URL:', window.location.href);
console.log('Referrer:', document.referrer);
</script>