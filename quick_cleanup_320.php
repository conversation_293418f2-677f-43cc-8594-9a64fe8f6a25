<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Clean up any invoice 320
    $pdo->exec("DELETE FROM invoice_lines WHERE invoice_id = 320");
    $pdo->exec("DELETE FROM user_generated_invoices WHERE invoice_id = 320");
    $pdo->exec("DELETE FROM retrocession_data_entry WHERE invoice_id = 320");
    $pdo->exec("DELETE FROM invoices WHERE id = 320");
    
    echo "✓ Cleaned up invoice #320\n";
    echo "\n<a href='test_retrocession_generation.php?user_id=1&month=7&year=2025'>Generate retrocession invoice now</a>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}