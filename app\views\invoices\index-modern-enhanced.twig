{% extends "base-modern.twig" %}
{% import '_macros/table-helper-v2.twig' as tableHelper %}

{% block title %}{{ __('invoices.invoices') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.invoices') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/billing-wizard" class="btn btn-info">
                <i class="bi bi-magic me-2"></i>{{ __('invoices.billing_wizard') }}
            </a>
            <a href="{{ base_url }}/invoices/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('invoices.total_invoices') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_invoices|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-file-text text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('invoices.total_revenue') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.total_revenue|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-euro text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('invoices.unpaid_invoices') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.unpaid_invoices|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-circle text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-danger h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-danger text-uppercase mb-1">
                                {{ __('invoices.outstanding_amount') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.outstanding_amount|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-cash-stack text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table with Enhanced Helper -->
    {% set tableContent %}
        {{ tableHelper.tableHeader([
            { label: __('invoices.invoice_number'), sortable: true },
            { label: __('invoices.document_type'), sortable: true },
            { label: __('invoices.invoice_type'), sortable: true },
            { label: __('clients.client') ~ '/' ~ __('patients.patient'), sortable: true },
            { label: __('invoices.issue_date'), sortable: true },
            { label: __('invoices.due_date'), sortable: true },
            { label: __('invoices.amount'), sortable: true, class: 'text-end' },
            { label: __('common.status'), sortable: true },
            { label: __('common.actions'), width: 80, sortable: false, reorderable: false, isAction: true }
        ], true) }}
        <tbody>
            {% for invoice in invoices %}
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input row-checkbox" value="{{ invoice.id }}">
                </td>
                <td>
                    <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="text-decoration-none fw-medium">
                        {{ invoice.invoice_number }}
                    </a>
                </td>
                <td>
                    {% if invoice.doc_type_color %}
                        <span class="badge" style="background-color: {{ invoice.doc_type_color }};">
                            <i class="{{ invoice.doc_type_icon }} me-1"></i>
                            {{ invoice.doc_type_display_name }}
                        </span>
                    {% else %}
                        <span class="badge bg-secondary">{{ __('common.unknown') }}</span>
                    {% endif %}
                </td>
                <td>
                    {% if invoice.type_color %}
                        <span class="badge" style="background-color: {{ invoice.type_color }};">
                            {{ invoice.type_name }}
                        </span>
                    {% else %}
                        {{ invoice.type_name }}
                    {% endif %}
                </td>
                <td>
                    {% if invoice.patient_id %}
                        <i class="bi bi-person text-primary me-1"></i>{{ invoice.patient_name }}
                    {% else %}
                        <i class="bi bi-building text-info me-1"></i>{{ invoice.client_name }}
                    {% endif %}
                </td>
                <td data-sort="{{ invoice.issue_date|date('Y-m-d') }}">
                    {{ invoice.issue_date|date('d/m/Y') }}
                </td>
                <td data-sort="{{ invoice.due_date ? invoice.due_date|date('Y-m-d') : '9999-12-31' }}">
                    {% if invoice.due_date %}
                        {{ invoice.due_date|date('d/m/Y') }}
                        {% if invoice.is_overdue %}
                            <i class="bi bi-exclamation-circle text-danger ms-1" 
                               data-bs-toggle="tooltip" 
                               title="{{ __('invoices.overdue') }}"></i>
                        {% endif %}
                    {% else %}
                        <span class="text-muted">-</span>
                    {% endif %}
                </td>
                <td class="text-end" data-sort="{{ invoice.total }}">
                    <span class="fw-medium">{{ currency }}{{ invoice.total|number_format(2, ',', ' ') }}</span>
                </td>
                <td>
                    {% set statusConfig = {
                        'draft': { class: 'secondary', label: __('invoices.status.draft') },
                        'sent': { class: 'info', label: __('invoices.status.sent') },
                        'paid': { class: 'success', label: __('invoices.status.paid') },
                        'partial': { class: 'warning', label: __('invoices.status.partial') },
                        'overdue': { class: 'danger', label: __('invoices.status.overdue') },
                        'cancelled': { class: 'dark', label: __('invoices.status.cancelled') }
                    } %}
                    {% set status = statusConfig[invoice.status]|default({ class: 'secondary', label: invoice.status }) %}
                    <span class="badge bg-{{ status.class }}">{{ status.label }}</span>
                </td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}">
                                    <i class="bi bi-eye me-2"></i>{{ __('common.view') }}
                                </a>
                            </li>
                            {% if invoice.status == 'draft' %}
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/edit">
                                    <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank">
                                    <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/download">
                                    <i class="bi bi-download me-2"></i>{{ __('invoices.download_pdf') }}
                                </a>
                            </li>
                            {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="sendInvoice({{ invoice.id }})">
                                    <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_invoice') }}
                                </a>
                            </li>
                            {% endif %}
                            {% if invoice.status == 'sent' or invoice.status == 'partial' %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="recordPayment({{ invoice.id }})">
                                    <i class="bi bi-cash me-2"></i>{{ __('invoices.record_payment') }}
                                </a>
                            </li>
                            {% endif %}
                            {% if invoice.status != 'paid' %}
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/credit-note">
                                    <i class="bi bi-receipt me-2"></i>{{ __('invoices.create_credit_note') }}
                                </a>
                            </li>
                            {% endif %}
                            {% if invoice.status == 'draft' or isAdmin %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="#" onclick="deleteInvoice({{ invoice.id }}, '{{ invoice.status }}')">
                                    <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </td>
            </tr>
            {% else %}
            {{ tableHelper.emptyState(__('invoices.no_invoices_found'), 'bi-file-earmark-x', 10) }}
            {% endfor %}
        </tbody>
    {% endset %}

    {{ tableHelper.tableWithFilters({
        baseUrl: base_url,
        tableId: 'invoicesTable',
        formAction: base_url ~ '/invoices',
        storageKey: 'invoices_filters_v2',
        tableContent: tableContent,
        searchColumns: [1, 3, 4], 
        searchPlaceholder: __('invoices.search_placeholder'),
        sortable: true,
        reorderable: false,
        columnOrder: columnOrder,
        defaultSort: { column: 5, direction: 'desc' },
        showColumnToggle: false,
        filters: [
            {
                id: 'status',
                name: 'status',
                label: __('common.status'),
                type: 'select',
                width: 3,
                value: filters.status|default(''),
                placeholder: __('invoices.filter_all'),
                options: {
                    'draft': __('invoices.filter_draft'),
                    'sent': __('invoices.status.sent'),
                    'paid': __('invoices.filter_paid'),
                    'partial': __('invoices.status.partial'),
                    'overdue': __('invoices.filter_overdue'),
                    'cancelled': __('invoices.status.cancelled')
                }
            },
            {
                id: 'type',
                name: 'type',
                label: __('invoices.invoice_type'),
                type: 'select',
                width: 3,
                value: filters.type|default(''),
                placeholder: __('common.all'),
                options: invoiceTypes|default({})
            },
            {
                id: 'date_from',
                name: 'date_from',
                label: __('common.from'),
                type: 'date',
                width: 2,
                value: filters.date_from|default('')
            },
            {
                id: 'date_to',
                name: 'date_to',
                label: __('common.to'),
                type: 'date',
                width: 2,
                value: filters.date_to|default('')
            }
        ],
        filterConfigs: [
            { id: 'status', autoSubmit: true },
            { id: 'type', autoSubmit: true },
            { id: 'date_from', autoSubmit: true },
            { id: 'date_to', autoSubmit: true }
        ],
        showImport: true,
        importUrl: base_url ~ '/invoices/import',
        showExport: true,
        exportUrl: base_url ~ '/invoices/export',
        exportFormats: ['csv', 'excel', 'pdf'],
        showBulkActions: true,
        bulkActions: [
            {
                action: 'send',
                label: __('invoices.send_selected'),
                icon: 'bi bi-envelope',
                url: base_url ~ '/invoices/bulk-send'
            },
            {
                action: 'export',
                label: __('invoices.export_selected'),
                icon: 'bi bi-download',
                url: base_url ~ '/invoices/bulk-export'
            },
            {
                action: 'mark_paid',
                label: __('invoices.mark_as_paid'),
                icon: 'bi bi-check-circle',
                url: base_url ~ '/invoices/bulk-mark-paid',
                divider: true
            },
            {
                action: 'delete',
                label: __('invoices.delete_selected'),
                icon: 'bi bi-trash',
                class: 'text-danger',
                url: base_url ~ '/invoices/bulk-delete',
                confirm: true,
                confirmMessage: __('invoices.bulk_delete_confirm')
            }
        ],
        bulkActionConfigs: [
            {
                action: 'send',
                url: base_url ~ '/invoices/bulk-send'
            },
            {
                action: 'export',
                url: base_url ~ '/invoices/bulk-export'
            },
            {
                action: 'mark_paid',
                url: base_url ~ '/invoices/bulk-mark-paid',
                confirm: true,
                confirmMessage: __('invoices.bulk_mark_paid_confirm')
            },
            {
                action: 'delete',
                url: base_url ~ '/invoices/bulk-delete',
                confirm: true,
                confirmMessage: __('invoices.bulk_delete_confirm')
            }
        ],
        bulkActionUrl: base_url ~ '/invoices/bulk-action',
        pagination: {
            current_page: current_page|default(1),
            total_pages: total_pages|default(1),
            base_url: base_url ~ '/invoices'
        },
        resetUrl: base_url ~ '/invoices?reset_filters=1'
    }) }}
</div>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function sendInvoice(id) {
    if (confirm('{{ __("invoices.send_confirm") }}')) {
        window.location.href = '{{ base_url }}/invoices/' + id + '/send';
    }
}

function recordPayment(id) {
    window.location.href = '{{ base_url }}/invoices/' + id + '/payment';
}

function deleteInvoice(id, status) {
    let warningText = '{{ __("invoices.delete_warning") }}';
    let warningTitle = '{{ __("common.are_you_sure") }}';
    
    // Show stronger warning for admins deleting sent invoices
    if (status && status !== 'draft' && {{ isAdmin ? 'true' : 'false' }}) {
        warningTitle = '⚠️ {{ __("invoices.admin_delete_sent_warning_title") }}';
        warningText = '{{ __("invoices.admin_delete_sent_warning_text") }}';
    }
    
    Swal.fire({
        title: warningTitle,
        text: warningText,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: '{{ __("common.delete") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/' + id + '/delete';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Custom export function with current view state
function exportInvoices(format) {
    const url = new URL('{{ base_url }}/invoices/export');
    url.searchParams.set('format', format);
    
    // Add current filters
    const filters = ['status', 'type', 'date_from', 'date_to', 'search'];
    filters.forEach(filter => {
        const value = document.getElementById(filter)?.value;
        if (value) {
            url.searchParams.set(filter, value);
        }
    });
    
    // Add table state
    if (tableHelper) {
        if (tableHelper.currentSort) {
            url.searchParams.set('sort_column', tableHelper.currentSort.column);
            url.searchParams.set('sort_direction', tableHelper.currentSort.direction);
        }
        if (tableHelper.columnOrder) {
            url.searchParams.set('column_order', JSON.stringify(tableHelper.columnOrder));
        }
    }
    
    window.location.href = url.toString();
}
</script>
{% endblock %}

{% block table_helper %}
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Wait for table helper script to load
function initializeTableHelper() {
    if (typeof window.initTableHelper === 'function' && window.tableHelperConfig) {
        try {
            window.tableHelper = window.initTableHelper(window.tableHelperConfig);
        } catch (error) {
            console.error('Error initializing table helper:', error);
        }
    }
}

// Try to initialize immediately if script is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTableHelper);
} else {
    initializeTableHelper();
}

// Also try after a short delay as fallback
setTimeout(initializeTableHelper, 500);
</script>
{% endblock %}