<?php
/**
 * Clear PHP opcache
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>Clearing PHP Cache</h1>";

// Clear opcache
if (function_exists('opcache_reset')) {
    if (opcache_reset()) {
        echo "<p style='color: green;'>✓ Opcache cleared successfully</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to clear opcache</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Opcache is not enabled</p>";
}

// Clear file stat cache
clearstatcache(true);
echo "<p style='color: green;'>✓ File stat cache cleared</p>";

// Show current file content
echo "<h2>Current MonthlyInvoiceGenerator.php line 500:</h2>";
$file = file_get_contents(__DIR__ . '/app/services/MonthlyInvoiceGenerator.php');
$lines = explode("\n", $file);
echo "<pre style='background: #f0f0f0; padding: 10px;'>";
echo "Line 499: " . htmlspecialchars(trim($lines[498])) . "\n";
echo "Line 500: " . htmlspecialchars(trim($lines[499])) . "\n";
echo "Line 501: " . htmlspecialchars(trim($lines[500])) . "\n";
echo "</pre>";

echo "<p><a href='/fit/public/invoices/bulk-loyer'>Return to Bulk Loyer page</a></p>";