<?php
// Simple email test without framework overhead

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html><html><head><title>Email Test</title></head><body>";
    echo "<h2>Testing Email for DIV Invoice</h2>";
    
    // Get Frank's user data
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color: red;'>User Frank not found!</p>";
        exit;
    }
    
    echo "<p>User: {$user['first_name']} {$user['last_name']} (Email: {$user['email']})</p>";
    
    // Check for existing DIV invoice
    $stmt = $db->prepare("
        SELECT i.*, 
               it.name as invoice_type_name,
               it.code as invoice_type_code,
               dt.name as document_type_name,
               dt.code as document_type_code
        FROM invoices i
        LEFT JOIN invoice_types it ON i.invoice_type_id = it.id
        LEFT JOIN document_types dt ON i.document_type_id = dt.id
        WHERE i.user_id = 1 
        AND i.document_type_id = 1 
        AND i.invoice_type_id = 4
        ORDER BY i.id DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p>No DIV invoice found. Creating one...</p>";
        
        // Get next invoice number
        $stmt = $db->query("SELECT MAX(CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED)) as max_num FROM invoices WHERE invoice_number LIKE 'FAC-%'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $nextNum = ($result['max_num'] ?? 0) + 1;
        $invoiceNumber = sprintf('FAC-DIV-2025-%04d', $nextNum);
        
        // Create invoice
        $stmt = $db->prepare("
            INSERT INTO invoices (
                document_type_id, invoice_type_id, invoice_number,
                issue_date, due_date, status, user_id,
                subject, period, created_at, created_by,
                payment_term_id
            ) VALUES (
                1, 4, :invoice_number,
                CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'draft', 1,
                'LOYER + CHARGES', 'JUILLET 2025', NOW(), 1,
                1
            )
        ");
        
        $stmt->execute([':invoice_number' => $invoiceNumber]);
        $invoiceId = $db->lastInsertId();
        
        // Add items
        $stmt = $db->prepare("
            INSERT INTO invoice_items (
                invoice_id, description, quantity, unit_price,
                vat_rate, vat_amount, display_order
            ) VALUES (
                :invoice_id, 'Loyer mensuel', 1, 100,
                17, 17, 1
            )
        ");
        $stmt->execute([':invoice_id' => $invoiceId]);
        
        // Update totals
        $db->exec("
            UPDATE invoices 
            SET subtotal = 100, 
                vat_amount = 17, 
                total = 117 
            WHERE id = $invoiceId
        ");
        
        echo "<p>✓ Created invoice $invoiceNumber (ID: $invoiceId)</p>";
        
        // Reload invoice data
        $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        echo "<p>Found invoice: {$invoice['invoice_number']} (Status: {$invoice['status']})</p>";
        $invoiceId = $invoice['id'];
    }
    
    // Test email sending using PHPMailer directly
    echo "<h3>Testing Email Send...</h3>";
    
    // Check email templates
    $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Active email templates: {$result['count']}</p>";
    
    if ($result['count'] == 0) {
        echo "<p style='color: red;'>No email templates! Run <a href='/fit/setup_email_templates.php'>setup_email_templates.php</a> first.</p>";
        exit;
    }
    
    // Simple email test
    require_once __DIR__ . '/vendor/phpmailer/phpmailer/src/PHPMailer.php';
    require_once __DIR__ . '/vendor/phpmailer/phpmailer/src/SMTP.php';
    require_once __DIR__ . '/vendor/phpmailer/phpmailer/src/Exception.php';
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = 'localhost';
        $mail->Port = 1025;
        $mail->SMTPAuth = false;
        $mail->SMTPSecure = false;
        $mail->SMTPAutoTLS = false;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'Fit360 AdminDesk');
        $mail->addAddress($user['email'], $user['first_name'] . ' ' . $user['last_name']);
        
        // Content
        $mail->isHTML(true);
        $mail->CharSet = 'UTF-8';
        $mail->Subject = "Facture {$invoice['invoice_number']} - Test";
        $mail->Body = '<p>Bonjour ' . $user['first_name'] . ',</p>
<p>Ceci est un test d\'envoi d\'email pour votre facture <strong>' . $invoice['invoice_number'] . '</strong>.</p>
<p>Montant: <strong>117,00 €</strong></p>
<p>Cordialement,<br>Fit360 AdminDesk</p>';
        
        $mail->AltBody = strip_tags($mail->Body);
        
        // Send
        $mail->send();
        
        echo "<p style='color: green;'>✓ Email sent successfully!</p>";
        echo "<p>Subject: " . htmlspecialchars($mail->Subject) . "</p>";
        echo "<p><strong>Check Mailhog at <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></strong></p>";
        
        // Log the email
        $stmt = $db->prepare("
            INSERT INTO email_logs (
                invoice_id, recipient_email, subject,
                status, sent_at, created_at
            ) VALUES (
                :invoice_id, :email, :subject,
                'sent', NOW(), NOW()
            )
        ");
        
        $stmt->execute([
            ':invoice_id' => $invoiceId,
            ':email' => $user['email'],
            ':subject' => $mail->Subject
        ]);
        
        echo "<p>✓ Email logged in database</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Email Error: " . $e->getMessage() . "</p>";
        echo "<p>Mailer Error: " . $mail->ErrorInfo . "</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='/fit/public/invoices'>Back to Invoices</a> | ";
    echo "<a href='http://localhost:8025' target='_blank'>Open Mailhog</a></p>";
    echo "</body></html>";
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage();
}