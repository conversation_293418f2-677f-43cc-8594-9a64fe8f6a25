<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Verifying Invoice 271 Items in Database</h2>";
    
    // Get invoice items directly from database
    $sql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id ORDER BY id";
    $stmt = $db->prepare($sql);
    $stmt->execute(['invoice_id' => $invoiceId]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Database Content for Invoice 271:</h3>";
    
    if (empty($items)) {
        echo "<p style='color: red;'>No items found in database for invoice 271!</p>";
        
        // Check if invoice exists
        $checkInvoice = "SELECT * FROM invoices WHERE id = :id";
        $checkStmt = $db->prepare($checkInvoice);
        $checkStmt->execute(['id' => $invoiceId]);
        $invoice = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "<p>Invoice exists with:</p>";
            echo "<ul>";
            echo "<li>Number: {$invoice['invoice_number']}</li>";
            echo "<li>Status: {$invoice['status']}</li>";
            echo "<li>Total: €" . number_format($invoice['total'], 2) . "</li>";
            echo "</ul>";
            echo "<p><a href='/fit/recreate_invoice_271_items.php'>Click here to recreate invoice items</a></p>";
        } else {
            echo "<p style='color: red;'>Invoice 271 not found in database!</p>";
        }
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th>";
        echo "<th>Description</th>";
        echo "<th>Quantity</th>";
        echo "<th>Unit Price</th>";
        echo "<th>VAT Rate</th>";
        echo "<th>Total</th>";
        echo "<th>Contains 'CNS'?</th>";
        echo "</tr>";
        
        $hasCNS = false;
        foreach ($items as $item) {
            $containsCNS = strpos($item['description'], 'CNS') !== false;
            if ($containsCNS) $hasCNS = true;
            
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td style='" . ($containsCNS ? "background-color: #ffeeee;" : "") . "'>{$item['description']}</td>";
            echo "<td>{$item['quantity']}</td>";
            echo "<td>€" . number_format($item['unit_price'], 2) . "</td>";
            echo "<td>{$item['vat_rate']}%</td>";
            echo "<td>€" . number_format($item['total_amount'], 2) . "</td>";
            echo "<td style='text-align: center;'>" . ($containsCNS ? "❌ YES" : "✅ NO") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if ($hasCNS) {
            echo "<h3 style='color: red;'>⚠️ Database still contains 'CNS' in descriptions!</h3>";
            echo "<p>Let me update them now...</p>";
            
            // Update the items
            $updateSql = "UPDATE invoice_items 
                         SET description = REPLACE(description, 'RÉTROCESSION CNS', 'RÉTROCESSION')
                         WHERE invoice_id = :invoice_id 
                         AND description LIKE '%RÉTROCESSION CNS%'";
            
            $updateStmt = $db->prepare($updateSql);
            $updateStmt->execute(['invoice_id' => $invoiceId]);
            $updatedCount = $updateStmt->rowCount();
            
            echo "<p style='color: green;'>✅ Updated $updatedCount item(s)</p>";
            
            // Show updated items
            echo "<h3>After Update:</h3>";
            $stmt->execute(['invoice_id' => $invoiceId]);
            $updatedItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
            echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>Description (Updated)</th></tr>";
            foreach ($updatedItems as $item) {
                echo "<tr>";
                echo "<td>{$item['id']}</td>";
                echo "<td style='background-color: #eeffee;'>{$item['description']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<h3 style='color: green;'>✅ Database is correct - no 'CNS' found in descriptions</h3>";
        }
    }
    
    // Clear cache suggestions
    echo "<h3>If you still see 'CNS' in the invoice view:</h3>";
    echo "<ol>";
    echo "<li><strong>Clear Browser Cache:</strong> Press Ctrl+F5 or Cmd+Shift+R</li>";
    echo "<li><strong>Clear Application Cache:</strong> <a href='/fit/public/clear-cache.php'>Click here if cache clearing script exists</a></li>";
    echo "<li><strong>Check Twig Cache:</strong> The template engine might be caching the view</li>";
    echo "</ol>";
    
    // Show cache directory info
    $cacheDir = dirname(__DIR__) . '/storage/cache';
    if (is_dir($cacheDir)) {
        echo "<p>Cache directory exists at: $cacheDir</p>";
        if (is_dir($cacheDir . '/twig')) {
            echo "<p style='color: orange;'>Twig cache directory found. You may need to clear it.</p>";
        }
    }
    
    echo "<p><a href='/fit/public/invoices/$invoiceId' class='btn' style='background: blue; color: white; padding: 10px; text-decoration: none;'>View Invoice 271</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>