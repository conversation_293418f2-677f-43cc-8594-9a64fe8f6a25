<?php
/**
 * Invoice Generation Simulation Script
 * 
 * This script simulates invoice generation for all types (RET, LOY, LOC)
 * without actually creating anything in the database.
 * 
 * Usage: php simulate_invoice_generation.php [--month=7] [--year=2025] [--user=123]
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Parse command line arguments
$options = getopt('', ['month:', 'year:', 'user:', 'type:']);
$filterMonth = $options['month'] ?? date('n');
$filterYear = $options['year'] ?? date('Y');
$filterUser = $options['user'] ?? null;
$filterType = $options['type'] ?? null;

// Colors for output
$GREEN = "\033[0;32m";
$RED = "\033[0;31m";
$YELLOW = "\033[0;33m";
$BLUE = "\033[0;34m";
$CYAN = "\033[0;36m";
$RESET = "\033[0m";

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die("{$RED}Database connection failed: " . $e->getMessage() . "{$RESET}\n");
}

echo "{$CYAN}=== Invoice Generation Simulation ==={$RESET}\n";
echo "Invoice Generation Month: $filterMonth/$filterYear\n";

// Calculate data months for different invoice types
$dataMonthRET = $filterMonth - 1;
$dataYearRET = $filterYear;
if ($dataMonthRET < 1) {
    $dataMonthRET = 12;
    $dataYearRET--;
}

$dataMonthLOC = $dataMonthRET;
$dataYearLOC = $dataYearRET;

echo "{$YELLOW}Data Periods:{$RESET}\n";
echo "  - RET (Retrocession): Using data from $dataMonthRET/$dataYearRET\n";
echo "  - LOY (Loyer): Using data from $filterMonth/$filterYear\n";
echo "  - LOC (Location): Using data from $dataMonthLOC/$dataYearLOC\n";

if ($filterUser) echo "User ID: $filterUser\n";
if ($filterType) echo "Type: $filterType\n";
echo "\n";

// Check available invoice types
$stmt = $pdo->query("SELECT * FROM config_invoice_types WHERE prefix IN ('RET', 'LOY', 'LOC')");
$invoiceTypes = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $invoiceTypes[$row['prefix']] = $row;
}

if (!isset($invoiceTypes['RET'])) {
    echo "{$YELLOW}WARNING: RET invoice type not configured in config_invoice_types{$RESET}\n";
}

// Helper function to format money
function formatMoney($amount) {
    return '€' . number_format($amount, 2);
}

// Helper function to calculate VAT
function calculateVAT($amount, $rate, $isInclusive = true) {
    if ($isInclusive) {
        // Amount includes VAT, extract base and VAT
        $base = $amount / (1 + $rate / 100);
        $vat = $amount - $base;
        return ['base' => $base, 'vat' => $vat, 'total' => $amount];
    } else {
        // Amount excludes VAT, add VAT
        $vat = $amount * ($rate / 100);
        $total = $amount + $vat;
        return ['base' => $amount, 'vat' => $vat, 'total' => $total];
    }
}

// ===========================================
// SIMULATE RET (RETROCESSION) INVOICES
// ===========================================
if (!$filterType || $filterType === 'RET') {
    echo "\n{$BLUE}=== RET (Retrocession) Invoice Simulation ==={$RESET}\n\n";
    
    // Check if year column exists
    $yearCheck = $pdo->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYearColumn = $yearCheck->rowCount() > 0;
    
    // Get users with retrocession data for the month
    $sql = "
        SELECT 
            u.id as user_id,
            u.first_name,
            u.last_name,
            u.address,
            u.postal_code,
            u.city,
            u.vat_number,
            uma.cns_amount,
            uma.patient_amount,
            urs.cns_value,
            urs.patient_value,
            urs.cns_label,
            urs.patient_label,
            urs.secretary_value,
            urs.secretary_type,
            -- Check if invoice already exists
            ugi.invoice_id as existing_invoice_id,
            i.invoice_number as existing_invoice_number
        FROM user_monthly_retrocession_amounts uma
        JOIN users u ON u.id = uma.user_id
        LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
            AND (urs.valid_from IS NULL OR urs.valid_from <= CURDATE())
            AND (urs.valid_to IS NULL OR urs.valid_to >= CURDATE())
        LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
            AND ugi.invoice_type = 'RET' 
            AND ugi.period_month = :month 
            AND ugi.period_year = :year
        LEFT JOIN invoices i ON i.id = ugi.invoice_id
        WHERE uma.month = :month2" . ($hasYearColumn ? " AND uma.year = :year2" : "") . "
    ";
    
    if ($filterUser) {
        $sql .= " AND u.id = :user_id";
    }
    
    $stmt = $pdo->prepare($sql);
    $params = ['month' => $filterMonth, 'year' => $filterYear, 'month2' => $dataMonthRET];
    if ($hasYearColumn) {
        $params['year2'] = $dataYearRET;
    }
    if ($filterUser) {
        $params['user_id'] = $filterUser;
    }
    $stmt->execute($params);
    
    $retCount = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $retCount++;
        $name = $row['first_name'] . ' ' . $row['last_name'];
        
        echo "{$GREEN}User #{$row['user_id']}: $name{$RESET}\n";
        
        if ($row['existing_invoice_id']) {
            echo "{$YELLOW}  ⚠ Invoice already exists: {$row['existing_invoice_number']}{$RESET}\n\n";
            continue;
        }
        
        // Check settings
        if (!$row['cns_value'] || !$row['patient_value']) {
            echo "{$RED}  ✗ Missing retrocession settings (percentages){$RESET}\n\n";
            continue;
        }
        
        echo "  CNS Amount: " . formatMoney($row['cns_amount']) . "\n";
        echo "  Patient Amount: " . formatMoney($row['patient_amount']) . "\n";
        echo "  CNS %: {$row['cns_value']}%\n";
        echo "  Patient %: {$row['patient_value']}%\n\n";
        
        // Calculate retrocession amounts
        $cnsRetrocession = $row['cns_amount'] * ($row['cns_value'] / 100);
        $patientRetrocession = $row['patient_amount'] * ($row['patient_value'] / 100);
        $subtotal = $cnsRetrocession + $patientRetrocession;
        
        // Secretary calculation using actual percentage from settings
        $secretaryPercentage = $row['secretary_value'] ?? 10;
        $secretaryType = $row['secretary_type'] ?? 'percentage';
        
        if ($secretaryType === 'percentage' && $secretaryPercentage > 0) {
            $secretaryBase = $subtotal * ($secretaryPercentage / 100);
        } else {
            $secretaryBase = $secretaryPercentage;
        }
        
        $secretaryVAT = calculateVAT($secretaryBase, 17, false);
        
        echo "  {$CYAN}Invoice Lines:{$RESET}\n";
        echo "  1. " . ($row['cns_label'] ?: 'Rétrocession CNS') . " " . intval($row['cns_value']) . " %: " . formatMoney($cnsRetrocession) . " (0% VAT)\n";
        echo "  2. " . ($row['patient_label'] ?: 'Rétrocession PATIENTS') . " " . intval($row['patient_value']) . " %: " . formatMoney($patientRetrocession) . " (0% VAT)\n";
        if ($secretaryPercentage > 0) {
            $displayPercent = ($secretaryType === 'percentage') ? intval($secretaryPercentage) . "%" : "";
            echo "  3. Secrétariat" . ($displayPercent ? " ($displayPercent)" : "") . ": " . formatMoney($secretaryVAT['base']) . " + " . formatMoney($secretaryVAT['vat']) . " VAT = " . formatMoney($secretaryVAT['total']) . "\n\n";
        } else {
            echo "\n";
        }
        
        $totalAmount = $subtotal + $secretaryVAT['total'];
        echo "  {$GREEN}TOTAL: " . formatMoney($totalAmount) . "{$RESET}\n";
        echo "  ---\n\n";
    }
    
    if ($retCount === 0) {
        echo "  No users found with retrocession data for $filterMonth/$filterYear\n";
    }
}

// ===========================================
// SIMULATE LOY (LOYER/RENT) INVOICES
// ===========================================
if (!$filterType || $filterType === 'LOY') {
    echo "\n{$BLUE}=== LOY (Loyer/Rent) Invoice Simulation ==={$RESET}\n\n";
    
    // Get users with financial obligations
    $sql = "
        SELECT 
            u.id as user_id,
            u.first_name,
            u.last_name,
            u.address,
            u.postal_code,
            u.city,
            u.vat_number,
            ufo.rent_amount,
            ufo.charges_amount,
            ufo.secretary_tvac_17,
            ufo.secretary_htva,
            ufo.tva_17,
            -- Check if invoice already exists
            ugi.invoice_id as existing_invoice_id,
            i.invoice_number as existing_invoice_number
        FROM user_financial_obligations ufo
        JOIN users u ON u.id = ufo.user_id
        LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
            AND ugi.invoice_type = 'LOY' 
            AND ugi.period_month = :month 
            AND ugi.period_year = :year
        LEFT JOIN invoices i ON i.id = ugi.invoice_id
        WHERE 1=1
    ";
    
    if ($filterUser) {
        $sql .= " AND u.id = :user_id";
    }
    
    $stmt = $pdo->prepare($sql);
    $params = ['month' => $filterMonth, 'year' => $filterYear];
    if ($filterUser) {
        $params['user_id'] = $filterUser;
    }
    $stmt->execute($params);
    
    $loyCount = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $loyCount++;
        $name = $row['first_name'] . ' ' . $row['last_name'];
        
        echo "{$GREEN}User #{$row['user_id']}: $name{$RESET}\n";
        
        if ($row['existing_invoice_id']) {
            echo "{$YELLOW}  ⚠ Invoice already exists: {$row['existing_invoice_number']}{$RESET}\n\n";
            continue;
        }
        
        // Check if any amounts are configured
        if (!$row['rent_amount'] && !$row['charges_amount'] && !$row['secretary_tvac_17'] && !$row['secretary_htva']) {
            echo "{$YELLOW}  ⚠ No financial obligations configured{$RESET}\n\n";
            continue;
        }
        
        echo "  {$CYAN}Invoice Lines:{$RESET}\n";
        
        $totalAmount = 0;
        $lineNum = 1;
        
        // Rent (0% VAT)
        if ($row['rent_amount'] > 0) {
            echo "  $lineNum. Loyer: " . formatMoney($row['rent_amount']) . " (0% VAT)\n";
            $totalAmount += $row['rent_amount'];
            $lineNum++;
        }
        
        // Charges (0% VAT)
        if ($row['charges_amount'] > 0) {
            echo "  $lineNum. Charges: " . formatMoney($row['charges_amount']) . " (0% VAT)\n";
            $totalAmount += $row['charges_amount'];
            $lineNum++;
        }
        
        // Secretary - either TVAC or HTVA+TVA
        if ($row['secretary_tvac_17'] > 0) {
            // Amount includes VAT
            $vat = calculateVAT($row['secretary_tvac_17'], 17, true);
            echo "  $lineNum. Secrétariat: " . formatMoney($vat['base']) . " + " . formatMoney($vat['vat']) . " VAT = " . formatMoney($vat['total']) . "\n";
            $totalAmount += $row['secretary_tvac_17'];
        } elseif ($row['secretary_htva'] > 0) {
            // Amount excludes VAT
            $totalSecretary = $row['secretary_htva'] + ($row['tva_17'] ?? 0);
            echo "  $lineNum. Secrétariat: " . formatMoney($row['secretary_htva']) . " + " . formatMoney($row['tva_17'] ?? 0) . " VAT = " . formatMoney($totalSecretary) . "\n";
            $totalAmount += $totalSecretary;
        }
        
        echo "\n  {$GREEN}TOTAL: " . formatMoney($totalAmount) . "{$RESET}\n";
        echo "  ---\n\n";
    }
    
    if ($loyCount === 0) {
        echo "  No users found with financial obligations\n";
    }
}

// ===========================================
// SIMULATE LOC (LOCATION/COURSE) INVOICES
// ===========================================
if (!$filterType || $filterType === 'LOC') {
    echo "\n{$BLUE}=== LOC (Location/Course) Invoice Simulation ==={$RESET}\n\n";
    
    // Get users with course data for the month
    $sql = "
        SELECT 
            u.id as user_id,
            u.first_name,
            u.last_name,
            u.address,
            u.postal_code,
            u.city,
            u.vat_number,
            cc.course_id,
            cc.course_count,
            c.course_name,
            c.hourly_rate,
            c.vat_rate,
            -- Check if invoice already exists
            ugi.invoice_id as existing_invoice_id,
            i.invoice_number as existing_invoice_number
        FROM user_monthly_course_counts cc
        JOIN users u ON u.id = cc.user_id
        JOIN user_courses c ON c.id = cc.course_id
        LEFT JOIN user_generated_invoices ugi ON ugi.user_id = u.id 
            AND ugi.invoice_type = 'LOC' 
            AND ugi.period_month = :month 
            AND ugi.period_year = :year
        LEFT JOIN invoices i ON i.id = ugi.invoice_id
        WHERE cc.month = :month2 AND cc.year = :year2
    ";
    
    if ($filterUser) {
        $sql .= " AND u.id = :user_id";
    }
    
    $sql .= " ORDER BY u.id, c.course_name";
    
    $stmt = $pdo->prepare($sql);
    $params = ['month' => $filterMonth, 'year' => $filterYear, 'month2' => $dataMonthLOC, 'year2' => $dataYearLOC];
    if ($filterUser) {
        $params['user_id'] = $filterUser;
    }
    $stmt->execute($params);
    
    $currentUserId = null;
    $userCourses = [];
    $locCount = 0;
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // Group courses by user
        if ($currentUserId !== $row['user_id']) {
            // Display previous user if any
            if ($currentUserId !== null && !empty($userCourses)) {
                displayCourseInvoice($userCourses);
            }
            
            $currentUserId = $row['user_id'];
            $userCourses = [];
            
            $name = $row['first_name'] . ' ' . $row['last_name'];
            echo "{$GREEN}User #{$row['user_id']}: $name{$RESET}\n";
            
            if ($row['existing_invoice_id']) {
                echo "{$YELLOW}  ⚠ Invoice already exists: {$row['existing_invoice_number']}{$RESET}\n\n";
                $currentUserId = null; // Skip this user
                continue;
            }
            
            $locCount++;
        }
        
        if ($currentUserId !== null) {
            $userCourses[] = $row;
        }
    }
    
    // Display last user
    if ($currentUserId !== null && !empty($userCourses)) {
        displayCourseInvoice($userCourses);
    }
    
    if ($locCount === 0) {
        echo "  No users found with course data for $dataMonthLOC/$dataYearLOC\n";
    }
}

// Helper function to display course invoice
function displayCourseInvoice($courses) {
    global $CYAN, $GREEN, $RESET;
    
    echo "  {$CYAN}Invoice Lines:{$RESET}\n";
    
    $totalAmount = 0;
    $lineNum = 1;
    
    foreach ($courses as $course) {
        $subtotal = $course['course_count'] * $course['hourly_rate'];
        $vat = calculateVAT($subtotal, $course['vat_rate'], false);
        
        echo "  $lineNum. {$course['course_name']}: {$course['course_count']}h × " . formatMoney($course['hourly_rate']) . "/h = " . formatMoney($subtotal);
        if ($course['vat_rate'] > 0) {
            echo " + " . formatMoney($vat['vat']) . " VAT ({$course['vat_rate']}%) = " . formatMoney($vat['total']);
        }
        echo "\n";
        
        $totalAmount += $vat['total'];
        $lineNum++;
    }
    
    echo "\n  {$GREEN}TOTAL: " . formatMoney($totalAmount) . "{$RESET}\n";
    echo "  ---\n\n";
}

// Summary
echo "\n{$CYAN}=== Summary ==={$RESET}\n";
echo "Month: $filterMonth/$filterYear\n";

// Count potential invoices
$sql = "SELECT 
    'RET' as type,
    COUNT(DISTINCT uma.user_id) as count
FROM user_monthly_retrocession_amounts uma
LEFT JOIN user_generated_invoices ugi ON ugi.user_id = uma.user_id 
    AND ugi.invoice_type = 'RET' 
    AND ugi.period_month = :month 
    AND ugi.period_year = :year
WHERE uma.month = :month2" . ($hasYearColumn ? " AND uma.year = :year2" : "") . " AND ugi.id IS NULL

UNION ALL

SELECT 
    'LOY' as type,
    COUNT(DISTINCT ufo.user_id) as count
FROM user_financial_obligations ufo
LEFT JOIN user_generated_invoices ugi ON ugi.user_id = ufo.user_id 
    AND ugi.invoice_type = 'LOY' 
    AND ugi.period_month = :month3 
    AND ugi.period_year = :year3
WHERE ugi.id IS NULL

UNION ALL

SELECT 
    'LOC' as type,
    COUNT(DISTINCT cc.user_id) as count
FROM user_monthly_course_counts cc
LEFT JOIN user_generated_invoices ugi ON ugi.user_id = cc.user_id 
    AND ugi.invoice_type = 'LOC' 
    AND ugi.period_month = :month4
    AND ugi.period_year = :year4
WHERE cc.month = :month5 AND cc.year = :year5 AND ugi.id IS NULL";

$stmt = $pdo->prepare($sql);
$executeParams = [
    'month' => $filterMonth, 'year' => $filterYear,
    'month2' => $dataMonthRET,
    'month3' => $filterMonth, 'year3' => $filterYear,
    'month4' => $filterMonth, 'year4' => $filterYear,
    'month5' => $dataMonthLOC, 'year5' => $dataYearLOC
];
if ($hasYearColumn) {
    $executeParams['year2'] = $dataYearRET;
}
$stmt->execute($executeParams);

echo "\nPotential invoices to generate:\n";
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "  {$row['type']}: {$row['count']} invoices\n";
}

echo "\n{$YELLOW}Note: This is a simulation. No invoices were created.{$RESET}\n";
echo "To generate actual invoices, use the bulk generation page or the unified invoice generator.\n";