<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Document Types Configuration</h2>";
    
    // Check document_types table
    echo "<h3>document_types table:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM document_types 
        WHERE code = 'invoice'
    ");
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($docType) {
        echo "<p>Invoice document type:</p>";
        echo "<ul>";
        echo "<li>ID: {$docType['id']}</li>";
        echo "<li>Code: {$docType['code']}</li>";
        echo "<li>Prefix: <strong>{$docType['prefix']}</strong></li>";
        echo "</ul>";
    }
    
    // Check config_invoice_types
    echo "<h3>config_invoice_types table (RET types):</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret2', 'ret3')
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['code']}</td>";
        echo "<td><strong>{$type['prefix']}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>The Problem:</h3>";
    echo "<p>When generating invoice numbers, the system combines:</p>";
    echo "<ol>";
    echo "<li>Document type prefix: <strong>{$docType['prefix']}</strong></li>";
    echo "<li>Invoice type prefix: <strong>FAC-RET25</strong></li>";
    echo "<li>Year: <strong>2025</strong></li>";
    echo "<li>Number: <strong>0200</strong></li>";
    echo "</ol>";
    echo "<p>Result: <strong>FAC-FAC-RET25-2025-0200</strong></p>";
    
    echo "<h3>Solution Options:</h3>";
    echo "<ol>";
    echo "<li><strong>Option 1:</strong> Change config_invoice_types prefixes from 'FAC-RET25' to just 'RET25'</li>";
    echo "<li><strong>Option 2:</strong> Update the invoice generation logic to handle prefixes that already contain 'FAC-'</li>";
    echo "</ol>";
    
    echo "<h3>Applying Option 1 (Simplest fix):</h3>";
    
    // Update the prefixes
    $updates = [
        ['code' => 'ret2', 'new_prefix' => 'RET25'],
        ['code' => 'ret3', 'new_prefix' => 'RET30']
    ];
    
    foreach ($updates as $update) {
        $stmt = $db->prepare("UPDATE config_invoice_types SET prefix = :prefix WHERE code = :code");
        $stmt->execute(['prefix' => $update['new_prefix'], 'code' => $update['code']]);
        echo "<p>✓ Updated {$update['code']} prefix to <strong>{$update['new_prefix']}</strong></p>";
    }
    
    // Show final state
    echo "<h3>After fix:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret2', 'ret3')
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>New Prefix</th><th>Result</th></tr>";
    foreach ($types as $type) {
        $example = "FAC-{$type['prefix']}-2025-0200";
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['code']}</td>";
        echo "<td><strong>{$type['prefix']}</strong></td>";
        echo "<td><strong>{$example}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✓ Fixed!</h3>";
    echo "<p>The prefixes have been updated. Now invoice generation will produce:</p>";
    echo "<ul>";
    echo "<li>FAC-RET25-2025-XXXX (for 5% secretary)</li>";
    echo "<li>FAC-RET30-2025-XXXX (for 10% secretary)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}