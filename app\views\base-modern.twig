<!DOCTYPE html>
<html lang="{{ app.language|default('fr') }}" data-bs-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{{ title|default('Dashboard') }}{% endblock %} - {{ app_name }}</title>
    <meta name="author" content="{{ app_name }}">
    <meta name="description" content="{{ app_name }} - Modern Health Center Billing System">
    <link rel="icon" type="image/x-icon" href="{{ base_url }}/favicon.ico">
    <meta name="csrf-token" content="{{ csrf_token|default('') }}">
    
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <!-- Modern Theme CSS -->
    <link rel="stylesheet" href="{{ base_url }}/assets/css/modern-theme.css">
    
    <!-- jQuery - Load early to support inline scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Dynamic Color Scheme CSS -->
    {% if activeColorScheme is defined and activeColorScheme %}
    <style id="color-scheme-styles">
        :root {
{{ activeColorScheme.getCssVariables()|raw }}
        }
        
        /* Override Bootstrap and theme colors with higher specificity */
        body {
            background-color: var(--bs-body-bg) !important;
            color: var(--bs-body-color) !important;
        }
        
        /* Sidebar Styling */
        .app-sidebar {
            background-color: var(--sidebar-bg) !important;
            color: var(--sidebar-text) !important;
        }
        
        .app-sidebar .sidebar-brand {
            background-color: var(--sidebar-bg) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .app-sidebar .brand-link {
            color: var(--sidebar-text) !important;
        }
        
        .app-sidebar .nav-link {
            color: var(--sidebar-text) !important;
            transition: all 0.3s ease;
        }
        
        .app-sidebar .nav-link:hover {
            background-color: var(--sidebar-hover) !important;
            color: #fff !important;
        }
        
        .app-sidebar .nav-link.active {
            background-color: var(--sidebar-active) !important;
            color: #fff !important;
        }
        
        .app-sidebar .nav-header {
            color: var(--sidebar-text) !important;
            opacity: 0.6;
        }
        
        /* Navbar/Header Styling */
        .app-header {
            background-color: var(--navbar-bg) !important;
            color: var(--navbar-text) !important;
            border-bottom: 1px solid var(--bs-border-color) !important;
        }
        
        .app-header .btn-link {
            color: var(--navbar-text) !important;
        }
        
        .app-header .navbar-brand {
            color: var(--navbar-text) !important;
        }
        
        .app-header .dropdown-toggle {
            color: var(--navbar-text) !important;
        }
        
        .app-header .user-menu span {
            color: var(--navbar-text) !important;
        }
        
        /* Search bar in navbar */
        .navbar-search .form-control {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: var(--navbar-text) !important;
        }
        
        .navbar-search .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6) !important;
        }
        
        .navbar-search .form-control:focus {
            background-color: rgba(255, 255, 255, 0.15) !important;
            border-color: var(--bs-primary) !important;
        }
        
        .navbar-search .search-icon {
            color: var(--navbar-text) !important;
            opacity: 0.7;
        }
        
        /* Card Styling */
        .card {
            background-color: var(--bs-card-bg) !important;
            border-color: var(--bs-border-color) !important;
        }
        
        /* Button Styling */
        .btn-primary {
            background-color: var(--bs-primary) !important;
            border-color: var(--bs-primary) !important;
        }
        
        .btn-secondary {
            background-color: var(--bs-secondary) !important;
            border-color: var(--bs-secondary) !important;
        }
        
        .btn-success {
            background-color: var(--bs-success) !important;
            border-color: var(--bs-success) !important;
        }
        
        .btn-danger {
            background-color: var(--bs-danger) !important;
            border-color: var(--bs-danger) !important;
        }
        
        .btn-warning {
            background-color: var(--bs-warning) !important;
            border-color: var(--bs-warning) !important;
        }
        
        .btn-info {
            background-color: var(--bs-info) !important;
            border-color: var(--bs-info) !important;
        }
        
        .text-primary { color: var(--bs-primary) !important; }
        .text-secondary { color: var(--bs-secondary) !important; }
        .text-success { color: var(--bs-success) !important; }
        .text-danger { color: var(--bs-danger) !important; }
        .text-warning { color: var(--bs-warning) !important; }
        .text-info { color: var(--bs-info) !important; }
        
        .bg-primary { background-color: var(--bs-primary) !important; }
        .bg-secondary { background-color: var(--bs-secondary) !important; }
        .bg-success { background-color: var(--bs-success) !important; }
        .bg-danger { background-color: var(--bs-danger) !important; }
        .bg-warning { background-color: var(--bs-warning) !important; }
        .bg-info { background-color: var(--bs-info) !important; }
        
        /* Dropdown menus */
        .dropdown-menu {
            background-color: var(--bs-card-bg) !important;
            border-color: var(--bs-border-color) !important;
        }
        
        .dropdown-item {
            color: var(--bs-body-color) !important;
        }
        
        .dropdown-item:hover {
            background-color: var(--bs-primary) !important;
            color: #fff !important;
        }
        
        .dropdown-divider {
            border-color: var(--bs-border-color) !important;
        }
        
        /* Notification badge on navbar */
        .notification-bell .badge {
            background-color: var(--bs-danger) !important;
        }
        
        /* User avatar in navbar */
        .user-image {
            border: 2px solid var(--navbar-text) !important;
        }
        
        /* Sidebar submenu */
        .app-sidebar .nav-treeview {
            background-color: rgba(0, 0, 0, 0.1) !important;
        }
        
        .app-sidebar .nav-treeview .nav-link {
            padding-left: 2.5rem;
            color: var(--sidebar-text) !important;
            opacity: 0.8;
        }
        
        .app-sidebar .nav-treeview .nav-link:hover {
            opacity: 1;
        }
        
        /* Sidebar icons */
        .app-sidebar .nav-icon {
            color: var(--sidebar-text) !important;
        }
        
        .app-sidebar .nav-link.active .nav-icon {
            color: #fff !important;
        }
        
        /* Fix for config page icon circles */
        .rounded-circle[style*="width: 80px"] {
            box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.05);
        }
        
        /* Ensure icons are visible */
        .rounded-circle i {
            filter: brightness(0.9) contrast(1.2);
        }
    </style>
    <!-- Color Scheme Cache Buster: {{ "now"|date("YmdHis") }} -->
    {% endif %}
    
    <!-- Inline critical CSS for loading overlay -->
    <style>
        #loading-overlay {
            transition: opacity 0.3s ease-out;
        }
        #loading-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        /* Ensure nav icons and text are properly aligned */
        .sidebar-menu .nav-link {
            display: flex !important;
            align-items: center !important;
        }
        
        .sidebar-menu .nav-link .nav-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }
        
        .sidebar-menu .nav-link p {
            margin-bottom: 0;
            line-height: 1;
        }
        
    </style>
    
    <style>
        /* Global dropdown fixes */
        .dropdown {
            position: relative !important;
        }
        
        .dropdown-menu {
            margin: 0 !important;
            border: 1px solid rgba(0,0,0,.15);
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
        }
        
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        /* Fix for dropdowns in tables */
        .table .dropdown {
            position: static;
        }
        
        .table .dropdown-menu {
            position: absolute !important;
        }
        
        /* Ensure dropdown buttons show pointer cursor */
        [data-dropdown-fixed] {
            cursor: pointer;
        }
        
        /* Right-aligned dropdown menus */
        .dropdown-menu[style*="right: 0"] {
            right: 0 !important;
            left: auto !important;
        }
        
        /* Minimum width for dropdown menus */
        .dropdown-menu {
            min-width: 160px;
        }
    </style>
    
    <!-- Icon Spacing Fix -->
    <link rel="stylesheet" href="{{ base_url }}/css/icon-spacing-fix.css">
    
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 bg-white d-flex align-items-center justify-content-center" style="z-index: 9999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- Immediate script to remove overlay after max 3 seconds -->
    <script>
        // Debug logging
        console.log('Modern template loading...');
        console.log('Base URL:', '{{ base_url }}');
        console.log('Template:', '{{ session.template|default("unknown") }}');
        
        setTimeout(function() {
            var overlay = document.getElementById('loading-overlay');
            if (overlay) {
                console.log('Removing loading overlay (3s timeout)');
                overlay.style.display = 'none';
            }
        }, 3000);
    </script>
    
    <!-- Noscript fallback -->
    <noscript>
        <style>
            #loading-overlay { display: none !important; }
        </style>
    </noscript>

    <!-- App Wrapper -->
    <div class="app-wrapper">
        <!-- Header/Navbar -->
        <nav class="app-header navbar navbar-expand">
            <div class="container-fluid">
                <!-- Left Side -->
                <div class="d-flex align-items-center">
                    <!-- Sidebar Toggle -->
                    <button class="btn btn-link p-2" id="sidebar-toggle">
                        <i class="bi bi-list fs-4"></i>
                    </button>
                    
                    <!-- Brand (visible on mobile) -->
                    <a class="navbar-brand d-lg-none ms-2" href="{{ base_url }}/">
                        {{ app_name }}
                    </a>
                </div>

                <!-- Center - Search Bar (desktop only) -->
                <div class="navbar-search d-none d-md-block mx-auto">
                    <form action="{{ base_url }}/search" method="get" id="navbarSearchForm">
                        <div class="position-relative">
                            <i class="bi bi-search search-icon"></i>
                            <input type="search" name="q" class="form-control" placeholder="{{ __('common.search') }}..." autocomplete="off" id="navbarSearchInput">
                        </div>
                    </form>
                </div>

                <!-- Right Side -->
                <div class="d-flex align-items-center gap-2">
                    <!-- Theme Toggle -->
                    <button class="btn btn-link p-2 theme-toggle" id="theme-toggle" title="{{ __('common.toggle_theme') }}">
                        <i class="bi bi-sun-fill fs-5" id="theme-icon"></i>
                    </button>

                    <!-- Notifications -->
                    <div class="dropdown">
                        <button class="btn btn-link p-2 notification-bell" data-bs-toggle="dropdown">
                            <i class="bi bi-bell fs-5"></i>
                            <span class="badge bg-danger">3</span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-lg p-0" style="width: 350px;">
                            <div class="dropdown-header bg-primary text-white">
                                <h6 class="mb-0">{{ __('common.notifications') }}</h6>
                            </div>
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-start">
                                        <div class="text-primary me-3">
                                            <i class="bi bi-envelope-fill"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">New invoice created</h6>
                                            <p class="mb-1 text-muted small">Invoice #INV-2024-001 has been created</p>
                                            <small class="text-muted">5 minutes ago</small>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-start">
                                        <div class="text-success me-3">
                                            <i class="bi bi-check-circle-fill"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Payment received</h6>
                                            <p class="mb-1 text-muted small">Payment of $500 received from John Doe</p>
                                            <small class="text-muted">2 hours ago</small>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-start">
                                        <div class="text-warning me-3">
                                            <i class="bi bi-exclamation-triangle-fill"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Invoice overdue</h6>
                                            <p class="mb-1 text-muted small">Invoice #INV-2023-145 is now overdue</p>
                                            <small class="text-muted">1 day ago</small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="dropdown-footer text-center py-2">
                                <a href="{{ base_url }}/notifications" class="text-primary text-decoration-none">
                                    {{ __('common.view_all_notifications') }} <i class="bi bi-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="dropdown user-menu">
                        <button class="dropdown-toggle border-0 bg-transparent" data-bs-toggle="dropdown">
                            {% if session.user_avatar %}
                                <img src="{{ base_url }}/uploads/avatars/{{ session.user_avatar }}" class="user-image" alt="User">
                            {% else %}
                                <div class="user-image bg-primary text-white d-flex align-items-center justify-content-center">
                                    {{ session.user_name|default('U')|first|upper }}
                                </div>
                            {% endif %}
                            <span class="d-none d-md-inline ms-2">{{ session.user_name|default('User') }}</span>
                            <i class="bi bi-chevron-down ms-1"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <div class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    {% if session.user_avatar %}
                                        <img src="{{ base_url }}/uploads/avatars/{{ session.user_avatar }}" class="rounded-circle me-3" style="width: 48px; height: 48px;" alt="User">
                                    {% else %}
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                                            {{ session.user_name|default('U')|first|upper }}
                                        </div>
                                    {% endif %}
                                    <div>
                                        <h6 class="mb-0">{{ session.user_name|default('User') }}</h6>
                                        <small class="text-muted">{{ session.user_email|default('') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ base_url }}/profile">
                                <i class="bi bi-person me-2"></i> {{ __('users.my_profile') }}
                            </a>
                            <a class="dropdown-item" href="{{ base_url }}/settings">
                                <i class="bi bi-gear me-2"></i> {{ __('common.settings') }}
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-danger" href="{{ base_url }}/logout">
                                <i class="bi bi-box-arrow-right me-2"></i> {{ __('auth.logout') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <a href="{{ base_url }}/" class="brand-link">
                    <i class="bi bi-heart-pulse-fill text-primary fs-4"></i>
                    <span class="brand-text">{{ app_name }}</span>
                </a>
            </div>
            
            <!-- Sidebar Menu -->
            <div class="sidebar-wrapper">
                <nav>
                    <ul class="sidebar-menu nav flex-column">
                        {% block sidebar %}
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ base_url }}/" class="nav-link {{ current_route == 'home' ? 'active' : '' }}">
                                <i class="nav-icon bi bi-speedometer2"></i>
                                <p>{{ __('common.dashboard') }}</p>
                            </a>
                        </li>
                        
                        <!-- Patients -->
                        {% if shouldShowMenu('patients') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/patients" class="nav-link {{ current_route starts with 'patients' ? 'active' : '' }} {{ getMenuClass('patients') }}">
                                <i class="nav-icon fas fa-user-injured"></i>
                                <p>{{ __('patients.title') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Clients -->
                        {% if shouldShowMenu('clients') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/clients" class="nav-link {{ current_route starts with 'clients' ? 'active' : '' }} {{ getMenuClass('clients') }}">
                                <i class="nav-icon bi bi-building"></i>
                                <p>{{ __('clients.title') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Invoices -->
                        {% if shouldShowMenu('invoices') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/invoices" class="nav-link {{ current_route starts with 'invoices' ? 'active' : '' }} {{ getMenuClass('invoices') }}">
                                <i class="nav-icon bi bi-file-earmark-text"></i>
                                <p>{{ __('invoices.invoices') }}</p>
                                <span class="badge bg-warning ms-auto">12</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Products -->
                        {% if shouldShowMenu('products') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/products" class="nav-link {{ current_route starts with 'products' ? 'active' : '' }} {{ getMenuClass('products') }}">
                                <i class="nav-icon bi bi-box-seam"></i>
                                <p>{{ __('products.title') | default('Products') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- POS -->
                        {% if shouldShowMenu('pos') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/pos" class="nav-link {{ current_route starts with 'pos' ? 'active' : '' }} {{ getMenuClass('pos') }}">
                                <i class="nav-icon bi bi-cash-register"></i>
                                <p>{{ __('pos.title') | default('Point of Sale') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Users (with submenu) -->
                        {% if shouldShowMenu('users') %}
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#users-menu">
                                <i class="nav-icon bi bi-people"></i>
                                <p>
                                    {{ __('users.users') }}
                                    <i class="nav-arrow bi bi-chevron-right ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview collapse" id="users-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/users" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('users.user_list') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/users/groups" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('users.user_groups') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Configuration (no submenu) -->
                        {% if shouldShowMenu('config') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/config" class="nav-link {{ current_route starts with 'config' ? 'active' : '' }}">
                                <i class="nav-icon bi bi-gear"></i>
                                <p>{{ __('config.configuration') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Translations (with submenu) -->
                        {% if shouldShowMenu('translations') %}
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#translations-menu">
                                <i class="nav-icon bi bi-translate"></i>
                                <p>
                                    {{ __('translations.translations') }}
                                    <i class="nav-arrow bi bi-chevron-right ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview collapse" id="translations-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/translations" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('translations.editor') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/translations/multilingual" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('translations.multilang_editor') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/translations/diagnostic" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('translations.diagnostic') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Billing & Finance (with submenu) -->
                        {% if shouldShowMenu('billing') %}
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#billing-menu">
                                <i class="nav-icon bi bi-cash-coin"></i>
                                <p>
                                    {{ __('common.billing') }}
                                    <i class="nav-arrow bi bi-chevron-right ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview collapse" id="billing-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/billing-wizard" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('invoices.billing_wizard') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/retrocession" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('invoices.retrocession') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/vouchers" class="nav-link">
                                        <i class="nav-icon bi bi-circle"></i>
                                        <p>{{ __('invoices.vouchers') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Admin Menu - Only for admins -->
                        {% if session.user is defined and session.user.is_admin == true %}
                        <li class="nav-item mt-3">
                            <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#admin-menu">
                                <i class="nav-icon bi bi-shield-lock"></i>
                                <p>
                                    {{ __('admin.admin_tools') }}
                                    <i class="nav-arrow bi bi-chevron-right ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview collapse" id="admin-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/admin/menu-config" class="nav-link">
                                        <i class="nav-icon bi bi-menu-button-wide"></i>
                                        <p>{{ __('admin.menu_configuration') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        {% endblock %}
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Content Header -->
            <div class="app-content-header">
                <div class="container-fluid">
                    <div class="row align-items-center">
                        <div class="col-sm-6">
                            <h1>{% block page_title %}{{ title|default('Dashboard') }}{% endblock %}</h1>
                        </div>
                        <div class="col-sm-6">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb float-sm-end mb-0">
                                    {% block breadcrumb %}
                                    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('common.home') }}</a></li>
                                    <li class="breadcrumb-item active">{{ title|default('Dashboard') }}</li>
                                    {% endblock %}
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content -->
            <div class="app-content">
                <div class="container-fluid">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer text-center py-3 mt-auto">
            <div class="container-fluid">
                <span class="text-muted">&copy; {{ 'now'|date('Y') }} {{ app_name }}. {{ __('common.all_rights_reserved') }}</span>
                <span class="float-end">Version 2.0.0</span>
            </div>
        </footer>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toast-container" style="z-index: 1100;"></div>

    <!-- Off-canvas Sidebar for Mobile -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobile-sidebar">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">{{ app_name }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body p-0">
            <!-- Mobile menu content will be cloned here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- Table Helper (if needed) -->
    {% block table_helper %}{% endblock %}
    
    <!-- Auto-initialize Table Helper -->
    <script src="{{ base_url }}/js/table-helper-init.js"></script>
    
    <!-- Custom Scripts -->
    <script>
        // Configure toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // Remove loading overlay when DOM is ready (faster than waiting for all resources)
        document.addEventListener('DOMContentLoaded', function() {
            // Set a timeout to remove overlay even if other scripts fail
            setTimeout(function() {
                var overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.classList.add('fade-out');
                    setTimeout(function() {
                        overlay.style.display = 'none';
                    }, 300);
                }
            }, 100);
        });
        
        // Also remove on full page load as backup
        window.addEventListener('load', function() {
            var overlay = document.getElementById('loading-overlay');
            if (overlay && overlay.style.display !== 'none') {
                overlay.classList.add('fade-out');
                setTimeout(function() {
                    overlay.style.display = 'none';
                }, 300);
            }
        });
        
        // Error handler to ensure overlay is removed even if there are JS errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.message);
            var overlay = document.getElementById('loading-overlay');
            if (overlay && overlay.style.display !== 'none') {
                overlay.style.display = 'none';
            }
        });

        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const htmlElement = document.documentElement;
        
        if (themeToggle && themeIcon) {
            // Check for saved theme preference
            const savedTheme = localStorage.getItem('theme') || 'light';
            htmlElement.setAttribute('data-bs-theme', savedTheme);
            updateThemeIcon(savedTheme);
            
            themeToggle.addEventListener('click', function() {
                const currentTheme = htmlElement.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                
                htmlElement.setAttribute('data-bs-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });
            
            function updateThemeIcon(theme) {
                if (theme === 'dark') {
                    themeIcon.classList.remove('bi-sun-fill');
                    themeIcon.classList.add('bi-moon-fill');
                } else {
                    themeIcon.classList.remove('bi-moon-fill');
                    themeIcon.classList.add('bi-sun-fill');
                }
            }
        }

        // Sidebar Toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('sidebar-collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // Restore sidebar state
            if (localStorage.getItem('sidebar-collapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }
        }

        // Mobile Sidebar
        if (window.innerWidth < 992) {
            const mobileSidebarEl = document.getElementById('mobile-sidebar');
            if (mobileSidebarEl && sidebarToggle) {
                const mobileSidebar = new bootstrap.Offcanvas(mobileSidebarEl);
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    mobileSidebar.show();
                });
                
                // Clone sidebar content to mobile offcanvas
                const sidebarContent = document.querySelector('.sidebar-wrapper');
                const offcanvasBody = document.querySelector('#mobile-sidebar .offcanvas-body');
                if (sidebarContent && offcanvasBody) {
                    offcanvasBody.appendChild(sidebarContent.cloneNode(true));
                }
            }
        }

        // Submenu Toggle Animation
        document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]').forEach(link => {
            link.addEventListener('click', function() {
                const icon = this.querySelector('.nav-arrow');
                if (icon) {
                    icon.style.transform = this.getAttribute('aria-expanded') === 'true' ? 'rotate(90deg)' : 'rotate(0)';
                }
            });
        });

        // Toast Notification Function
        function showToast(message, type = 'info') {
            try {
                // Check if Bootstrap is loaded
                if (typeof bootstrap === 'undefined') {
                    console.error('Bootstrap is not loaded');
                    return;
                }
                
                const toastContainer = document.getElementById('toast-container');
                if (!toastContainer) {
                    console.error('Toast container not found');
                    return;
                }
                
                const toastHtml = `
                    <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
                        <div class="d-flex">
                            <div class="toast-body">
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;
                
                const toastWrapper = document.createElement('div');
                toastWrapper.innerHTML = toastHtml;
                const toastElement = toastWrapper.firstElementChild;
                
                if (!toastElement) {
                    console.error('Failed to create toast element');
                    return;
                }
                
                toastContainer.appendChild(toastElement);
                
                // Small delay to ensure DOM is ready
                setTimeout(() => {
                    try {
                        const toast = new bootstrap.Toast(toastElement);
                        toast.show();
                    } catch (e) {
                        console.error('Error showing toast:', e);
                    }
                }, 10);
                
            } catch (error) {
                console.error('Error in showToast:', error);
            }
        }

        // Display Flash Messages
        document.addEventListener('DOMContentLoaded', function() {
            {% if flash.success is defined and flash.success %}
                showToast('{{ flash.success|e('js') }}', 'success');
            {% endif %}
            {% if flash.error is defined and flash.error %}
                showToast('{{ flash.error|e('js') }}', 'danger');
            {% endif %}
            {% if flash.warning is defined and flash.warning %}
                showToast('{{ flash.warning|e('js') }}', 'warning');
            {% endif %}
            {% if flash.info is defined and flash.info %}
                showToast('{{ flash.info|e('js') }}', 'info');
            {% endif %}
        });

        // Add fade-in animation to cards
        document.querySelectorAll('.card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.animation = `fadeIn 0.5s ease-out ${index * 0.1}s forwards`;
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const href = this.getAttribute('href');
                if (href && href !== '#') {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });
        
        // Prevent empty search form submission and Enter key interference
        const navbarSearchForm = document.getElementById('navbarSearchForm');
        const navbarSearchInput = document.getElementById('navbarSearchInput');
        
        if (navbarSearchForm && navbarSearchInput) {
            // Prevent form submission on Enter if search is empty
            navbarSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && this.value.trim() === '') {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            });
            
            // Prevent form submission if search is empty
            navbarSearchForm.addEventListener('submit', function(e) {
                if (navbarSearchInput.value.trim() === '') {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            });
            
            // Remove focus from search input when clicking outside
            document.addEventListener('click', function(e) {
                if (!navbarSearchForm.contains(e.target)) {
                    navbarSearchInput.blur();
                }
            });
        }
    </script>
    
    <script>
    // Global dropdown fix for all pages - DISABLED to let Bootstrap handle dropdowns
    document.addEventListener('DOMContentLoaded', function() {
        // Remove any inline styles that might be hiding dropdowns
        document.querySelectorAll('.dropdown-menu[style*="display: none"]').forEach(menu => {
            menu.style.display = '';
        });
        
        // Initialize Bootstrap dropdowns if needed
        document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(dropdownToggle => {
            if (!dropdownToggle.hasAttribute('data-dropdown-fixed')) {
                new bootstrap.Dropdown(dropdownToggle);
            }
        });
        
        return; // Skip the custom dropdown code below
        
        // Fix all dropdowns to work without Bootstrap's auto-handling
        function fixDropdown(dropdownBtn) {
            const dropdownDiv = dropdownBtn.closest('.dropdown');
            const dropdownMenu = dropdownDiv?.querySelector('.dropdown-menu');
            
            if (!dropdownMenu || dropdownBtn.hasAttribute('data-dropdown-fixed')) {
                return;
            }
            
            // Mark as fixed to avoid duplicate processing
            dropdownBtn.setAttribute('data-dropdown-fixed', 'true');
            
            // Clone button to remove Bootstrap handlers
            const newBtn = dropdownBtn.cloneNode(true);
            dropdownBtn.parentNode.replaceChild(newBtn, dropdownBtn);
            
            // Remove Bootstrap attributes
            newBtn.removeAttribute('data-bs-toggle');
            newBtn.removeAttribute('data-bs-target');
            newBtn.removeAttribute('data-bs-boundary');
            newBtn.removeAttribute('data-bs-flip');
            
            let isOpen = false;
            
            // Simple click handler
            newBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                if (this.disabled) return;
                
                // Close all other dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    if (menu !== dropdownMenu) {
                        menu.style.display = 'none';
                        menu.classList.remove('show');
                        menu.closest('.dropdown')?.querySelector('button')?.classList.remove('show');
                    }
                });
                
                isOpen = !isOpen;
                
                if (isOpen) {
                    // Show menu
                    dropdownMenu.style.display = 'block';
                    dropdownMenu.style.position = 'absolute';
                    dropdownMenu.style.top = '100%';
                    dropdownMenu.style.zIndex = '10000';
                    
                    // Check if dropdown would go off the right edge
                    const btnRect = this.getBoundingClientRect();
                    const menuWidth = dropdownMenu.offsetWidth || 200;
                    const windowWidth = window.innerWidth;
                    
                    if (btnRect.left + menuWidth > windowWidth - 20) {
                        // Align to right edge of button
                        dropdownMenu.style.left = 'auto';
                        dropdownMenu.style.right = '0';
                    } else {
                        // Align to left edge of button
                        dropdownMenu.style.left = '0';
                        dropdownMenu.style.right = 'auto';
                    }
                    
                    dropdownMenu.classList.add('show');
                    this.classList.add('show');
                    this.setAttribute('aria-expanded', 'true');
                } else {
                    // Hide menu
                    dropdownMenu.style.display = 'none';
                    dropdownMenu.classList.remove('show');
                    this.classList.remove('show');
                    this.setAttribute('aria-expanded', 'false');
                }
            });
            
            // Store reference for outside click handler
            dropdownMenu._dropdownBtn = newBtn;
            dropdownMenu._isOpen = () => isOpen;
            dropdownMenu._setOpen = (val) => { isOpen = val; };
        }
        
        // Fix existing dropdowns
        document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(fixDropdown);
        
        // Watch for new dropdowns added dynamically
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.hasAttribute && node.hasAttribute('data-bs-toggle') && node.getAttribute('data-bs-toggle') === 'dropdown') {
                            fixDropdown(node);
                        }
                        // Check children
                        if (node.querySelectorAll) {
                            node.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(fixDropdown);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Global click handler to close dropdowns
        document.addEventListener('click', function(e) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                const btn = menu._dropdownBtn;
                const isOpen = menu._isOpen;
                const setOpen = menu._setOpen;
                
                if (btn && isOpen && setOpen) {
                    const dropdownDiv = menu.closest('.dropdown');
                    if (!dropdownDiv?.contains(e.target)) {
                        setOpen(false);
                        menu.style.display = 'none';
                        menu.classList.remove('show');
                        btn.classList.remove('show');
                        btn.setAttribute('aria-expanded', 'false');
                    }
                }
            });
        });
    });
    </script>
    
    <!-- Simple Dropdown Fix Script -->
    <script src="{{ base_url }}/js/dropdown-fix-simple.js"></script>
    
    <!-- Invoice Form Fix - Load on invoice pages -->
    {% if 'invoice' in app.request.uri %}
        <script src="{{ base_url }}/js/fix-invoice-form.js"></script>
    {% endif %}
    
    {% block scripts %}{% endblock %}
</body>
</html>