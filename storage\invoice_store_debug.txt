2025-07-18 15:08:56 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

2025-07-19 10:59:30 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

2025-07-19 12:00:47 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

2025-07-19 12:37:13 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

2025-07-19 12:37:52 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

2025-07-19 16:33:41 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date, action, send_email

2025-07-19 16:42:15 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date, action, send_email

2025-07-19 16:47:49 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date, action, send_email

2025-07-19 17:10:15 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date, action, send_email

2025-07-19 17:14:31 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date, action, send_email

2025-07-19 17:47:32 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date, action, send_email

