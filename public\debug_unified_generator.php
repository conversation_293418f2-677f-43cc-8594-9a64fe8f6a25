<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Debugging UnifiedInvoiceGenerator for Rémi</h2>";
    
    // Simulate UnifiedInvoiceGenerator
    $userId = 18; // Rémi
    $month = 7; // July (current month for generation)
    $year = 2025;
    
    // For RET, we use previous month
    $dataMonth = $month - 1; // June = 6
    $dataYear = $year;
    if ($dataMonth < 1) {
        $dataMonth = 12;
        $dataYear--;
    }
    
    echo "<h3>Generation Context:</h3>";
    echo "<p>User ID: $userId (Rémi Heine)</p>";
    echo "<p>Generating in month/year: $month/$year</p>";
    echo "<p>Using data from month/year: $dataMonth/$dataYear</p>";
    
    // Check year column existence (like UnifiedInvoiceGenerator does)
    $yearCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYearColumn = $yearCheck->rowCount() > 0;
    echo "<p>Has year column: " . ($hasYearColumn ? "YES" : "NO") . "</p>";
    
    // Execute the EXACT same query as UnifiedInvoiceGenerator
    echo "<h3>Executing UnifiedInvoiceGenerator Query:</h3>";
    
    if ($hasYearColumn) {
        echo "<p>Using query WITH year:</p>";
        echo "<pre style='background: #f5f5f5; padding: 10px;'>";
        echo "SELECT cns_amount, patient_amount 
FROM user_monthly_retrocession_amounts 
WHERE user_id = $userId AND month = $dataMonth AND year = $dataYear";
        echo "</pre>";
        
        $stmt = $db->prepare("
            SELECT cns_amount, patient_amount 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month AND year = :year
        ");
        $stmt->execute(['user_id' => $userId, 'month' => $dataMonth, 'year' => $dataYear]);
    } else {
        echo "<p>Using query WITHOUT year:</p>";
        $stmt = $db->prepare("
            SELECT cns_amount, patient_amount 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month
        ");
        $stmt->execute(['user_id' => $userId, 'month' => $dataMonth]);
    }
    
    $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Query Result:</h3>";
    if ($amounts) {
        echo "<p style='color: green;'>✓ Found data:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><td>CNS Amount</td><td>" . number_format($amounts['cns_amount'], 2) . " €</td></tr>";
        echo "<tr><td>Patient Amount</td><td>" . number_format($amounts['patient_amount'], 2) . " €</td></tr>";
        echo "</table>";
        
        // Check the condition that would cause NULL return
        if (!$amounts || ($amounts['cns_amount'] == 0 && $amounts['patient_amount'] == 0)) {
            echo "<p style='color: red;'>⚠️ Would return NULL because both amounts are 0</p>";
        } else {
            echo "<p style='color: green;'>✓ Data is valid, should proceed with generation</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ No data found!</p>";
    }
    
    // Double-check what's in the database
    echo "<h3>Direct Database Check:</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_monthly_retrocession_amounts 
        WHERE user_id = :user_id 
        ORDER BY year DESC, month DESC
        LIMIT 12
    ");
    $stmt->execute(['user_id' => $userId]);
    $allData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Month</th><th>Year</th><th>CNS</th><th>Patient</th><th>Active</th></tr>";
    foreach ($allData as $row) {
        $highlight = ($row['month'] == $dataMonth && $row['year'] == $dataYear) ? "style='background-color: #d4edda;'" : "";
        echo "<tr $highlight>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['month']}</td>";
        echo "<td>{$row['year']}</td>";
        echo "<td>" . number_format($row['cns_amount'], 2) . "</td>";
        echo "<td>" . number_format($row['patient_amount'], 2) . "</td>";
        echo "<td>{$row['is_active']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Analysis:</h3>";
    
    if ($amounts && $amounts['cns_amount'] > 0) {
        echo "<p>The data exists and should work. The issue might be:</p>";
        echo "<ol>";
        echo "<li>A mismatch in month calculation (using June vs July)</li>";
        echo "<li>The UnifiedInvoiceGenerator might be checking additional conditions</li>";
        echo "<li>There might be a transaction/session issue</li>";
        echo "</ol>";
    } else {
        echo "<p>The UnifiedInvoiceGenerator query is not finding the data.</p>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}