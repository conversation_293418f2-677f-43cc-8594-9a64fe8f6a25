{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.create_invoice') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.create_invoice') }}</h1>
        <a href="{{ base_url }}/invoices" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
        </a>
    </div>

    <!-- Action Buttons -->
    <div class="card shadow-sm mb-4">
        <div class="card-body py-2">
            <div class="d-flex gap-2 justify-content-end">
                <button type="submit" form="invoiceForm" name="action" value="save" class="btn btn-primary">
                    <i class="bi bi-save me-2"></i>{{ __('common.save_draft') }}
                </button>
                <button type="submit" form="invoiceForm" name="action" value="save_and_send" class="btn btn-success">
                    <i class="bi bi-send me-2"></i>{{ __('invoices.save_and_send') }}
                </button>
                <a href="{{ base_url }}/invoices" class="btn btn-secondary">
                    <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                </a>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ base_url }}/invoices" id="invoiceForm" class="needs-validation" novalidate data-status="draft">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <!-- Invoice Details -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>{{ __('invoices.invoice_details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="document_type_id" class="form-label">{{ __('invoices.document_type') }} *</label>
                        <select class="form-select" id="document_type_id" name="document_type_id" required>
                            <option value="">{{ __('common.select') }}</option>
                            {% for dtype in documentTypes %}
                                <option value="{{ dtype.id }}" 
                                        data-prefix="{{ dtype.prefix }}" 
                                        data-negative="{{ dtype.is_negative }}" 
                                        data-requires-ref="{{ dtype.requires_reference }}"
                                        {% if dtype.code == 'invoice' %}selected{% endif %}>
                                    {{ dtype.name|default(dtype.code|upper) }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="invoice_type_id" class="form-label">{{ __('invoices.invoice_category') }}</label>
                        <select class="form-select" id="invoice_type_id" name="invoice_type_id">
                            <option value="">{{ __('common.select') }}</option>
                            {% for type in invoiceTypes %}
                                <option value="{{ type.id }}" data-prefix="{{ type.prefix }}"
                                        {% if (duplicateData.invoice_type_id is defined and duplicateData.invoice_type_id == type.id) or (duplicateData.invoice_type_id is not defined and defaultInvoiceTypeId == type.id) %}selected{% endif %}>
                                    {{ type.display_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="template_id" class="form-label">{{ __('invoices.template') }}</label>
                        <select class="form-select" id="template_id" name="template_id">
                            <option value="">{{ __('invoices.no_template')|default('No template') }}</option>
                            {% for template in templates %}
                                <option value="{{ template.id }}">
                                    {{ template.name }}
                                </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">{{ __('invoices.template_hint')|default('Select a template to pre-fill invoice lines') }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="invoice_number" class="form-label">{{ __('invoices.invoice_number') }}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                   value="{{ suggestedNumber }}" 
                                   placeholder="{{ __('invoices.enter_invoice_number') }}">
                        </div>
                        <small class="text-muted">{{ __('invoices.invoice_number_editable')|default('You can modify this number if needed') }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="issue_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                        <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ 'now'|date('Y-m-d') }}" required>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="subject" class="form-label">{{ __('invoices.subject') }}</label>
                        <input type="text" class="form-control" id="subject" name="subject" placeholder="{{ __('invoices.subject_placeholder')|default('Ex: Loyer + Charges') }}">
                        <small class="text-muted">{{ __('invoices.subject_hint')|default('Objet de la facture') }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="period" class="form-label">{{ __('invoices.period') }}</label>
                        <input type="text" class="form-control" id="period" name="period" placeholder="{{ __('invoices.period_placeholder')|default('Ex: JUIN 2025') }}">
                        <small class="text-muted">{{ __('invoices.period_hint')|default('Période concernée') }}</small>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="billable_type" class="form-label">{{ __('invoices.bill_to') }} *</label>
                        <select class="form-select" id="billable_type" name="billable_type" required>
                            <option value="">{{ __('common.select') }}</option>
                            <option value="client">{{ __('clients.client') }}</option>
                            <option value="user" selected>{{ __('users.user') }} ({{ __('invoices.internal') }})</option>
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="billable_id" class="form-label">{{ __('common.select') }} <span id="billable_label">{{ __('users.user') }}</span> *</label>
                        <div class="input-group">
                            <select class="form-select" id="billable_id" name="billable_id" required>
                                <option value="">{{ __('common.select') }}</option>
                            </select>
                            <button class="btn btn-success" type="button" id="addNewBillableBtn" disabled>
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                        <small class="text-muted" id="billable_hint">{{ __('invoices.internal_invoice_hint') }}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>{{ __('invoices.invoice_items') }}</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-light me-2" id="searchProductBtn">
                        <i class="bi bi-search me-1"></i>{{ __('invoices.search_product')|default('Search Product') }}
                    </button>
                    <button type="button" class="btn btn-sm btn-light" id="addItemBtn">
                        <i class="bi bi-plus-circle me-1"></i>{{ __('invoices.add_item')|default('Add Item') }}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="itemsTable">
                        <thead>
                            <tr id="itemsTableHeader">
                                <th width="40%">{{ __('invoices.description') }}</th>
                                <th width="15%">{{ __('invoices.quantity') }}</th>
                                <th width="15%">{{ __('invoices.unit_price') }}</th>
                                <th width="15%">{{ __('invoices.vat_rate') }}</th>
                                <th width="15%">{{ __('invoices.total') }}</th>
                                <th width="50"></th>
                            </tr>
                        </thead>
                        <tbody id="itemsBody">
                            <!-- Items will be added dynamically -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="subtotal">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="vatAmount">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.total') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="total" class="text-primary">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('invoices.additional_information') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-12">
                        <label for="notes" class="form-label">{{ __('invoices.notes') }}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                 placeholder="{{ __('invoices.notes_placeholder') }}"></textarea>
                    </div>
                    
                    <div class="col-md-12">
                        <label for="internal_notes" class="form-label">{{ __('invoices.internal_notes') }}</label>
                        <textarea class="form-control" id="internal_notes" name="internal_notes" rows="2" 
                                 placeholder="{{ __('invoices.internal_notes_placeholder') }}"></textarea>
                        <small class="text-muted">{{ __('invoices.internal_notes_hint') }}</small>
                    </div>
                </div>
            </div>
                </div>

        <!-- Payment Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>{{ __('invoices.payment_terms') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="payment_term_id" class="form-label">{{ __('invoices.payment_terms') }}</label>
                        <select class="form-select" id="payment_term_id" name="payment_term_id">
                            <option value="">{{ __('common.select') }}</option>
                            {% for term in paymentTerms %}
                                <option value="{{ term.id }}" data-days="{{ term.days }}" 
                                        {% if term.id == defaultPaymentTermId %}selected{% endif %}>
                                    {{ term.display_name }}
                                    {% if term.display_description %}
                                        ({{ term.display_description }})
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">{{ __('config.payment_term_description_hint') }}</small>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="due_date" class="form-label">{{ __('invoices.due_date') }}</label>
                        <input type="date" class="form-control" id="due_date" name="due_date" value="">
                        <small class="text-muted">{{ __('invoices.leave_empty_immediate') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Item Template -->
<template id="itemTemplate">
    <tr class="invoice-item">
        <td>
            <input type="hidden" name="items[INDEX][item_id]" class="item-id">
            <input type="text" class="form-control form-control-sm item-description" name="items[INDEX][description]" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-quantity" name="items[INDEX][quantity]" value="1" min="1" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-price" name="items[INDEX][unit_price]" min="0" step="0.01" required>
        </td>
        <td>
            <select class="form-select form-select-sm item-vat" name="items[INDEX][vat_rate_id]" required>
                <!-- VAT options will be loaded dynamically -->
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm item-total" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger remove-item">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    </tr>
</template>

<script>
console.log('=== Invoice Create Script Started ===');

let itemIndex = 0;
const currency = '{{ currency }}';
let vatRates = [];
const secretaryPercent = {{ secretary_percent|default(10) }};

// Check invoice type from URL
const urlParams = new URLSearchParams(window.location.search);
const invoiceType = urlParams.get('type') || 'rental';
const urlUserId = urlParams.get('user_id');
console.log('Invoice type from URL:', invoiceType);
console.log('User ID from URL:', urlUserId);

// Store these globally for use in other functions
window.urlInvoiceType = invoiceType;
window.urlUserId = urlUserId;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Setting up invoice form...');
    
    // Get elements first
    const docTypeElement = document.getElementById('document_type_id');
    const genNumberBtn = document.getElementById('generateNumberBtn');
    const billableTypeElement = document.getElementById('billable_type');
    
    // Check if this is a retrocession invoice FIRST
    const isRetrocession = invoiceType === 'retrocession_30' || invoiceType === 'retrocession_25';
    
    // Load VAT rates and columns, then add first item
    Promise.all([
        loadVatRates(),
        isRetrocession ? Promise.resolve() : updateInvoiceItemColumns() // Skip column update for retrocession
    ]).then(() => {
        // Update price label if LOC invoice type is already selected
        updatePriceLabel();
        // Check if we have duplicate data
        {% if duplicateData %}
        console.log('Duplicate data found, populating form...');
        
        // Set form fields from duplicate data
        {% if duplicateData.document_type_id %}
        document.getElementById('document_type_id').value = '{{ duplicateData.document_type_id }}';
        {% endif %}
        
        {% if duplicateData.invoice_type_id %}
        document.getElementById('invoice_type_id').value = '{{ duplicateData.invoice_type_id }}';
        {% endif %}
        
        {% if duplicateData.subject %}
        document.getElementById('subject').value = '{{ duplicateData.subject|escape('js') }}';
        {% endif %}
        
        {% if duplicateData.period %}
        document.getElementById('period').value = '{{ duplicateData.period|escape('js') }}';
        {% endif %}
        
        {% if duplicateData.payment_term_id %}
        document.getElementById('payment_term_id').value = '{{ duplicateData.payment_term_id }}';
        {% endif %}
        
        // Set billable type and id
        {% if duplicateData.client_id %}
        document.getElementById('billable_type').value = 'client';
        loadBillableOptions().then(() => {
            document.getElementById('billable_id').value = 'client_{{ duplicateData.client_id }}';
        });
        {% elseif duplicateData.user_id %}
        document.getElementById('billable_type').value = 'user';
        loadBillableOptions().then(() => {
            document.getElementById('billable_id').value = 'user_{{ duplicateData.user_id }}';
        });
        {% endif %}
        
        // Check if this is a retrocession invoice to handle it specially
        {% if duplicateData.invoice_type_id %}
        const duplicateInvoiceTypeId = '{{ duplicateData.invoice_type_id }}';
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        let isRetrocessionDuplicate = false;
        
        // Check if this is a retrocession type
        if (invoiceTypeSelect) {
            for (let option of invoiceTypeSelect.options) {
                if (option.value === duplicateInvoiceTypeId && option.getAttribute('data-prefix') === 'RET') {
                    isRetrocessionDuplicate = true;
                    break;
                }
            }
        }
        
        if (isRetrocessionDuplicate) {
            console.log('Duplicating retrocession invoice - initializing complete retrocession structure');
            
            // Use the complete initialization function
            initializeRetrocessionInvoiceComplete();
            
            // After initialization, populate amounts from duplicate data
            setTimeout(() => {
                
                // If we have CNS and patient amounts from duplicate data, set them
                {% if duplicateData.cns_base_amount or duplicateData.items %}
                setTimeout(() => {
                    // Look for CNS and patient amounts in the duplicate data
                    {% for item in duplicateData.items %}
                    {% if item.description and 'CNS' in item.description %}
                    const cnsInput = document.getElementById('cns_amount');
                    if (cnsInput) {
                        // Calculate base amount from the 20% retrocession
                        const cnsRetrocession = {{ item.unit_price|default(0) }};
                        const cnsBase = cnsRetrocession / 0.20;
                        cnsInput.value = cnsBase.toFixed(2);
                    }
                    {% elseif item.description and 'PATIENTS' in item.description %}
                    const patientInput = document.getElementById('patient_amount');
                    if (patientInput) {
                        // Calculate base amount from the 20% retrocession
                        const patientRetrocession = {{ item.unit_price|default(0) }};
                        const patientBase = patientRetrocession / 0.20;
                        patientInput.value = patientBase.toFixed(2);
                    }
                    {% endif %}
                    {% endfor %}
                    
                    // Trigger calculation
                    calculateRetrocessionAmounts();
                }, 50);
                {% endif %}
            }, 200); // Wait for complete initialization to finish
        } else {
            // Not a retrocession invoice, add items normally
            {% if duplicateData.items %}
            {% for item in duplicateData.items %}
            window.addItemWithData({
                description: '{{ item.description|escape('js') }}',
                quantity: '{{ item.quantity }}',
                unit_price: '{{ item.unit_price }}',
                vat_rate: '{{ item.vat_rate }}',
                item_id: '{{ item.item_id|default('') }}',
                reference: '{{ item.reference|default('')|escape('js') }}',
                unit: '{{ item.unit|default('')|escape('js') }}'
            });
            {% endfor %}
            {% else %}
            // No duplicate items, add default empty item
            addItem();
            {% endif %}
        }
        {% else %}
        // No duplicate data, add items normally
        {% if duplicateData.items %}
        {% for item in duplicateData.items %}
        window.addItemWithData({
            description: '{{ item.description|escape('js') }}',
            quantity: '{{ item.quantity }}',
            unit_price: '{{ item.unit_price }}',
            vat_rate: '{{ item.vat_rate }}',
            item_id: '{{ item.item_id|default('') }}',
            reference: '{{ item.reference|default('')|escape('js') }}',
            unit: '{{ item.unit|default('')|escape('js') }}'
        });
        {% endfor %}
        {% else %}
        // No duplicate items, add default empty item
        addItem();
        {% endif %}
        {% endif %}
        
        {% else %}
        // Handle retrocession invoice initialization BEFORE adding items
        if (isRetrocession) {
            console.log('Initializing retrocession invoice from URL parameter...');
            initializeRetrocessionInvoiceComplete();
        } else {
            // No duplicate data, add default empty item
            addItem();
        }
        // Load billable options since user type is selected by default
        if (billableTypeElement && billableTypeElement.value) {
            loadBillableOptions();
        }
        {% endif %}
        
        // If invoice type is pre-selected via defaultInvoiceTypeId, trigger the change handler
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        if (invoiceTypeSelect && invoiceTypeSelect.value) {
            console.log('Invoice type pre-selected:', invoiceTypeSelect.value);
            // Trigger loadBillableOptions if billable type is user
            if (billableTypeElement && billableTypeElement.value === 'user') {
                console.log('Triggering loadBillableOptions for pre-selected invoice type');
                loadBillableOptions();
            }
            
            // Check if it's a LOY invoice and trigger auto-fill
            const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
            const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            console.log('Pre-selected invoice type code:', typeCode);
            
            if (typeCode === 'LOY') {
                console.log('LOY invoice detected on page load - triggering auto-fill');
                // Manually trigger the invoice type change handler to auto-fill fields
                setTimeout(() => {
                    const changeEvent = new Event('change', { bubbles: true });
                    invoiceTypeSelect.dispatchEvent(changeEvent);
                    console.log('LOY auto-fill triggered');
                    
                    // If user_id is in URL, select that user and load their obligations
                    if (urlUserId) {
                        console.log('Auto-selecting user from URL:', urlUserId);
                        setTimeout(() => {
                            const billableIdSelect = document.getElementById('billable_id');
                            if (billableIdSelect) {
                                const userOption = billableIdSelect.querySelector(`option[value="user_${urlUserId}"]`);
                                if (userOption) {
                                    billableIdSelect.value = `user_${urlUserId}`;
                                    console.log('User selected:', billableIdSelect.value);
                                    // Trigger change event to load financial obligations
                                    const changeEvent = new Event('change', { bubbles: true });
                                    billableIdSelect.dispatchEvent(changeEvent);
                                } else {
                                    console.warn('User option not found for ID:', urlUserId);
                                }
                            }
                        }, 600); // Additional delay to ensure billable options are loaded
                    }
                }, 300); // Small delay to ensure everything is loaded
            }
        }
    }).catch(error => {
        console.error('Error during initialization:', error);
        // Still try to add an item with default config
        addItem();
    });
    
    // Invoice numbers are now generated on save - no manual generation needed
    
    // Handle billable type change - CRITICAL EVENT
    if (billableTypeElement) {
        console.log('Attaching billable type change listener...');
        billableTypeElement.addEventListener('change', function(e) {
            console.log('*** BILLABLE TYPE CHANGED TO:', e.target.value, '***');
            loadBillableOptions();
        });
        console.log('Billable type change listener attached successfully');
    } else {
        console.error('ERROR: billable_type element not found!');
    }
    
    // Add event listener for billable_id change
    const billableIdElement = document.getElementById('billable_id');
    if (billableIdElement) {
        billableIdElement.addEventListener('change', function(e) {
            console.log('Billable ID changed:', e.target.value);
            
            // Check if this is a LOC invoice and a user is selected
            const invoiceTypeSelect = document.getElementById('invoice_type_id');
            const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
            const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            const isLOCInvoice = typeCode === 'LOC';
            const isLOYInvoice = typeCode === 'LOY';
            
            const billableType = document.getElementById('billable_type').value;
            const selectedValue = e.target.value;
            
            if (billableType === 'user' && selectedValue) {
                // Extract user ID from the value (format: user_123)
                const userId = selectedValue.replace('user_', '');
                
                if (isLOCInvoice && userId) {
                    // Load courses for LOC invoices only
                    loadUserCourses(userId);
                } else if (isLOYInvoice && userId) {
                    // For LOY invoices, load financial obligations
                    loadUserFinancialObligations(userId);
                } else if ((typeCode === 'RET' || typeCode === 'RT30' || typeCode === 'RT25' || typeCode === 'RET25' || typeCode === 'RET30') && userId) {
                    // Load retrocession settings for retrocession invoices
                    loadUserRetrocessionSettings(userId).then(settings => {
                        // Re-initialize retrocession invoice with user settings
                        const isRet25 = typeCode === 'RT25' || typeCode === 'RET25';
                        initializeRetrocessionInvoice(isRet25, settings);
                        
                    });
                }
            }
        });
        console.log('Billable ID change listener attached successfully');
    }
    
    // Handle payment term change
    document.getElementById('payment_term_id').addEventListener('change', updateDueDate);
    
    // Handle issue date change
    document.getElementById('issue_date').addEventListener('change', function() {
        updateDueDate();
        // Also update period for rental invoices
        updateRentalPeriod();
    });
    
    // Handle invoice type change to show/hide CNS fields
    document.getElementById('invoice_type_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        const typeId = this.value;
        
        // Reload billable options when invoice type changes
        const billableType = document.getElementById('billable_type');
        if (billableType && billableType.value === 'user') {
            console.log('Invoice type changed - reloading billable options');
            loadBillableOptions();
        }
        
        // Check if this is a retrocession type and we're not already on a retrocession URL
        if (typeCode && typeCode.startsWith('RET')) {
            const currentUrl = new URL(window.location.href);
            const currentType = currentUrl.searchParams.get('type');
            
            // Determine the type parameter based on the prefix
            let typeParam = '';
            if (typeCode === 'RET30' || typeCode === 'RET') {
                typeParam = 'retrocession_30';
            } else if (typeCode === 'RET25') {
                typeParam = 'retrocession_25';
            }
            
            // If we're not already on the correct retrocession URL, redirect
            if (typeParam && currentType !== typeParam) {
                console.log('🔄 Retrocession type selected - redirecting to proper initialization URL');
                
                // Preserve any existing parameters
                currentUrl.searchParams.set('type', typeParam);
                
                // Preserve the duplicate parameter if it exists
                const duplicateId = currentUrl.searchParams.get('duplicate');
                if (duplicateId) {
                    currentUrl.searchParams.set('duplicate', duplicateId);
                }
                
                // Redirect to the URL with the type parameter
                window.location.href = currentUrl.toString();
                return; // Stop further processing
            }
        }
        
        // Continue with normal processing for non-retrocession types
        handleInvoiceTypeChange();
        updateInvoiceNumber(); // Update invoice number when type changes
        
        // Only update columns if NOT a retrocession invoice
        if (!typeCode || !typeCode.startsWith('RET')) {
            updateInvoiceItemColumns(); // Update columns when invoice type changes
        } else {
            // For retrocession, make sure headers stay correct
            console.log('🎯 Retrocession selected - ensuring headers remain correct');
            setTimeout(() => {
                updateInvoiceTableHeaders('RET');
            }, 50);
        }
        
        updateTemplateOptions(); // Update available templates based on invoice type
    });
    
    // Handle document type change to update columns and invoice number
    if (docTypeElement) {
        docTypeElement.addEventListener('change', function() {
            // Update invoice number based on new document type
            updateInvoiceNumber();
            
            // Check if this is a retrocession invoice before updating columns
            const invoiceTypeSelect = document.getElementById('invoice_type_id');
            const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
            const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            
            if (!typeCode || !typeCode.startsWith('RET')) {
                updateInvoiceItemColumns(); // Update columns when document type changes
            } else {
                console.log('🛑 Skipping column update for retrocession on document type change');
            }
        });
        console.log('Document type change listener attached');
    }
    
    // Event listeners for retrocession inputs are now added in addRetrocessionItem function
    
    // Handle template selection
    const templateSelect = document.getElementById('template_id');
    if (templateSelect) {
        templateSelect.addEventListener('change', function() {
            const templateId = this.value;
            if (templateId) {
                applyTemplate(templateId);
            }
        });
    }
    
    // Add item button
    document.getElementById('addItemBtn').addEventListener('click', addItem);
    
    // Apply voucher - commented out as voucher section was removed
    // document.getElementById('applyVoucherBtn').addEventListener('click', applyVoucher);
    
    // Add new billable button
    document.getElementById('addNewBillableBtn').addEventListener('click', function() {
        const type = document.getElementById('billable_type').value;
        if (type === 'client') {
            window.open('{{ base_url }}/clients/create?quick=1', 'newClient', 'width=800,height=600');
        } else if (type === 'patient') {
            window.open('{{ base_url }}/patients/create?quick=1', 'newPatient', 'width=800,height=600');
        }
    });
    
    // Handle submit button clicks - buttons already have name="action" and value
    // Bootstrap will handle the form submission with validation
    const submitButtons = document.querySelectorAll('button[type="submit"][form="invoiceForm"]');
    submitButtons.forEach(button => {
        console.log('Submit button registered:', button.textContent.trim(), 'value:', button.value);
    });
    
    // Email confirmation modal handling
    const emailModal = new bootstrap.Modal(document.getElementById('emailConfirmModal'));
    let pendingSubmission = false;
    
    // Add action tracking
    let currentAction = 'save';
    
    // Track which button was clicked
    document.querySelectorAll('button[name="action"]').forEach(button => {
        button.addEventListener('click', function() {
            currentAction = this.value;
            console.log('Button clicked, action set to:', currentAction);
        });
    });
    
    // Function to remove empty invoice rows
    function removeEmptyInvoiceRows() {
        const tbody = document.getElementById('itemsBody');
        if (!tbody) return;
        
        const rows = tbody.querySelectorAll('tr.invoice-item');
        rows.forEach(row => {
            const descField = row.querySelector('input[name*="[description]"]');
            const priceField = row.querySelector('input[name*="[unit_price]"]');
            
            // If both description and price are empty, remove the row
            if (descField && priceField && !descField.value.trim() && !priceField.value.trim()) {
                console.log('Removing empty row');
                row.remove();
            }
        });
        
        // Re-index remaining rows
        const remainingRows = tbody.querySelectorAll('tr.invoice-item');
        remainingRows.forEach((row, index) => {
            // Update all input names to have correct index
            row.querySelectorAll('input, select').forEach(input => {
                if (input.name && input.name.includes('items[')) {
                    input.name = input.name.replace(/items\[\d+\]/, `items[${index}]`);
                }
            });
        });
    }
    
    // Override form submission for save_and_send
    const invoiceForm = document.getElementById('invoiceForm');
    invoiceForm.addEventListener('submit', function(event) {
        // Get the action from the submitter button or use tracked action
        const action = event.submitter ? event.submitter.value : currentAction;
        console.log('Form submit event - action:', action);
        
        // If action is save_and_send and we haven't shown the modal yet
        if (action === 'save_and_send' && !pendingSubmission) {
            event.preventDefault();
            event.stopPropagation();
            
            // Remove empty invoice item rows before validation
            removeEmptyInvoiceRows();
            
            // Check form validity first
            if (!invoiceForm.checkValidity()) {
                invoiceForm.classList.add('was-validated');
                return;
            }
            
            // Get recipient information
            const billableSelect = document.getElementById('billable_id');
            const selectedOption = billableSelect.options[billableSelect.selectedIndex];
            const recipientName = selectedOption ? selectedOption.text : '';
            
            // Get user email (we'll need to fetch this)
            const billableType = document.getElementById('billable_type').value;
            const billableId = billableSelect.value;
            
            // Calculate total
            const totalElement = document.getElementById('total');
            const total = totalElement ? totalElement.textContent : '0.00';
            
            // Populate modal
            document.getElementById('summaryRecipient').textContent = recipientName;
            document.getElementById('summaryTotal').textContent = total;
            document.getElementById('recipientEmail').textContent = 'Loading...';
            
            // Fetch email address
            if (billableType === 'user' && billableId) {
                const userId = billableId.replace('user_', '');
                fetch(`{{ base_url }}/api/users/${userId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.user) {
                            document.getElementById('recipientEmail').textContent = data.user.email || 'No email address';
                        }
                    })
                    .catch(() => {
                        document.getElementById('recipientEmail').textContent = 'Email not found';
                    });
            }
            
            // Show modal
            emailModal.show();
        }
    });
    
    // Handle modal buttons
    document.getElementById('saveWithoutEmailBtn').addEventListener('click', function() {
        document.getElementById('sendEmailPreference').value = 'false';
        pendingSubmission = true;
        emailModal.hide();
        
        // Create and click a hidden submit button with save_and_send action
        const hiddenSubmit = document.createElement('button');
        hiddenSubmit.type = 'submit';
        hiddenSubmit.name = 'action';
        hiddenSubmit.value = 'save_and_send';
        hiddenSubmit.style.display = 'none';
        invoiceForm.appendChild(hiddenSubmit);
        hiddenSubmit.click();
        invoiceForm.removeChild(hiddenSubmit);
    });
    
    document.getElementById('saveAndEmailBtn').addEventListener('click', function() {
        document.getElementById('sendEmailPreference').value = 'true';
        pendingSubmission = true;
        emailModal.hide();
        
        // Create and click a hidden submit button with save_and_send action
        const hiddenSubmit = document.createElement('button');
        hiddenSubmit.type = 'submit';
        hiddenSubmit.name = 'action';
        hiddenSubmit.value = 'save_and_send';
        hiddenSubmit.style.display = 'none';
        invoiceForm.appendChild(hiddenSubmit);
        hiddenSubmit.click();
        invoiceForm.removeChild(hiddenSubmit);
    });
    
    // Form validation with debugging
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            console.log('=== FORM SUBMISSION STARTED ===');
            console.log('Submission triggered by:', event.submitter?.textContent || 'Unknown');
            console.log('Submitter element:', event.submitter);
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);
            
            try {
                // Debug: Log all form data
                const formData = new FormData(form);
                console.log('Form data before validation:');
                for (let [key, value] of formData.entries()) {
                    console.log(`  ${key}: ${value}`);
                }
                
                // Check action field (from button click)
                const actionField = formData.get('action');
                console.log('Action field value:', actionField || 'No action field - will be set by button click');
                
                // If no action field, add it based on current action
                if (!actionField && currentAction) {
                    console.log('Adding action field with value:', currentAction);
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'action';
                    actionInput.value = currentAction;
                    form.appendChild(actionInput);
                }
                
                // Check if we have at least one item
                const items = document.querySelectorAll('.invoice-item');
                console.log('Number of invoice items:', items.length);
                if (items.length === 0) {
                    event.preventDefault();
                    event.stopPropagation();
                    console.error('❌ No invoice items found');
                    alert('Please add at least one invoice item');
                    return;
                }
                
                // Check if this is a retrocession invoice
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
                const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                const isRetrocessionForm = typeCode && typeCode.startsWith('RET');
                console.log('Invoice type:', selectedOption?.textContent, 'Code:', typeCode, 'Is Retrocession:', isRetrocessionForm);
                
                // Log all required fields
                console.log('=== REQUIRED FIELDS CHECK ===');
                const requiredFields = form.querySelectorAll('[required]');
                console.log('Total required fields:', requiredFields.length);
                requiredFields.forEach(field => {
                    const fieldInfo = {
                        name: field.name || field.id,
                        type: field.type,
                        value: field.value,
                        disabled: field.disabled,
                        visible: field.offsetParent !== null,
                        validity: field.checkValidity()
                    };
                    console.log('Required field:', fieldInfo);
                });
                
                // For retrocession invoices, ensure VAT fields have values even if disabled
                if (isRetrocessionForm) {
                    console.log('=== RETROCESSION VAT FIELDS ===');
                    items.forEach((row, index) => {
                        const vatSelect = row.querySelector('.item-vat');
                        if (vatSelect) {
                            console.log(`Row ${index} VAT:`, {
                                name: vatSelect.name,
                                value: vatSelect.value,
                                disabled: vatSelect.disabled,
                                required: vatSelect.required,
                                selectedIndex: vatSelect.selectedIndex,
                                selectedText: vatSelect.options[vatSelect.selectedIndex]?.text
                            });
                            
                            // For disabled fields, we need to add a hidden input with the value
                            if (vatSelect.disabled) {
                                // Check if hidden input already exists
                                const hiddenInputName = vatSelect.name + '_hidden';
                                let hiddenInput = row.querySelector(`input[name="${hiddenInputName}"]`);
                                
                                if (!hiddenInput) {
                                    // Create hidden input with same name as the select
                                    hiddenInput = document.createElement('input');
                                    hiddenInput.type = 'hidden';
                                    hiddenInput.name = vatSelect.name; // Use same name so it gets submitted
                                    hiddenInput.value = vatSelect.value;
                                    vatSelect.parentNode.appendChild(hiddenInput);
                                    console.log(`✅ Added hidden VAT input for row ${index}: ${hiddenInput.value}`);
                                } else {
                                    // Update existing hidden input value
                                    hiddenInput.value = vatSelect.value;
                                    console.log(`✅ Updated hidden VAT value for row ${index}: ${hiddenInput.value}`);
                                }
                            }
                        }
                        
                        // Log all item fields
                        const itemFields = row.querySelectorAll('input, select, textarea');
                        console.log(`Row ${index} fields:`, Array.from(itemFields).map(f => ({
                            name: f.name,
                            value: f.value,
                            type: f.type,
                            required: f.required,
                            disabled: f.disabled
                        })));
                    });
                }
                
                // Check form validity
                // Remove empty invoice rows before validation
                removeEmptyInvoiceRows();
                
                console.log('=== FORM VALIDATION ===');
                const isValid = form.checkValidity();
                console.log('Form validity:', isValid);
                
                if (!isValid) {
                    event.preventDefault();
                    event.stopPropagation();
                    console.error('❌ Form validation failed');
                    
                    // Show which fields are invalid
                    const invalidFields = form.querySelectorAll(':invalid');
                    console.log('Invalid fields count:', invalidFields.length);
                    invalidFields.forEach((field, index) => {
                        console.error(`Invalid field ${index + 1}:`, {
                            name: field.name || field.id,
                            type: field.type,
                            required: field.required,
                            disabled: field.disabled,
                            value: field.value,
                            validationMessage: field.validationMessage,
                            parent: field.parentElement?.className
                        });
                        
                        // Special handling for retrocession VAT fields
                        if (isRetrocessionForm && field.name && field.name.includes('vat_rate_id') && field.disabled) {
                            console.log('🔧 Removing required from disabled VAT field:', field.name);
                            field.required = false;
                        }
                    });
                    
                    // Retry validation for retrocession after fixing VAT fields
                    if (isRetrocessionForm) {
                        const isValidAfterFix = form.checkValidity();
                        console.log('Form validity after VAT fix:', isValidAfterFix);
                        
                        if (!isValidAfterFix) {
                            console.log('❌ Still invalid after fixes');
                            const stillInvalid = form.querySelectorAll(':invalid');
                            console.log('Still invalid count:', stillInvalid.length);
                            stillInvalid.forEach(field => {
                                console.error('Still invalid:', field.name || field.id, 'Message:', field.validationMessage);
                            });
                        } else {
                            console.log('✅ Form valid after fixes, allowing submission');
                            // Remove preventDefault to allow submission
                            return;
                        }
                    }
                } else {
                    console.log('✅ Form validation passed, submitting...');
                    console.log('Final form data being submitted:');
                    const finalData = new FormData(form);
                    for (let [key, value] of finalData.entries()) {
                        if (key.includes('item') || key === 'action' || key === 'billable_id' || key.includes('vat_rate_id')) {
                            console.log(`  ${key}: ${value}`);
                        }
                    }
                    
                    // Allow form to submit naturally
                    console.log('Form will now submit to:', form.action);
                    console.log('Form method:', form.method);
                    
                    // Log final check before submission
                    console.log('=== FINAL SUBMISSION CHECK ===');
                    console.log('CSRF Token present:', !!formData.get('csrf_token'));
                    console.log('Action value:', formData.get('action'));
                    console.log('Items count:', document.querySelectorAll('.invoice-item').length);
                    
                    // Check if all VAT fields have values
                    const vatInputs = form.querySelectorAll('input[name*="vat_rate_id"]');
                    console.log('VAT inputs found:', vatInputs.length);
                    vatInputs.forEach((input, idx) => {
                        console.log(`VAT input ${idx}: name=${input.name}, value=${input.value}, type=${input.type}`);
                    });
                    
                    console.log('✅ Form submission proceeding...');
                }
                
            } catch (error) {
                console.error('❌ Error during form submission:', error);
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
            console.log('=== FORM SUBMISSION END ===');
        });
    });
});

// Load VAT rates from server
async function loadVatRates() {
    try {
        const response = await fetch('{{ base_url }}/api/vat-rates');
        const data = await response.json();
        if (data.success) {
            vatRates = data.rates;
        } else {
            // Fallback VAT rates if API fails
            vatRates = [
                {id: 1, name: 'Standard', rate: 17, is_default: true},
                {id: 2, name: 'Reduced', rate: 8, is_default: false},
                {id: 3, name: 'Super Reduced', rate: 3, is_default: false},
                {id: 4, name: 'Parking', rate: 14, is_default: false},
                {id: 5, name: 'Exempt', rate: 0, is_default: false}
            ];
        }
    } catch (error) {
        console.error('Error loading VAT rates:', error);
        // Use fallback rates
        vatRates = [
            {id: 1, name: 'Standard', rate: 17, is_default: true},
            {id: 2, name: 'Reduced', rate: 8, is_default: false},
            {id: 3, name: 'Super Reduced', rate: 3, is_default: false},
            {id: 4, name: 'Parking', rate: 14, is_default: false},
            {id: 5, name: 'Exempt', rate: 0, is_default: false}
        ];
    }
}

// Original addItem function removed - now using addItemWithData through window.addItem

function removeItem(row) {
    if (document.querySelectorAll('.invoice-item').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('{{ __("invoices.at_least_one_item") }}');
    }
}

// Original calculateItemTotal function removed - now using updated version that handles discount and subtotal

function calculateTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    // Check invoice type
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const isLOCInvoice = typeCode === 'LOC';
    const isLOYInvoice = typeCode === 'LOY';
    
    if (isLOYInvoice) {
        // For LOY invoices: standard VAT calculation
        document.querySelectorAll('.invoice-item').forEach(row => {
            const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
            const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
            const vatSelect = row.querySelector('.item-vat');
            const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
            
            const itemSubtotal = quantity * price;
            const itemVat = itemSubtotal * (vatRate / 100);
            
            subtotal += itemSubtotal;
            vatAmount += itemVat;
            
            // Update item total display
            const totalField = row.querySelector('.item-total');
            if (totalField) {
                const itemTotal = itemSubtotal + itemVat;
                totalField.value = currency + itemTotal.toFixed(2);
            }
        });
        
        const total = subtotal + vatAmount;
        
        document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
        document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
        document.getElementById('total').textContent = currency + total.toFixed(2);
    } else if (isLOCInvoice) {
        // For LOC invoices: prices are TTC, calculate VAT from total
        let totalTTC = 0;
        let commonVatRate = 0;
        
        document.querySelectorAll('.invoice-item').forEach((row, index) => {
            const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
            const priceTTC = parseFloat(row.querySelector('.item-price')?.value) || 0;
            const itemTotalTTC = quantity * priceTTC;
            totalTTC += itemTotalTTC;
            
            // Get the VAT rate (should be the same for all items in LOC invoices)
            if (index === 0) {
                const vatSelect = row.querySelector('.item-vat');
                commonVatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
            }
        });
        
        // Extract VAT from TTC total
        const divisor = 1 + (commonVatRate / 100);
        subtotal = totalTTC / divisor;
        vatAmount = totalTTC - subtotal;
        
        // Round to 2 decimal places
        subtotal = Math.round(subtotal * 100) / 100;
        vatAmount = Math.round(vatAmount * 100) / 100;
        
        const total = totalTTC;
        
        document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
        document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
        document.getElementById('total').textContent = currency + total.toFixed(2);
    } else {
        // Standard calculation for other invoice types
        document.querySelectorAll('.invoice-item').forEach(row => {
            const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
            const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
            const discount = parseFloat(row.querySelector('.item-discount')?.value) || 0;
            const vatSelect = row.querySelector('.item-vat');
            const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
            
            const itemSubtotal = quantity * price;
            const discountAmount = itemSubtotal * (discount / 100);
            const discountedSubtotal = itemSubtotal - discountAmount;
            const itemVat = discountedSubtotal * (vatRate / 100);
            
            subtotal += discountedSubtotal;
            vatAmount += itemVat;
        });
        
        // Include secretary fee if applicable (only for non-retrocession invoices)
        const secretaryFeeElement = document.getElementById('secretary_fee_amount');
        const secretaryFee = secretaryFeeElement ? parseFloat(secretaryFeeElement.value) || 0 : 0;
        const total = subtotal + vatAmount + secretaryFee;
        
        document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
        document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
        document.getElementById('total').textContent = currency + total.toFixed(2);
    }
}

function updatePriceLabel() {
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const isLOCInvoice = typeCode === 'LOC';
    
    // Update table header
    const priceHeader = document.querySelector('#itemsTableHeader th:nth-child(3)');
    if (priceHeader) {
        priceHeader.textContent = isLOCInvoice ? 'Prix TTC' : '{{ __("invoices.unit_price") }}';
    }
}

function loadUserCourses(userId) {
    console.log('Loading courses for user:', userId);
    
    // Check if there are existing items
    const existingItems = document.querySelectorAll('.invoice-item');
    if (existingItems.length > 0) {
        // Check if any item has content
        let hasContent = false;
        existingItems.forEach(item => {
            const desc = item.querySelector('.item-description')?.value;
            if (desc && desc.trim() !== '') {
                hasContent = true;
            }
        });
        
        if (hasContent) {
            if (!confirm('{{ __("invoices.replace_existing_items") | default("This will replace existing items. Continue?") }}')) {
                return;
            }
        }
    }
    
    // Show loading indicator
    const itemsBody = document.getElementById('itemsBody');
    const originalContent = itemsBody.innerHTML;
    itemsBody.innerHTML = '<tr><td colspan="6" class="text-center py-4"><i class="bi bi-hourglass-split me-2"></i>{{ __("common.loading") | default("Loading...") }}</td></tr>';
    
    // Fetch user courses
    fetch(`{{ base_url }}/users/${userId}/courses`)
        .then(response => response.json())
        .then(data => {
            if (!data.success || !data.data) {
                throw new Error('Failed to load courses');
            }
            
            // Filter active courses
            const activeCourses = data.data.filter(course => course.is_active == 1);
            
            if (activeCourses.length === 0) {
                itemsBody.innerHTML = originalContent;
                alert('{{ __("courses.no_active_courses") | default("No active courses found for this user") }}');
                return;
            }
            
            // Clear existing items
            itemsBody.innerHTML = '';
            itemIndex = 0;
            
            // Add one line per course
            activeCourses.forEach((course, index) => {
                // Find the VAT rate ID that matches the course VAT rate
                let vatRateId = '';
                for (let vat of vatRates) {
                    if (parseFloat(vat.rate) === parseFloat(course.vat_rate)) {
                        vatRateId = vat.id;
                        break;
                    }
                }
                
                // If no exact match, use the first available VAT rate
                if (!vatRateId && vatRates.length > 0) {
                    vatRateId = vatRates[0].id;
                }
                
                // Add item with course data
                addItemWithData({
                    description: course.course_name,
                    quantity: '1',
                    unit_price: course.hourly_rate, // This is already TTC
                    vat_rate_id: vatRateId
                });
            });
            
            // Recalculate totals
            calculateTotals();
            
            // Ensure subject and period are filled for LOC invoices
            const subjectField = document.getElementById('subject');
            if (subjectField && !subjectField.value) {
                subjectField.value = 'LOCATION SALLE';
            }
            
            const periodField = document.getElementById('period');
            if (periodField && !periodField.value) {
                // French month names
                const frenchMonths = [
                    'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL',
                    'MAI', 'JUIN', 'JUILLET', 'AOÛT',
                    'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
                ];
                
                // Get issue date
                const issueDateField = document.getElementById('issue_date');
                const issueDate = issueDateField ? new Date(issueDateField.value) : new Date();
                
                // Calculate previous month
                const previousMonth = new Date(issueDate);
                previousMonth.setMonth(previousMonth.getMonth() - 1);
                
                const monthIndex = previousMonth.getMonth();
                const year = previousMonth.getFullYear();
                
                periodField.value = frenchMonths[monthIndex] + ' ' + year;
            }
            
            console.log(`Loaded ${activeCourses.length} courses`);
        })
        .catch(error => {
            console.error('Error loading courses:', error);
            itemsBody.innerHTML = originalContent;
            alert('{{ __("common.error_loading_data") | default("Error loading courses") }}: ' + error.message);
        });
}

function loadUserRetrocessionSettings(userId) {
    console.log('Loading retrocession settings for user:', userId);
    
    return fetch(`{{ base_url }}/users/${userId}/retrocession-settings`)
        .then(response => response.json())
        .then(data => {
            if (!data.success || !data.data) {
                throw new Error('Failed to load retrocession settings');
            }
            
            console.log('Retrocession settings loaded:', data.data);
            return data.data;
        })
        .catch(error => {
            console.error('Error loading retrocession settings:', error);
            // Return default settings on error
            return {
                cns_type: 'percentage',
                cns_value: 20,
                patient_type: 'percentage',
                patient_value: 20,
                secretary_type: 'percentage',
                secretary_value: 10,
                is_default: true
            };
        });
}

function loadUserFinancialObligations(userId) {
    console.log('Loading financial obligations for user:', userId);
    
    // Check if there are existing items
    const existingItems = document.querySelectorAll('.invoice-item');
    if (existingItems.length > 0) {
        // Check if any item has content
        let hasContent = false;
        existingItems.forEach(item => {
            const desc = item.querySelector('.item-description')?.value;
            if (desc && desc.trim() !== '') {
                hasContent = true;
            }
        });
        
        if (hasContent) {
            if (!confirm('{{ __("invoices.replace_existing_items") | default("This will replace existing items. Continue?") }}')) {
                return;
            }
        }
    }
    
    // Show loading indicator
    const itemsBody = document.getElementById('itemsBody');
    const originalContent = itemsBody.innerHTML;
    itemsBody.innerHTML = '<tr><td colspan="6" class="text-center py-4"><i class="bi bi-hourglass-split me-2"></i>{{ __("common.loading") | default("Loading...") }}</td></tr>';
    
    // Fetch user financial obligations
    fetch(`{{ base_url }}/api/users/${userId}/financial-obligations`)
        .then(response => response.json())
        .then(data => {
            if (!data.success || !data.data) {
                throw new Error('Failed to load financial obligations');
            }
            
            const obligations = data.data;
            console.log('Financial obligations loaded:', obligations);
            
            // Convert to numbers for proper comparison
            const rentAmount = parseFloat(obligations.rent_amount) || 0;
            const chargesAmount = parseFloat(obligations.charges_amount) || 0;
            
            console.log('Parsed amounts - Rent:', rentAmount, 'Charges:', chargesAmount);
            
            if (!obligations || (rentAmount == 0 && chargesAmount == 0)) {
                itemsBody.innerHTML = originalContent;
                alert('{{ __("invoices.no_financial_obligations") | default("No financial obligations found for this user") }}');
                return;
            }
            
            // Clear existing items
            itemsBody.innerHTML = '';
            itemIndex = 0;
            
            // Find 0% VAT rate ID
            console.log('Available VAT rates:', vatRates);
            let zeroVatRateId = '';
            let vat17RateId = '';
            
            for (let vat of vatRates) {
                console.log('Checking VAT rate:', vat.rate, 'type:', typeof vat.rate, 'id:', vat.id);
                if (parseFloat(vat.rate) === 0 && !zeroVatRateId) {
                    zeroVatRateId = vat.id;
                    console.log('Found 0% VAT rate with ID:', zeroVatRateId);
                }
                if (parseFloat(vat.rate) === 17 && !vat17RateId) {
                    vat17RateId = vat.id;
                    console.log('Found 17% VAT rate with ID:', vat17RateId);
                }
            }
            
            // If no 0% rate found, use the first available
            if (!zeroVatRateId && vatRates.length > 0) {
                console.warn('No 0% VAT rate found, using first available');
                zeroVatRateId = vatRates[0].id;
            }
            
            // Add one line for rent (loyer)
            if (rentAmount > 0) {
                console.log('Adding rent line:', rentAmount, 'with VAT ID:', zeroVatRateId);
                window.addItemWithData({
                    description: 'Loyer mensuel',
                    quantity: '1',
                    unit_price: rentAmount.toString(),
                    vat_rate_id: zeroVatRateId, // VAT rate ID
                    item_id: '',
                    reference: ''
                });
            }
            
            // Add one line for charges if applicable
            if (chargesAmount > 0) {
                console.log('Adding charges line:', chargesAmount, 'with VAT ID:', zeroVatRateId);
                window.addItemWithData({
                    description: 'Charges location',
                    quantity: '1',
                    unit_price: chargesAmount.toString(),
                    vat_rate_id: zeroVatRateId, // VAT rate ID
                    item_id: '',
                    reference: ''
                });
            }
            
            // Add secretary services for managers group (only if values > 0)
            const secretaryTvac17 = parseFloat(obligations.secretary_tvac_17) || 0;
            const secretaryHtva = parseFloat(obligations.secretary_htva) || 0;
            const tva17 = parseFloat(obligations.tva_17) || 0;
            
            // VAT rates already found above
            
            // For secretary fees: use secretary_htva as the HT amount
            if (secretaryHtva > 0) {
                console.log('Adding frais secrétariat line: HT', secretaryHtva, 'with 17% VAT');
                window.addItemWithData({
                    description: 'Frais secrétariat',
                    quantity: '1',
                    unit_price: secretaryHtva.toString(), // HT amount (€1,000)
                    vat_rate_id: vat17RateId || zeroVatRateId, // 17% VAT
                    item_id: '',
                    reference: ''
                });
            }
            
            // Calculate totals
            calculateTotals();
        })
        .catch(error => {
            console.error('Error loading financial obligations:', error);
            itemsBody.innerHTML = originalContent;
            alert('{{ __("common.error_loading_data") | default("Error loading financial obligations") }}: ' + error.message);
        });
}

function handleInvoiceTypeChange() {
    console.log('🔔 handleInvoiceTypeChange called');
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const typeId = invoiceTypeSelect.value;
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    console.log('📌 Type details - ID:', typeId, 'Name:', typeName, 'Code:', typeCode);
    
    // Check if this is a retrocession invoice
    const isRetrocession = (typeCode && typeCode.startsWith('RET')) || typeName.includes('rétrocession') || typeName.includes('retrocession');
    
    console.log('🎯 Is Retrocession?', isRetrocession);
    
    if (isRetrocession) {
        console.log('✅ Processing RETROCESSION invoice type change');
        // Use the complete initialization function
        initializeRetrocessionInvoiceComplete();
    } else {
        // Update table headers to default
        updateInvoiceTableHeaders('');
        
        // Clear table if switching from retrocession
        const itemsBody = document.getElementById('itemsBody');
        if (itemsBody && itemsBody.querySelector('[data-line-type]')) {
            itemsBody.innerHTML = '';
            itemIndex = 0;
            addItem(); // Add default item
        }
    }
    
    // Update price label based on invoice type
    updatePriceLabel();
    
    // Recalculate totals with the new calculation method
    calculateTotals();
    
    // Handle rental invoice type (Loyer/LOY)
    if (typeId === '1' || typeName.includes('loyer') || typeCode === 'LOY') {
        // Auto-fill subject if empty
        const subjectField = document.getElementById('subject');
        if (subjectField && !subjectField.value) {
            subjectField.value = 'LOYER + CHARGES';
        }
        
        // Auto-fill period with current month if empty (for rental invoices)
        const periodField = document.getElementById('period');
        if (periodField && !periodField.value) {
            // French month names
            const frenchMonths = [
                'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL',
                'MAI', 'JUIN', 'JUILLET', 'AOÛT',
                'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
            ];
            
            // Get issue date - use current month for rental invoices
            const issueDateField = document.getElementById('issue_date');
            const issueDate = issueDateField ? new Date(issueDateField.value) : new Date();
            
            // For rental invoices: use current month
            const monthIndex = issueDate.getMonth();
            const year = issueDate.getFullYear();
            
            periodField.value = frenchMonths[monthIndex] + ' ' + year;
        }
        
        // Add default rental lines if table is empty
        const itemsBody = document.getElementById('itemsBody');
        if (itemsBody && itemsBody.children.length === 0) {
            // Update columns first, then add default items
            updateInvoiceItemColumns().then(() => {
                // Add "Loyer mensuel" line
                addItemWithData({
                    description: 'Loyer mensuel',
                    quantity: '1',
                    unit_price: '',
                    vat_rate_id: defaultVatRateId || ''
                });
                
                // Add "Charges location" line
                addItemWithData({
                    description: 'Charges location',
                    quantity: '1',
                    unit_price: '',
                    vat_rate_id: defaultVatRateId || ''
                });
            });
        }
    }
    
    // Handle LOC invoice type (Location)
    if (typeCode === 'LOC') {
        // Auto-fill subject if empty
        const subjectField = document.getElementById('subject');
        if (subjectField && !subjectField.value) {
            subjectField.value = 'LOCATION SALLE';
        }
        
        // Auto-fill period with previous month if empty
        const periodField = document.getElementById('period');
        if (periodField && !periodField.value) {
            // French month names
            const frenchMonths = [
                'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL',
                'MAI', 'JUIN', 'JUILLET', 'AOÛT',
                'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
            ];
            
            // Get issue date
            const issueDateField = document.getElementById('issue_date');
            const issueDate = issueDateField ? new Date(issueDateField.value) : new Date();
            
            // Calculate previous month
            const previousMonth = new Date(issueDate);
            previousMonth.setMonth(previousMonth.getMonth() - 1);
            
            const monthIndex = previousMonth.getMonth();
            const year = previousMonth.getFullYear();
            
            periodField.value = frenchMonths[monthIndex] + ' ' + year;
        }
    }
    
    // Update table headers for retrocession invoices
    updateInvoiceTableHeaders(typeCode);
}

function updateRentalPeriod() {
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const typeId = invoiceTypeSelect.value;
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    
    const periodField = document.getElementById('period');
    if (!periodField) return;
    
    // French month names
    const frenchMonths = [
        'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL',
        'MAI', 'JUIN', 'JUILLET', 'AOÛT',
        'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
    ];
    
    // Get issue date
    const issueDateField = document.getElementById('issue_date');
    const issueDate = issueDateField ? new Date(issueDateField.value) : new Date();
    
    // Get type code for more accurate detection
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    // Check if it's a LOY rental invoice (use current month)
    if (typeCode === 'LOY') {
        // For LOY invoices: use current month
        const monthIndex = issueDate.getMonth();
        const year = issueDate.getFullYear();
        periodField.value = frenchMonths[monthIndex] + ' ' + year;
    }
    // Check if it's a LOC invoice (use previous month)
    else if (typeCode === 'LOC' || typeId === '1' || typeName.includes('location')) {
        // For LOC invoices: use previous month
        const previousMonth = new Date(issueDate);
        previousMonth.setMonth(previousMonth.getMonth() - 1);
        
        const monthIndex = previousMonth.getMonth();
        const year = previousMonth.getFullYear();
        periodField.value = frenchMonths[monthIndex] + ' ' + year;
    }
    // Check if it's a retrocession invoice
    else if (typeName.includes('retrocession') || typeName.includes('rétrocession')) {
        // For retrocession invoices: use previous month
        const previousMonth = new Date(issueDate);
        previousMonth.setMonth(previousMonth.getMonth() - 1);
        
        const monthIndex = previousMonth.getMonth();
        const year = previousMonth.getFullYear();
        periodField.value = frenchMonths[monthIndex] + ' ' + year;
    }
}

// Complete retrocession invoice initialization
function initializeRetrocessionInvoiceComplete() {
    console.log('🚀 Complete retrocession initialization starting...');
    
    // Determine if this is 25% or 30% retrocession
    const urlParams = new URLSearchParams(window.location.search);
    const typeParam = urlParams.get('type');
    const isRet25 = typeParam === 'retrocession_25';
    
    // Set invoice type to retrocession in dropdown
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    if (invoiceTypeSelect) {
        // Find the correct retrocession option
        for (let option of invoiceTypeSelect.options) {
            const prefix = option.getAttribute('data-prefix');
            if (isRet25 && prefix === 'RET25') {
                invoiceTypeSelect.value = option.value;
                console.log('Set invoice type to Retrocession 25%');
                break;
            } else if (!isRet25 && (prefix === 'RET30' || prefix === 'RET')) {
                invoiceTypeSelect.value = option.value;
                console.log('Set invoice type to Retrocession 30%');
                break;
            }
        }
    }
    
    // Set default subject for retrocession
    const subjectField = document.getElementById('subject');
    if (subjectField && !subjectField.value) {
        subjectField.value = 'RÉTROCESSION';
        console.log('Set subject to RÉTROCESSION');
    }
    
    // Update period to previous month
    updateRentalPeriod();
    
    // Update invoice number to use RET prefix
    updateInvoiceNumber();
    console.log('Updated invoice number for retrocession');
    
    // Clear any existing items first
    const itemsBody = document.getElementById('itemsBody');
    if (itemsBody) {
        itemsBody.innerHTML = '';
        itemIndex = 0;
    }
    
    // Update table headers for retrocession format immediately
    updateInvoiceTableHeaders('RET');
    
    // Update tfoot colspan for retrocession (5 columns instead of 4)
    const tfootCells = document.querySelectorAll('#itemsTable tfoot td[colspan="4"]');
    tfootCells.forEach(cell => {
        cell.setAttribute('colspan', '3'); // Montant Base, OBJET, TOTAL columns
    });
    
    // Initialize retrocession invoice with pre-configured lines
    setTimeout(() => {
        // Check if a user is already selected
        const billableId = document.getElementById('billable_id')?.value;
        if (billableId && billableId.startsWith('user_')) {
            const userId = billableId.replace('user_', '');
            // Load user settings
            loadUserRetrocessionSettings(userId).then(settings => {
                initializeRetrocessionInvoice(isRet25, settings);
            });
        } else {
            // Initialize with default settings
            initializeRetrocessionInvoice(isRet25);
        }
    }, 100); // Small delay to ensure DOM is ready
}

// Initialize retrocession invoice with dynamic lines based on user settings
function initializeRetrocessionInvoice(isRet25 = false, userSettings = null) {
    const itemsBody = document.getElementById('itemsBody');
    if (!itemsBody) {
        console.error('itemsBody not found');
        return;
    }
    
    console.log('Initializing retrocession invoice with user settings...');
    
    // Clear existing items
    itemsBody.innerHTML = '';
    itemIndex = 0;
    
    // Use provided settings or defaults
    const settings = userSettings || {
        cns_type: 'percentage',
        cns_value: 20,
        patient_type: 'percentage',
        patient_value: 20,
        secretary_type: 'percentage',
        secretary_value: isRet25 ? 5 : 10
    };
    
    // Create lines array based on settings (only if value > 0)
    const lines = [];
    
    // CNS line
    if (parseFloat(settings.cns_value) > 0) {
        // Use custom label if provided, otherwise use default
        const cnsLabel = settings.cns_label || 'RÉTROCESSION CNS';
        const cnsDescription = `${cnsLabel} ${settings.cns_value}${settings.cns_type === 'percentage' ? '%' : '€'}`;
        
        lines.push({
            hasBaseInput: true,
            baseInputId: 'cns_amount',
            placeholder: '{{ __("invoices.enter_cns_amount")|default("Montant CNS") }}',
            description: cnsDescription,
            lineType: 'cns',
            vatRate: 0,
            value: settings.cns_value,
            valueType: settings.cns_type
        });
    }
    
    // Patient line
    if (parseFloat(settings.patient_value) > 0) {
        // Use custom label if provided
        const patientLabel = settings.patient_label || 'RÉTROCESSION PATIENTS';
        const patientDescription = `${patientLabel} ${settings.patient_value}${settings.patient_type === 'percentage' ? '%' : '€'}`;
        
        lines.push({
            hasBaseInput: true,
            baseInputId: 'patient_amount',
            placeholder: '{{ __("invoices.enter_patient_amount")|default("Montant Patients") }}',
            description: patientDescription,
            lineType: 'patient',
            vatRate: 0,
            value: settings.patient_value,
            valueType: settings.patient_type
        });
    }
    
    // Secretary line
    if (parseFloat(settings.secretary_value) > 0) {
        // Use custom label if provided
        const secretaryLabel = settings.secretary_label || 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL';
        const secretaryDescription = `${secretaryLabel} ${settings.secretary_value}${settings.secretary_type === 'percentage' ? '%' : '€'}`;
        
        lines.push({
            showTotal: true,
            totalId: 'secretary_total',
            description: secretaryDescription,
            lineType: 'secretary',
            vatRate: 17,
            value: settings.secretary_value,
            valueType: settings.secretary_type
        });
    }
    
    // Check if we have any lines
    if (lines.length === 0) {
        itemsBody.innerHTML = '<tr><td colspan="6" class="text-center text-warning py-4">{{ __("invoices.no_retrocession_lines") | default("Aucune ligne de rétrocession à créer (tous les montants sont à 0)") }}</td></tr>';
        return;
    }
    
    // Store settings for later use in calculations
    window.currentRetrocessionSettings = settings;
    
    // Add all lines
    lines.forEach((lineData, index) => {
        const row = document.createElement('tr');
        row.className = 'invoice-item';
        row.setAttribute('data-line-type', lineData.lineType);
        
        // Hidden quantity field
        const hiddenQty = document.createElement('input');
        hiddenQty.type = 'hidden';
        hiddenQty.name = `items[${index}][quantity]`;
        hiddenQty.value = '1';
        hiddenQty.className = 'item-quantity';
        row.appendChild(hiddenQty);
        
        // Column 1: Description (OBJET) - First column
        const col1 = document.createElement('td');
        col1.innerHTML = `
            <input type="hidden" name="items[${index}][item_id]" class="item-id" value="">
            <input type="text" class="form-control form-control-sm item-description" 
                   name="items[${index}][description]" 
                   value="${lineData.description}" 
                   readonly required>
        `;
        row.appendChild(col1);
        
        // Column 2: Base amount input or total display (Montant Base) - Second column
        const col2 = document.createElement('td');
        if (lineData.hasBaseInput) {
            col2.innerHTML = `
                <input type="number" class="form-control form-control-sm retrocession-base-amount" 
                       id="${lineData.baseInputId}" 
                       placeholder="${lineData.placeholder}"
                       min="0" step="0.01" value="">
            `;
        } else if (lineData.showTotal) {
            col2.innerHTML = `
                <input type="text" class="form-control form-control-sm" 
                       id="${lineData.totalId}" 
                       value="€0.00" readonly 
                       style="background-color: #f8f9fa; font-weight: 500;">
            `;
        }
        row.appendChild(col2);
        
        // Column 3: Total
        const col3 = document.createElement('td');
        col3.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
        row.appendChild(col3);
        
        // Column 4: HTVA
        const col4 = document.createElement('td');
        col4.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${index}][unit_price]" value="0.00" min="0" step="0.01" readonly>`;
        row.appendChild(col4);
        
        // Column 5: VAT Rate
        const col5 = document.createElement('td');
        const vatSelect = document.createElement('select');
        vatSelect.className = 'form-select form-select-sm item-vat';
        vatSelect.name = `items[${index}][vat_rate_id]`;
        // Don't set required on disabled fields
        vatSelect.disabled = true;
        
        // Add VAT options and select the appropriate rate
        vatRates.forEach(vat => {
            const option = document.createElement('option');
            option.value = vat.id;
            option.setAttribute('data-rate', vat.rate);
            option.textContent = `${vat.rate}`;
            if (parseFloat(vat.rate) === lineData.vatRate) {
                option.selected = true;
                // Set the value explicitly to ensure it's saved
                vatSelect.value = vat.id;
            }
            vatSelect.appendChild(option);
        });
        col5.appendChild(vatSelect);
        
        // Add hidden input for disabled select to ensure value is submitted
        const hiddenVat = document.createElement('input');
        hiddenVat.type = 'hidden';
        hiddenVat.name = vatSelect.name;
        hiddenVat.value = vatSelect.value;
        col5.appendChild(hiddenVat);
        
        // Update hidden input when select changes (even though it's disabled)
        vatSelect.addEventListener('change', function() {
            hiddenVat.value = this.value;
        });
        
        row.appendChild(col5);
        
        // Column 6: Actions
        const col6 = document.createElement('td');
        col6.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item" disabled><i class="bi bi-trash"></i></button>`;
        row.appendChild(col6);
        
        // Add to table
        itemsBody.appendChild(row);
    });
    
    // Set itemIndex to the number of lines added
    itemIndex = lines.length;
    
    // Add event listeners to base amount inputs
    const cnsInput = document.getElementById('cns_amount');
    const patientInput = document.getElementById('patient_amount');
    
    if (cnsInput) {
        cnsInput.addEventListener('input', calculateRetrocessionAmounts);
    }
    if (patientInput) {
        patientInput.addEventListener('input', calculateRetrocessionAmounts);
    }
    
    // Calculate initial totals (all should be 0)
    calculateRetrocessionTotals();
    
    console.log('Retrocession initialization complete - added ' + lines.length + ' lines');
}

// Calculate retrocession amounts based on inline inputs
function calculateRetrocessionAmounts() {
    const cnsInput = document.getElementById('cns_amount');
    const patientInput = document.getElementById('patient_amount');
    
    const cnsAmount = cnsInput ? parseFloat(cnsInput.value) || 0 : 0;
    const patientAmount = patientInput ? parseFloat(patientInput.value) || 0 : 0;
    
    // Use stored settings or defaults
    const settings = window.currentRetrocessionSettings || {
        cns_type: 'percentage',
        cns_value: 20,
        patient_type: 'percentage',
        patient_value: 20,
        secretary_type: 'percentage',
        secretary_value: 10
    };
    
    // Calculate retrocession parts based on settings
    let partCns = 0;
    let partPatients = 0;
    
    if (settings.cns_type === 'percentage') {
        partCns = cnsAmount * (settings.cns_value / 100);
    } else {
        partCns = settings.cns_value; // Fixed amount
    }
    
    if (settings.patient_type === 'percentage') {
        partPatients = patientAmount * (settings.patient_value / 100);
    } else {
        partPatients = settings.patient_value; // Fixed amount
    }
    
    // Calculate secretary fee based on settings
    const totalCnsPatient = cnsAmount + patientAmount;
    let secretaryFeeTvac = 0;
    
    if (settings.secretary_type === 'percentage') {
        secretaryFeeTvac = totalCnsPatient * (settings.secretary_value / 100);
    } else {
        secretaryFeeTvac = settings.secretary_value; // Fixed amount
    }
    
    // Calculate HTVA (remove 17% VAT)
    const secretaryFeeHtva = secretaryFeeTvac / 1.17;
    
    // Update the secretary total display
    const secretaryTotalInput = document.getElementById('secretary_total');
    if (secretaryTotalInput) {
        secretaryTotalInput.value = currency + totalCnsPatient.toFixed(2);
    }
    
    // Update the calculated amounts in each row
    const rows = document.querySelectorAll('.invoice-item');
    rows.forEach(row => {
        const lineType = row.getAttribute('data-line-type');
        const priceInput = row.querySelector('.item-price');
        
        if (lineType === 'cns' && priceInput) {
            priceInput.value = partCns.toFixed(2);
        } else if (lineType === 'patient' && priceInput) {
            priceInput.value = partPatients.toFixed(2);
        } else if (lineType === 'secretary' && priceInput) {
            priceInput.value = secretaryFeeHtva.toFixed(2);
        }
        
        // Recalculate row total
        calculateRetrocessionItemTotal(row);
    });
    
    // Use retrocession-specific totals calculation
    calculateRetrocessionTotals();
}

// Legacy function for compatibility
function calculateRetrocession() {
    calculateRetrocessionAmounts();
}

// Helper function to get 0% VAT rate ID
function getZeroVatRateId() {
    for (let vat of vatRates) {
        if (parseFloat(vat.rate) === 0) {
            return vat.id;
        }
    }
    return vatRates[0]?.id || '';
}

// Helper function to get 17% VAT rate ID  
function get17VatRateId() {
    for (let vat of vatRates) {
        if (parseFloat(vat.rate) === 17) {
            return vat.id;
        }
    }
    return vatRates[0]?.id || '';
}

// Backward compatibility for old function name
function calculateSecretaryFee() {
    calculateRetrocession();
}


// Invoice number is now generated on save - no need for this function

// Debug: Script execution checkpoint
console.log('Invoice create script - Loading client/user data...');

// Store clients and users data safely with error handling
let clientsData = [];
let usersData = [];
let coachesData = [];
let practitionersData = [];

try {
    clientsData = {{ clients|default([])|json_encode|raw }};
    console.log('Clients data loaded successfully:', clientsData.length, 'clients');
    if (clientsData.length > 0) {
        console.log('First client:', clientsData[0]);
    }
} catch (error) {
    console.error('Error loading clients data:', error);
    console.log('Clients variable exists:', {{ clients is defined ? 'true' : 'false' }});
}

try {
    usersData = {{ users|default([])|json_encode|raw }};
    console.log('Users data loaded successfully:', usersData.length, 'users');
    if (usersData.length > 0) {
        console.log('First user:', usersData[0]);
    }
} catch (error) {
    console.error('Error loading users data:', error);
    console.log('Users variable exists:', {{ users is defined ? 'true' : 'false' }});
}

try {
    coachesData = {{ coaches|default([])|json_encode|raw }};
    console.log('Coaches data loaded successfully:', coachesData.length, 'coaches');
    if (coachesData.length > 0) {
        console.log('First coach:', coachesData[0]);
    }
} catch (error) {
    console.error('Error loading coaches data:', error);
    console.log('Coaches variable exists:', {{ coaches is defined ? 'true' : 'false' }});
}

let loyaltyUsersData = [];
try {
    loyaltyUsersData = {{ loyaltyUsers|default([])|json_encode|raw }};
    console.log('Loyalty users data loaded successfully:', loyaltyUsersData.length, 'users');
} catch (error) {
    console.error('Error loading loyalty users data:', error);
}

try {
    practitionersData = {{ practitioners|default([])|json_encode|raw }};
    console.log('Practitioners data loaded successfully:', practitionersData.length, 'practitioners');
    if (practitionersData.length > 0) {
        console.log('First practitioner:', practitionersData[0]);
    }
} catch (error) {
    console.error('Error loading practitioners data:', error);
    console.log('Practitioners variable exists:', {{ practitioners is defined ? 'true' : 'false' }});
}

function loadBillableOptions() {
    return new Promise((resolve, reject) => {
        const type = document.getElementById('billable_type').value;
        const select = document.getElementById('billable_id');
        const label = document.getElementById('billable_label');
        const hint = document.getElementById('billable_hint');
        const addBtn = document.getElementById('addNewBillableBtn');
        
        console.log('loadBillableOptions called with type:', type);
        
        if (!type) {
            select.disabled = true;
            addBtn.disabled = true;
            select.innerHTML = '<option value="">{{ __("common.select_type_first") }}</option>';
            resolve(); // Resolve even when no type
            return;
        }
        
        select.disabled = false;
        
        // Clear the select options
        select.innerHTML = '<option value="">{{ __("common.select") }}</option>';
        
        try {
            // Handle different types
            if (type === 'client') {
                console.log('Loading clients, count:', clientsData.length);
                label.textContent = '{{ __("clients.client") }}';
                hint.textContent = '{{ __("invoices.cant_find_client_add_new") }}';
                addBtn.disabled = false;
                
                // Add clients to dropdown using safe JSON data
                clientsData.forEach(client => {
                    const option = new Option(`${client.name} (${client.client_number})`, `client_${client.id}`);
                    select.add(option);
                });
                console.log('Clients loaded into dropdown, options count:', select.options.length);
                
            } else if (type === 'user') {
                // Check invoice type to determine which users to show
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                const selectedOption = invoiceTypeSelect ? invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex] : null;
                const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
                
                console.log('Invoice type detection - Code:', typeCode, 'Name:', typeName);
                
                // Determine invoice type categories
                const isLoyaltyInvoice = typeCode === 'LOY';
                const isLocationInvoice = typeCode === 'LOC' || 
                                        (typeName.includes('location') && !typeName.includes('loyalty'));
                const isRetrocessionInvoice = typeCode === 'RET' || typeCode === 'RT30' || typeCode === 'RT25' ||
                                            typeName.includes('rétrocession') || typeName.includes('retrocession');
                
                if (isLoyaltyInvoice) {
                    // Show Medical and Managers users for LOY invoices
                    label.textContent = '{{ __("users.user") | default("User") }}';
                    hint.textContent = '{{ __("invoices.select_user_for_loyalty") | default("Select a user from Medical or Managers group") }}';
                    addBtn.disabled = true;
                    
                    console.log('Loading Medical/Managers users for LOY invoice, count:', loyaltyUsersData.length);
                    loyaltyUsersData.forEach(user => {
                        // For LOY invoices, only show first name
                        const firstName = user.first_name || user.name.split(' ')[0] || user.name;
                        const option = new Option(firstName, `user_${user.id}`);
                        select.add(option);
                    });
                    
                } else if (isLocationInvoice) {
                    // Show coaches for location invoices
                    label.textContent = '{{ __("users.coach") | default("Coach") }}';
                    hint.textContent = '{{ __("invoices.select_coach_for_location") | default("Select the coach for location rental") }}';
                    addBtn.disabled = true;
                    
                    console.log('Loading coaches for location invoice, count:', coachesData.length);
                    coachesData.forEach(coach => {
                        const courseName = coach.course_name ? ` - ${coach.course_name}` : '';
                        const option = new Option(`${coach.name} (${coach.username})${courseName}`, `user_${coach.id}`);
                        select.add(option);
                    });
                    
                } else if (isRetrocessionInvoice) {
                    // Show practitioners for retrocession invoices
                    label.textContent = '{{ __("users.practitioner") | default("Practitioner") }}';
                    hint.textContent = '{{ __("invoices.select_practitioner_for_retrocession") | default("Select the practitioner for retrocession calculation") }}';
                    addBtn.disabled = true;
                    
                    console.log('Loading practitioners for retrocession invoice, count:', practitionersData.length);
                    practitionersData.forEach(practitioner => {
                        const option = new Option(`${practitioner.name} (${practitioner.username})`, `user_${practitioner.id}`);
                        select.add(option);
                    });
                    
                } else {
                    // Default - show all users
                    label.textContent = '{{ __("users.user") }}';
                    hint.textContent = '{{ __("invoices.internal_invoice_hint") }}';
                    addBtn.disabled = true;
                    
                    console.log('Loading all users, count:', usersData.length);
                    usersData.forEach(user => {
                        const option = new Option(`${user.name} (${user.username})`, `user_${user.id}`);
                        select.add(option);
                    });
                }
            }
            
            // Resolve the promise after options are loaded
            resolve();
        } catch (error) {
            console.error('Error in loadBillableOptions:', error);
            alert('{{ __("common.error_loading_data") | default("Error loading data") }}: ' + error.message);
            reject(error);
        }
    });
}

// applyVoucher function removed - voucher section no longer exists in the form

function updateDueDate() {
    const paymentTermSelect = document.getElementById('payment_term_id');
    const issueDate = document.getElementById('issue_date').value;
    const dueDateInput = document.getElementById('due_date');
    
    if (!paymentTermSelect.value || !issueDate) {
        return;
    }
    
    const selectedOption = paymentTermSelect.options[paymentTermSelect.selectedIndex];
    const days = parseInt(selectedOption.getAttribute('data-days')) || 0;
    
    // Calculate due date
    const date = new Date(issueDate);
    date.setDate(date.getDate() + days);
    
    // Format as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}`;
}

// Update invoice number when document type changes
function updateInvoiceNumber() {
    const documentTypeId = document.getElementById('document_type_id')?.value;
    const invoiceTypeId = document.getElementById('invoice_type_id')?.value;
    const currentInvoiceNumber = document.getElementById('invoice_number')?.value || '';
    
    console.log('updateInvoiceNumber called - documentTypeId:', documentTypeId, 'invoiceTypeId:', invoiceTypeId, 'current:', currentInvoiceNumber);
    
    if (!documentTypeId) {
        console.log('No document type selected, skipping number update');
        return;
    }
    
    // Check if we already have an invoice number and should preserve the sequence
    if (currentInvoiceNumber && currentInvoiceNumber.length > 0) {
        // Extract the last 4 digits (sequence number)
        const match = currentInvoiceNumber.match(/(\d{4})$/);
        if (match) {
            const sequenceNumber = match[1];
            console.log('Preserving sequence number:', sequenceNumber);
            
            // Get the invoice type prefix
            const invoiceTypeSelect = document.getElementById('invoice_type_id');
            const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
            const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            
            if (typePrefix) {
                // Special handling for retrocession types
                let newNumber;
                if (typePrefix === 'RET') {
                    // Determine RET30 or RET25 based on existing pattern or default to RET30
                    const retType = currentInvoiceNumber.includes('RET25') ? 'RET25' : 'RET30';
                    newNumber = `FAC-${retType}-${new Date().getFullYear()}-${sequenceNumber}`;
                } else {
                    // Standard format: FAC-{TYPE}-{YEAR}-{SEQUENCE}
                    newNumber = `FAC-${typePrefix}-${new Date().getFullYear()}-${sequenceNumber}`;
                }
                
                console.log('Updated invoice number to:', newNumber);
                document.getElementById('invoice_number').value = newNumber;
                return; // Don't fetch from server
            }
        }
    }
    
    // If no existing number or can't extract sequence, fetch new number from server
    let url = '{{ base_url }}/invoices/generate-number?document_type_id=' + documentTypeId;
    if (invoiceTypeId) {
        url += '&invoice_type_id=' + invoiceTypeId;
    }
    
    console.log('Fetching new invoice number from:', url);
    
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success && data.number) {
                console.log('Setting invoice number to:', data.number);
                document.getElementById('invoice_number').value = data.number;
            } else {
                console.error('Failed to get invoice number:', data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching invoice number:', error);
        });
}

// Update table headers based on invoice type
function updateInvoiceTableHeaders(typeCode) {
    console.log('🔵 updateInvoiceTableHeaders called with:', typeCode);
    const headerRow = document.getElementById('itemsTableHeader');
    if (!headerRow) {
        console.error('❌ itemsTableHeader not found!');
        return;
    }
    
    if (typeCode === 'RET') {
        console.log('✅ Setting RETROCESSION headers');
        // Retrocession invoice headers: OBJET | Montant Base | TOTAL | HTVA | % TVA | Actions
        headerRow.innerHTML = `
            <th width="45%">{{ __('invoices.object')|default('OBJET') }}</th>
            <th width="15%">{{ __('invoices.base_amount')|default('Montant Base') }}</th>
            <th width="15%">{{ __('invoices.total')|default('TOTAL') }}</th>
            <th width="15%">{{ __('invoices.amount_excl_vat')|default('HTVA') }}</th>
            <th width="10%">{{ __('invoices.vat_rate')|default('% TVA') }}</th>
            <th width="50"></th>
        `;
        console.log('✅ Retrocession headers set. Current headers:', headerRow.innerHTML);
    } else {
        console.log('📋 Setting DEFAULT headers');
        // Default headers
        headerRow.innerHTML = `
            <th width="40%">{{ __('invoices.description') }}</th>
            <th width="15%">{{ __('invoices.quantity') }}</th>
            <th width="15%">{{ __('invoices.unit_price') }}</th>
            <th width="15%">{{ __('invoices.vat_rate') }}</th>
            <th width="15%">{{ __('invoices.total') }}</th>
            <th width="50"></th>
        `;
    }
}

// Add retrocession item with specific column structure (without quantity)
function addRetrocessionItem(row, itemData, tbody) {
    // Store formula if provided
    if (itemData.calculation_formula) {
        row.setAttribute('data-formula', itemData.calculation_formula);
    }
    
    // Store quantity as hidden field (always 1 for retrocession)
    const hiddenQty = document.createElement('input');
    hiddenQty.type = 'hidden';
    hiddenQty.name = `items[${itemIndex}][quantity]`;
    hiddenQty.value = '1';
    hiddenQty.className = 'item-quantity';
    row.appendChild(hiddenQty);
    
    // Check if this is a line with base amount input (CNS or Patient) or shows total
    const hasBaseInput = itemData.hasBaseInput || false;
    const showTotal = itemData.showTotal || false;
    
    // OBJET column (description) - First column
    const objetTd = document.createElement('td');
    objetTd.innerHTML = `
        <input type="hidden" name="items[${itemIndex}][item_id]" class="item-id" value="${itemData.item_id || ''}">
        <input type="text" class="form-control form-control-sm item-description" 
               name="items[${itemIndex}][description]" 
               value="${itemData.description || ''}" 
               ${itemData.readOnlyDescription ? 'readonly' : ''} required>
    `;
    row.appendChild(objetTd);
    
    // Montant Base column (for CNS/Patient lines) or total display - Second column
    const baseTd = document.createElement('td');
    if (hasBaseInput) {
        baseTd.innerHTML = `
            <input type="number" class="form-control form-control-sm retrocession-base-amount" 
                   id="${itemData.baseInputId || ''}" 
                   placeholder="${itemData.placeholder || ''}"
                   min="0" step="0.01" value="${itemData.baseAmount || ''}">
        `;
    } else if (showTotal) {
        baseTd.innerHTML = `
            <input type="text" class="form-control form-control-sm" 
                   id="${itemData.totalId || ''}" 
                   value="€0.00" readonly 
                   style="background-color: #f8f9fa; font-weight: 500;">
        `;
    } else {
        baseTd.innerHTML = ''; // Empty cell if no base input needed
    }
    row.appendChild(baseTd);
    
    // TOTAL column
    const totalTd = document.createElement('td');
    totalTd.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
    row.appendChild(totalTd);
    
    // HTVA column (amount without VAT)
    const htvaTd = document.createElement('td');
    htvaTd.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${itemIndex}][unit_price]" value="${itemData.unit_price || ''}" min="0" step="0.01" readonly>`;
    row.appendChild(htvaTd);
    
    // % TVA column
    const vatTd = document.createElement('td');
    const vatSelect = document.createElement('select');
    vatSelect.className = 'form-select form-select-sm item-vat';
    vatSelect.name = `items[${itemIndex}][vat_rate_id]`;
    // Don't set required on disabled fields - it causes validation to fail
    vatSelect.disabled = true; // Disabled by default for retrocession
    
    // Add VAT options
    vatRates.forEach(vat => {
        const option = document.createElement('option');
        option.value = vat.id;
        option.setAttribute('data-rate', vat.rate);
        option.textContent = `${vat.rate}%`;
        if (itemData.vat_rate_id == vat.id || (!itemData.vat_rate_id && vat.is_default)) {
            option.selected = true;
        }
        vatSelect.appendChild(option);
    });
    vatTd.appendChild(vatSelect);
    
    // Add hidden input for disabled select to ensure value is submitted
    const hiddenVat = document.createElement('input');
    hiddenVat.type = 'hidden';
    hiddenVat.name = vatSelect.name;
    hiddenVat.value = vatSelect.value;
    vatTd.appendChild(hiddenVat);
    
    // Update hidden input when select changes
    vatSelect.addEventListener('change', function() {
        hiddenVat.value = this.value;
    });
    
    row.appendChild(vatTd);
    
    // Actions column
    const actionsTd = document.createElement('td');
    actionsTd.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item" disabled><i class="bi bi-trash"></i></button>`;
    row.appendChild(actionsTd);
    
    // Store line type for calculation purposes
    row.setAttribute('data-line-type', itemData.lineType || '');
    
    // Add event listeners
    const baseInput = row.querySelector('.retrocession-base-amount');
    if (baseInput) {
        baseInput.addEventListener('input', calculateRetrocessionAmounts);
    }
    
    tbody.appendChild(row);
    itemIndex++;
    
    // Calculate initial total for this row
    calculateRetrocessionItemTotal(row);
}

// Calculate total for retrocession item (HTVA + VAT = TOTAL)
function calculateRetrocessionItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const htva = parseFloat(row.querySelector('.item-price')?.value) || 0; // HTVA amount
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
    
    // For retrocession, the price is already the HTVA amount
    const totalHtva = quantity * htva;
    const vat = totalHtva * (vatRate / 100);
    const total = totalHtva + vat;
    
    // Update total field
    const totalInput = row.querySelector('.item-total');
    if (totalInput) {
        totalInput.value = currency + total.toFixed(2);
    }
}

// Calculate totals specifically for retrocession invoices
function calculateRetrocessionTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    // Get all invoice rows
    const rows = document.querySelectorAll('.invoice-item');
    
    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
        const htva = parseFloat(row.querySelector('.item-price')?.value) || 0;
        const vatSelect = row.querySelector('.item-vat');
        const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
        
        // Calculate this row's contribution
        const rowSubtotal = quantity * htva;
        const rowVat = rowSubtotal * (vatRate / 100);
        
        subtotal += rowSubtotal;
        vatAmount += rowVat;
    });
    
    const total = subtotal + vatAmount;
    
    // Update the display
    document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
    document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
    document.getElementById('total').textContent = currency + total.toFixed(2);
}

// Update available templates based on invoice type
function updateTemplateOptions() {
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const templateSelect = document.getElementById('template_id');
    
    if (!invoiceTypeSelect || !templateSelect) {
        return;
    }
    
    const selectedType = invoiceTypeSelect.value;
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typePrefix = selectedOption?.getAttribute('data-prefix') || '';
    
    console.log('Updating templates for invoice type:', selectedType, 'prefix:', typePrefix);
    
    // Determine the invoice type string to use for template lookup
    let typeString = '{{ type }}'; // Original type from URL
    
    // Map invoice type prefixes to type strings
    if (typePrefix === 'RET') {
        typeString = 'retrocession_30';
    } else if (typePrefix === 'LOY') {
        typeString = 'rental';
    }
    
    // Fetch templates for this type
    fetch(`{{ base_url }}/api/invoice-templates?type=${encodeURIComponent(typeString)}`)
        .then(response => response.json())
        .then(data => {
            // Clear current options except the first one
            while (templateSelect.options.length > 1) {
                templateSelect.remove(1);
            }
            
            // Add new options
            if (data.templates && data.templates.length > 0) {
                data.templates.forEach(template => {
                    const option = new Option(template.name, template.id);
                    templateSelect.add(option);
                });
                console.log(`Added ${data.templates.length} templates`);
            } else {
                console.log('No templates found for type:', typeString);
            }
        })
        .catch(error => {
            console.error('Error fetching templates:', error);
        });
}

// Store current column configuration
let currentColumnConfig = [];

// Update invoice item columns based on document type and invoice type
function updateInvoiceItemColumns() {
    const documentTypeId = document.getElementById('document_type_id')?.value || '';
    const invoiceTypeId = document.getElementById('invoice_type_id')?.value || '';
    
    console.log('🔧 updateInvoiceItemColumns called - docType:', documentTypeId, 'invoiceType:', invoiceTypeId);
    
    // Check if this is a retrocession invoice and skip column update
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    if (typeCode && typeCode.startsWith('RET')) {
        console.log('🛑 Skipping column update for RETROCESSION invoice (type: ' + typeCode + ')');
        return Promise.resolve(); // Return resolved promise to maintain chain
    }
    
    // Build URL with parameters
    let url = '{{ base_url }}/api/column-config/invoice_items';
    const params = new URLSearchParams();
    if (documentTypeId) params.append('documentTypeId', documentTypeId);
    if (invoiceTypeId) params.append('invoiceTypeId', invoiceTypeId);
    params.append('_t', Date.now()); // Cache buster
    if (params.toString()) url += '?' + params.toString();
    
    console.log('📡 Fetching column config from:', url);
    
    return fetch(url)
        .then(response => response.json())
        .then(data => {
            console.log('📥 Column config response:', data);
            if (data.success) {
                currentColumnConfig = data.columns;
                console.log('📊 Column configuration loaded:', currentColumnConfig);
                
                // If it's a retrocession invoice, use special handling
                if (data.isRetrocession) {
                    console.log('🎯 RETROCESSION detected in API response');
                    updateInvoiceTableHeaders('RET');
                    // Don't rebuild the table with generic columns
                    return;
                }
                
                console.log('📋 Not retrocession, rebuilding table...');
                rebuildInvoiceItemsTable();
            } else {
                console.error('❌ Failed to load column config:', data.message);
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error loading column config:', error);
            // Use default configuration
            currentColumnConfig = [
                {id: 'description', name: '{{ __("common.description") }}', visible: true, required: true},
                {id: 'quantity', name: '{{ __("common.quantity") }}', visible: true},
                {id: 'unit_price', name: '{{ __("invoices.unit_price") }}', visible: true},
                {id: 'vat_rate', name: '{{ __("invoices.vat_rate") }}', visible: true},
                {id: 'total', name: '{{ __("common.total") }}', visible: true, required: true}
            ];
            rebuildInvoiceItemsTable();
        });
}

// Rebuild the invoice items table based on column configuration
function rebuildInvoiceItemsTable() {
    console.log('🔄 rebuildInvoiceItemsTable called');
    
    // Check if this is a retrocession invoice FIRST before doing anything
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    console.log('🔍 Invoice type code:', typeCode);
    
    if (typeCode && typeCode.startsWith('RET')) {
        console.log('🛑 Skipping rebuild for RETROCESSION invoice (type: ' + typeCode + ')');
        // Don't rebuild for retrocession, let the special handler manage it
        return;
    }
    
    // Also check if table has retrocession structure (as additional safety)
    const firstRow = document.querySelector('.invoice-item');
    if (firstRow && firstRow.querySelector('[data-line-type]')) {
        console.log('🛑 Retrocession structure detected - skipping rebuild');
        return;
    }
    
    const thead = document.querySelector('#itemsTable thead tr');
    const tbody = document.getElementById('itemsBody');
    
    if (!thead || !currentColumnConfig.length) {
        console.log('⚠️ Skipping rebuild - no thead or columnConfig');
        return;
    }
    
    // Save existing items data
    const existingItems = [];
    document.querySelectorAll('.invoice-item').forEach(row => {
        const item = {
            description: row.querySelector('[name*="[description]"]')?.value || '',
            reference: row.querySelector('[name*="[reference]"]')?.value || '',
            quantity: row.querySelector('[name*="[quantity]"]')?.value || '1',
            unit: row.querySelector('[name*="[unit]"]')?.value || '',
            unit_price: row.querySelector('[name*="[unit_price]"]')?.value || '',
            discount: row.querySelector('[name*="[discount]"]')?.value || '0',
            vat_rate_id: row.querySelector('[name*="[vat_rate_id]"]')?.value || ''
        };
        existingItems.push(item);
    });
    
    // Clear and rebuild table headers
    thead.innerHTML = '';
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const th = document.createElement('th');
        th.textContent = col.name;
        
        // Set width based on column type
        switch(col.id) {
            case 'description':
                th.style.width = '40%';
                break;
            case 'reference':
                th.style.width = '15%';
                break;
            case 'quantity':
            case 'unit':
            case 'unit_price':
            case 'discount':
            case 'vat_rate':
            case 'subtotal':
                th.style.width = '10%';
                break;
            case 'total':
                th.style.width = '15%';
                break;
        }
        
        thead.appendChild(th);
    });
    
    // Add actions column
    const actionsHeader = document.createElement('th');
    actionsHeader.style.width = '50px';
    thead.appendChild(actionsHeader);
    
    // Clear tbody and re-add items with new structure
    tbody.innerHTML = '';
    itemIndex = 0;
    
    // Re-add existing items
    existingItems.forEach(itemData => {
        addItemWithData(itemData);
    });
    
    // If no items, add one empty item
    if (existingItems.length === 0) {
        addItem();
    }
}

// Modified addItem function to use column configuration
function addItemWithData(itemData = {}) {
    const tbody = document.getElementById('itemsBody');
    const row = document.createElement('tr');
    row.className = 'invoice-item';
    
    // Store formula if provided
    if (itemData.calculation_formula) {
        row.setAttribute('data-formula', itemData.calculation_formula);
    }
    
    // Check if this is a retrocession invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const isRetrocession = typeCode === 'RET';
    
    // Use different column structure for retrocession invoices
    if (isRetrocession) {
        addRetrocessionItem(row, itemData, tbody);
        return;
    }
    
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const td = document.createElement('td');
        
        switch(col.id) {
            case 'description':
                let providerInputs = '';
                if (itemData.provider_type && itemData.provider_id) {
                    providerInputs = `
                        <input type="hidden" name="items[${itemIndex}][provider_type]" value="${itemData.provider_type}">
                        <input type="hidden" name="items[${itemIndex}][provider_id]" value="${itemData.provider_id}">
                        <input type="hidden" name="items[${itemIndex}][provider_name]" value="${itemData.provider_name || ''}">
                    `;
                }
                td.innerHTML = `
                    <input type="hidden" name="items[${itemIndex}][item_id]" class="item-id" value="${itemData.item_id || ''}">
                    ${providerInputs}
                    <input type="text" class="form-control form-control-sm item-description" name="items[${itemIndex}][description]" value="${itemData.description || ''}" required>
                `;
                if (itemData.provider_name) {
                    td.innerHTML += `<small class="text-muted">Prestataire: ${itemData.provider_name}</small>`;
                }
                break;
                
            case 'reference':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-reference" name="items[${itemIndex}][reference]" value="${itemData.reference || ''}">`;
                break;
                
            case 'quantity':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-quantity" name="items[${itemIndex}][quantity]" value="${itemData.quantity || '1'}" min="0.01" step="0.01" required>`;
                break;
                
            case 'unit':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-unit" name="items[${itemIndex}][unit]" value="${itemData.unit || ''}">`;
                break;
                
            case 'unit_price':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${itemIndex}][unit_price]" value="${itemData.unit_price || ''}" min="0" step="0.01" required>`;
                break;
                
            case 'discount':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-discount" name="items[${itemIndex}][discount]" value="${itemData.discount || '0'}" min="0" max="100" step="0.01">`;
                break;
                
            case 'vat_rate':
                const vatSelect = document.createElement('select');
                vatSelect.className = 'form-select form-select-sm item-vat';
                vatSelect.name = `items[${itemIndex}][vat_rate_id]`;
                vatSelect.required = true;
                
                // Add VAT options
                vatRates.forEach(vat => {
                    const option = document.createElement('option');
                    option.value = vat.id;
                    option.setAttribute('data-rate', vat.rate);
                    option.textContent = `${vat.rate}`;
                    if (itemData.vat_rate_id == vat.id || (!itemData.vat_rate_id && vat.is_default)) {
                        option.selected = true;
                    }
                    vatSelect.appendChild(option);
                });
                
                td.appendChild(vatSelect);
                break;
                
            case 'subtotal':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-subtotal" readonly>`;
                break;
                
            case 'total':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
                break;
        }
        
        row.appendChild(td);
    });
    
    // Add actions column
    const actionsTd = document.createElement('td');
    actionsTd.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item"><i class="bi bi-trash"></i></button>`;
    row.appendChild(actionsTd);
    
    // Add event listeners
    const quantityInput = row.querySelector('.item-quantity');
    const priceInput = row.querySelector('.item-price');
    const discountInput = row.querySelector('.item-discount');
    const vatSelect = row.querySelector('.item-vat');
    const removeBtn = row.querySelector('.remove-item');
    
    if (quantityInput) quantityInput.addEventListener('input', () => calculateItemTotal(row));
    if (priceInput) priceInput.addEventListener('input', () => calculateItemTotal(row));
    if (discountInput) discountInput.addEventListener('input', () => calculateItemTotal(row));
    if (vatSelect) vatSelect.addEventListener('change', () => calculateItemTotal(row));
    if (removeBtn) removeBtn.addEventListener('click', () => removeItem(row));
    
    tbody.appendChild(row);
    itemIndex++;
    
    // Calculate initial total for this row
    calculateItemTotal(row);
}

// Update the original addItem function to use the new one
window.addItem = function() {
    // Check if this is a retrocession invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    if (typeCode === 'RET') {
        // For retrocession, don't use column config, just add the item
        addItemWithData();
        return;
    }
    
    // Check if columns are loaded
    if (!currentColumnConfig || currentColumnConfig.length === 0) {
        // Use default columns if not loaded yet
        currentColumnConfig = [
            {id: 'description', name: '{{ __("common.description") }}', visible: true, required: true},
            {id: 'quantity', name: '{{ __("common.quantity") }}', visible: true},
            {id: 'unit_price', name: '{{ __("invoices.unit_price") }}', visible: true},
            {id: 'vat_rate', name: '{{ __("invoices.vat_rate") }}', visible: true},
            {id: 'total', name: '{{ __("common.total") }}', visible: true, required: true}
        ];
    }
    addItemWithData();
};

// Update calculateItemTotal to handle discount and subtotal columns
function calculateItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
    const discount = parseFloat(row.querySelector('.item-discount')?.value) || 0;
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
    
    const subtotal = quantity * price;
    const discountAmount = subtotal * (discount / 100);
    const discountedSubtotal = subtotal - discountAmount;
    const vat = discountedSubtotal * (vatRate / 100);
    const total = discountedSubtotal + vat;
    
    // Update subtotal field if visible
    const subtotalInput = row.querySelector('.item-subtotal');
    if (subtotalInput) {
        subtotalInput.value = currency + discountedSubtotal.toFixed(2);
    }
    
    // Update total field
    const totalInput = row.querySelector('.item-total');
    if (totalInput) {
        totalInput.value = currency + total.toFixed(2);
    }
    
    calculateTotals();
}

// Function to apply template
function applyTemplate(templateId) {
    // Show loading indicator
    const itemsBody = document.getElementById('itemsBody');
    const existingItems = itemsBody.querySelectorAll('.invoice-item').length;
    
    // Confirm if there are existing items
    if (existingItems > 0) {
        if (!confirm('{{ __("invoices.replace_existing_items")|default("This will replace existing items. Continue?") }}')) {
            document.getElementById('template_id').value = '';
            return;
        }
    }
    
    // Fetch template details
    fetch(`{{ base_url }}/api/invoice-templates/${templateId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.template) {
                // Clear existing items
                itemsBody.innerHTML = '';
                itemIndex = 0;
                
                // Add template line items
                if (data.template.line_items && data.template.line_items.length > 0) {
                    data.template.line_items.forEach(item => {
                        window.addItemWithData({
                            description: item.description,
                            quantity: item.default_quantity || '1',
                            unit_price: item.default_unit_price || '',
                            vat_rate_id: item.vat_rate_id || '',
                            item_id: item.catalog_item_id || '',
                            calculation_formula: item.calculation_formula || ''
                        });
                    });
                    
                    // After all items are added, apply formulas if any
                    setTimeout(() => {
                        applyTemplateFormulas();
                    }, 100);
                }
                
                // Apply template settings if any
                if (data.template.settings) {
                    // Apply payment terms if set
                    if (data.template.settings.payment_terms) {
                        const paymentTermSelect = document.getElementById('payment_term_id');
                        if (paymentTermSelect) {
                            const option = Array.from(paymentTermSelect.options).find(opt => 
                                opt.getAttribute('data-days') === data.template.settings.payment_terms
                            );
                            if (option) {
                                paymentTermSelect.value = option.value;
                                updateDueDate();
                            }
                        }
                    }
                    
                    // Apply subject if set
                    if (data.template.settings.default_subject) {
                        const subjectField = document.getElementById('subject');
                        if (subjectField && !subjectField.value) {
                            subjectField.value = data.template.settings.default_subject;
                        }
                    }
                }
                
                // Show success message
                console.log('Template applied successfully');
                
                // If this is a retrocession template, trigger calculation
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
                const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                
                if (typeCode === 'RET') {
                    // Initialize retrocession invoice when template is applied
                    initializeRetrocessionInvoice();
                }
            }
        })
        .catch(error => {
            console.error('Error applying template:', error);
            alert('{{ __("invoices.template_apply_error")|default("Error applying template") }}');
            document.getElementById('template_id').value = '';
        });
}

// Apply template formulas to calculate prices
function applyTemplateFormulas() {
    console.log('Applying template formulas...');
    
    const rows = document.querySelectorAll('.invoice-item');
    const subtotal = parseFloat(document.getElementById('subtotal').textContent.replace('€', '')) || 0;
    
    rows.forEach((row, index) => {
        // Check if this row has a formula stored
        const formula = row.getAttribute('data-formula');
        if (formula) {
            console.log(`Row ${index} has formula: ${formula}`);
            
            // Calculate based on formula
            let calculatedPrice = 0;
            
            // Replace variables in formula
            let evalFormula = formula.replace('{subtotal}', subtotal);
            
            try {
                // Safely evaluate the formula
                calculatedPrice = eval(evalFormula);
                console.log(`Calculated price: ${calculatedPrice}`);
                
                // Set the unit price
                const priceInput = row.querySelector('.item-price');
                if (priceInput) {
                    priceInput.value = calculatedPrice.toFixed(2);
                    // Trigger change event to recalculate totals
                    priceInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
            } catch (e) {
                console.error('Error evaluating formula:', e);
            }
        }
    });
}

// Set base_url for JavaScript
window.base_url = '{{ base_url }}';

// Debug function to check form state
window.debugInvoiceForm = function() {
    console.log('=== INVOICE FORM DEBUG ===');
    const form = document.getElementById('invoiceForm');
    if (!form) {
        console.error('Form not found!');
        return;
    }
    
    // Check all required fields
    const requiredFields = form.querySelectorAll('[required]');
    console.log(`Total required fields: ${requiredFields.length}`);
    
    const invalidFields = [];
    const validFields = [];
    
    requiredFields.forEach(field => {
        const info = {
            name: field.name || field.id,
            type: field.type,
            value: field.value,
            required: field.required,
            disabled: field.disabled,
            visible: field.offsetParent !== null,
            valid: field.checkValidity(),
            message: field.validationMessage
        };
        
        if (!field.checkValidity()) {
            invalidFields.push(info);
        } else {
            validFields.push(info);
        }
    });
    
    console.log('Valid fields:', validFields);
    console.log('Invalid fields:', invalidFields);
    
    // Check invoice items
    const items = document.querySelectorAll('.invoice-item');
    console.log(`Invoice items: ${items.length}`);
    
    // Check invoice type
    const invoiceType = document.getElementById('invoice_type_id');
    console.log('Invoice type:', invoiceType?.value, invoiceType?.options[invoiceType.selectedIndex]?.text);
    
    // Check billable
    const billable = document.getElementById('billable_id');
    console.log('Billable:', billable?.value, billable?.options[billable.selectedIndex]?.text);
    
    // Try manual validation
    console.log('Form validity:', form.checkValidity());
    
    return {
        requiredCount: requiredFields.length,
        invalidCount: invalidFields.length,
        itemCount: items.length,
        invoiceType: invoiceType?.value,
        billable: billable?.value,
        isValid: form.checkValidity()
    };
};

// Callback function to refresh billable options after creating new client/patient in popup
window.refreshBillableOptions = function() {
    loadBillableOptions();
};
</script>

<!-- Product Search Modal -->
<div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productSearchModalLabel">{{ __('invoices.search_product')|default('Search Product') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="productSearchInput" 
                           placeholder="{{ __('invoices.search_placeholder')|default('Search by name, code or description...') }}">
                </div>
                <div id="productSearchResults" class="list-group" style="max-height: 400px; overflow-y: auto;">
                    <!-- Search results will be displayed here -->
                </div>
                <div id="productSearchLoading" class="text-center py-3 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div id="productSearchEmpty" class="alert alert-info d-none">
                    {{ __('invoices.no_products_found')|default('No products found') }}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Product Search Functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.getElementById('searchProductBtn');
    const modal = new bootstrap.Modal(document.getElementById('productSearchModal'));
    const searchInput = document.getElementById('productSearchInput');
    const resultsContainer = document.getElementById('productSearchResults');
    const loadingDiv = document.getElementById('productSearchLoading');
    const emptyDiv = document.getElementById('productSearchEmpty');
    
    let searchTimeout;
    
    // Open modal on button click
    searchBtn.addEventListener('click', function() {
        modal.show();
        searchInput.focus();
    });
    
    // Search on input
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            resultsContainer.innerHTML = '';
            emptyDiv.classList.add('d-none');
            return;
        }
        
        searchTimeout = setTimeout(() => searchProducts(query), 300);
    });
    
    // Search products function
    function searchProducts(query) {
        loadingDiv.classList.remove('d-none');
        emptyDiv.classList.add('d-none');
        resultsContainer.innerHTML = '';
        
        fetch(`{{ base_url }}/api/products/search?term=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                loadingDiv.classList.add('d-none');
                
                if (!data.success || !data.items || data.items.length === 0) {
                    emptyDiv.classList.remove('d-none');
                    return;
                }
                
                data.items.forEach(product => {
                    const item = document.createElement('a');
                    item.href = '#';
                    item.className = 'list-group-item list-group-item-action';
                    item.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${product.name}</h6>
                                <small class="text-muted">${product.code || ''}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">${currency}${parseFloat(product.unit_price).toFixed(2)}</div>
                                <small class="text-muted">{{ __('invoices.vat') }}: ${product.vat_rate}%</small>
                            </div>
                        </div>
                    `;
                    
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        addProductToInvoice(product);
                        modal.hide();
                    });
                    
                    resultsContainer.appendChild(item);
                });
            })
            .catch(error => {
                console.error('Error searching products:', error);
                loadingDiv.classList.add('d-none');
                emptyDiv.classList.remove('d-none');
            });
    }
    
    // Add product to invoice
    function addProductToInvoice(product) {
        // Find VAT rate ID from the rate value
        const vatSelects = document.querySelectorAll('.item-vat');
        let vatRateId = '';
        
        if (vatSelects.length > 0) {
            const firstVatSelect = vatSelects[0];
            for (let option of firstVatSelect.options) {
                if (parseFloat(option.dataset.rate) === parseFloat(product.vat_rate)) {
                    vatRateId = option.value;
                    break;
                }
            }
        }
        
        // Add item with product data
        window.addItemWithData({
            description: product.name,
            quantity: '1',
            unit_price: product.unit_price,
            vat_rate_id: vatRateId,
            item_id: product.id,
            code: product.code
        });
    }
});
</script>

<!-- Include fix scripts -->
<!-- Invoice number generation moved to server-side on save
<script src="{{ base_url }}/js/invoice-create-fix.js"></script> -->

<!-- Enhanced Product Selection -->
<script src="{{ base_url }}/js/invoice-product-selection.js"></script>
<!-- Commented out as it overrides the main loadBillableOptions function -->
<!-- <script src="{{ base_url }}/js/invoice-billable-fix.js"></script> -->
<script src="{{ base_url }}/js/invoice-create-complete-fix.js"></script>
<script src="{{ base_url }}/js/invoice-save-fix.js"></script>
<script src="{{ base_url }}/js/invoice-error-handler.js"></script>
<script src="{{ base_url }}/js/invoice-form-fixes.js"></script>

<!-- Email Confirmation Modal -->
<div class="modal fade" id="emailConfirmModal" tabindex="-1" aria-labelledby="emailConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailConfirmModalLabel">
                    <i class="bi bi-envelope me-2"></i>{{ __('invoices.confirm_send_email')|default('Confirm Email Send') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <span id="emailConfirmMessage">{{ __('invoices.email_will_be_sent_to')|default('The invoice will be sent by email to:') }}</span>
                    <strong id="recipientEmail"></strong>
                </div>
                
                <div class="invoice-summary mt-3">
                    <table class="table table-sm">
                        <tr>
                            <td>{{ __('invoices.invoice_number') }}:</td>
                            <td><strong id="summaryInvoiceNumber"></strong></td>
                        </tr>
                        <tr>
                            <td>{{ __('invoices.recipient') }}:</td>
                            <td><strong id="summaryRecipient"></strong></td>
                        </tr>
                        <tr>
                            <td>{{ __('invoices.total') }}:</td>
                            <td><strong id="summaryTotal"></strong></td>
                        </tr>
                    </table>
                </div>
                
                <p class="mt-3 mb-0">{{ __('invoices.choose_action')|default('Choose an action:') }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="saveWithoutEmailBtn">
                    <i class="bi bi-save me-2"></i>{{ __('invoices.save_without_email')|default('Save without sending email') }}
                </button>
                <button type="button" class="btn btn-success" id="saveAndEmailBtn">
                    <i class="bi bi-send me-2"></i>{{ __('invoices.save_and_send_email')|default('Save and send email') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden field for email preference -->
<input type="hidden" id="sendEmailPreference" name="send_email" value="" form="invoiceForm">

{% endblock %}

{% block scripts %}
<!-- Product Live Search -->
<link rel="stylesheet" href="{{ base_url }}/css/product-live-search.css">
<link rel="stylesheet" href="{{ base_url }}/css/product-inline-creation.css">
<link rel="stylesheet" href="{{ base_url }}/css/inline-invoice-editor.css">
<script src="{{ base_url }}/js/product-live-search.js"></script>
<script src="{{ base_url }}/js/inline-invoice-editor.js"></script>

<!-- Ensure invoice product selection is available globally -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make sure the invoiceProductSelection is available for live search integration
    if (typeof InvoiceProductSelection !== 'undefined' && !window.invoiceProductSelection) {
        window.invoiceProductSelection = new InvoiceProductSelection();
    }
    
    // Ensure product live search is initialized
    setTimeout(function() {
        if (typeof initProductLiveSearch === 'function') {
            console.log('Manually initializing product live search');
            initProductLiveSearch();
        }
    }, 500);
});
</script>
{% endblock %}