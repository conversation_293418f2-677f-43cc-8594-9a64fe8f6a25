<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

try {
    $db = Flight::db();
    
    echo "Checking for invoice 278 or FAC-DIV-2025-0190...\n\n";
    
    // Check invoices table
    $stmt = $db->prepare("SELECT id, invoice_number, status, created_at, updated_at, deleted_at FROM invoices WHERE id = 278 OR invoice_number = 'FAC-DIV-2025-0190'");
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($invoices)) {
        echo "Found in invoices table:\n";
        foreach ($invoices as $invoice) {
            echo "ID: {$invoice['id']}, Number: {$invoice['invoice_number']}, Status: {$invoice['status']}\n";
            echo "Created: {$invoice['created_at']}, Updated: {$invoice['updated_at']}, Deleted: {$invoice['deleted_at']}\n\n";
        }
    } else {
        echo "No invoice found with ID 278 or number FAC-DIV-2025-0190\n\n";
    }
    
    // Check last few invoices
    echo "Last 5 invoices created:\n";
    $stmt = $db->query("SELECT id, invoice_number, status, created_at, deleted_at FROM invoices ORDER BY id DESC LIMIT 5");
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($recent as $inv) {
        echo "ID: {$inv['id']}, Number: {$inv['invoice_number']}, Status: {$inv['status']}, Created: {$inv['created_at']}";
        if ($inv['deleted_at']) {
            echo ", DELETED: {$inv['deleted_at']}";
        }
        echo "\n";
    }
    
    // Check for any errors in the last hour
    echo "\n\nChecking for recent errors in logs...\n";
    $logFile = __DIR__ . '/storage/logs/app.log';
    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        $lines = explode("\n", $logs);
        $recentErrors = [];
        $oneHourAgo = date('Y-m-d H:i', strtotime('-1 hour'));
        
        foreach ($lines as $line) {
            if (strpos($line, 'ERROR') !== false && strpos($line, '2025-07-19') !== false) {
                if (preg_match('/\[(\d{4}-\d{2}-\d{2}T\d{2}:\d{2})/', $line, $matches)) {
                    $logTime = str_replace('T', ' ', substr($matches[1], 0, 16));
                    if ($logTime >= $oneHourAgo) {
                        $recentErrors[] = $line;
                    }
                }
            }
        }
        
        if (!empty($recentErrors)) {
            echo "Recent errors found:\n";
            foreach ($recentErrors as $error) {
                echo substr($error, 0, 200) . "...\n";
            }
        } else {
            echo "No recent errors in app.log\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}