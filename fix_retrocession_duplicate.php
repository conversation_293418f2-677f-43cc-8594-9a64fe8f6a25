<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fix Retrocession Data Duplicate Entry</h2>\n";
    
    // First, find <PERSON>'s practitioner ID
    echo "<h3>1. Finding <PERSON>'s Practitioner ID:</h3>\n";
    $stmt = $pdo->prepare("
        SELECT u.id, u.first_name, u.last_name, c.id as client_id, c.name
        FROM users u
        JOIN clients c ON (c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name)
        WHERE u.id = 1
    ");
    $stmt->execute();
    $user = $stmt->fetch();
    
    echo "User: {$user['first_name']} {$user['last_name']} (ID: {$user['id']})\n";
    echo "Client/Practitioner: {$user['name']} (ID: {$user['client_id']})\n";
    
    // Check existing retrocession_data_entry records
    echo "\n<h3>2. Existing retrocession_data_entry records:</h3>\n";
    $stmt = $pdo->prepare("
        SELECT r.*, i.invoice_number, i.status as invoice_status
        FROM retrocession_data_entry r
        LEFT JOIN invoices i ON r.invoice_id = i.id
        WHERE r.practitioner_id = :practitioner_id 
        AND r.period_year = 2025
        ORDER BY r.period_month DESC
    ");
    $stmt->execute(['practitioner_id' => $user['client_id']]);
    
    $entries = $stmt->fetchAll();
    if (count($entries) > 0) {
        echo "<table border='1'>\n";
        echo "<tr><th>ID</th><th>Month</th><th>Year</th><th>CNS</th><th>Patient</th><th>Status</th><th>Invoice ID</th><th>Invoice #</th><th>Action</th></tr>\n";
        
        foreach ($entries as $entry) {
            echo "<tr>";
            echo "<td>{$entry['id']}</td>";
            echo "<td>{$entry['period_month']}</td>";
            echo "<td>{$entry['period_year']}</td>";
            echo "<td>{$entry['cns_amount']}€</td>";
            echo "<td>{$entry['patient_amount']}€</td>";
            echo "<td>{$entry['status']}</td>";
            echo "<td>{$entry['invoice_id']}</td>";
            echo "<td>" . ($entry['invoice_number'] ?: '-') . "</td>";
            echo "<td>";
            
            // If there's a July 2025 entry without a valid invoice
            if ($entry['period_month'] == 7 && $entry['period_year'] == 2025) {
                if (!$entry['invoice_number']) {
                    echo "<a href='?action=delete_entry&id={$entry['id']}'>Delete entry</a> | ";
                }
                echo "<a href='?action=reset_invoice&id={$entry['id']}'>Reset invoice link</a>";
            }
            
            echo "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Check for duplicate July 2025 entries
        $julyEntries = array_filter($entries, function($e) {
            return $e['period_month'] == 7 && $e['period_year'] == 2025;
        });
        
        if (count($julyEntries) > 0) {
            echo "\n<p style='color: orange;'>⚠️ Found " . count($julyEntries) . " entry(ies) for July 2025. This is blocking new invoice generation.</p>\n";
        }
    } else {
        echo "<p>No retrocession entries found for this practitioner.</p>\n";
    }
    
    // Check user_generated_invoices
    echo "\n<h3>3. User Generated Invoices tracking:</h3>\n";
    $stmt = $pdo->prepare("
        SELECT ugi.*, i.invoice_number
        FROM user_generated_invoices ugi
        LEFT JOIN invoices i ON ugi.invoice_id = i.id
        WHERE ugi.user_id = 1 
        AND ugi.period_month = 7 
        AND ugi.period_year = 2025
        AND ugi.invoice_type = 'RET'
    ");
    $stmt->execute();
    
    $ugiEntries = $stmt->fetchAll();
    if (count($ugiEntries) > 0) {
        echo "<table border='1'>\n";
        echo "<tr><th>ID</th><th>Invoice ID</th><th>Invoice Exists?</th><th>Action</th></tr>\n";
        
        foreach ($ugiEntries as $ugi) {
            echo "<tr>";
            echo "<td>{$ugi['id']}</td>";
            echo "<td>{$ugi['invoice_id']}</td>";
            echo "<td>" . ($ugi['invoice_number'] ? 'Yes' : '<span style="color:red">No</span>') . "</td>";
            echo "<td><a href='?action=delete_ugi&id={$ugi['id']}'>Delete</a></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Handle actions
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'delete_entry':
                $id = intval($_GET['id']);
                $pdo->exec("DELETE FROM retrocession_data_entry WHERE id = $id");
                echo "\n<p style='color: green;'>✓ Deleted retrocession_data_entry record</p>\n";
                break;
                
            case 'reset_invoice':
                $id = intval($_GET['id']);
                $pdo->exec("UPDATE retrocession_data_entry SET invoice_id = NULL, status = 'confirmed' WHERE id = $id");
                echo "\n<p style='color: green;'>✓ Reset invoice link</p>\n";
                break;
                
            case 'delete_ugi':
                $id = intval($_GET['id']);
                $pdo->exec("DELETE FROM user_generated_invoices WHERE id = $id");
                echo "\n<p style='color: green;'>✓ Deleted user_generated_invoices record</p>\n";
                break;
                
            case 'clean_all':
                // Delete July 2025 entries for Frank
                $pdo->exec("
                    DELETE FROM retrocession_data_entry 
                    WHERE practitioner_id = {$user['client_id']} 
                    AND period_month = 7 
                    AND period_year = 2025
                ");
                $pdo->exec("
                    DELETE FROM user_generated_invoices 
                    WHERE user_id = 1 
                    AND period_month = 7 
                    AND period_year = 2025 
                    AND invoice_type = 'RET'
                ");
                echo "\n<p style='color: green;'>✓ Cleaned all July 2025 entries</p>\n";
                break;
        }
        echo "<p><a href='fix_retrocession_duplicate.php'>Refresh</a></p>\n";
    }
    
    echo "\n<hr>\n";
    echo "<h3>Quick Actions:</h3>\n";
    echo "<p><a href='?action=clean_all' onclick='return confirm(\"This will delete all July 2025 retrocession entries for Frank Huet. Continue?\")'>Clean all July 2025 entries</a></p>\n";
    
    echo "\n<p><strong>After cleaning:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>The duplicate entries will be removed</li>\n";
    echo "<li>You can generate a new invoice</li>\n";
    echo "<li><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation'>Return to Bulk Generation</a></li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}