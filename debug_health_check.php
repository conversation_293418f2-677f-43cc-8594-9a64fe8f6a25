#!/usr/bin/env php
<?php

// Fit360 AdminDesk - Debug Health Check Script
// Run from command line: php debug_health_check.php
// Or access via browser: http://localhost/fit/debug_health_check.php

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Detect if running from CLI or web
$is_cli = (php_sapi_name() === 'cli');

// Output formatting
function output($message, $is_cli) {
    if ($is_cli) {
        echo $message . PHP_EOL;
    } else {
        echo nl2br(htmlspecialchars($message)) . "<br>\n";
    }
}

function section_header($title, $is_cli) {
    if ($is_cli) {
        echo "\n=== $title ===\n";
    } else {
        echo "<h3>$title</h3>\n";
    }
}

// Start output
if (!$is_cli) {
    echo "<!DOCTYPE html><html><head><title>Fit360 Debug Health Check</title>";
    echo "<style>body { font-family: monospace; padding: 20px; } h3 { color: #333; } .success { color: green; } .error { color: red; } .warning { color: orange; }</style>";
    echo "</head><body><h1>Fit360 AdminDesk - Debug Health Check</h1>";
}

output("Timestamp: " . date('Y-m-d H:i:s'), $is_cli);

// 1. System Information
section_header("System Information", $is_cli);
output("PHP Version: " . phpversion(), $is_cli);
output("Operating System: " . PHP_OS, $is_cli);
output("Server API: " . php_sapi_name(), $is_cli);
output("Working Directory: " . getcwd(), $is_cli);
output("Current User: " . get_current_user(), $is_cli);

// 2. PHP Configuration
section_header("PHP Configuration", $is_cli);
output("Memory Limit: " . ini_get('memory_limit'), $is_cli);
output("Max Execution Time: " . ini_get('max_execution_time') . "s", $is_cli);
output("Post Max Size: " . ini_get('post_max_size'), $is_cli);
output("Upload Max Filesize: " . ini_get('upload_max_filesize'), $is_cli);
output("Error Reporting: " . error_reporting(), $is_cli);
output("Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off'), $is_cli);

// 3. PHP Extensions Check
section_header("PHP Extensions", $is_cli);
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'session', 'filter', 'fileinfo', 'curl', 'openssl'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '<span class="success">✓ Loaded</span>' : '<span class="error">✗ Missing</span>';
    if ($is_cli) {
        $status = $loaded ? '✓ Loaded' : '✗ Missing';
    }
    output("$ext: $status", $is_cli);
}

// 4. Environment Configuration
section_header("Environment Configuration", $is_cli);
if (!file_exists('.env')) {
    output('<span class="warning">⚠️ .env file not found!</span>', $is_cli);
} else {
    output("✓ .env file found", $is_cli);
    
    // Load environment variables
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        try {
            $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
            $dotenv->load();
            
            $env_vars = [
                'DB_HOST' => 'DB_HOST',
                'DB_DATABASE' => 'DB_DATABASE',
                'DB_USERNAME' => 'DB_USERNAME',
                'APP_URL' => 'APP_URL',
                'APP_ENV' => 'APP_ENV',
                'APP_DEBUG' => 'APP_DEBUG',
                'MAIL_MAILER' => 'MAIL_MAILER'
            ];
            foreach ($env_vars as $display => $var) {
                if (isset($_ENV[$var]) && !empty($_ENV[$var])) {
                    if (in_array($var, ['DB_USERNAME', 'DB_PASSWORD', 'MAIL_PASSWORD'])) {
                        output("$display: ****** (set)", $is_cli);
                    } else {
                        output("$display: " . $_ENV[$var], $is_cli);
                    }
                } else {
                    output("<span class='warning'>$display: ⚠️ NOT SET</span>", $is_cli);
                }
            }
        } catch (Exception $e) {
            output("<span class='error'>Error loading .env: " . $e->getMessage() . "</span>", $is_cli);
        }
    } else {
        output("<span class='error'>⚠️ vendor/autoload.php not found - run composer install</span>", $is_cli);
    }
}

// 5. Database Connection Test
section_header("Database Connection Test", $is_cli);
if (isset($_ENV['DB_HOST']) && isset($_ENV['DB_DATABASE']) && isset($_ENV['DB_USERNAME'])) {
    try {
        $pdo = new PDO(
            'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
            $_ENV['DB_USERNAME'],
            $_ENV['DB_PASSWORD'] ?? '',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        output("<span class='success'>✓ Database connection successful</span>", $is_cli);
        output("Database: " . $_ENV['DB_DATABASE'], $is_cli);
        output("Host: " . $_ENV['DB_HOST'], $is_cli);
        
        // Check database size
        $stmt = $pdo->query("
            SELECT 
                table_schema AS 'Database',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
            FROM information_schema.tables 
            WHERE table_schema = '" . $_ENV['DB_DATABASE'] . "'
            GROUP BY table_schema
        ");
        $size = $stmt->fetch(PDO::FETCH_ASSOC);
        output("Database Size: " . ($size['Size (MB)'] ?? '0') . " MB", $is_cli);
        
        // Check key tables
        output("\nTable Record Counts:", $is_cli);
        $tables = ['users', 'invoices', 'clients', 'invoice_lines', 'retrocession_data_entry', 'translations', 'user_groups', 'config'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetchColumn();
                output(sprintf("  %-25s: %d records", $table, $count), $is_cli);
            } catch (Exception $e) {
                output(sprintf("  %-25s: <span class='warning'>⚠️ Table not found</span>", $table), $is_cli);
            }
        }
        
        // Check for recent errors in logs
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM logs WHERE level = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
            $error_count = $stmt->fetchColumn();
            if ($error_count > 0) {
                output("\n<span class='warning'>⚠️ Found $error_count errors in logs (last 24h)</span>", $is_cli);
            }
        } catch (Exception $e) {
            // Logs table may not exist - this is not critical
        }
        
    } catch (Exception $e) {
        output("<span class='error'>✗ Database connection failed: " . $e->getMessage() . "</span>", $is_cli);
    }
} else {
    output("<span class='error'>✗ Database configuration missing in .env</span>", $is_cli);
}

// 6. File Permissions Check
section_header("Directory Permissions", $is_cli);
$directories = [
    'storage/logs',
    'storage/cache',
    'storage/cache/twig',
    'storage/uploads',
    'public/uploads',
    'public/assets'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir);
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $status = $writable ? '<span class="success">✓ Writable</span>' : '<span class="error">✗ NOT writable</span>';
        if ($is_cli) {
            $status = $writable ? '✓ Writable' : '✗ NOT writable';
        }
        output("$dir - $perms - $status", $is_cli);
    } else {
        output("<span class='warning'>$dir - Directory does not exist</span>", $is_cli);
    }
}

// 7. Cache System Check
section_header("Cache System", $is_cli);
$cache_dir = 'storage/cache/twig';
if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '/*');
    $count = count($files);
    $size = 0;
    foreach ($files as $file) {
        if (is_file($file)) {
            $size += filesize($file);
        }
    }
    $size_mb = round($size / 1024 / 1024, 2);
    output("Twig Cache: $count files, $size_mb MB", $is_cli);
    
    // Check if cache is writable
    $test_file = $cache_dir . '/test_' . time() . '.tmp';
    if (@file_put_contents($test_file, 'test') !== false) {
        unlink($test_file);
        output("Cache Write Test: <span class='success'>✓ Passed</span>", $is_cli);
    } else {
        output("Cache Write Test: <span class='error'>✗ Failed</span>", $is_cli);
    }
} else {
    output("<span class='warning'>⚠️ Twig cache directory not found</span>", $is_cli);
}

// 8. Session Check
section_header("Session Configuration", $is_cli);
output("Session Save Path: " . session_save_path(), $is_cli);
output("Session Save Handler: " . ini_get('session.save_handler'), $is_cli);
output("Session Cookie Lifetime: " . ini_get('session.cookie_lifetime') . "s", $is_cli);
output("Session GC Maxlifetime: " . ini_get('session.gc_maxlifetime') . "s", $is_cli);

// 9. Email Configuration Check
section_header("Email Configuration", $is_cli);
if (isset($_ENV['MAIL_MAILER'])) {
    output("Mail Driver: " . $_ENV['MAIL_MAILER'], $is_cli);
    if ($_ENV['MAIL_MAILER'] === 'smtp') {
        output("SMTP Host: " . ($_ENV['MAIL_HOST'] ?? 'NOT SET'), $is_cli);
        output("SMTP Port: " . ($_ENV['MAIL_PORT'] ?? 'NOT SET'), $is_cli);
        output("SMTP Encryption: " . ($_ENV['MAIL_ENCRYPTION'] ?? 'none'), $is_cli);
    }
} else {
    output("<span class='warning'>⚠️ Email configuration not found</span>", $is_cli);
}

// 10. Recent Error Summary
if (isset($pdo)) {
    section_header("Recent Activity Summary (Last 24h)", $is_cli);
    try {
        // Recent logins
        $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE last_login_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $logins = $stmt->fetchColumn();
        output("User Logins: $logins", $is_cli);
        
        // Recent invoices
        $stmt = $pdo->query("SELECT COUNT(*) FROM invoices WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $invoices = $stmt->fetchColumn();
        output("Invoices Created: $invoices", $is_cli);
        
        // Recent retrocessions
        $stmt = $pdo->query("SELECT COUNT(*) FROM retrocession_data_entry WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $retrocessions = $stmt->fetchColumn();
        output("Retrocessions Created: $retrocessions", $is_cli);
        
    } catch (Exception $e) {
        output("<span class='warning'>Could not fetch activity summary</span>", $is_cli);
    }
}

// Summary
section_header("Health Check Summary", $is_cli);
output("✓ Health check completed at " . date('Y-m-d H:i:s'), $is_cli);
output("Review any warnings or errors above for potential issues.", $is_cli);

if (!$is_cli) {
    echo "</body></html>";
}