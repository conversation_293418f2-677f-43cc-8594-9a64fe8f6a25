<?php
/**
 * Setup Invoice Types for Unified Invoice Generation
 * 
 * Run this script to ensure all invoice types are properly configured
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connected successfully\n\n";
} catch (Exception $e) {
    die("✗ Database connection failed: " . $e->getMessage() . "\n");
}

echo "=== Setting up Invoice Types ===\n\n";

// 1. Check if config_invoice_types table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'config_invoice_types'");
if ($stmt->rowCount() == 0) {
    echo "✗ Table 'config_invoice_types' does not exist!\n";
    exit(1);
}

// 2. Check and create invoice types
$invoiceTypes = [
    [
        'prefix' => 'RET',
        'name' => json_encode([
            'fr' => 'Rétrocession',
            'en' => 'Retrocession',
            'de' => 'Rückzahlung'
        ]),
        'description' => 'Factures de rétrocession pour les praticiens'
    ],
    [
        'prefix' => 'LOY',
        'name' => json_encode([
            'fr' => 'Loyer',
            'en' => 'Rent',
            'de' => 'Miete'
        ]),
        'description' => 'Factures de loyer et charges'
    ],
    [
        'prefix' => 'LOC',
        'name' => json_encode([
            'fr' => 'Location',
            'en' => 'Rental',
            'de' => 'Vermietung'
        ]),
        'description' => 'Factures de location de salles/cours'
    ]
];

foreach ($invoiceTypes as $type) {
    // Check if type exists
    $stmt = $pdo->prepare("SELECT id FROM config_invoice_types WHERE prefix = :prefix");
    $stmt->execute(['prefix' => $type['prefix']]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "ℹ Invoice type '{$type['prefix']}' already exists (ID: {$existing['id']})\n";
    } else {
        // Create new invoice type
        $stmt = $pdo->prepare("
            INSERT INTO config_invoice_types (prefix, name, created_at, updated_at) 
            VALUES (:prefix, :name, NOW(), NOW())
        ");
        $stmt->execute([
            'prefix' => $type['prefix'],
            'name' => $type['name']
        ]);
        $id = $pdo->lastInsertId();
        $displayName = json_decode($type['name'], true)['fr'] ?? 'Unknown';
        echo "✓ Created invoice type '{$type['prefix']}' - $displayName (ID: $id)\n";
    }
}

// 3. Create invoice_types view if it doesn't exist
echo "\n=== Creating invoice_types view ===\n";
try {
    $pdo->exec("DROP VIEW IF EXISTS invoice_types");
    $pdo->exec("
        CREATE VIEW invoice_types AS
        SELECT 
            id,
            prefix as code,
            name,
            1 as is_active,
            created_at,
            updated_at
        FROM config_invoice_types
    ");
    echo "✓ Created invoice_types view\n";
} catch (Exception $e) {
    echo "ℹ Could not create view: " . $e->getMessage() . "\n";
}

// 4. Add configuration entries if needed
echo "\n=== Setting up configuration entries ===\n";
$configs = [
    ['key' => 'retrocession_invoice_type', 'value' => 'RET', 'category' => 'invoicing'],
    ['key' => 'loyer_invoice_type', 'value' => 'LOY', 'category' => 'invoicing'],
    ['key' => 'location_invoice_type', 'value' => 'LOC', 'category' => 'invoicing']
];

foreach ($configs as $config) {
    $stmt = $pdo->prepare("SELECT id FROM config WHERE `key` = :key");
    $stmt->execute(['key' => $config['key']]);
    
    if ($stmt->fetch()) {
        echo "ℹ Config '{$config['key']}' already exists\n";
    } else {
        $stmt = $pdo->prepare("
            INSERT INTO config (`key`, value, category) 
            VALUES (:key, :value, :category)
        ");
        $stmt->execute($config);
        echo "✓ Created config '{$config['key']}' = '{$config['value']}'\n";
    }
}

// 5. Show current invoice types
echo "\n=== Current Invoice Types ===\n";
$stmt = $pdo->query("SELECT * FROM config_invoice_types ORDER BY id");
$types = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "\nID | Prefix | Name\n";
echo "---|--------|------------------\n";
foreach ($types as $type) {
    $names = json_decode($type['name'], true);
    $displayName = is_array($names) ? ($names['fr'] ?? $names['en'] ?? 'N/A') : $type['name'];
    printf("%-2d | %-6s | %s\n", $type['id'], $type['prefix'], $displayName);
}

// 6. Check required tables
echo "\n=== Checking Required Tables ===\n";
$requiredTables = [
    'user_generated_invoices' => 'Tracks all auto-generated invoices',
    'user_monthly_retrocession_amounts' => 'Monthly retrocession data',
    'user_financial_obligations' => 'Financial obligations for Loyer',
    'user_monthly_course_counts' => 'Course counts for Location',
    'user_retrocession_settings' => 'Retrocession calculation settings',
    'user_courses' => 'Course definitions'
];

foreach ($requiredTables as $table => $description) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
    if ($stmt->rowCount() > 0) {
        echo "✓ $table - $description\n";
    } else {
        echo "✗ $table - MISSING! $description\n";
    }
}

// 7. Run migration 103 if needed
$stmt = $pdo->query("SHOW TABLES LIKE 'user_generated_invoices'");
if ($stmt->rowCount() == 0) {
    echo "\n=== Running Migration 103 ===\n";
    echo "The user_generated_invoices table is missing.\n";
    echo "Please run: php run_migration_103.php\n";
}

echo "\n=== Setup Complete ===\n";
echo "\nYou can now:\n";
echo "1. Use the web test interface: http://localhost/fit/public/test-invoice-simple.php\n";
echo "2. Use the CLI test script: php test_unified_invoice_generation.php --debug\n";
echo "3. Use the bulk generation page: http://localhost/fit/public/invoices/bulk-generation\n";