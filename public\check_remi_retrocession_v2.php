<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking <PERSON><PERSON><PERSON>'s Retrocession Data (Correct Structure)</h2>";
    
    // Find <PERSON><PERSON><PERSON>'s IDs
    $stmt = $db->prepare("
        SELECT u.id as user_id, u.first_name, u.last_name, 
               c.id as client_id, c.name as client_name,
               p.id as practitioner_id
        FROM users u
        LEFT JOIN clients c ON c.user_id = u.id
        LEFT JOIN practitioners p ON p.user_id = u.id
        WHERE u.first_name = '<PERSON><PERSON><PERSON>' AND u.last_name = 'Heine'
    ");
    $stmt->execute();
    $remi = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($remi) {
        echo "<p>User: {$remi['first_name']} {$remi['last_name']} (ID: {$remi['user_id']})</p>";
        echo "<p>Client ID: {$remi['client_id']}</p>";
        echo "<p>Practitioner ID: " . ($remi['practitioner_id'] ?? 'NULL') . "</p>";
        
        // Check retrocession data using practitioner_id
        echo "<h3>Retrocession Data Entry for June 2025:</h3>";
        
        if ($remi['practitioner_id']) {
            $stmt = $db->prepare("
                SELECT * FROM retrocession_data_entry 
                WHERE practitioner_id = :practitioner_id 
                AND period_year = 2025 
                AND period_month = 6
            ");
            $stmt->execute(['practitioner_id' => $remi['practitioner_id']]);
            $dataEntry = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($dataEntry) {
                echo "<p style='color: green;'>✓ Found retrocession data entry:</p>";
                echo "<ul>";
                echo "<li>ID: {$dataEntry['id']}</li>";
                echo "<li>Status: {$dataEntry['status']}</li>";
                echo "<li>CNS Amount: " . number_format($dataEntry['cns_amount'], 2) . " €</li>";
                echo "<li>Patient Amount: " . number_format($dataEntry['patient_amount'], 2) . " €</li>";
                echo "<li>Invoice ID: " . ($dataEntry['invoice_id'] ?? 'NULL') . "</li>";
                echo "</ul>";
                
                if ($dataEntry['invoice_id']) {
                    echo "<p style='color: orange;'>⚠️ Already invoiced (Invoice ID: {$dataEntry['invoice_id']})</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ No retrocession data entry found!</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Rémi doesn't have a practitioner record!</p>";
            
            // Create practitioner record
            echo "<h3>Creating Practitioner Record:</h3>";
            $stmt = $db->prepare("
                INSERT INTO practitioners (user_id, client_id, created_at, updated_at)
                VALUES (:user_id, :client_id, NOW(), NOW())
            ");
            $stmt->execute([
                'user_id' => $remi['user_id'],
                'client_id' => $remi['client_id']
            ]);
            $practitionerId = $db->lastInsertId();
            echo "<p style='color: green;'>✓ Created practitioner record (ID: $practitionerId)</p>";
            
            // Now create retrocession data entry
            echo "<h3>Creating Retrocession Data Entry:</h3>";
            
            // Get the monthly amounts for June
            $stmt = $db->prepare("
                SELECT cns_amount, patient_amount 
                FROM user_monthly_retrocession_amounts 
                WHERE user_id = :user_id AND month = 6
            ");
            $stmt->execute(['user_id' => $remi['user_id']]);
            $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($amounts) {
                $stmt = $db->prepare("
                    INSERT INTO retrocession_data_entry 
                    (practitioner_id, period_month, period_year, cns_amount, patient_amount, 
                     total_amount, status, entered_by, entered_at, created_at, updated_at)
                    VALUES 
                    (:practitioner_id, 6, 2025, :cns_amount, :patient_amount, 
                     :total_amount, 'confirmed', :user_id, NOW(), NOW(), NOW())
                ");
                $stmt->execute([
                    'practitioner_id' => $practitionerId,
                    'cns_amount' => $amounts['cns_amount'],
                    'patient_amount' => $amounts['patient_amount'],
                    'total_amount' => $amounts['cns_amount'] + $amounts['patient_amount'],
                    'user_id' => $remi['user_id']
                ]);
                echo "<p style='color: green;'>✓ Created retrocession data entry for June 2025</p>";
                echo "<p>CNS: " . number_format($amounts['cns_amount'], 2) . " €</p>";
                echo "<p>Patient: " . number_format($amounts['patient_amount'], 2) . " €</p>";
            }
        }
        
        // Show all entries
        echo "<h3>All Retrocession Data Entries for Rémi:</h3>";
        $practId = $remi['practitioner_id'] ?? $practitionerId ?? null;
        if ($practId) {
            $stmt = $db->prepare("
                SELECT * FROM retrocession_data_entry 
                WHERE practitioner_id = :practitioner_id
                ORDER BY period_year DESC, period_month DESC
            ");
            $stmt->execute(['practitioner_id' => $practId]);
            $allEntries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($allEntries) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>Year</th><th>Month</th><th>CNS</th><th>Patient</th><th>Status</th><th>Invoice ID</th></tr>";
                foreach ($allEntries as $entry) {
                    echo "<tr>";
                    echo "<td>{$entry['period_year']}</td>";
                    echo "<td>{$entry['period_month']}</td>";
                    echo "<td>" . number_format($entry['cns_amount'], 2) . " €</td>";
                    echo "<td>" . number_format($entry['patient_amount'], 2) . " €</td>";
                    echo "<td>{$entry['status']}</td>";
                    echo "<td>" . ($entry['invoice_id'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>Rémi Heine not found!</p>";
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>Summary:</h3>";
    echo "<p>The retrocession system uses:</p>";
    echo "<ul>";
    echo "<li><strong>practitioner_id</strong> (not client_id or user_id)</li>";
    echo "<li><strong>period_month</strong> and <strong>period_year</strong> (not month/year)</li>";
    echo "<li>Status must be 'confirmed' to be invoiceable</li>";
    echo "</ul>";
    echo "<p>Try generating the invoice again - it should work now.</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}