<?php
/**
 * Comprehensive App Analysis Script
 * Analyzes the Fit360 AdminDesk application state
 */

require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== Fit360 AdminDesk Analysis ===\n";
echo "Generated: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'],
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    
    // 1. Database Overview
    echo "1. DATABASE OVERVIEW\n";
    echo str_repeat('-', 50) . "\n";
    
    $tables = [
        'users' => 'System Users',
        'clients' => 'Clients/Practitioners', 
        'invoices' => 'Invoices',
        'invoice_lines' => 'Invoice Line Items',
        'config_payment_terms' => 'Payment Terms',
        'config_invoice_types' => 'Invoice Types',
        'retrocession_data_entry' => 'Retrocession Data',
        'user_monthly_retrocession_amounts' => 'Monthly Amounts',
        'translations' => 'Translations'
    ];
    
    foreach ($tables as $table => $description) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            printf("%-35s: %5d records\n", $description, $count);
        } catch (Exception $e) {
            printf("%-35s: Table not found\n", $description);
        }
    }
    
    // 2. Invoice Analysis
    echo "\n2. INVOICE ANALYSIS\n";
    echo str_repeat('-', 50) . "\n";
    
    $stmt = $pdo->query("
        SELECT 
            status, 
            COUNT(*) as count,
            SUM(total) as total_amount
        FROM invoices 
        GROUP BY status
    ");
    
    echo "Invoice Status Summary:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        printf("%-10s: %3d invoices (%.2f €)\n", 
            $row['status'], 
            $row['count'], 
            $row['total_amount']
        );
    }
    
    // Invoice types usage
    echo "\nInvoice Types Usage:\n";
    $stmt = $pdo->query("
        SELECT 
            cit.code,
            cit.prefix,
            COUNT(i.id) as count
        FROM config_invoice_types cit
        LEFT JOIN invoices i ON i.type_id = cit.id
        GROUP BY cit.id
        ORDER BY count DESC
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        printf("%-15s (%-10s): %3d invoices\n", 
            $row['code'] ?? 'N/A', 
            $row['prefix'], 
            $row['count']
        );
    }
    
    // 3. User/Practitioner Analysis
    echo "\n3. USER/PRACTITIONER ANALYSIS\n";
    echo str_repeat('-', 50) . "\n";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(DISTINCT u.id) as total_users,
            COUNT(DISTINCT CASE WHEN c.is_practitioner = 1 THEN u.id END) as practitioners
        FROM users u
        LEFT JOIN clients c ON c.id = u.client_id
    ");
    
    $userStats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total Users: {$userStats['total_users']}\n";
    echo "Practitioners: {$userStats['practitioners']}\n";
    
    // Monthly amounts configured
    $stmt = $pdo->query("
        SELECT 
            COUNT(DISTINCT user_id) as users_with_amounts,
            COUNT(DISTINCT CONCAT(user_id, '-', month)) as configured_months,
            SUM(cns_amount + patient_amount) as total_monthly_amounts
        FROM user_monthly_retrocession_amounts
        WHERE cns_amount > 0 OR patient_amount > 0
    ");
    
    $monthlyStats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\nMonthly Retrocession Configuration:\n";
    echo "Users with amounts: {$monthlyStats['users_with_amounts']}\n";
    echo "Configured months: {$monthlyStats['configured_months']}\n";
    echo "Total monthly amounts: " . number_format($monthlyStats['total_monthly_amounts'], 2) . " €\n";
    
    // 4. Data Quality Check
    echo "\n4. DATA QUALITY CHECK\n";
    echo str_repeat('-', 50) . "\n";
    
    // Orphaned records
    $checks = [
        "Invoices without clients" => "SELECT COUNT(*) FROM invoices WHERE client_id NOT IN (SELECT id FROM clients)",
        "Invoice lines without invoices" => "SELECT COUNT(*) FROM invoice_lines WHERE invoice_id NOT IN (SELECT id FROM invoices)",
        "Users without clients" => "SELECT COUNT(*) FROM users WHERE client_id IS NOT NULL AND client_id NOT IN (SELECT id FROM clients)",
        "Invalid payment terms" => "SELECT COUNT(*) FROM invoices WHERE payment_term_id IS NOT NULL AND payment_term_id NOT IN (SELECT id FROM config_payment_terms)"
    ];
    
    foreach ($checks as $description => $query) {
        try {
            $count = $pdo->query($query)->fetchColumn();
            $status = $count == 0 ? '✓' : '⚠️';
            printf("%s %-35s: %d\n", $status, $description, $count);
        } catch (Exception $e) {
            printf("✗ %-35s: Error checking\n", $description);
        }
    }
    
    // 5. Performance Metrics
    echo "\n5. PERFORMANCE METRICS\n";
    echo str_repeat('-', 50) . "\n";
    
    // Database size
    $stmt = $pdo->query("
        SELECT 
            table_schema AS 'Database',
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size_MB'
        FROM information_schema.tables 
        WHERE table_schema = '" . $_ENV['DB_DATABASE'] . "'
        GROUP BY table_schema
    ");
    $dbSize = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Database Size: {$dbSize['Size_MB']} MB\n";
    
    // Largest tables
    echo "\nLargest Tables:\n";
    $stmt = $pdo->query("
        SELECT 
            table_name,
            table_rows,
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB'
        FROM information_schema.tables
        WHERE table_schema = '" . $_ENV['DB_DATABASE'] . "'
        ORDER BY (data_length + index_length) DESC
        LIMIT 5
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        printf("%-25s: %6d rows (%s MB)\n", 
            $row['table_name'], 
            $row['table_rows'], 
            $row['Size_MB']
        );
    }
    
    // 6. Recent Activity
    echo "\n6. RECENT ACTIVITY\n";
    echo str_repeat('-', 50) . "\n";
    
    // Recent invoices
    $stmt = $pdo->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as invoices_created
        FROM invoices
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ");
    
    echo "Invoices Created (Last 7 Days):\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        printf("%s: %d invoices\n", $row['date'], $row['invoices_created']);
    }
    
    // 7. Configuration Status
    echo "\n7. CONFIGURATION STATUS\n";
    echo str_repeat('-', 50) . "\n";
    
    $configs = [
        'app_name' => 'Application Name',
        'default_language' => 'Default Language',
        'theme' => 'UI Theme',
        'invoice_prefix' => 'Invoice Prefix',
        'vat_rate' => 'VAT Rate'
    ];
    
    foreach ($configs as $key => $description) {
        $stmt = $pdo->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        $value = $stmt->fetchColumn();
        printf("%-20s: %s\n", $description, $value ?: 'Not configured');
    }
    
    echo "\n=== Analysis Complete ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}