# Changelog

All notable changes to Fit360 AdminDesk will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.3.5] - 2025-07-20

### Fixed
- **Bulk Loyer Invoice Generation**
  - Fixed invoice line items not displaying due to table mismatch (invoice_items vs invoice_lines)
  - Fixed invoice number prefix missing (now shows FAC-LOC-2025-XXXX correctly)
  - Fixed double-counting of secretary amounts (was showing 2,840€ instead of 1,670€)
  - Fixed "Object of class Invoice could not be converted to string" error
  - Fixed "Undefined array key" errors for subtotal, name, and other fields
  - Fixed generated column error for vat_amount field
  
### Changed
- **Invoice Generation Logic**
  - Now uses invoice_lines table instead of invoice_items for consistency
  - Improved handling of secretary amounts (uses EITHER TVAC OR HTVA+TVA, not both)
  - Added proper type_id support for invoice number generation
  - Enhanced error handling and database context management

## [2.3.4] - 2025-07-20

### Added
- **Retrocession Monthly Configuration System**
  - 12-month grid interface in user profiles for CNS and patient amounts
  - Visual indicators showing which months have generated invoices
  - One-click invoice generation from user profile
  - Automatic population of retrocession data from monthly configuration
  
- **Invoice Regeneration Capability**
  - Delete and regenerate retrocession invoices while preserving data
  - Automatic status reset when deleted invoice is detected
  - Proper handling of foreign key constraints
  
- **Enhanced Permission Controls**
  - Manager/Admin only delete functionality for invoices
  - Role-based visibility of delete buttons
  - Separate permissions for draft vs sent invoice deletion
  
- **User-Specific Retrocession Settings**
  - Custom percentages per user (CNS, Patient, Secretary)
  - Custom labels for invoice lines
  - Effective date management for settings
  
- **Database Enhancements**
  - New `user_monthly_retrocession_amounts` table
  - Migration script 090_create_user_monthly_retrocession_amounts.sql
  - Proper `.env` usage in migration scripts

### Changed
- **Invoice Deletion Behavior**
  - Retrocession data entries now preserved (invoice_id set to NULL)
  - Email logs properly deleted on invoice removal
  - Cascade deletion for all related records
  
- **Error Handling**
  - Clean JSON responses with output buffer management
  - Better error messages for common issues
  - Enhanced logging throughout retrocession workflow
  
- **UI/UX Improvements**
  - Generate buttons disabled for already invoiced months
  - Green background and checkmarks for generated invoices
  - Previous month logic (generates June invoice in July)
  - Integrated retrocession settings in user edit form

### Fixed
- **Invoice Regeneration Issues**
  - "Une facture existe déjà pour cette période" error after deletion
  - "No retrocession data found" when entry exists
  - Foreign key constraint violations on deletion
  
- **Technical Fixes**
  - PDO namespace issues (`\PDO::FETCH_ASSOC`)
  - JSON parse errors from PHP output
  - Transaction handling in nested operations
  - Proper status updates for retrocession entries

### Technical Details
- **Modified Files**:
  - `/app/controllers/UserController.php` - Added generateRetrocession method
  - `/app/models/Invoice.php` - Enhanced delete method
  - `/app/views/users/form-modern.twig` - Added monthly configuration UI
  - `/app/services/RetrocessionCalculator.php` - User settings integration
  - `/app/views/invoices/show-modern.twig` - Delete functionality
  - `/app/views/invoices/index-modern.twig` - Permission-based buttons

## [2.3.3] - 2025-07-19

### Added
- Email system enhancements for invoice sending
- PDF attachments to invoice emails
- Period information in email subjects (e.g., "Facture (JUIN 2025)")

### Fixed
- Invoice email sending functionality
- CSRF protection for form submissions
- Method not allowed errors in invoice list views
- PDF generation consistency between email and download

### Changed
- Email now uses same PDF generation as download function
- Updated to use invoice-pdf.php for all PDF generation

## [2.3.2] - 2025-07-14

### Added
- Invoice date management enhancements
- Custom issue date support in invoice creation
- Automatic due date calculation based on payment terms

### Fixed
- Issue date handling in invoice forms
- Due date calculation (1 month = 31 days)

## [2.3.1] - 2025-01-29

### Changed
- Simplified permission system from dual role/group to groups-only
- Removed 200+ temporary test files
- Prepared codebase for GitHub deployment

### Fixed
- Missing French translations for user management
- Last login tracking (last_login → last_login_at)

## [2.3.0] - 2025-01-20

### Added
- Mobile responsiveness implementation
- Touch-optimized forms (44px minimum targets)
- Responsive table solutions
- Swipe gestures for navigation
- Pull-to-refresh functionality
- Bottom navigation bar
- Floating action buttons
- Context7 MCP server integration