name: Translation Sync
description: Synchronizes translations across language files and database, finds missing translations
version: 1.0.0
author: Claude-Flow

inputs:
  - name: languages
    type: array
    description: Languages to check (default checks all)
    required: false
    default: ["fr", "en", "de"]
    
  - name: fix_missing
    type: boolean
    description: Automatically add missing translations with placeholders
    required: false
    default: false

env:
  lang_dir: "app/lang"
  database_url: "${DATABASE_URL}"

steps:
  - name: Scan Language Files
    type: shell
    command: |
      php -r "
      \$langDir = '${env.lang_dir}';
      \$languages = json_decode('{{ json_encode(inputs.languages) }}', true);
      \$allKeys = [];
      \$langData = [];
      
      // Scan all language files
      foreach (\$languages as \$lang) {
          \$langPath = \$langDir . '/' . \$lang;
          if (!is_dir(\$langPath)) {
              echo \"Warning: Language directory \$langPath not found\\n\";
              continue;
          }
          
          \$langData[\$lang] = [];
          \$files = glob(\$langPath . '/*.php');
          
          foreach (\$files as \$file) {
              \$filename = basename(\$file, '.php');
              \$translations = include \$file;
              
              if (is_array(\$translations)) {
                  \$langData[\$lang][\$filename] = \$translations;
                  
                  // Collect all keys
                  foreach (\$translations as \$key => \$value) {
                      \$fullKey = \$filename . '.' . \$key;
                      \$allKeys[\$fullKey] = true;
                  }
              }
          }
      }
      
      echo json_encode([
          'languages' => array_keys(\$langData),
          'total_keys' => count(\$allKeys),
          'all_keys' => array_keys(\$allKeys),
          'lang_data' => \$langData
      ]);
      "
    output: scan_result

  - name: Check Database Translations
    type: shell
    command: |
      php -r "
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      \$pdo = new PDO(
          'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
          \$_ENV['DB_USER'],
          \$_ENV['DB_PASS']
      );
      
      // Get all database translations
      \$stmt = \$pdo->query('SELECT key_name, translations FROM translations');
      \$dbTranslations = [];
      
      while (\$row = \$stmt->fetch(PDO::FETCH_ASSOC)) {
          \$dbTranslations[\$row['key_name']] = json_decode(\$row['translations'], true);
      }
      
      echo json_encode([
          'db_count' => count(\$dbTranslations),
          'db_translations' => \$dbTranslations
      ]);
      "
    output: db_translations

  - name: Find Missing Translations
    type: shell
    command: |
      php -r "
      \$scanResult = json_decode('{{ json_encode(scan_result) }}', true);
      \$languages = \$scanResult['languages'];
      \$allKeys = \$scanResult['all_keys'];
      \$langData = \$scanResult['lang_data'];
      
      \$missing = [];
      \$report = [];
      
      // Check each language for missing keys
      foreach (\$languages as \$lang) {
          \$missing[\$lang] = [];
          
          foreach (\$allKeys as \$fullKey) {
              list(\$file, \$key) = explode('.', \$fullKey, 2);
              
              if (!isset(\$langData[\$lang][\$file][\$key])) {
                  \$missing[\$lang][] = \$fullKey;
              }
          }
          
          \$report[] = sprintf('Language %s: %d missing translations', \$lang, count(\$missing[\$lang]));
      }
      
      echo \"\\n=== Translation Analysis Report ===\\n\";
      echo \"Total unique translation keys: \" . count(\$allKeys) . \"\\n\";
      echo \"Languages checked: \" . implode(', ', \$languages) . \"\\n\\n\";
      
      foreach (\$report as \$line) {
          echo \$line . \"\\n\";
      }
      
      // Show details of missing translations
      foreach (\$missing as \$lang => \$keys) {
          if (count(\$keys) > 0) {
              echo \"\\nMissing translations for \$lang:\\n\";
              foreach (array_slice(\$keys, 0, 10) as \$key) {
                  echo \"  - \$key\\n\";
              }
              if (count(\$keys) > 10) {
                  echo \"  ... and \" . (count(\$keys) - 10) . \" more\\n\";
              }
          }
      }
      
      echo json_encode(['missing' => \$missing]);
      "
    output: missing_translations

  - name: Fix Missing Translations
    type: conditional
    condition: "{{ inputs.fix_missing }}"
    steps:
      - name: Add Missing Keys
        type: shell
        command: |
          php -r "
          \$missing = json_decode('{{ json_encode(missing_translations.missing) }}', true);
          \$langDir = '${env.lang_dir}';
          \$fixed = 0;
          
          foreach (\$missing as \$lang => \$keys) {
              foreach (\$keys as \$fullKey) {
                  list(\$file, \$key) = explode('.', \$fullKey, 2);
                  \$filePath = \$langDir . '/' . \$lang . '/' . \$file . '.php';
                  
                  if (file_exists(\$filePath)) {
                      \$translations = include \$filePath;
                      
                      // Add missing key with placeholder
                      \$translations[\$key] = \"[TODO: Translate '\$key' to \$lang]\";
                      
                      // Write back to file
                      \$content = \"<?php\\n\\nreturn \" . var_export(\$translations, true) . \";\\n\";
                      file_put_contents(\$filePath, \$content);
                      \$fixed++;
                  }
              }
          }
          
          echo \"\\nFixed \$fixed missing translations with placeholders.\\n\";
          echo \"Remember to replace the [TODO: ...] placeholders with actual translations!\\n\";
          "

  - name: Generate Translation Coverage Report
    type: shell
    command: |
      php -r "
      \$scanResult = json_decode('{{ json_encode(scan_result) }}', true);
      \$dbResult = json_decode('{{ json_encode(db_translations) }}', true);
      
      echo \"\\n=== Translation Coverage Summary ===\\n\";
      echo \"File-based translations: \" . \$scanResult['total_keys'] . \" keys\\n\";
      echo \"Database translations: \" . \$dbResult['db_count'] . \" keys\\n\";
      
      // Calculate coverage percentage
      foreach (\$scanResult['languages'] as \$lang) {
          \$total = \$scanResult['total_keys'];
          \$missing = count(json_decode('{{ json_encode(missing_translations.missing) }}', true)[\$lang] ?? []);
          \$coverage = \$total > 0 ? round(((\$total - \$missing) / \$total) * 100, 2) : 0;
          
          echo \"\\n\$lang coverage: \$coverage%\\n\";
      }
      
      echo \"\\nWorkflow completed successfully!\\n\";
      "

on_error:
  - name: Log Error
    type: shell
    command: |
      echo "Translation sync error: {{ error_message }}" >> storage/logs/translation-errors.log

on_success:
  - name: Log Success
    type: shell
    command: |
      echo "Translation sync completed at $(date)" >> storage/logs/translation-success.log