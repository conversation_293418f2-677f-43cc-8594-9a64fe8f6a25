<?php
/**
 * Test PDF attachment using PHPMailer
 * This is a more reliable way to send emails with attachments
 */

use <PERSON><PERSON>Mail<PERSON>\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require_once __DIR__ . '/vendor/autoload.php';

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
            continue;
        }
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Create a test PDF
$pdf = new TCPDF();
$pdf->SetCreator('Fit360 AdminDesk');
$pdf->SetTitle('Test Invoice');
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
$pdf->AddPage();
$pdf->SetFont('helvetica', 'B', 16);
$pdf->Cell(0, 10, 'INVOICE TEST-001', 0, 1, 'C');
$pdf->SetFont('helvetica', '', 12);
$pdf->Ln(10);
$pdf->Cell(0, 10, 'Client: Test Client', 0, 1);
$pdf->Cell(0, 10, 'Date: ' . date('d/m/Y'), 0, 1);
$pdf->Cell(0, 10, 'Amount: €1,170.00', 0, 1);
$pdf->Ln(10);
$pdf->MultiCell(0, 10, 'This is a test invoice with PDF attachment sent via PHPMailer.', 0, 'L');
$pdfContent = $pdf->Output('', 'S');

// Save PDF to temp file
$pdfFile = __DIR__ . '/temp_invoice.pdf';
file_put_contents($pdfFile, $pdfContent);

?>
<!DOCTYPE html>
<html>
<head>
    <title>PHPMailer PDF Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>PHPMailer PDF Attachment Test</h1>
    
    <?php
    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = $_ENV['MAIL_HOST'] ?? 'localhost';
        $mail->Port       = $_ENV['MAIL_PORT'] ?? 1025;
        $mail->SMTPAuth   = false;  // Mailhog doesn't need authentication
        $mail->SMTPSecure = false;  // Disable encryption for Mailhog
        $mail->SMTPAutoTLS = false;
        
        // Recipients
        $mail->setFrom($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>', 'Fit360 AdminDesk');
        $mail->addAddress('<EMAIL>', 'Test Client');
        
        // Attachments
        $mail->addAttachment($pdfFile, 'Invoice-TEST-001.pdf');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Invoice TEST-001 - PHPMailer Test';
        $mail->Body    = '<h2>Invoice from Fit360</h2>
                         <p>Dear Client,</p>
                         <p>Please find attached your invoice TEST-001.</p>
                         <table style="border: 1px solid #ddd; padding: 10px;">
                             <tr><td><strong>Invoice Number:</strong></td><td>TEST-001</td></tr>
                             <tr><td><strong>Amount:</strong></td><td style="color: green;">€1,170.00</td></tr>
                             <tr><td><strong>Due Date:</strong></td><td>' . date('d/m/Y', strtotime('+30 days')) . '</td></tr>
                         </table>
                         <p>Best regards,<br>Fit360 Team</p>';
        $mail->AltBody = "Dear Client,\n\nPlease find attached your invoice TEST-001.\n\nInvoice Number: TEST-001\nAmount: €1,170.00\nDue Date: " . date('d/m/Y', strtotime('+30 days')) . "\n\nBest regards,\nFit360 Team";

        $mail->send();
        echo '<div class="success">';
        echo '<h3>✓ Email sent successfully with PHPMailer!</h3>';
        echo '<p>Check Mailhog at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a></p>';
        echo '<p>The email should have a PDF attachment that you can download.</p>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<h3>✗ Email could not be sent</h3>';
        echo '<p>Error: ' . $mail->ErrorInfo . '</p>';
        echo '<p>Exception: ' . $e->getMessage() . '</p>';
        echo '</div>';
        
        echo '<div class="info">';
        echo '<h4>Troubleshooting:</h4>';
        echo '<ol>';
        echo '<li>Make sure Mailhog is running (C:\\wamp64\\mailhog\\start_mailhog.bat)</li>';
        echo '<li>Check if Mailhog is accessible at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a></li>';
        echo '<li>Verify SMTP settings in .env file</li>';
        echo '</ol>';
        echo '</div>';
    }
    
    // Clean up temp file
    if (file_exists($pdfFile)) {
        unlink($pdfFile);
    }
    ?>
    
    <div class="info">
        <h3>Why use PHPMailer?</h3>
        <ul>
            <li>Better handling of MIME types and attachments</li>
            <li>More reliable than PHP's mail() function</li>
            <li>Proper SMTP support</li>
            <li>Better error handling and debugging</li>
        </ul>
        
        <h3>To integrate PHPMailer in EmailService:</h3>
        <p>If this test works, we should update the EmailService to use PHPMailer instead of mail().</p>
    </div>
    
    <div class="info">
        <h3>Current Configuration:</h3>
        <pre>
SMTP Host: <?php echo $_ENV['MAIL_HOST'] ?? 'localhost'; ?>

SMTP Port: <?php echo $_ENV['MAIL_PORT'] ?? '1025'; ?>

From: <?php echo $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>'; ?>
</pre>
    </div>
</body>
</html>