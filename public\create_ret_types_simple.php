<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Creating RET25 and RET30 Types</h2>";
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // First, delete any existing ret2/ret3 entries
        echo "<h3>Cleaning up old entries:</h3>";
        $stmt = $db->prepare("DELETE FROM config_invoice_types WHERE code IN ('ret2', 'ret3')");
        $stmt->execute();
        echo "<p>Deleted " . $stmt->rowCount() . " old entries</p>";
        
        // Create ret25
        echo "<h3>Creating new types:</h3>";
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
            VALUES ('ret25', 'FAC-RET25', :name, '#28a745', 1)
        ");
        $stmt->execute([':name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%'])]);
        echo "<p style='color: green;'>✓ Created ret25 (FAC-RET25)</p>";
        
        // Create ret30
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
            VALUES ('ret30', 'FAC-RET30', :name, '#007bff', 1)
        ");
        $stmt->execute([':name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%'])]);
        echo "<p style='color: green;'>✓ Created ret30 (FAC-RET30)</p>";
        
        // Commit
        $db->commit();
        echo "<p style='color: green;'>✓ Transaction committed successfully</p>";
        
        // Show final result
        echo "<h3>Final Configuration:</h3>";
        $stmt = $db->query("
            SELECT id, code, prefix, name 
            FROM config_invoice_types 
            WHERE code IN ('ret', 'ret25', 'ret30')
            ORDER BY code
        ");
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
        foreach ($types as $row) {
            $name = json_decode($row['name'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
            
            $rowStyle = '';
            if ($row['code'] == 'ret25') {
                $rowStyle = 'background-color: #d4edda;';
            } elseif ($row['code'] == 'ret30') {
                $rowStyle = 'background-color: #cce5ff;';
            }
            
            echo "<tr style='$rowStyle'>";
            echo "<td>{$row['id']}</td>";
            echo "<td><strong>{$row['code']}</strong></td>";
            echo "<td><strong>{$row['prefix']}</strong></td>";
            echo "<td>{$displayName}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>RET25 and RET30 types have been created successfully.</p>";
        echo "<p>You can now:</p>";
        echo "<ul>";
        echo "<li><a href='/fit/public/fix_frank_invoice_simple.php' style='color: #155724; font-weight: bold;'>Fix Frank's invoice to use RET25</a></li>";
        echo "<li><a href='/fit/public/invoices' style='color: #155724;'>Return to invoice list</a></li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Transaction rolled back</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}