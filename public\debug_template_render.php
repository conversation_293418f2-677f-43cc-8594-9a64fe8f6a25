<?php
/**
 * Debug Template Rendering for Invoice 279
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\EmailService;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Template Rendering</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .step { border-left: 4px solid #007bff; padding-left: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Debug Template Rendering for Invoice 279</h1>
    
    <?php
    try {
        $invoiceId = 279;
        $invoice = new Invoice();
        
        // Step 1: Get invoice data
        echo '<div class="step">';
        echo '<h2>Step 1: Get Invoice Data</h2>';
        
        $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
        
        if ($invoiceData) {
            echo '<div class="success">✅ Invoice data loaded</div>';
            
            // Check for potential issues
            echo '<h3>Key Data Points:</h3>';
            echo '<ul>';
            echo '<li>Invoice Number: ' . htmlspecialchars($invoiceData['invoice_number']) . '</li>';
            echo '<li>Issue Date: ' . htmlspecialchars($invoiceData['issue_date']) . '</li>';
            echo '<li>Due Date: ' . htmlspecialchars($invoiceData['due_date']) . '</li>';
            echo '<li>Total: ' . htmlspecialchars($invoiceData['total']) . '</li>';
            echo '<li>Period: ' . htmlspecialchars($invoiceData['period'] ?? 'NOT SET') . '</li>';
            echo '<li>Subject: ' . htmlspecialchars($invoiceData['subject'] ?? 'NOT SET') . '</li>';
            echo '<li>Has lines: ' . (isset($invoiceData['lines']) ? count($invoiceData['lines']) : 0) . '</li>';
            echo '</ul>';
            
            // Check user data
            if (isset($invoiceData['user'])) {
                echo '<h4>User Data:</h4>';
                echo '<ul>';
                echo '<li>Name: ' . htmlspecialchars($invoiceData['user']['full_name'] ?? 'Not set') . '</li>';
                echo '<li>Email: ' . htmlspecialchars($invoiceData['user']['email'] ?? 'Not set') . '</li>';
                echo '<li>Invoice Email: ' . htmlspecialchars($invoiceData['user']['invoice_email'] ?? 'Not set') . '</li>';
                echo '</ul>';
            }
        } else {
            echo '<div class="error">❌ Failed to load invoice data</div>';
            exit;
        }
        echo '</div>';
        
        // Step 2: Test template variable preparation
        echo '<div class="step">';
        echo '<h2>Step 2: Test Template Variable Preparation</h2>';
        
        // Try to manually prepare variables like EmailService does
        try {
            $variables = [];
            
            // Invoice variables
            $variables['INVOICE_NUMBER'] = $invoiceData['invoice_number'];
            $variables['ISSUE_DATE'] = date('d/m/Y', strtotime($invoiceData['issue_date']));
            $variables['DUE_DATE'] = date('d/m/Y', strtotime($invoiceData['due_date']));
            $variables['TOTAL_AMOUNT'] = number_format($invoiceData['total'], 2, ',', ' ');
            $variables['PAYMENT_TERMS'] = $invoiceData['payment_terms'] ?? '';
            $variables['SUBJECT'] = $invoiceData['subject'] ?? '';
            $variables['PERIOD'] = $invoiceData['period'] ?? '';
            
            // Client/User variables
            if (isset($invoiceData['user'])) {
                $variables['CLIENT_NAME'] = $invoiceData['user']['full_name'] ?? 
                                           $invoiceData['user']['name'] ?? 
                                           ($invoiceData['user']['first_name'] . ' ' . $invoiceData['user']['last_name']);
            }
            
            // Period variables for monthly invoice
            if (preg_match('/(\w+)\s+(\d{4})/i', $invoiceData['period'] ?? '', $matches)) {
                $variables['MONTH_NAME'] = $matches[1];
                $variables['YEAR'] = $matches[2];
            } else {
                $variables['MONTH_NAME'] = 'Unknown';
                $variables['YEAR'] = date('Y');
            }
            
            // Additional variables for rental template
            $variables['RENT_AMOUNT'] = '0,00';
            $variables['CHARGES_AMOUNT'] = '0,00';
            $variables['VAT_AMOUNT'] = number_format($invoiceData['vat_amount'] ?? 0, 2, ',', ' ');
            
            // Company variables
            $variables['COMPANY_NAME'] = 'Fit360 AdminDesk';
            
            echo '<div class="success">✅ Variables prepared successfully</div>';
            echo '<h3>Template Variables:</h3>';
            echo '<pre>' . print_r($variables, true) . '</pre>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error preparing variables: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';
        
        // Step 3: Test with reflection to find exact error
        echo '<div class="step">';
        echo '<h2>Step 3: Deep Debug EmailService</h2>';
        
        try {
            $emailService = new EmailService();
            $reflection = new ReflectionClass($emailService);
            
            // Test selectInvoiceTemplate
            $selectMethod = $reflection->getMethod('selectInvoiceTemplate');
            $selectMethod->setAccessible(true);
            
            echo '<h3>Testing selectInvoiceTemplate...</h3>';
            $template = $selectMethod->invoke($emailService, $invoiceData);
            
            if ($template) {
                echo '<div class="success">✅ Template selected: ' . htmlspecialchars($template['name']) . '</div>';
            } else {
                echo '<div class="error">❌ No template found</div>';
            }
            
            // Test renderTemplate
            if ($template) {
                $renderMethod = $reflection->getMethod('renderTemplate');
                $renderMethod->setAccessible(true);
                
                echo '<h3>Testing renderTemplate...</h3>';
                try {
                    $rendered = $renderMethod->invoke($emailService, $template, $invoiceData);
                    echo '<div class="success">✅ Template rendered successfully</div>';
                    echo '<div class="info">';
                    echo '<h4>Rendered Subject:</h4>';
                    echo '<p>' . htmlspecialchars($rendered['subject']) . '</p>';
                    echo '<h4>Rendered Body (first 200 chars):</h4>';
                    echo '<pre>' . htmlspecialchars(substr($rendered['body_text'], 0, 200)) . '...</pre>';
                    echo '</div>';
                } catch (Exception $e) {
                    echo '<div class="error">❌ renderTemplate error: ' . $e->getMessage() . '</div>';
                    echo '<pre>' . $e->getTraceAsString() . '</pre>';
                }
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Deep debug error: ' . $e->getMessage() . '</div>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
        }
        echo '</div>';
        
        // Step 4: Check for SQL errors in other methods
        echo '<div class="step">';
        echo '<h2>Step 4: Check Other SQL Queries</h2>';
        
        $db = Flight::db();
        
        // Check getConfig method
        echo '<h3>Testing getConfig SQL...</h3>';
        try {
            $stmt = $db->prepare("SELECT value FROM config WHERE `key` = ?");
            $stmt->execute(['company_name']);
            $value = $stmt->fetch(PDO::FETCH_COLUMN);
            echo '<div class="success">✅ getConfig SQL works - company_name: ' . ($value ?: 'not set') . '</div>';
        } catch (PDOException $e) {
            echo '<div class="error">❌ getConfig SQL error: ' . $e->getMessage() . '</div>';
        }
        
        // Check isFirstInvoice method
        echo '<h3>Testing isFirstInvoice SQL...</h3>';
        try {
            $stmt = $db->prepare("
                SELECT COUNT(*) as count
                FROM invoices
                WHERE (client_id = :entity_id OR user_id = :entity_id)
                AND status != 'draft'
            ");
            $stmt->execute([':entity_id' => 1]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo '<div class="success">✅ isFirstInvoice SQL works - count: ' . $result['count'] . '</div>';
        } catch (PDOException $e) {
            echo '<div class="error">❌ isFirstInvoice SQL error: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<h3>Error:</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
        echo '</div>';
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/test_email_simple.php">Simple Email Test</a> |
        <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Email Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>