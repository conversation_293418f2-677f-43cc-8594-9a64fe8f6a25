<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

use Flight;

$db = Flight::db();

echo "<h2>Email Templates Check</h2>";

// Check if email_templates table exists
try {
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✓ email_templates table exists</p>";
        
        // Count templates
        $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Total templates: " . $result['count'] . "</p>";
        
        if ($result['count'] == 0) {
            echo "<h3>❌ NO EMAIL TEMPLATES FOUND!</h3>";
            echo "<p>This is why emails are not being sent. Creating default templates...</p>";
            
            // Create default email templates
            $templates = [
                [
                    'name' => 'Nouvelle facture - Standard',
                    'code' => 'new_invoice_standard',
                    'email_type' => 'new_invoice',
                    'invoice_type' => null,
                    'subject' => 'Facture {INVOICE_NUMBER} - {COMPANY_NAME}',
                    'body_html' => '<p>Bonjour {CLIENT_NAME},</p>
<p>Veuillez trouver ci-joint votre facture <strong>{INVOICE_NUMBER}</strong> d\'un montant de <strong>{TOTAL_AMOUNT} €</strong>.</p>
<p>Date d\'échéance : <strong>{DUE_DATE}</strong></p>
<p>Cordialement,<br>{COMPANY_NAME}</p>',
                    'body_text' => 'Bonjour {CLIENT_NAME},

Veuillez trouver ci-joint votre facture {INVOICE_NUMBER} d\'un montant de {TOTAL_AMOUNT} €.

Date d\'échéance : {DUE_DATE}

Cordialement,
{COMPANY_NAME}',
                    'is_active' => 1,
                    'priority' => 100
                ],
                [
                    'name' => 'Nouvelle facture - Location',
                    'code' => 'new_invoice_rental',
                    'email_type' => 'new_invoice',
                    'invoice_type' => 'rental',
                    'subject' => 'Facture {INVOICE_NUMBER} - Loyer {MONTH_NAME} {YEAR}',
                    'body_html' => '<p>Bonjour {CLIENT_NAME},</p>
<p>Veuillez trouver ci-joint votre facture de loyer pour <strong>{MONTH_NAME} {YEAR}</strong>.</p>
<p>Montant : <strong>{TOTAL_AMOUNT} €</strong><br>
Numéro de facture : <strong>{INVOICE_NUMBER}</strong><br>
Date d\'échéance : <strong>{DUE_DATE}</strong></p>
<p>Cordialement,<br>{COMPANY_NAME}</p>',
                    'body_text' => 'Bonjour {CLIENT_NAME},

Veuillez trouver ci-joint votre facture de loyer pour {MONTH_NAME} {YEAR}.

Montant : {TOTAL_AMOUNT} €
Numéro de facture : {INVOICE_NUMBER}
Date d\'échéance : {DUE_DATE}

Cordialement,
{COMPANY_NAME}',
                    'is_active' => 1,
                    'priority' => 110
                ],
                [
                    'name' => 'Nouvelle facture - Rétrocession',
                    'code' => 'new_invoice_retrocession',
                    'email_type' => 'new_invoice',
                    'invoice_type' => 'retrocession_30',
                    'subject' => 'Facture de rétrocession {INVOICE_NUMBER} - {MONTH_NAME} {YEAR}',
                    'body_html' => '<p>Bonjour {PRACTITIONER_NAME},</p>
<p>Veuillez trouver ci-joint votre facture de rétrocession pour le mois de <strong>{MONTH_NAME} {YEAR}</strong>.</p>
<p>Détails :<br>
- Montant CNS : {CNS_AMOUNT} €<br>
- Montant Patients : {PATIENT_AMOUNT} €<br>
- Frais de secrétariat TVAC : {SECRETARIAT_AMOUNT} €<br>
- <strong>Total : {TOTAL_AMOUNT} €</strong></p>
<p>Date d\'échéance : <strong>{DUE_DATE}</strong></p>
<p>Cordialement,<br>{COMPANY_NAME}</p>',
                    'body_text' => 'Bonjour {PRACTITIONER_NAME},

Veuillez trouver ci-joint votre facture de rétrocession pour le mois de {MONTH_NAME} {YEAR}.

Détails :
- Montant CNS : {CNS_AMOUNT} €
- Montant Patients : {PATIENT_AMOUNT} €
- Frais de secrétariat TVAC : {SECRETARIAT_AMOUNT} €
- Total : {TOTAL_AMOUNT} €

Date d\'échéance : {DUE_DATE}

Cordialement,
{COMPANY_NAME}',
                    'is_active' => 1,
                    'priority' => 120
                ],
                [
                    'name' => 'Rappel de paiement',
                    'code' => 'reminder_1',
                    'email_type' => 'reminder',
                    'invoice_type' => null,
                    'subject' => 'Rappel - Facture {INVOICE_NUMBER} en attente',
                    'body_html' => '<p>Bonjour {CLIENT_NAME},</p>
<p>Nous vous rappelons que la facture <strong>{INVOICE_NUMBER}</strong> d\'un montant de <strong>{TOTAL_AMOUNT} €</strong> est en attente de paiement.</p>
<p>Cette facture était due le <strong>{DUE_DATE}</strong> (retard de {DAYS_OVERDUE} jours).</p>
<p>Nous vous remercions de bien vouloir régulariser cette situation dans les meilleurs délais.</p>
<p>Cordialement,<br>{COMPANY_NAME}</p>',
                    'body_text' => 'Bonjour {CLIENT_NAME},

Nous vous rappelons que la facture {INVOICE_NUMBER} d\'un montant de {TOTAL_AMOUNT} € est en attente de paiement.

Cette facture était due le {DUE_DATE} (retard de {DAYS_OVERDUE} jours).

Nous vous remercions de bien vouloir régulariser cette situation dans les meilleurs délais.

Cordialement,
{COMPANY_NAME}',
                    'is_active' => 1,
                    'priority' => 100
                ]
            ];
            
            // Insert templates
            $insertStmt = $db->prepare("
                INSERT INTO email_templates (
                    name, code, email_type, invoice_type,
                    subject, body_html, body_text,
                    is_active, priority, created_at
                ) VALUES (
                    :name, :code, :email_type, :invoice_type,
                    :subject, :body_html, :body_text,
                    :is_active, :priority, NOW()
                )
            ");
            
            $inserted = 0;
            foreach ($templates as $template) {
                try {
                    $insertStmt->execute($template);
                    $inserted++;
                } catch (Exception $e) {
                    echo "<p>Error inserting template '{$template['name']}': " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p>✓ Created $inserted default email templates</p>";
            
        } else {
            // List active templates
            $stmt = $db->query("SELECT id, name, code, email_type, invoice_type, is_active FROM email_templates WHERE is_active = 1");
            $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($templates) > 0) {
                echo "<h3>Active templates:</h3><ul>";
                foreach ($templates as $template) {
                    echo "<li>ID: {$template['id']}, Name: {$template['name']}, Code: {$template['code']}, Type: {$template['email_type']}, Invoice Type: " . ($template['invoice_type'] ?: 'All') . "</li>";
                }
                echo "</ul>";
            } else {
                echo "<p>❌ No active templates found!</p>";
            }
        }
        
    } else {
        echo "<p>❌ email_templates table does NOT exist!</p>";
        echo "<p>Creating email_templates table...</p>";
        
        // Create the table
        $createTableSQL = "
CREATE TABLE IF NOT EXISTS `email_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `code` varchar(50) NOT NULL,
    `email_type` varchar(50) NOT NULL,
    `invoice_type` varchar(50) DEFAULT NULL,
    `subject` varchar(255) NOT NULL,
    `body_html` text NOT NULL,
    `body_text` text NOT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `priority` int(11) DEFAULT 100,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_email_templates_code` (`code`),
    KEY `idx_email_templates_type` (`email_type`),
    KEY `idx_email_templates_invoice_type` (`invoice_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ";
        
        try {
            $db->exec($createTableSQL);
            echo "<p>✓ Created email_templates table</p>";
            echo "<p>Please refresh this page to add default templates.</p>";
        } catch (Exception $e) {
            echo "<p>Error creating table: " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Email Logs Table Check</h3>";
try {
    $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✓ email_logs table exists</p>";
        
        // Recent logs
        $stmt = $db->query("SELECT * FROM email_logs ORDER BY created_at DESC LIMIT 5");
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($logs) > 0) {
            echo "<h4>Recent email logs:</h4><ul>";
            foreach ($logs as $log) {
                echo "<li>Invoice: {$log['invoice_id']}, To: {$log['recipient_email']}, Status: {$log['status']}, Date: {$log['created_at']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No email logs found</p>";
        }
    } else {
        echo "<p>❌ email_logs table does NOT exist</p>";
        echo "<p>Creating email_logs table...</p>";
        
        $createLogsTable = "
CREATE TABLE IF NOT EXISTS `email_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `invoice_id` int(11) DEFAULT NULL,
    `template_id` int(11) DEFAULT NULL,
    `recipient_email` varchar(255) NOT NULL,
    `subject` varchar(255) DEFAULT NULL,
    `status` enum('sent','failed') NOT NULL,
    `sent_at` timestamp NULL DEFAULT NULL,
    `error_message` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_email_logs_invoice` (`invoice_id`),
    KEY `idx_email_logs_template` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ";
        
        try {
            $db->exec($createLogsTable);
            echo "<p>✓ Created email_logs table</p>";
        } catch (Exception $e) {
            echo "<p>Error creating table: " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/fit/public/invoices/create?type=rental'>Back to Create Invoice</a></p>";