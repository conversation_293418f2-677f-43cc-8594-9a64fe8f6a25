<?php
/**
 * Test Email Service with PDF Attachments
 * 
 * This script tests sending emails with PDF attachments using Mailhog.
 * Make sure Mailhog is running before executing this script.
 * 
 * Usage: php test_pdf_attachment_email.php
 * 
 * View sent emails at: http://localhost:8025
 */

// Load environment variables
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Services\EmailService;
use App\Services\PdfService;
use App\Models\Invoice;

// Initialize services
$emailService = new EmailService();
$pdfService = new PdfService();
$invoiceModel = new Invoice();

echo "PDF Attachment Email Test\n";
echo "========================\n\n";

// Test 1: Create a simple test PDF
echo "Test 1: Creating test PDF...\n";

// Create test invoice data
$testInvoiceData = [
    'id' => 999,
    'invoice_number' => 'TEST-PDF-001',
    'invoice_type' => 'invoice',
    'client_id' => 1,
    'issue_date' => date('Y-m-d'),
    'due_date' => date('Y-m-d', strtotime('+30 days')),
    'status' => 'draft',
    'subtotal' => 1000.00,
    'vat_amount' => 170.00,
    'total' => 1170.00,
    'payment_terms' => '30 days',
    'client' => [
        'id' => 1,
        'name' => 'Test Client',
        'email' => '<EMAIL>',
        'address' => '123 Test Street',
        'city' => 'Luxembourg',
        'postal_code' => 'L-1234',
        'country' => 'Luxembourg'
    ],
    'items' => [
        [
            'description' => 'Test Service',
            'quantity' => 1,
            'unit_price' => 1000.00,
            'vat_rate' => 17.00,
            'total' => 1000.00
        ]
    ],
    'company' => [
        'name' => 'Fit360 AdminDesk',
        'address' => '456 Business Ave',
        'city' => 'Luxembourg',
        'postal_code' => 'L-5678',
        'country' => 'Luxembourg',
        'email' => '<EMAIL>',
        'phone' => '+*********** 789'
    ]
];

try {
    // Generate PDF
    $pdfContent = $pdfService->generateInvoicePdf($testInvoiceData);
    echo "✓ PDF generated successfully (" . strlen($pdfContent) . " bytes)\n\n";
    
    // Test 2: Send email with PDF attachment
    echo "Test 2: Sending email with PDF attachment...\n";
    
    // Prepare email data
    $emailData = [
        'to' => '<EMAIL>',
        'subject' => 'Invoice ' . $testInvoiceData['invoice_number'] . ' - PDF Attachment Test',
        'body_text' => "Dear Test Client,\n\nPlease find attached your invoice " . $testInvoiceData['invoice_number'] . ".\n\nAmount: €" . number_format($testInvoiceData['total'], 2) . "\nDue Date: " . date('d/m/Y', strtotime($testInvoiceData['due_date'])) . "\n\nBest regards,\nFit360 Team",
        'body_html' => "<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .invoice-box { border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
        .amount { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <h2>Invoice from Fit360 AdminDesk</h2>
    <p>Dear Test Client,</p>
    <p>Please find attached your invoice.</p>
    <div class='invoice-box'>
        <p><strong>Invoice Number:</strong> " . $testInvoiceData['invoice_number'] . "</p>
        <p><strong>Amount:</strong> <span class='amount'>€" . number_format($testInvoiceData['total'], 2) . "</span></p>
        <p><strong>Due Date:</strong> " . date('d/m/Y', strtotime($testInvoiceData['due_date'])) . "</p>
    </div>
    <p>Best regards,<br>Fit360 Team</p>
</body>
</html>",
        'attachments' => [
            [
                'name' => $testInvoiceData['invoice_number'] . '.pdf',
                'content' => $pdfContent,
                'type' => 'application/pdf'
            ]
        ]
    ];
    
    // Send email using the private send method via reflection
    $reflection = new ReflectionClass($emailService);
    $sendMethod = $reflection->getMethod('send');
    $sendMethod->setAccessible(true);
    $result = $sendMethod->invoke($emailService, $emailData);
    
    if ($result['success']) {
        echo "✓ Email with PDF attachment sent successfully!\n";
        echo "  Message: " . $result['message'] . "\n\n";
    } else {
        echo "✗ Failed to send email: " . $result['message'] . "\n\n";
    }
    
    // Test 3: Send invoice email using the service method
    echo "Test 3: Testing sendInvoiceEmail method...\n";
    
    // Get a real invoice from the database
    $stmt = Flight::db()->prepare("SELECT id FROM invoices WHERE status != 'cancelled' ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $latestInvoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($latestInvoice) {
        $result = $emailService->sendInvoiceEmail($latestInvoice['id'], '<EMAIL>');
        if ($result['success']) {
            echo "✓ Invoice email sent successfully using sendInvoiceEmail method!\n";
            echo "  Message: " . $result['message'] . "\n\n";
        } else {
            echo "✗ Failed to send invoice email: " . $result['message'] . "\n\n";
        }
    } else {
        echo "! No invoices found in database for testing sendInvoiceEmail method\n\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "  File: " . $e->getFile() . "\n";
    echo "  Line: " . $e->getLine() . "\n\n";
}

echo "========================================\n";
echo "PDF attachment email tests completed!\n";
echo "Check Mailhog at: http://localhost:8025\n";
echo "========================================\n\n";

echo "Note: The emails should include PDF attachments.\n";
echo "In Mailhog, you should see:\n";
echo "- The email content (text and HTML versions)\n";
echo "- A downloadable PDF attachment\n";