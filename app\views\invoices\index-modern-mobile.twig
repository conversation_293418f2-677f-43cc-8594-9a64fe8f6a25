{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.invoices') }}{% endblock %}

{% block table_helper %}
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ base_url }}/css/table-helper.css">
<link rel="stylesheet" href="{{ base_url }}/css/table-dropdown-fix.css">
<link rel="stylesheet" href="{{ base_url }}/css/invoice-dropdown-override.css">
<style>
/* Dynamic search styles */
#search {
    padding-right: 2.5rem;
}

.table tr {
    transition: opacity 0.2s ease;
}

.table tr[style*="display: none"] {
    opacity: 0;
}

#search-results-count {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 0.875rem;
}

.highlight {
    background-color: #fff3cd;
    padding: 0 2px;
    border-radius: 2px;
}

/* TableHelper sort indicators styling */
.sort-indicator {
    font-size: 0.875rem;
}

th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
}

th[data-sortable="true"]:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Mobile-specific styles */
@media (max-width: 767px) {
    /* Stack action buttons vertically */
    .action-buttons-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .action-buttons-mobile .btn {
        width: 100%;
    }
    
    /* Hide less important columns on mobile */
    .hide-mobile {
        display: none !important;
    }
    
    /* Make table more compact on mobile */
    .table td, .table th {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    /* Optimize dropdown for mobile */
    .dropdown-menu {
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="page-header mb-4">
                <h1 class="page-title">
                    <i class="bi bi-file-text text-primary me-2"></i>{{ __('invoices.invoices') }}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.dashboard') }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ __('invoices.invoices') }}</li>
                    </ol>
                </nav>
            </div>

            <!-- Desktop action buttons -->
            <div class="action-buttons mb-4 desktop-only">
                <a href="{{ base_url }}/billing-wizard" class="btn btn-info">
                    <i class="bi bi-magic me-2"></i>{{ __('invoices.billing_wizard') }}
                </a>
                <a href="{{ base_url }}/invoices/generate-monthly" class="btn btn-warning">
                    <i class="bi bi-calendar-month me-2"></i>{{ __('invoices.generate_monthly') }}
                </a>
                <a href="{{ base_url }}/invoices/create" class="btn btn-primary" 
                   data-mobile-fab="true" 
                   data-mobile-fab-icon="<i class='fas fa-plus'></i>"
                   data-mobile-fab-label="{{ __('invoices.create_invoice') }}">
                    <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                </a>
            </div>

            <!-- Mobile action buttons -->
            <div class="action-buttons-mobile mobile-only">
                <a href="{{ base_url }}/invoices/create" class="btn btn-primary btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                </a>
                <a href="{{ base_url }}/invoices/generate-monthly" class="btn btn-warning">
                    <i class="bi bi-calendar-month me-2"></i>{{ __('invoices.generate_monthly') }}
                </a>
                <a href="{{ base_url }}/billing-wizard" class="btn btn-info">
                    <i class="bi bi-magic me-2"></i>{{ __('invoices.billing_wizard') }}
                </a>
            </div>

            <!-- Summary Cards - Mobile optimized -->
            <div class="row g-3 mb-4">
                <div class="col-6 col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-sm bg-primary bg-opacity-10">
                                        <i class="bi bi-file-text text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 text-truncate-mobile">{{ __('invoices.total_invoices') }}</p>
                                    <h4 class="mb-0">{{ total_invoices }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-sm bg-warning bg-opacity-10">
                                        <i class="bi bi-clock-history text-warning"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 text-truncate-mobile">{{ __('invoices.pending') }}</p>
                                    <h4 class="mb-0">{{ pending_count }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-sm bg-success bg-opacity-10">
                                        <i class="bi bi-check-circle text-success"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 text-truncate-mobile">{{ __('invoices.paid') }}</p>
                                    <h4 class="mb-0">{{ paid_count }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-sm bg-info bg-opacity-10">
                                        <i class="bi bi-currency-euro text-info"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 text-truncate-mobile">{{ __('invoices.total_amount') }}</p>
                                    <h4 class="mb-0">{{ formatMoney(total_amount) }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">{{ __('invoices.invoice_list') }}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-md-end gap-2 mt-3 mt-md-0">
                                <button type="button" class="btn btn-sm btn-outline-primary mobile-full-width" data-bs-toggle="collapse" data-bs-target="#filterSection">
                                    <i class="bi bi-funnel me-1"></i>{{ __('common.filters') }}
                                </button>
                                <a href="{{ base_url }}/invoices?reset_filters=1" class="btn btn-sm btn-secondary mobile-full-width" onclick="InvoiceFilters.clear();">
                                    <i class="bi bi-x-circle me-1"></i><span class="d-none d-sm-inline">{{ __('common.reset_filters') }}</span>
                                </a>
                                <div class="btn-group mobile-full-width">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-download me-1"></i><span class="d-none d-sm-inline">{{ __('common.export') }}</span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{{ base_url }}/invoices/export?format=csv"><i class="bi bi-file-earmark-csv me-2"></i>CSV</a></li>
                                        <li><a class="dropdown-item" href="{{ base_url }}/invoices/export?format=xlsx"><i class="bi bi-file-earmark-excel me-2"></i>Excel</a></li>
                                        <li><a class="dropdown-item" href="{{ base_url }}/invoices/export?format=pdf"><i class="bi bi-file-earmark-pdf me-2"></i>PDF</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Collapsible filter section -->
                    <div class="collapse mt-3" id="filterSection">
                        <!-- Filter form content here -->
                    </div>
                </div>
                
                <div class="card-body p-0">
                    <!-- Add mobile-responsive wrapper and mobile-card class for mobile view -->
                    <div class="table-mobile-responsive">
                        <table class="table table-hover mobile-cards" id="invoicesTable">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 50px;">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th data-sortable="true" data-field="number">{{ __('invoices.invoice_number') }}</th>
                                    <th data-sortable="true" data-field="client" class="hide-mobile">{{ __('invoices.client') }}</th>
                                    <th data-sortable="true" data-field="date">{{ __('invoices.date') }}</th>
                                    <th data-sortable="true" data-field="amount" class="text-end">{{ __('invoices.amount') }}</th>
                                    <th data-sortable="true" data-field="status">{{ __('invoices.status') }}</th>
                                    <th class="text-center hide-mobile">{{ __('invoices.sent') }}</th>
                                    <th class="text-end">{{ __('common.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                <tr data-invoice-id="{{ invoice.id }}">
                                    <td class="text-center" data-label="">
                                        <input type="checkbox" class="form-check-input invoice-checkbox" value="{{ invoice.id }}">
                                    </td>
                                    <td data-label="{{ __('invoices.invoice_number') }}">
                                        <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="text-decoration-none">
                                            {{ invoice.formatted_number|default(invoice.number) }}
                                        </a>
                                    </td>
                                    <td class="hide-mobile" data-label="{{ __('invoices.client') }}">
                                        {% if invoice.client %}
                                            <a href="{{ base_url }}/clients/{{ invoice.client.id }}" class="text-decoration-none">
                                                {{ invoice.client.name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td data-label="{{ __('invoices.date') }}">
                                        {{ formatDate(invoice.issue_date) }}
                                    </td>
                                    <td class="text-end" data-label="{{ __('invoices.amount') }}">
                                        {{ formatMoney(invoice.total_amount) }}
                                    </td>
                                    <td data-label="{{ __('invoices.status') }}">
                                        {% if invoice.status == 'draft' %}
                                            <span class="badge bg-secondary">{{ __('invoices.draft') }}</span>
                                        {% elseif invoice.status == 'sent' %}
                                            <span class="badge bg-info">{{ __('invoices.sent') }}</span>
                                        {% elseif invoice.status == 'paid' %}
                                            <span class="badge bg-success">{{ __('invoices.paid') }}</span>
                                        {% elseif invoice.status == 'partial' %}
                                            <span class="badge bg-warning">{{ __('invoices.partial') }}</span>
                                        {% elseif invoice.status == 'overdue' %}
                                            <span class="badge bg-danger">{{ __('invoices.overdue') }}</span>
                                        {% elseif invoice.status == 'cancelled' %}
                                            <span class="badge bg-dark">{{ __('invoices.cancelled') }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center hide-mobile" data-label="{{ __('invoices.sent') }}">
                                        {% if invoice.sent_at %}
                                            <i class="bi bi-check-circle text-success" title="{{ formatDateTime(invoice.sent_at) }}"></i>
                                        {% else %}
                                            <i class="bi bi-x-circle text-muted"></i>
                                        {% endif %}
                                    </td>
                                    <td class="text-end" data-label="{{ __('common.actions') }}">
                                        <!-- Mobile dropdown for actions -->
                                        <div class="dropdown d-inline-block d-md-none">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}">
                                                    <i class="bi bi-eye me-2"></i>{{ __('common.view') }}
                                                </a></li>
                                                {% if invoice.status == 'draft' %}
                                                <li><a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/edit">
                                                    <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                                                </a></li>
                                                {% endif %}
                                                <li><a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank">
                                                    <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/download">
                                                    <i class="bi bi-download me-2"></i>{{ __('invoices.download_pdf') }}
                                                </a></li>
                                                {% if invoice.status != 'draft' and invoice.status != 'cancelled' %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" onclick="sendInvoice({{ invoice.id }})">
                                                    <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_email') }}
                                                </a></li>
                                                {% endif %}
                                                {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                                                <li><a class="dropdown-item" href="#" onclick="recordPayment({{ invoice.id }})">
                                                    <i class="bi bi-cash-coin me-2"></i>{{ __('invoices.record_payment') }}
                                                </a></li>
                                                {% endif %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/duplicate">
                                                    <i class="bi bi-files me-2"></i>{{ __('common.duplicate') }}
                                                </a></li>
                                                {% if invoice.status == 'draft' %}
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteInvoice({{ invoice.id }}, '{{ invoice.status }}')">
                                                    <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                                                </a></li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                        
                                        <!-- Desktop button group -->
                                        <div class="btn-group btn-group-sm d-none d-md-inline-flex" role="group">
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" 
                                               class="btn btn-outline-primary" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('common.view') }}">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            {% if invoice.status == 'draft' %}
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}/edit" 
                                               class="btn btn-outline-secondary" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('common.edit') }}">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% endif %}
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}/print" 
                                               class="btn btn-outline-info" 
                                               target="_blank"
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('common.print') }}">
                                                <i class="bi bi-printer"></i>
                                            </a>
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}/download" 
                                               class="btn btn-outline-success" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('invoices.download_pdf') }}">
                                                <i class="bi bi-download"></i>
                                            </a>
                                            {% if invoice.status != 'draft' and invoice.status != 'cancelled' %}
                                                <button type="button" 
                                                    class="btn btn-outline-primary" 
                                                    onclick="sendInvoice({{ invoice.id }})"
                                                    data-bs-toggle="tooltip" 
                                                    title="{{ __('invoices.send_email') }}">
                                                    <i class="bi bi-envelope"></i>
                                                </button>
                                            {% endif %}
                                            {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                                                <button type="button" 
                                                    class="btn btn-outline-warning" 
                                                    onclick="recordPayment({{ invoice.id }})"
                                                    data-bs-toggle="tooltip" 
                                                    title="{{ __('invoices.record_payment') }}">
                                                    <i class="bi bi-cash-coin"></i>
                                                </button>
                                            {% endif %}
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}/duplicate" 
                                               class="btn btn-outline-info" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('common.duplicate')|default('Dupliquer') }}">
                                                <i class="bi bi-files"></i>
                                            </a>
                                            {% if invoice.status == 'draft' %}
                                                <button type="button" 
                                                    class="btn btn-outline-danger" 
                                                    onclick="deleteInvoice({{ invoice.id }}, '{{ invoice.status }}')"
                                                    data-bs-toggle="tooltip" 
                                                    title="{{ __('common.delete') }}">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if invoices is empty %}
                    <div class="text-center py-5">
                        <i class="bi bi-file-text display-1 text-muted"></i>
                        <p class="mt-3 text-muted">{{ __('invoices.no_invoices_found') }}</p>
                        <a href="{{ base_url }}/invoices/create" class="btn btn-primary mt-2">
                            <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_first_invoice') }}
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                {% if invoices is not empty %}
                <div class="card-footer bg-white">
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3">
                        <div class="dropdown">
                            <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" disabled id="bulkActionsBtn">
                                {{ __('common.bulk_actions') }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="bulkAction('export')">
                                    <i class="bi bi-download me-2"></i>{{ __('common.export_selected') }}
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="bulkAction('send')">
                                    <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_selected') }}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                                    <i class="bi bi-trash me-2"></i>{{ __('common.delete_selected') }}
                                </a></li>
                            </ul>
                        </div>
                        
                        <!-- Pagination -->
                        {% include '_partials/pagination.twig' with {
                            current_page: current_page,
                            total_pages: total_pages,
                            base_url: base_url ~ '/invoices'
                        } %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.record_payment') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="paymentForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">{{ __('invoices.amount') }}</label>
                        <input type="number" class="form-control" id="payment_amount" name="amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">{{ __('invoices.payment_date') }}</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="{{ 'now'|date('Y-m-d') }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">{{ __('invoices.payment_method') }}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">{{ __('common.select') }}</option>
                            <option value="bank_transfer">{{ __('invoices.bank_transfer') }}</option>
                            <option value="cash">{{ __('invoices.cash') }}</option>
                            <option value="check">{{ __('invoices.check') }}</option>
                            <option value="credit_card">{{ __('invoices.credit_card') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">{{ __('common.notes') }}</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize table helper with mobile support
document.addEventListener('DOMContentLoaded', function() {
    const tableHelper = new TableHelper('invoicesTable', {
        searchable: true,
        sortable: true,
        paginate: false,
        responsive: true, // Enable responsive features
        mobileCards: window.innerWidth <= 768 // Auto-enable card view on mobile
    });
    
    // Mobile-specific enhancements
    if (window.innerWidth <= 768) {
        // Add swipe support for table rows
        const rows = document.querySelectorAll('#invoicesTable tbody tr');
        rows.forEach(row => {
            let touchStartX = 0;
            let touchEndX = 0;
            
            row.addEventListener('touchstart', e => {
                touchStartX = e.changedTouches[0].screenX;
            });
            
            row.addEventListener('touchend', e => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });
            
            function handleSwipe() {
                if (touchEndX < touchStartX - 50) {
                    // Swipe left - show actions
                    const actionsCell = row.querySelector('td:last-child');
                    const dropdown = actionsCell.querySelector('.dropdown-toggle');
                    if (dropdown) {
                        dropdown.click();
                    }
                }
            }
        });
    }
});

// Rest of the JavaScript functions remain the same...
function sendInvoice(invoiceId) {
    if (!confirm("{{ __('invoices.confirm_send_email') }}")) {
        return;
    }
    
    fetch(`{{ base_url }}/invoices/${invoiceId}/send`, {
        method: 'POST',
        headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: "{{ __('messages.success') }}",
                text: data.message || "{{ __('invoices.email_sent_successfully') }}",
                timer: 3000,
                showConfirmButton: false
            });
            
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            Swal.fire({
                icon: 'error',
                title: "{{ __('messages.error') }}",
                text: data.message || "{{ __('invoices.email_send_failed') }}"
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: "{{ __('messages.error') }}",
            text: "{{ __('messages.operation_failed') }}"
        });
    });
}

function recordPayment(invoiceId) {
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    const form = document.getElementById('paymentForm');
    form.action = `{{ base_url }}/invoices/${invoiceId}/payment`;
    modal.show();
}

function deleteInvoice(invoiceId, status) {
    if (status !== 'draft') {
        Swal.fire({
            icon: 'error',
            title: "{{ __('messages.error') }}",
            text: "{{ __('invoices.can_only_delete_draft') }}"
        });
        return;
    }
    
    Swal.fire({
        title: "{{ __('messages.are_you_sure') }}",
        text: "{{ __('messages.cannot_be_undone') }}",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: "{{ __('common.delete') }}",
        cancelButtonText: "{{ __('common.cancel') }}"
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`{{ base_url }}/invoices/${invoiceId}/delete`, {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: "{{ __('messages.error') }}",
                        text: data.message
                    });
                }
            });
        }
    });
}

// Checkbox handling with mobile support
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.invoice-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
    updateBulkActions();
});

document.querySelectorAll('.invoice-checkbox').forEach(cb => {
    cb.addEventListener('change', updateBulkActions);
});

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
    const bulkBtn = document.getElementById('bulkActionsBtn');
    bulkBtn.disabled = checkedBoxes.length === 0;
}

function bulkAction(action) {
    const selected = Array.from(document.querySelectorAll('.invoice-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) return;
    
    // Handle bulk actions
    console.log('Bulk action:', action, 'for invoices:', selected);
}
</script>
{% endblock %}