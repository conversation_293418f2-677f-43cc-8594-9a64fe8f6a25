<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Updating Invoice ID: $invoiceId</h2>";
    
    // First, check current invoice details
    $checkSql = "SELECT * FROM invoices WHERE id = :id";
    $checkStmt = $db->prepare($checkSql);
    $checkStmt->execute(['id' => $invoiceId]);
    $invoice = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Current Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>Period: {$invoice['period']}</li>";
    echo "<li>Client: {$invoice['client_name']}</li>";
    echo "<li>Status: {$invoice['status']}</li>";
    echo "</ul>";
    
    // Update the invoice
    $updateSql = "UPDATE invoices 
                  SET invoice_number = :new_number,
                      period = :new_period
                  WHERE id = :id";
    
    $updateStmt = $db->prepare($updateSql);
    $result = $updateStmt->execute([
        'new_number' => 'FAC-LOC-2025-0198',
        'new_period' => 'AVRIL + MAI 2025',
        'id' => $invoiceId
    ]);
    
    if ($result) {
        echo "<h3 style='color: green;'>Invoice updated successfully!</h3>";
        
        // Show updated details
        $checkStmt->execute(['id' => $invoiceId]);
        $updated = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>Updated Invoice Details:</h3>";
        echo "<ul>";
        echo "<li>Number: {$updated['invoice_number']}</li>";
        echo "<li>Period: {$updated['period']}</li>";
        echo "</ul>";
        
        // Now update the invoice lines to change "RÉTROCESSION CNS" to "RÉTROCESSION" for Rémi Heine
        echo "<h3>Updating Invoice Lines...</h3>";
        
        // First check current lines
        $linesSql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id";
        $linesStmt = $db->prepare($linesSql);
        $linesStmt->execute(['invoice_id' => $invoiceId]);
        $lines = $linesStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Found " . count($lines) . " invoice lines</p>";
        
        // Update lines that contain "RÉTROCESSION CNS"
        $updateLinesSql = "UPDATE invoice_items 
                          SET description = REPLACE(description, 'RÉTROCESSION CNS', 'RÉTROCESSION')
                          WHERE invoice_id = :invoice_id 
                          AND description LIKE '%RÉTROCESSION CNS%'";
        
        $updateLinesStmt = $db->prepare($updateLinesSql);
        $updateResult = $updateLinesStmt->execute(['invoice_id' => $invoiceId]);
        
        if ($updateResult) {
            $affectedLines = $updateLinesStmt->rowCount();
            echo "<p style='color: green;'>Updated $affectedLines invoice line(s) to remove 'CNS' from description</p>";
            
            // Show updated lines
            $linesStmt->execute(['invoice_id' => $invoiceId]);
            $updatedLines = $linesStmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Updated Invoice Lines:</h4>";
            echo "<ul>";
            foreach ($updatedLines as $line) {
                echo "<li>{$line['description']} - €{$line['unit_price']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>Failed to update invoice lines!</p>";
        }
        
        echo "<p><a href='/fit/public/invoices/$invoiceId'>View Updated Invoice</a></p>";
    } else {
        echo "<p style='color: red;'>Failed to update invoice!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>