<?php
/**
 * Quick fix for effective date
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Update the effective date to 2025-01-01 for user 1
    $stmt = $pdo->prepare("UPDATE user_financial_obligations SET effective_date = '2025-01-01' WHERE user_id = 1 AND id = 8");
    $stmt->execute();
    
    echo "<h1>✅ Fixed!</h1>";
    echo "<p>Updated effective date to 2025-01-01 for user Frank Huet</p>";
    
    // Show current data
    $stmt = $pdo->prepare("SELECT * FROM user_financial_obligations WHERE user_id = 1");
    $stmt->execute();
    $data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Data:</h2>";
    echo "<ul>";
    echo "<li>Effective Date: " . $data['effective_date'] . "</li>";
    echo "<li>Rent: €" . number_format($data['rent_amount'], 2) . "</li>";
    echo "<li>Charges: €" . number_format($data['charges_amount'], 2) . "</li>";
    echo "<li>Secretary TVAC: €" . number_format($data['secretary_tvac_17'], 2) . "</li>";
    echo "<li>Total: €" . number_format($data['total_tvac'], 2) . "</li>";
    echo "</ul>";
    
    echo "<h2>Next Steps:</h2>";
    echo "<ol>";
    echo "<li><a href='/fit/public/debug-loy-data.php'>Check Debug Page</a> - Should now show ✅ instead of ❌</li>";
    echo "<li><a href='/fit/public/invoices/bulk-generation'>Go to Bulk Generation</a></li>";
    echo "<li>Select Frank Huet in LOY tab and generate invoice</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>