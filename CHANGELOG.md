# Changelog

All notable changes to Fit360 AdminDesk will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.5.0] - 2025-07-28

### Added
- **Inline Invoice Editing System**
  - Click-to-edit functionality for invoice line items (description, quantity, price, VAT rate)
  - Real-time auto-save with 500ms debouncing
  - Visual feedback system with hover effects, edit icons, and save animations
  - Full undo/redo support with keyboard shortcuts (Ctrl+Z/Y)
  - Field validation with inline error messages
  - Mobile-responsive design with 44px touch targets
  - API endpoints for saving individual and batch edits
  - Automatic invoice total recalculation
  - Activity logging for audit trail
  - Only available for draft invoices (security feature)

- **Product Live Search Enhancements**
  - Removed provider selection modal for inline-created products
  - Added `created_inline` flag to bypass modal interruptions
  - Improved user experience with seamless product addition

### Fixed
- **JavaScript Global Scope Conflicts**
  - Fixed `const style` redeclaration errors in invoice-error-handler.js
  - Fixed `const style` redeclaration errors in flash-message-enhancer.js
  - Wrapped problematic files in IIFEs to avoid global namespace pollution

- **Invoice Templates API Error**
  - Added safe table existence check in getInvoiceTemplates method
  - Returns empty array instead of throwing 500 error when table doesn't exist

- **Form Validation Issues**
  - Improved billable_id validation to be less intrusive
  - Only validates billable_id when form is submitted
  - Shows helpful context-specific error messages
  - Auto-removes empty invoice item rows before submission
  - Auto-focuses first empty field for better UX
  - Smooth scrolling to invalid fields

### Changed
- **Invoice Line Calculation Logic**
  - Updated recalculateInvoiceTotals to properly handle TVAC amounts
  - Fixed VAT calculation for line items (line_total - (line_total / (1 + vat_rate/100)))
  - Added automatic line_total recalculation when quantity or unit_price changes

### Technical Details
- **New Files Created**:
  - `/public/css/inline-invoice-editor.css` - Complete styling for inline editing
  - `/public/js/inline-invoice-editor.js` - Full JavaScript implementation
  - `/public/js/invoice-form-fixes.js` - Form validation improvements
  - `/public/test-inline-editor.html` - Static test page
  - `/public/test-inline-edit-real.php` - Test with real invoice data

- **Modified Files**:
  - `/app/controllers/InvoiceController.php` - Added updateInvoiceLine, batchUpdateInvoiceLines, recalculateInvoiceTotals methods
  - `/app/modules/invoices/routes.php` - Added API routes for inline editing
  - `/app/views/invoices/create-modern.twig` - Added CSS/JS includes and data-status attribute
  - `/app/views/invoices/edit-modern.twig` - Added CSS/JS includes and data-status attribute
  - `/public/js/product-live-search.js` - Added created_inline flag and modal bypass logic
  - `/public/js/invoice-product-selection.js` - Simplified provider modal (removed provider selection)

### Security
- CSRF token validation on all inline edit API calls
- User authentication and permission checks (invoices.update)
- Invoice status validation (only draft invoices can be edited)
- Input sanitization and validation on all fields
- SQL injection prevention with prepared statements

## [2.4.1] - 2025-07-27

### Added
- **Inline Product Creation Feature Analysis**
  - Comprehensive UI/UX design with mobile-first approach
  - Backend API architecture with Flight PHP integration
  - Database schema enhancements for tracking and rollback support
  - Security audit and recommendations
  - Detailed implementation plan with 19 prioritized tasks
  
- **Mobile Responsiveness Enhancements**
  - Mobile-specific CSS for product pages
  - Touch-friendly form controls
  - Card-based table layouts on mobile
  - Loading states and skeleton loaders
  
### Fixed
- **Dynamic Dashboard Access Issues**
  - Resolved authentication middleware blocking dashboard access
  - Created standalone dashboard implementation bypassing Flight framework issues
  - Fixed Twig template duplicate blocks error
  - Fixed dashboard API 404 errors
  - Added proper error handling and fallbacks
  
- **Dashboard Display Problems**
  - Fixed array to string conversion in Language helper
  - Fixed null config access in base Controller
  - Fixed CnsImportService database initialization timing
  - Added detailed error display in development mode
  
### Changed
- **Dashboard Implementation**
  - Created dashboard-standalone.php for direct access
  - Disabled dashboard-enhanced.js due to missing endpoints
  - Simplified refresh functionality with page reload
  - Improved error messages and debugging

### Technical Details
- **Files Created**:
  - `/public/dashboard-standalone.php` - Standalone dashboard implementation
  - `/app/views/dashboard-standalone.twig` - Dashboard template without Flight dependencies
  - `/public/css/mobile-products.css` - Product page mobile styles
  - `/public/css/mobile-dashboard.css` - Dashboard mobile styles
  - `/public/css/dashboard-loading.css` - Loading states and animations
  - Various test and diagnostic scripts

## [2.4.0] - 2025-01-30

### Added
- **Dynamic Dashboard System**
  - Complete transformation from static to dynamic dashboard
  - Real-time data updates from database
  - Auto-refresh functionality (60-second intervals)
  - Period filtering for revenue charts (week/month/year)
  - Activity logging and timeline display
  
- **Dashboard API Endpoints**
  - `/api/dashboard/stats` - Real-time statistics
  - `/api/dashboard/revenue-chart` - Revenue data with period filtering
  - `/api/dashboard/invoice-status-chart` - Invoice status breakdown
  - `/api/dashboard/recent-activities` - Activity timeline
  - `/api/dashboard/recent-invoices` - Recent invoices with pagination
  
- **Activity Logging System**
  - New `activity_logs` table for comprehensive audit trail
  - ActivityLog model with automatic tracking
  - Tracks all CRUD operations with field changes
  - IP address and user agent logging
  - Relative time display in French
  
- **DashboardController**
  - Proper MVC structure replacing inline route code
  - Centralized dashboard logic
  - JSON API responses for all endpoints
  - Error handling and default values
  
- **Frontend Dashboard Manager**
  - dashboard.js module for dynamic updates
  - Chart.js integration with smooth animations
  - Loading states and error handling
  - LocalStorage persistence for preferences
  - Mobile-responsive implementation

### Changed
- **Route Structure**
  - Dashboard route now uses DashboardController
  - Added new API routes group for dashboard
  - Improved code organization
  
- **Activity Tracking**
  - InvoiceController now logs all operations
  - ClientController tracks create/update/delete
  - Detailed change tracking with old/new values

### Technical Details
- **Files Created**:
  - `/app/controllers/DashboardController.php`
  - `/app/models/ActivityLog.php`
  - `/public/js/dashboard.js`
  - `/database/migrations/106_create_activity_logs_table.sql`
  - `/docs/DYNAMIC_DASHBOARD_IMPLEMENTATION.md`
  - Test scripts: `test-dynamic-dashboard.php`, `apply-activity-logs-migration.php`

## [2.3.9] - 2025-01-30

### Added
- **Month/Year Selection for Invoice Generation in User Profiles**
  - Added month and year dropdown selectors for all three invoice types:
    - Retrocession (RET): Month/year selectors with smart defaults (previous month)
    - Loyer (LOY): Month selector integrated with existing year navigation (current month default)
    - Course/Location (LOC): Month/year selectors (current month default)
  - Visual dropdown selectors with French month names
  - Smart defaults based on invoice type requirements
  
- **Future Month Validation**
  - Prevents generation of invoices for future months
  - Shows clear error message when attempting future generation
  - Client-side validation in JavaScript functions
  
- **Enhanced Test Script**
  - Updated test-user-invoice-generation.php with month/year selection
  - Added future month validation testing
  - Shows clear pass/fail indicators for validation tests
  - Allows testing any month/year combination

### Changed
- **Invoice Generation Functions**
  - `generateRetrocessionInvoice()`: Now reads selected month/year from dropdowns
  - `generateLoyerInvoiceWithMonth()`: New function using selected month
  - `generateCourseInvoice()`: Updated to use selected month/year
  - All functions now validate against future months
  
- **UI Improvements**
  - Better button icons (file-earmark-plus instead of lightning)
  - Consistent styling across all three invoice type sections
  - Added CSS for proper dropdown alignment and spacing
  
- **LOY Invoice Notes**
  - Changed to display current month instead of next month
  - Notes section now remains empty for LOY invoices

### Fixed
- **Backend Compatibility**
  - No backend changes required - controllers already accept month/year parameters
  - UnifiedInvoiceGenerator properly handles all date scenarios

## [2.3.8] - 2025-01-30

### Added
- **Enhanced Catalog Database Structure**
  - Added 17 new columns to catalog_items table for comprehensive product management
  - Inventory tracking: barcode, SKU, min_stock, max_stock, location, supplier_id
  - Financial data: purchase_price, margin tracking
  - Physical attributes: weight, dimensions, warranty_months
  - Marketing features: is_featured, tags (JSON), meta_title, meta_description
  - Branding: manufacturer, brand fields
  - Added 7 new columns to catalog_categories for hierarchy and SEO
  - Created catalog_stock_movements table for complete inventory tracking
  - Added performance indexes on all key fields
  - Foreign key constraints for data integrity
  
- **Migration Tools**
  - Created migration 105_add_catalog_columns.sql
  - Built apply-catalog-migration.php for safe column addition
  - Created fix-catalog-columns.php to handle missing columns
  - Added show-catalog-structure.php for structure verification

### Fixed
- **Catalog Migration Issues**
  - Resolved AFTER clause errors when stock_quantity column didn't exist
  - Fixed duplicate column errors for parent_id and slug in categories
  - Added proper error handling for existing columns and indexes
  - Implemented safe migration with individual column checks

### Changed
- **Version Number**: Updated to 2.3.8 to reflect catalog enhancements

## [2.3.7] - 2025-01-30

### Added
- **Product Live Search for DIV Invoices**
  - Real-time product search when typing in description fields
  - Auto-population of price and VAT rate from catalog
  - Keyboard navigation support (arrow keys, Enter, Escape)
  - Only activates for DIV (Divers) invoice types
  - Provider selection modal for miscellaneous category products
  - Search result caching for improved performance
  
- **Enhanced Product Selection**
  - Created product-live-search.js module with comprehensive functionality
  - Added product-live-search.css for professional dropdown styling
  - Integration with existing invoice-product-selection.js for provider handling
  - Mobile-responsive dropdown with touch support
  
- **Development Files**
  - test-live-search.php - Live search functionality testing
  - test-div-live-search.php - DIV invoice specific testing
  - test-click-fix.html - Click handling diagnostics
  - diagnose-live-search.js - Debugging utility

### Fixed
- **Product Selection Click Issues**
  - Fixed dropdown hiding too quickly on blur (increased delay to 300ms)
  - Added mousedown event handling to prevent input blur
  - Improved click detection with isClickingDropdown flag
  - Enhanced value setting with proper event triggers
  
- **Selector Mismatches**
  - Changed from .invoice-item-row to tr.invoice-item
  - Fixed VAT field name from vat_rate to vat_rate_id
  - Corrected row index extraction for table structure
  - Updated VAT selection to match by data-rate attribute

### Changed
- **Invoice Creation Workflow**
  - Live search now only appears for DIV invoice types
  - Product selection automatically focuses next field
  - Values are verified and re-set if needed after 50ms
  - Final verification logging after 500ms

## [2.3.6] - 2025-01-30

### Fixed
- **Invoice Type Configuration Issues**
  - Fixed duplicate invoice types preventing invoice generation
  - Resolved MySQL unique index constraint violations (4-character limit issue)
  - Standardized invoice type codes: LOY, LOC, RET25, RET30
  - Fixed UnifiedInvoiceGenerator SQL query (was looking for 'prefix' instead of 'code')
  - Corrected invoice numbering patterns to consistent format: FAC-{PREFIX}-{YEAR}-{NUMBER:4}
  
- **Course Management for Coaches**
  - Fixed third course not appearing after save due to missing CSRF validation
  - Added CSRF token validation to storeCourse, updateCourse, and deleteCourse methods
  - Fixed console errors from unreachable code
  - Resolved course saving issues with unique constraint on (user_id, course_name)
  
- **Bulk Invoice Generation Enhancements**
  - Modified LOC invoice format: Subject now shows "LOCATION SALLE"
  - Description shows only course names without decimal points
  - Fixed quantity display to show integers for sessions (was showing 5.00 instead of 5)
  - Fixed international VAT positioning (now below payment conditions)
  - Added proper handling of vat_intercommunautaire field

### Changed
- **Invoice Type System**
  - Simplified invoice type codes for better database compatibility
  - Updated config mappings: ret_invoice_type='RET2', loy_invoice_type='LOY', loc_invoice_type='LOC'
  - Fixed prefix values to match codes (was LOY with prefix FAC-LOY, now LOY with prefix LOY)
  
- **Testing Infrastructure**
  - Created comprehensive test script for all three invoice generation types
  - Added diagnostic scripts for troubleshooting invoice type issues
  - Implemented cleanup scripts for database maintenance

### Added
- **Development Tools**
  - Created specialized Claude Code subagents for different development aspects
  - Added automatic documentation update commands (/update-docs, /update-all-docs)
  - Integrated Task Master AI workflow for systematic development
  
- **Diagnostic Scripts**
  - test-user-invoice-generation.php - Comprehensive invoice generation testing
  - check-invoice-type-mapping.php - Diagnose configuration issues
  - fix-all-invoice-patterns.php - Standardize invoice numbering patterns
  - delete-test-invoices.php - Clean up test data

## [2.3.5] - 2025-07-20

### Fixed
- **Bulk Loyer Invoice Generation**
  - Fixed invoice line items not displaying due to table mismatch (invoice_items vs invoice_lines)
  - Fixed invoice number prefix missing (now shows FAC-LOC-2025-XXXX correctly)
  - Fixed double-counting of secretary amounts (was showing 2,840€ instead of 1,670€)
  - Fixed "Object of class Invoice could not be converted to string" error
  - Fixed "Undefined array key" errors for subtotal, name, and other fields
  - Fixed generated column error for vat_amount field
  
### Changed
- **Invoice Generation Logic**
  - Now uses invoice_lines table instead of invoice_items for consistency
  - Improved handling of secretary amounts (uses EITHER TVAC OR HTVA+TVA, not both)
  - Added proper type_id support for invoice number generation
  - Enhanced error handling and database context management

## [2.3.4] - 2025-07-20

### Added
- **Retrocession Monthly Configuration System**
  - 12-month grid interface in user profiles for CNS and patient amounts
  - Visual indicators showing which months have generated invoices
  - One-click invoice generation from user profile
  - Automatic population of retrocession data from monthly configuration
  
- **Invoice Regeneration Capability**
  - Delete and regenerate retrocession invoices while preserving data
  - Automatic status reset when deleted invoice is detected
  - Proper handling of foreign key constraints
  
- **Enhanced Permission Controls**
  - Manager/Admin only delete functionality for invoices
  - Role-based visibility of delete buttons
  - Separate permissions for draft vs sent invoice deletion
  
- **User-Specific Retrocession Settings**
  - Custom percentages per user (CNS, Patient, Secretary)
  - Custom labels for invoice lines
  - Effective date management for settings
  
- **Database Enhancements**
  - New `user_monthly_retrocession_amounts` table
  - Migration script 090_create_user_monthly_retrocession_amounts.sql
  - Proper `.env` usage in migration scripts

### Changed
- **Invoice Deletion Behavior**
  - Retrocession data entries now preserved (invoice_id set to NULL)
  - Email logs properly deleted on invoice removal
  - Cascade deletion for all related records
  
- **Error Handling**
  - Clean JSON responses with output buffer management
  - Better error messages for common issues
  - Enhanced logging throughout retrocession workflow
  
- **UI/UX Improvements**
  - Generate buttons disabled for already invoiced months
  - Green background and checkmarks for generated invoices
  - Previous month logic (generates June invoice in July)
  - Integrated retrocession settings in user edit form

### Fixed
- **Invoice Regeneration Issues**
  - "Une facture existe déjà pour cette période" error after deletion
  - "No retrocession data found" when entry exists
  - Foreign key constraint violations on deletion
  
- **Technical Fixes**
  - PDO namespace issues (`\PDO::FETCH_ASSOC`)
  - JSON parse errors from PHP output
  - Transaction handling in nested operations
  - Proper status updates for retrocession entries

### Technical Details
- **Modified Files**:
  - `/app/controllers/UserController.php` - Added generateRetrocession method
  - `/app/models/Invoice.php` - Enhanced delete method
  - `/app/views/users/form-modern.twig` - Added monthly configuration UI
  - `/app/services/RetrocessionCalculator.php` - User settings integration
  - `/app/views/invoices/show-modern.twig` - Delete functionality
  - `/app/views/invoices/index-modern.twig` - Permission-based buttons

## [2.3.3] - 2025-07-19

### Added
- Email system enhancements for invoice sending
- PDF attachments to invoice emails
- Period information in email subjects (e.g., "Facture (JUIN 2025)")

### Fixed
- Invoice email sending functionality
- CSRF protection for form submissions
- Method not allowed errors in invoice list views
- PDF generation consistency between email and download

### Changed
- Email now uses same PDF generation as download function
- Updated to use invoice-pdf.php for all PDF generation

## [2.3.2] - 2025-07-14

### Added
- Invoice date management enhancements
- Custom issue date support in invoice creation
- Automatic due date calculation based on payment terms

### Fixed
- Issue date handling in invoice forms
- Due date calculation (1 month = 31 days)

## [2.3.1] - 2025-01-29

### Changed
- Simplified permission system from dual role/group to groups-only
- Removed 200+ temporary test files
- Prepared codebase for GitHub deployment

### Fixed
- Missing French translations for user management
- Last login tracking (last_login → last_login_at)

## [2.3.0] - 2025-01-20

### Added
- Mobile responsiveness implementation
- Touch-optimized forms (44px minimum targets)
- Responsive table solutions
- Swipe gestures for navigation
- Pull-to-refresh functionality
- Bottom navigation bar
- Floating action buttons
- Context7 MCP server integration