<?php
// Find users who are likely coaches based on having courses

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Find Coaches</h1>";

// Find all users with courses
echo "<h2>Users with Course Names (Likely Coaches):</h2>";
$stmt = $db->query("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, 
           u.course_name, u.is_active, u.can_be_invoiced,
           GROUP_CONCAT(ugm.group_id) as `groups`
    FROM users u
    LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE u.course_name IS NOT NULL AND u.course_name != ''
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
    GROUP BY u.id
    ORDER BY u.first_name, u.last_name
");
$usersWithCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Found " . count($usersWithCourses) . " active users with courses who can be invoiced</p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Course</th><th>Groups</th><th>Action</th></tr>";
foreach ($usersWithCourses as $user) {
    echo "<tr>";
    echo "<td>{$user['id']}</td>";
    echo "<td>{$user['name']}</td>";
    echo "<td>{$user['username']}</td>";
    echo "<td><strong>{$user['course_name']}</strong></td>";
    echo "<td>{$user['groups']}</td>";
    echo "<td>";
    if (!strpos($user['groups'], '2')) {
        echo "<button onclick='addToCoachGroup({$user['id']})'>Add to Coach Group</button>";
    } else {
        echo "✅ Already in group 2";
    }
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

// Check what groups these users are in
echo "<h2>Group Distribution:</h2>";
$stmt = $db->query("
    SELECT ug.id, ug.name, COUNT(DISTINCT u.id) as coach_count
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    JOIN user_groups ug ON ugm.group_id = ug.id
    WHERE u.course_name IS NOT NULL AND u.course_name != ''
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
    GROUP BY ug.id, ug.name
    ORDER BY coach_count DESC
");
$groupDist = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Group ID</th><th>Group Name</th><th>Users with Courses</th></tr>";
foreach ($groupDist as $group) {
    echo "<tr>";
    echo "<td>{$group['id']}</td>";
    echo "<td>{$group['name']}</td>";
    echo "<td>{$group['coach_count']}</td>";
    echo "</tr>";
}
echo "</table>";

// SQL to add coaches to group 2
if (count($usersWithCourses) > 0) {
    echo "<h2>SQL to Add All Coaches to Group 2:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px;'>";
    foreach ($usersWithCourses as $user) {
        if (!strpos($user['groups'], '2')) {
            echo "INSERT IGNORE INTO user_group_members (user_id, group_id) VALUES ({$user['id']}, 2);\n";
        }
    }
    echo "</pre>";
}

?>

<script>
function addToCoachGroup(userId) {
    if (confirm('Add user ' + userId + ' to coach group (2)?')) {
        // In a real implementation, this would make an AJAX call
        alert('To add user to coach group, run:\nINSERT INTO user_group_members (user_id, group_id) VALUES (' + userId + ', 2);');
    }
}
</script>

<hr>
<p><a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a></p>