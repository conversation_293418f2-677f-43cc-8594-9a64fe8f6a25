<?php
/**
 * Test Email Service with Mailhog
 * 
 * This script tests the email functionality using Mailhog.
 * Make sure Mailhog is running before executing this script.
 * 
 * Usage: php test_email_service.php
 * 
 * View sent emails at: http://localhost:8025
 */

// Load environment variables
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
            continue;
        }
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Display current mail configuration
echo "Email Configuration:\n";
echo "===================\n";
echo "MAIL_HOST: " . ($_ENV['MAIL_HOST'] ?? 'not set') . "\n";
echo "MAIL_PORT: " . ($_ENV['MAIL_PORT'] ?? 'not set') . "\n";
echo "MAIL_FROM_ADDRESS: " . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'not set') . "\n";
echo "MAIL_FROM_NAME: " . ($_ENV['MAIL_FROM_NAME'] ?? 'not set') . "\n\n";

// Test 1: Simple text email
echo "Test 1: Sending simple text email...\n";

$to = '<EMAIL>';
$subject = 'Test Email from Fit360';
$message = "This is a test email sent from Fit360 AdminDesk.\n\nIf you can see this in Mailhog, the email service is working correctly!";
$headers = 'From: ' . ($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>');

// Configure PHP to use Mailhog
ini_set('SMTP', $_ENV['MAIL_HOST'] ?? 'localhost');
ini_set('smtp_port', $_ENV['MAIL_PORT'] ?? '1025');
ini_set('sendmail_from', $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>');

if (mail($to, $subject, $message, $headers)) {
    echo "✓ Simple text email sent successfully!\n";
} else {
    echo "✗ Failed to send simple text email.\n";
}

// Test 2: HTML email
echo "\nTest 2: Sending HTML email...\n";

$to = '<EMAIL>';
$subject = 'Invoice FAC-2025-0001 from Fit360';

$boundary = md5(time());
$headers = "From: " . ($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>') . "\r\n";
$headers .= "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: multipart/alternative; boundary=\"$boundary\"\r\n";

$message = "--$boundary\r\n";
$message .= "Content-Type: text/plain; charset=UTF-8\r\n";
$message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
$message .= "Dear Client,\n\nPlease find attached your invoice FAC-2025-0001.\n\nAmount: €1,500.00\nDue Date: 15/02/2025\n\nBest regards,\nFit360 Team\r\n\r\n";

$message .= "--$boundary\r\n";
$message .= "Content-Type: text/html; charset=UTF-8\r\n";
$message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
$message .= "<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .invoice-header { background-color: #f8f9fa; padding: 20px; }
        .invoice-details { margin: 20px 0; }
        .amount { font-size: 24px; color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <div class='invoice-header'>
        <h2>Invoice from Fit360 AdminDesk</h2>
    </div>
    <div class='invoice-details'>
        <p>Dear Client,</p>
        <p>Please find below your invoice details:</p>
        <ul>
            <li><strong>Invoice Number:</strong> FAC-2025-0001</li>
            <li><strong>Amount:</strong> <span class='amount'>€1,500.00</span></li>
            <li><strong>Due Date:</strong> 15/02/2025</li>
        </ul>
        <p>Thank you for your business!</p>
        <p>Best regards,<br>Fit360 Team</p>
    </div>
</body>
</html>\r\n\r\n";

$message .= "--$boundary--";

if (mail($to, $subject, $message, $headers)) {
    echo "✓ HTML email sent successfully!\n";
} else {
    echo "✗ Failed to send HTML email.\n";
}

// Test 3: Multiple recipients
echo "\nTest 3: Sending to multiple recipients...\n";

$recipients = '<EMAIL>, <EMAIL>, <EMAIL>';
$subject = 'Group Notification from Fit360';
$message = "This is a test notification sent to multiple recipients.";
$headers = 'From: ' . ($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>');

if (mail($recipients, $subject, $message, $headers)) {
    echo "✓ Email sent to multiple recipients successfully!\n";
} else {
    echo "✗ Failed to send email to multiple recipients.\n";
}

echo "\n";
echo "========================================\n";
echo "Email tests completed!\n";
echo "Check Mailhog at: http://localhost:8025\n";
echo "========================================\n";
echo "\nNote: Make sure Mailhog is running:\n";
echo "1. Navigate to C:\\wamp64\\mailhog\\\n";
echo "2. Run start_mailhog.bat\n";