<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Retrocession Labels Update</h2>";
    
    // Check current labels
    $stmt = $db->prepare("
        SELECT id, user_id, cns_label, patient_label, secretary_label 
        FROM user_retrocession_settings 
        WHERE user_id = 1 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "<h3>Current labels for user 1:</h3>";
        echo "<ul>";
        echo "<li>CNS: " . htmlspecialchars($result['cns_label']) . "</li>";
        echo "<li>Patient: " . htmlspecialchars($result['patient_label']) . "</li>";
        echo "<li>Secretary: " . htmlspecialchars($result['secretary_label']) . "</li>";
        echo "</ul>";
        
        // Check if they need updating
        $needsUpdate = false;
        $updates = [];
        
        if ($result['cns_label'] === 'RÉTROCESSION CNS') {
            $updates['cns_label'] = 'Rétrocession CNS';
            $needsUpdate = true;
        }
        
        if ($result['patient_label'] === 'RÉTROCESSION PATIENTS') {
            $updates['patient_label'] = 'Rétrocession PATIENTS';
            $needsUpdate = true;
        }
        
        if ($result['secretary_label'] === 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL') {
            $updates['secretary_label'] = 'Frais secrétariat et mise à disposition matériel';
            $needsUpdate = true;
        }
        
        if ($needsUpdate) {
            echo "<h3>Updating labels to proper case...</h3>";
            
            $sql = "UPDATE user_retrocession_settings SET ";
            $params = [];
            $setParts = [];
            
            foreach ($updates as $field => $value) {
                $setParts[] = "$field = :$field";
                $params[$field] = $value;
            }
            
            $sql .= implode(', ', $setParts);
            $sql .= " WHERE id = :id";
            $params['id'] = $result['id'];
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            echo "<p style='color: green;'>Labels updated successfully!</p>";
            echo "<h3>New values:</h3>";
            echo "<ul>";
            foreach ($updates as $field => $value) {
                echo "<li>$field: " . htmlspecialchars($value) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: blue;'>Labels are already in proper case. No update needed.</p>";
        }
    } else {
        echo "<p style='color: red;'>No retrocession settings found for user 1</p>";
    }
    
    // Also update any other users with uppercase labels
    echo "<h3>Checking other users...</h3>";
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM user_retrocession_settings 
        WHERE cns_label = 'RÉTROCESSION CNS' 
        OR patient_label = 'RÉTROCESSION PATIENTS'
        OR secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL'
    ");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "<p>Found $count records with uppercase labels. Updating all...</p>";
        
        $stmt = $db->prepare("
            UPDATE user_retrocession_settings 
            SET 
                cns_label = CASE 
                    WHEN cns_label = 'RÉTROCESSION CNS' THEN 'Rétrocession CNS'
                    ELSE cns_label 
                END,
                patient_label = CASE 
                    WHEN patient_label = 'RÉTROCESSION PATIENTS' THEN 'Rétrocession PATIENTS'
                    ELSE patient_label 
                END,
                secretary_label = CASE 
                    WHEN secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL' 
                    THEN 'Frais secrétariat et mise à disposition matériel'
                    ELSE secretary_label 
                END
            WHERE cns_label = 'RÉTROCESSION CNS' 
            OR patient_label = 'RÉTROCESSION PATIENTS'
            OR secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL'
        ");
        $stmt->execute();
        
        echo "<p style='color: green;'>All records updated!</p>";
    } else {
        echo "<p>No other records with uppercase labels found.</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='/fit/public/invoices'>Go back to invoices</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}