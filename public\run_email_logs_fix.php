<?php
/**
 * Run Email Logs Fix Migration
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

echo "<h1>Fix Email Logs Table</h1>";
echo "<pre>";

try {
    $pdo = new PDO(
        "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4",
        $dbUser,
        $dbPass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "Connected to database: $dbName\n\n";
    
    // Read and execute migration
    $migrationFile = __DIR__ . '/../database/migrations/100_fix_email_logs_template_id.sql';
    $sql = file_get_contents($migrationFile);
    
    echo "Running migration:\n$sql\n\n";
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            echo "Executing: " . substr($statement, 0, 50) . "...\n";
            $pdo->exec($statement);
            echo "✓ Success\n\n";
        }
    }
    
    // Verify the change
    echo "Verifying template_id column:\n";
    $stmt = $pdo->query("DESCRIBE email_logs template_id");
    $column = $stmt->fetch();
    
    echo "Column details:\n";
    foreach ($column as $key => $value) {
        echo "  $key: $value\n";
    }
    
    if ($column['Null'] === 'YES') {
        echo "\n✓ template_id is now nullable!\n";
    } else {
        echo "\n✗ template_id is still NOT NULL\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
echo "<hr>";
echo '<p><a href="test_email_logging.php">Back to Test Email Logging</a></p>';
?>