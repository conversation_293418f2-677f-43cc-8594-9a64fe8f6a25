<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Please log in first.");
}

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Coach Group Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
        .info { background-color: #e7f3ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Coach Group Debug</h1>
    
    <div class="info">
        <strong>Session Info:</strong><br>
        User ID: <?php echo $_SESSION['user_id']; ?><br>
        Username: <?php echo $_SESSION['username'] ?? 'N/A'; ?>
    </div>

<?php
try {
    // Connect to database using .env
    $db = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';port=' . $_ENV['DB_PORT'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 1. Check coach group exists
    echo "<h2>1. Coach Group Check</h2>";
    $stmt = $db->prepare("SELECT * FROM user_groups WHERE id = 24");
    $stmt->execute();
    $group = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($group) {
        echo "<p class='success'>✓ Group 24 exists: " . htmlspecialchars($group['name']) . "</p>";
    } else {
        echo "<p class='error'>✗ Group 24 not found!</p>";
    }
    
    // 2. Get all members of group 24
    echo "<h2>2. All Members of Group 24</h2>";
    $stmt = $db->prepare("
        SELECT u.*, ugm.group_id,
               CONCAT(u.first_name, ' ', u.last_name) as full_name
        FROM users u
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 24
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute();
    $allMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total members in group 24: " . count($allMembers) . "</p>";
    
    if (count($allMembers) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Email</th><th>Active</th><th>Can Invoice</th></tr>";
        foreach ($allMembers as $member) {
            echo "<tr>";
            echo "<td>" . $member['id'] . "</td>";
            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($member['username']) . "</td>";
            echo "<td>" . htmlspecialchars($member['email']) . "</td>";
            echo "<td>" . ($member['is_active'] ? '✓' : '✗') . "</td>";
            echo "<td>" . ($member['can_be_invoiced'] ? '✓' : '✗') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. Get billable coaches (as the invoice form would)
    echo "<h2>3. Billable Coaches (Active + Can Invoice)</h2>";
    $stmt = $db->prepare("
        SELECT u.id, u.username, u.email, 
               CONCAT(u.first_name, ' ', u.last_name) as name,
               u.billing_email, u.billing_address, u.billing_city,
               u.billing_postal_code, u.billing_country, u.vat_number,
               u.is_intracommunity, u.vat_intercommunautaire,
               u.course_name, u.hourly_rate, u.hourly_vat_rate,
               u.is_active, u.can_be_invoiced
        FROM users u
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 24
        AND u.can_be_invoiced = 1
        AND u.is_active = 1
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute();
    $billableCoaches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Billable coaches count: " . count($billableCoaches) . "</p>";
    
    if (count($billableCoaches) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Course</th></tr>";
        foreach ($billableCoaches as $coach) {
            echo "<tr>";
            echo "<td>" . $coach['id'] . "</td>";
            echo "<td>" . htmlspecialchars($coach['name']) . "</td>";
            echo "<td>" . htmlspecialchars($coach['username']) . "</td>";
            echo "<td>" . htmlspecialchars($coach['course_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>No billable coaches found! Check that coaches have:</p>";
        echo "<ul>";
        echo "<li>is_active = 1</li>";
        echo "<li>can_be_invoiced = 1</li>";
        echo "</ul>";
    }
    
    // 4. Direct link to group members page
    echo "<h2>4. Direct Links</h2>";
    echo "<p><a href='/fit/public/users/groups/24/members'>View Group 24 Members Page</a></p>";
    echo "<p><a href='/fit/public/invoices/create?type=location'>Create Location Invoice</a></p>";
    
    // 5. SQL to fix coaches if needed
    echo "<h2>5. Fix Coaches (if needed)</h2>";
    echo "<p>If coaches exist but can't be invoiced, run this SQL:</p>";
    echo "<pre style='background: #f5f5f5; padding: 10px;'>";
    echo "UPDATE users u
INNER JOIN user_group_members ugm ON u.id = ugm.user_id
SET u.can_be_invoiced = 1, u.is_active = 1
WHERE ugm.group_id = 24;";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p class='error'>Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Check your .env file has correct database credentials:</p>";
    echo "<ul>";
    echo "<li>DB_CONNECTION=" . ($_ENV['DB_CONNECTION'] ?? 'not set') . "</li>";
    echo "<li>DB_HOST=" . ($_ENV['DB_HOST'] ?? 'not set') . "</li>";
    echo "<li>DB_PORT=" . ($_ENV['DB_PORT'] ?? 'not set') . "</li>";
    echo "<li>DB_DATABASE=" . ($_ENV['DB_DATABASE'] ?? 'not set') . "</li>";
    echo "<li>DB_USERNAME=" . ($_ENV['DB_USERNAME'] ?? 'not set') . "</li>";
    echo "<li>DB_PASSWORD=" . (isset($_ENV['DB_PASSWORD']) ? '***' : 'not set') . "</li>";
    echo "</ul>";
}
?>
</body>
</html>