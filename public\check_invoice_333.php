<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Invoice #333 Line Items</h2>";
    
    // Check invoice lines
    $stmt = $db->prepare("
        SELECT id, description, line_total 
        FROM invoice_lines 
        WHERE invoice_id = 333 
        ORDER BY sort_order
    ");
    $stmt->execute();
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($lines) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Description</th><th>Total</th></tr>";
        foreach ($lines as $line) {
            echo "<tr>";
            echo "<td>" . $line['id'] . "</td>";
            echo "<td>" . htmlspecialchars($line['description']) . "</td>";
            echo "<td>" . number_format($line['line_total'], 2) . " €</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Analysis:</h3>";
        echo "<p>The invoice was created with the old uppercase labels. To see the new labels in action, you need to:</p>";
        echo "<ol>";
        echo "<li>Delete invoice #333</li>";
        echo "<li>Generate a new retrocession invoice</li>";
        echo "</ol>";
        echo "<p><a href='/fit/public/invoices/333/delete' onclick='return confirm(\"Delete invoice #333?\")'>Delete Invoice #333</a></p>";
    } else {
        echo "<p>No lines found for invoice #333</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}