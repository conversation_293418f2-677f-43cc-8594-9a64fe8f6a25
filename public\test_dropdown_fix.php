<?php
require_once __DIR__ . '/../app/config/bootstrap.php';

// Set page title
$title = 'Test Dropdown Fix';

// Create some test data for the table
$testInvoices = [];
for ($i = 1; $i <= 20; $i++) {
    $testInvoices[] = [
        'id' => $i,
        'invoice_number' => 'INV-2025-' . str_pad($i, 4, '0', STR_PAD_LEFT),
        'client_name' => 'Test Client ' . $i,
        'issue_date' => date('Y-m-d', strtotime('-' . $i . ' days')),
        'total' => rand(100, 5000),
        'status' => ['draft', 'sent', 'paid', 'overdue'][rand(0, 3)]
    ];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Icon spacing fix -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/css/icon-spacing-fix.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .test-section {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4"><?php echo $title; ?></h1>
        
        <!-- Test Section 1: Regular Table with Dropdowns -->
        <div class="test-section">
            <h2>Test 1: Table with Dropdowns (Responsive)</h2>
            <p>This table is wrapped in a responsive container. Dropdowns should position correctly even when scrolling.</p>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($testInvoices as $invoice): ?>
                        <tr>
                            <td><?php echo $invoice['invoice_number']; ?></td>
                            <td><?php echo $invoice['client_name']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($invoice['issue_date'])); ?></td>
                            <td>€<?php echo number_format($invoice['total'], 2); ?></td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo $invoice['status'] == 'paid' ? 'success' : 
                                        ($invoice['status'] == 'draft' ? 'secondary' : 
                                        ($invoice['status'] == 'sent' ? 'info' : 'danger')); 
                                ?>">
                                    <?php echo ucfirst($invoice['status']); ?>
                                </span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-eye"></i>View</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil"></i>Edit</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-printer"></i>Print</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash"></i>Delete</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Test Section 2: Dropdown at Bottom of Page -->
        <div class="test-section">
            <h2>Test 2: Dropdown Near Bottom</h2>
            <p>This dropdown is near the bottom of the viewport. It should flip up automatically.</p>
            
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    Test Dropdown
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">Action 1</a></li>
                    <li><a class="dropdown-item" href="#">Action 2</a></li>
                    <li><a class="dropdown-item" href="#">Action 3</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#">Separated link</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Test Section 3: Multiple Dropdowns -->
        <div class="test-section">
            <h2>Test 3: Multiple Dropdowns Side by Side</h2>
            <p>Opening one dropdown should close others.</p>
            
            <div class="d-flex gap-2">
                <?php for ($i = 1; $i <= 5; $i++): ?>
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Dropdown <?php echo $i; ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">Dropdown <?php echo $i; ?></h6></li>
                        <li><a class="dropdown-item" href="#">Action 1</a></li>
                        <li><a class="dropdown-item" href="#">Action 2</a></li>
                        <li><a class="dropdown-item" href="#">Action 3</a></li>
                    </ul>
                </div>
                <?php endfor; ?>
            </div>
        </div>
        
        <!-- Status Messages -->
        <div class="alert alert-info mt-4">
            <h5>Test Instructions:</h5>
            <ol>
                <li>Click on the three-dots icons in the table - dropdowns should appear correctly positioned</li>
                <li>Scroll the table horizontally (if on mobile) - dropdowns should stay in place</li>
                <li>Try the dropdown near the bottom - it should flip upward</li>
                <li>Open multiple dropdowns - only one should be open at a time</li>
                <li>Resize the window - dropdowns should reposition correctly</li>
            </ol>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Dropdown Fixes -->
    <script src="<?php echo BASE_URL; ?>/js/dropdown-fix.js"></script>
    <script src="<?php echo BASE_URL; ?>/js/table-dropdown-fix.js"></script>
    
    <script>
        // Log when dropdowns are opened
        document.addEventListener('show.bs.dropdown', function(e) {
            console.log('Dropdown opened:', e.target);
        });
        
        document.addEventListener('hide.bs.dropdown', function(e) {
            console.log('Dropdown closed:', e.target);
        });
        
        // Add some debug info
        console.log('Dropdown fix scripts loaded');
        console.log('Number of dropdowns found:', document.querySelectorAll('.dropdown').length);
    </script>
</body>
</html>