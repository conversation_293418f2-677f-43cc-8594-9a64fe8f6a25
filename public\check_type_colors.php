<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Invoice Types with Colors</h2>";
    
    // Check if color column exists
    $stmt = $db->query("SHOW COLUMNS FROM invoice_types");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Columns in invoice_types: " . implode(', ', $columns) . "</p>";
    
    // Check invoice types
    $sql = "SELECT * FROM invoice_types ORDER BY id";
    $stmt = $db->query($sql);
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr>";
    foreach (array_keys($types[0] ?? []) as $col) {
        echo "<th>" . htmlspecialchars($col) . "</th>";
    }
    echo "</tr>";
    
    foreach ($types as $type) {
        echo "<tr>";
        foreach ($type as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Check how type_color is determined in invoices
    echo "<h3>Sample Invoice Type Colors</h3>";
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.invoice_type_id,
            it.name as type_name,
            it.code,
            i.subject
        FROM invoices i
        LEFT JOIN invoice_types it ON it.id = i.invoice_type_id
        WHERE i.deleted_at IS NULL
        ORDER BY i.id DESC
        LIMIT 10
    ");
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice</th><th>Type ID</th><th>Type Name</th><th>Code</th><th>Subject</th></tr>";
    foreach ($invoices as $invoice) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($invoice['invoice_number']) . "</td>";
        echo "<td>" . $invoice['invoice_type_id'] . "</td>";
        echo "<td>" . htmlspecialchars($invoice['type_name']) . "</td>";
        echo "<td>" . htmlspecialchars($invoice['code']) . "</td>";
        echo "<td>" . htmlspecialchars($invoice['subject']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}