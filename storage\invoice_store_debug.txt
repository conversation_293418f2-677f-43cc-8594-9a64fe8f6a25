2025-07-18 15:08:56 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

2025-07-19 10:59:30 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date

