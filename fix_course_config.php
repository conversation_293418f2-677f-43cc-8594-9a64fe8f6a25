<?php
/**
 * Fix the course configuration entries
 */

// Load environment variables
require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load .env file
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection from .env
$host = $_ENV['DB_HOST'];
$dbname = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Connected to database successfully.\n\n";
    
    // 1. Add config entry (without description column)
    try {
        $sql = "INSERT INTO `config` (`key`, `value`, `category`) VALUES
                ('course_invoice_type', 'COURS', 'invoicing')
                ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)";
        
        $pdo->exec($sql);
        echo "✓ Added course_invoice_type to config table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "ℹ Config entry already exists\n";
        } else {
            echo "✗ Error adding config: " . $e->getMessage() . "\n";
        }
    }
    
    // 2. Add COURS invoice type
    try {
        $sql = "INSERT INTO `invoice_types` (`code`, `name`, `prefix`, `description`, `color`, `order`, `is_active`) 
                VALUES ('COURS', 'Cours', 'COURS', 'Factures pour les cours', '#17a2b8', 10, 1)
                ON DUPLICATE KEY UPDATE 
                    `name` = VALUES(`name`),
                    `prefix` = VALUES(`prefix`),
                    `is_active` = 1";
        
        $pdo->exec($sql);
        echo "✓ Added COURS invoice type\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "ℹ COURS invoice type already exists\n";
        } else {
            echo "✗ Error adding invoice type: " . $e->getMessage() . "\n";
        }
    }
    
    // 3. Verify everything is set up
    echo "\n--- Verification ---\n";
    
    // Check config
    $stmt = $pdo->prepare("SELECT `value` FROM config WHERE `key` = 'course_invoice_type'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($config) {
        echo "✓ Config: course_invoice_type = " . $config['value'] . "\n";
    } else {
        echo "✗ Config entry not found\n";
    }
    
    // Check invoice type
    $stmt = $pdo->prepare("SELECT * FROM invoice_types WHERE code = 'COURS'");
    $stmt->execute();
    $type = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($type) {
        echo "✓ Invoice type: COURS (ID: {$type['id']}, Name: {$type['name']}, Prefix: {$type['prefix']})\n";
    } else {
        echo "✗ Invoice type COURS not found\n";
    }
    
    // Check table
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_monthly_course_counts'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Table 'user_monthly_course_counts' exists\n";
    } else {
        echo "✗ Table 'user_monthly_course_counts' not found\n";
    }
    
    echo "\n✓ Setup completed! The monthly course tracking feature is ready to use.\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}