<?php
// Check group configuration

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Group Configuration Check</h1>";

// Check config settings
echo "<h2>Configuration Settings:</h2>";
$configs = ['coach_group_id', 'practitioner_group_id'];
foreach ($configs as $config) {
    $stmt = $db->prepare("SELECT value FROM config_settings WHERE name = :name");
    $stmt->execute(['name' => $config]);
    $value = $stmt->fetchColumn();
    if ($value !== false) {
        echo "<p>✅ $config = $value</p>";
    } else {
        echo "<p>❌ $config not found (will use default)</p>";
    }
}

// Check groups
echo "<h2>User Groups:</h2>";
$stmt = $db->query("SELECT id, name, description FROM user_groups ORDER BY id");
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Member Count</th></tr>";
foreach ($groups as $group) {
    $stmt = $db->prepare("SELECT COUNT(*) FROM user_group_members WHERE group_id = :id");
    $stmt->execute(['id' => $group['id']]);
    $count = $stmt->fetchColumn();
    echo "<tr>";
    echo "<td>{$group['id']}</td>";
    echo "<td>{$group['name']}</td>";
    echo "<td>{$group['description']}</td>";
    echo "<td>$count members</td>";
    echo "</tr>";
}
echo "</table>";

// Check coaches (group 2)
echo "<h2>Coaches (Group 2):</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, u.course_name
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 2
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<p>Found " . count($coaches) . " active coaches who can be invoiced</p>";
if (count($coaches) > 0) {
    echo "<ul>";
    foreach ($coaches as $coach) {
        $course = $coach['course_name'] ? " - {$coach['course_name']}" : "";
        echo "<li>{$coach['name']} ({$coach['username']})$course</li>";
    }
    echo "</ul>";
}

// Check practitioners (group 4)
echo "<h2>Practitioners (Group 4 - Kiné):</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 4
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$practitioners = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<p>Found " . count($practitioners) . " active practitioners who can be invoiced</p>";
if (count($practitioners) > 0) {
    echo "<ul>";
    foreach ($practitioners as $practitioner) {
        echo "<li>{$practitioner['name']} ({$practitioner['username']})</li>";
    }
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='/fit/public/test_invoice_dropdowns.html'>Back to Test Page</a></p>";
?>