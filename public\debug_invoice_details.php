<?php
/**
 * Debug Invoice Details Method
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Invoice Details</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Debug Invoice Details for #279</h1>
    
    <?php
    try {
        $invoice = new Invoice();
        
        // Check if the method exists
        if (method_exists($invoice, 'getInvoiceWithDetails')) {
            echo "<p class='success'>✓ getInvoiceWithDetails method exists</p>";
            
            // Call it
            echo "<h2>Calling getInvoiceWithDetails(279):</h2>";
            $details = $invoice->getInvoiceWithDetails(279);
            
            if ($details) {
                echo "<pre>" . print_r($details, true) . "</pre>";
                
                // Check specific fields that might cause issues
                echo "<h3>Key Fields Check:</h3>";
                echo "<ul>";
                echo "<li>Has client data: " . (isset($details['client']) ? 'YES' : 'NO') . "</li>";
                echo "<li>Has user data: " . (isset($details['user']) ? 'YES' : 'NO') . "</li>";
                echo "<li>Client email: " . ($details['client']['email'] ?? 'not set') . "</li>";
                echo "<li>User email: " . ($details['user']['email'] ?? 'not set') . "</li>";
                echo "<li>User invoice_email: " . ($details['user']['invoice_email'] ?? 'not set') . "</li>";
                echo "<li>Invoice type: " . ($details['invoice_type'] ?? 'not set') . "</li>";
                echo "<li>Invoice type code: " . ($details['invoice_type_code'] ?? 'not set') . "</li>";
                echo "</ul>";
            } else {
                echo "<p class='error'>getInvoiceWithDetails returned null/false</p>";
            }
        } else {
            echo "<p class='error'>❌ getInvoiceWithDetails method does not exist!</p>";
            
            // Try alternative methods
            echo "<h3>Trying alternative: find(279)</h3>";
            $invoiceData = Invoice::find(279);
            if ($invoiceData) {
                echo "<pre>" . print_r($invoiceData, true) . "</pre>";
            }
        }
        
        // Check the actual SQL error by testing the logEmail query directly
        echo "<h2>Testing logEmail SQL Query:</h2>";
        
        $db = Flight::db();
        
        // Prepare test data
        $testData = [
            ':invoice_id' => 279,
            ':template_id' => null,
            ':recipient_type' => 'primary',
            ':recipient_email' => '<EMAIL>',
            ':subject' => 'Test Subject',
            ':body_preview' => 'Test body preview',
            ':attachments_sent' => json_encode([['name' => 'test.pdf', 'type' => 'application/pdf']]),
            ':status' => 'failed',
            ':sent_at' => null,
            ':error_message' => 'Test error'
        ];
        
        $sql = "
            INSERT INTO email_logs (
                invoice_id, template_id, recipient_type, recipient_email,
                subject, body_preview, attachments_sent, status, 
                sent_at, error_message, created_at
            ) VALUES (
                :invoice_id, :template_id, :recipient_type, :recipient_email,
                :subject, :body_preview, :attachments_sent, :status,
                :sent_at, :error_message, NOW()
            )
        ";
        
        echo "<p>Testing SQL with parameters:</p>";
        echo "<pre>" . print_r($testData, true) . "</pre>";
        
        try {
            $stmt = $db->prepare($sql);
            $stmt->execute($testData);
            echo "<p class='success'>✓ SQL query executed successfully!</p>";
            
            // Clean up test entry
            $db->exec("DELETE FROM email_logs WHERE invoice_id = 279 AND subject = 'Test Subject'");
        } catch (PDOException $e) {
            echo "<p class='error'>❌ SQL Error: " . $e->getMessage() . "</p>";
            echo "<p>Error Code: " . $e->getCode() . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/test_invoice_279_email.php">Test Email Send</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>