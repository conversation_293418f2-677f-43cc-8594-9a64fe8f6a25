<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Invoice #319 Cleanup</h2>\n";
    
    // Get invoice details
    $stmt = $pdo->prepare("
        SELECT i.*, ugi.user_id, ugi.period_month, ugi.period_year,
               u.first_name, u.last_name
        FROM invoices i
        LEFT JOIN user_generated_invoices ugi ON i.id = ugi.invoice_id
        LEFT JOIN users u ON ugi.user_id = u.id
        WHERE i.id = 319
    ");
    $stmt->execute();
    $invoice = $stmt->fetch();
    
    if ($invoice) {
        echo "<h3>Invoice Details:</h3>\n";
        echo "ID: {$invoice['id']}\n";
        echo "Number: " . ($invoice['invoice_number'] ?: 'NOT SET') . "\n";
        echo "Status: {$invoice['status']}\n";
        echo "User: {$invoice['first_name']} {$invoice['last_name']}\n";
        echo "Period: {$invoice['period_month']}/{$invoice['period_year']}\n";
        echo "Amount: {$invoice['total_amount']}€\n";
        
        // Check invoice lines
        $stmt = $pdo->query("SELECT COUNT(*) FROM invoice_lines WHERE invoice_id = 319");
        $lineCount = $stmt->fetchColumn();
        echo "Invoice Lines: $lineCount\n";
        
        // Check retrocession entry
        $stmt = $pdo->query("SELECT COUNT(*) FROM retrocession_data_entry WHERE invoice_id = 319");
        $retroCount = $stmt->fetchColumn();
        echo "Retrocession Entries: $retroCount\n";
        
        echo "\n<h3>Options:</h3>\n";
        echo "<a href='?action=delete'>Delete this invoice and regenerate</a> | ";
        echo "<a href='?action=complete'>Try to complete this invoice</a>\n";
        
        if (isset($_GET['action'])) {
            if ($_GET['action'] == 'delete') {
                echo "\n<h3>Deleting Invoice #319...</h3>\n";
                
                // Delete in correct order
                $pdo->exec("DELETE FROM retrocession_data_entry WHERE invoice_id = 319");
                echo "✓ Deleted retrocession entries\n";
                
                $pdo->exec("DELETE FROM invoice_lines WHERE invoice_id = 319");
                echo "✓ Deleted invoice lines\n";
                
                $pdo->exec("DELETE FROM user_generated_invoices WHERE invoice_id = 319");
                echo "✓ Deleted generation tracking\n";
                
                $pdo->exec("DELETE FROM invoices WHERE id = 319");
                echo "✓ Deleted invoice\n";
                
                echo "\n<a href='test_retrocession_generation.php?user_id=1&month=7&year=2025'>Generate new invoice now</a>\n";
                
            } elseif ($_GET['action'] == 'complete') {
                echo "\n<h3>Checking Invoice Completeness...</h3>\n";
                
                // Check what's missing
                if (empty($invoice['invoice_number'])) {
                    echo "⚠️ Missing invoice number\n";
                    
                    // Generate invoice number
                    $prefix = 'FAC-RET-2025-';
                    $stmt = $pdo->query("
                        SELECT MAX(CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED)) as max_num
                        FROM invoices 
                        WHERE invoice_number LIKE '$prefix%'
                    ");
                    $maxNum = $stmt->fetchColumn() ?: 0;
                    $newNumber = $prefix . str_pad($maxNum + 1, 4, '0', STR_PAD_LEFT);
                    
                    $pdo->exec("UPDATE invoices SET invoice_number = '$newNumber' WHERE id = 319");
                    echo "✓ Set invoice number to: $newNumber\n";
                }
                
                if ($lineCount == 0) {
                    echo "⚠️ No invoice lines - this invoice is incomplete\n";
                    echo "Recommend deleting and regenerating\n";
                } else {
                    echo "✓ Invoice has $lineCount lines\n";
                    echo "\n<a href='/fit/public/invoices'>View in invoice list</a>\n";
                }
            }
        }
        
    } else {
        echo "Invoice #319 not found\n";
        echo "\n<a href='test_retrocession_generation.php?user_id=1&month=7&year=2025'>Generate new invoice</a>\n";
    }
    
    // Show other July 2025 invoices for Frank
    echo "\n<h3>Other July 2025 Invoices for Frank Huet:</h3>\n";
    $stmt = $pdo->query("
        SELECT i.id, i.invoice_number, i.status, i.total_htva + i.total_vat as total_amount
        FROM invoices i
        JOIN user_generated_invoices ugi ON i.id = ugi.invoice_id
        WHERE ugi.user_id = 1 
        AND ugi.period_month = 7 
        AND ugi.period_year = 2025
        AND ugi.invoice_type = 'RET'
    ");
    
    while ($row = $stmt->fetch()) {
        echo "Invoice #{$row['id']}: {$row['invoice_number']} - Status: {$row['status']} - {$row['total_amount']}€\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}