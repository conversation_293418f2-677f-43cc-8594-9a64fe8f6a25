<?php
/**
 * Debug what Invoice->create() returns
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Set HTML header
header('Content-Type: text/plain; charset=utf-8');

// Clear opcache
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✓ Opcache cleared\n\n";
}

echo "=== DEBUGGING Invoice->create() METHOD ===\n\n";

// Create a test invoice to see what create() returns
try {
    $invoice = new \App\Models\Invoice();
    
    // Check if create method exists
    if (method_exists($invoice, 'create')) {
        echo "✓ Invoice has create() method\n";
    } else {
        echo "✗ Invoice does NOT have create() method\n";
        echo "Available methods:\n";
        $methods = get_class_methods($invoice);
        foreach ($methods as $method) {
            if (strpos($method, 'create') !== false) {
                echo "  - $method\n";
            }
        }
    }
    
    // Check parent class
    echo "\nParent class: " . get_parent_class($invoice) . "\n";
    
    // Check if parent has create method
    $parentMethods = get_class_methods(get_parent_class($invoice));
    echo "\nParent class methods containing 'create':\n";
    foreach ($parentMethods as $method) {
        if (strpos($method, 'create') !== false) {
            echo "  - $method\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== CHECKING Model BASE CLASS ===\n";
$modelFile = file_get_contents(__DIR__ . '/app/Core/Model.php');
if (strpos($modelFile, 'function create') !== false) {
    echo "✓ Model class has a create() method\n";
    
    // Find the create method
    preg_match('/public\s+function\s+create\s*\([^)]*\)\s*{[^}]+}/s', $modelFile, $matches);
    if ($matches) {
        echo "\nCreate method found:\n";
        echo substr($matches[0], 0, 200) . "...\n";
    }
} else {
    echo "✗ Model class does NOT have a create() method\n";
}

echo "\n=== SOLUTION ===\n";
echo "It seems Invoice model might not have a standard create() method.\n";
echo "We should use the createInvoice() method instead, which is the proper method for creating invoices.\n";