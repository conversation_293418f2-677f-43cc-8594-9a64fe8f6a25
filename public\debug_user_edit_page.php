<?php
// Debug user edit page data

require_once '../app/config/bootstrap.php';

$userId = 14;

// Simulate what the controller does
$user = \App\Models\User::getWithGroups($userId);
$groups = \App\Models\UserGroup::getAll();
$userGroupIds = array_map('intval', array_column($user['groups'], 'id'));

// Check if user is in coach group
$coachGroupId = Flight::get('coach_group_id') ?? 24;
$isCoach = in_array($coachGroupId, $userGroupIds);

echo "<h1>Debug User Edit Page - User ID: $userId</h1>";

echo "<h2>1. User Groups Data:</h2>";
echo "<p>User groups from database:</p>";
echo "<pre>";
print_r($user['groups']);
echo "</pre>";

echo "<p>userGroupIds array (after array_map intval):</p>";
echo "<pre>";
print_r($userGroupIds);
echo "</pre>";

echo "<h2>2. All Groups:</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Group ID</th><th>Type</th><th>Group Name</th><th>Should be checked?</th></tr>";
foreach ($groups as $group) {
    $groupId = $group['id'];
    $groupIdType = gettype($groupId);
    $inArray = in_array($groupId, $userGroupIds);
    $inArrayInt = in_array((int)$groupId, $userGroupIds);
    
    echo "<tr>";
    echo "<td>$groupId</td>";
    echo "<td>$groupIdType</td>";
    echo "<td>{$group['name']}</td>";
    echo "<td>in_array: " . ($inArray ? 'YES' : 'NO') . " | in_array(int): " . ($inArrayInt ? 'YES' : 'NO') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>3. Coach Check:</h2>";
echo "<p>Coach Group ID: $coachGroupId</p>";
echo "<p>Is user a coach? " . ($isCoach ? 'YES' : 'NO') . "</p>";

echo "<h2>4. Courses Data:</h2>";
if ($isCoach) {
    $courses = \App\Models\UserCourse::getByUserId($userId);
    echo "<p>Courses found: " . count($courses) . "</p>";
    echo "<pre>";
    print_r($courses);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>User is not a coach, courses section won't show</p>";
}

echo "<h2>5. What the template receives:</h2>";
echo "<p>Variables passed to template:</p>";
echo "<ul>";
echo "<li>userGroups: " . (is_array($userGroupIds) ? "Array with " . count($userGroupIds) . " items" : "Not an array") . "</li>";
echo "<li>is_coach: " . ($isCoach ? 'true' : 'false') . "</li>";
echo "<li>courses: " . ($isCoach ? "Array with " . count($courses ?? []) . " items" : "Not set") . "</li>";
echo "</ul>";

// Test Twig 'in' operator simulation
echo "<h2>6. Testing 'in' operator logic:</h2>";
foreach ($groups as $group) {
    $result = in_array($group['id'], $userGroupIds);
    $resultStr = in_array((string)$group['id'], array_map('strval', $userGroupIds));
    echo "<p>Group {$group['id']} ({$group['name']}): ";
    echo "in_array = " . ($result ? 'YES' : 'NO') . ", ";
    echo "in_array(string) = " . ($resultStr ? 'YES' : 'NO');
    echo "</p>";
}

// Direct SQL check
echo "<h2>7. Direct SQL verification:</h2>";
$db = Flight::db();
$stmt = $db->prepare("
    SELECT ugm.group_id, ug.name 
    FROM user_group_members ugm
    JOIN user_groups ug ON ugm.group_id = ug.id
    WHERE ugm.user_id = ?
");
$stmt->execute([$userId]);
$directGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<pre>";
print_r($directGroups);
echo "</pre>";
?>