<?php
/**
 * Quick Email Test
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

$testEmail = $_GET['email'] ?? '<EMAIL>';

echo "<h1>Quick Email Test</h1>";
echo "<pre>";

try {
    $mail = new PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host       = 'localhost';
    $mail->Port       = 1025;
    $mail->SMTPAuth   = false;
    $mail->SMTPSecure = false;
    $mail->SMTPAutoTLS = false;
    
    // Recipients
    $mail->setFrom('<EMAIL>', 'Fit360 AdminDesk');
    $mail->addAddress($testEmail);
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Test Email - ' . date('Y-m-d H:i:s');
    $mail->Body    = '<h1>Test Email</h1><p>If you see this message, email sending is working!</p>';
    $mail->AltBody = 'If you see this message, email sending is working!';
    
    $mail->send();
    echo "✓ Email sent successfully to: $testEmail\n\n";
    echo "Check Mailhog at: http://localhost:8025\n";
    echo "If port 8025 doesn't work, it might be running on a different port.\n\n";
    
    // Try to check if Mailhog API is accessible
    $ch = curl_init('http://localhost:8025/api/v2/messages');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 2);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        echo "✓ Mailhog web interface is accessible at http://localhost:8025\n";
    } else {
        echo "⚠️  Cannot access Mailhog web interface. It might be running on a different port.\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error sending email: " . $e->getMessage() . "\n";
    echo "Details: " . $mail->ErrorInfo . "\n";
}

echo "</pre>";

echo '<p><a href="?email=' . urlencode($testEmail) . '">Send Another Test</a></p>';
echo '<p>Or test with different email: <a href="?email=<EMAIL>">?email=<EMAIL></a></p>';
?>