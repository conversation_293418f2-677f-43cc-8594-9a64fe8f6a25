{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.retrocession') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.retrocession') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/retrocession/rate-profiles" class="btn btn-info">
                <i class="bi bi-calculator me-2"></i>{{ __('retrocession.rate_profiles') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkGenerateModal">
                <i class="bi bi-lightning me-2"></i>{{ __('retrocession.bulk_generate') }}
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('retrocession.active_practitioners') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.active_practitioners|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('retrocession.current_month_total') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.current_month_total|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-euro text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('retrocession.pending_entries') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.pending_entries|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-hourglass-split text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('retrocession.rate_profiles') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.rate_profiles|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-percent text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Period Selection -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/retrocession" class="row g-3">
                <div class="col-md-3">
                    <label for="period_month" class="form-label">{{ __('retrocession.period') }}</label>
                    <select class="form-select" id="period_month" name="month">
                        {% for i in 1..12 %}
                            <option value="{{ i }}" {{ current_month == i ? 'selected' : '' }}>
                                {{ ('2024-' ~ i ~ '-01')|date('F') }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="period_year" class="form-label">&nbsp;</label>
                    <select class="form-select" id="period_year" name="year">
                        {% for year in available_years %}
                            <option value="{{ year }}" {{ current_year == year ? 'selected' : '' }}>{{ year }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="filter_status" class="form-label">{{ __('common.status') }}</label>
                    <select class="form-select" id="filter_status" name="status">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="pending" {{ filters.status == 'pending' ? 'selected' : '' }}>{{ __('retrocession.pending') }}</option>
                        <option value="confirmed" {{ filters.status == 'confirmed' ? 'selected' : '' }}>{{ __('retrocession.confirmed') }}</option>
                        <option value="invoiced" {{ filters.status == 'invoiced' ? 'selected' : '' }}>{{ __('retrocession.invoiced') }}</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-2"></i>{{ __('common.apply_filters') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Practitioners List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ __('retrocession.practitioner_summary') }}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ __('retrocession.practitioner') }}</th>
                            <th>{{ __('retrocession.rate_profile') }}</th>
                            <th>{{ __('retrocession.services_count') }}</th>
                            <th>{{ __('retrocession.gross_amount') }}</th>
                            <th>{{ __('retrocession.retrocession_amount') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for practitioner in practitioners %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        <span class="avatar-text rounded-circle bg-primary">
                                            {{ practitioner.name|first|upper }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ practitioner.name }}</div>
                                        <small class="text-muted">{{ practitioner.specialty }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if practitioner.rate_profile %}
                                    <span class="badge bg-info">{{ practitioner.rate_profile.name }}</span>
                                {% else %}
                                    <span class="text-muted">{{ __('retrocession.no_profile') }}</span>
                                {% endif %}
                            </td>
                            <td>{{ practitioner.services_count }}</td>
                            <td>{{ currency }}{{ practitioner.gross_amount|number_format(2, ',', ' ') }}</td>
                            <td>
                                <strong class="text-success">
                                    {{ currency }}{{ practitioner.retrocession_amount|number_format(2, ',', ' ') }}
                                </strong>
                                {% if practitioner.rate_profile %}
                                    <small class="text-muted">({{ practitioner.rate_profile.rate }} %)</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if practitioner.status == 'pending' %}
                                    <span class="badge bg-warning">{{ __('retrocession.pending') }}</span>
                                {% elseif practitioner.status == 'confirmed' %}
                                    <span class="badge bg-info">{{ __('retrocession.confirmed') }}</span>
                                {% elseif practitioner.status == 'invoiced' %}
                                    <span class="badge bg-success">{{ __('retrocession.invoiced') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown" data-bs-boundary="viewport" data-bs-flip="true">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% if practitioner.status == 'pending' %}
                                        <li>
                                            <a class="dropdown-item" href="{{ base_url }}/retrocession/{{ practitioner.id }}/data-entry">
                                                <i class="bi bi-pencil me-2"></i>{{ __('retrocession.data_entry') }}
                                            </a>
                                        </li>
                                        {% endif %}
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="viewDetails({{ practitioner.id }})">
                                                <i class="bi bi-eye me-2"></i>{{ __('common.view_details') }}
                                            </a>
                                        </li>
                                        {% if practitioner.status == 'confirmed' %}
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="generateInvoice({{ practitioner.id }})">
                                                <i class="bi bi-file-earmark-text me-2"></i>{{ __('retrocession.generate_invoice') }}
                                            </a>
                                        </li>
                                        {% endif %}
                                        {% if practitioner.invoice_id %}
                                        <li>
                                            <a class="dropdown-item" href="{{ base_url }}/invoices/{{ practitioner.invoice_id }}">
                                                <i class="bi bi-receipt me-2"></i>{{ __('retrocession.view_invoice') }}
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center py-4 text-muted">
                                {{ __('retrocession.no_data_found') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if practitioners|length > 0 %}
                    <tfoot>
                        <tr class="fw-bold">
                            <td colspan="3">{{ __('common.total') }}</td>
                            <td>{{ currency }}{{ totals.gross_amount|number_format(2, ',', ' ') }}</td>
                            <td class="text-success">{{ currency }}{{ totals.retrocession_amount|number_format(2, ',', ' ') }}</td>
                            <td colspan="2"></td>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Generate Modal -->
<div class="modal fade" id="bulkGenerateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/retrocession/bulk-generate">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('retrocession.bulk_generate_invoices') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        {{ __('retrocession.bulk_generate_info') }}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('retrocession.select_practitioners') }}</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="selectAll" checked>
                            <label class="form-check-label" for="selectAll">
                                {{ __('common.select_all') }}
                            </label>
                        </div>
                        <hr>
                        <div style="max-height: 300px; overflow-y: auto;">
                            {% for practitioner in confirmed_practitioners %}
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input practitioner-check" 
                                       id="pract_{{ practitioner.id }}" name="practitioner_ids[]" 
                                       value="{{ practitioner.id }}" checked>
                                <label class="form-check-label" for="pract_{{ practitioner.id }}">
                                    {{ practitioner.name }} - {{ currency }}{{ practitioner.retrocession_amount|number_format(2, ',', ' ') }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoice_date" class="form-label">{{ __('invoices.issue_date') }}</label>
                        <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                               value="{{ 'now'|date('Y-m-d') }}" required>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-lightning me-2"></i>{{ __('retrocession.generate_invoices') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Select all practitioners
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.practitioner-check');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function viewDetails(id) {
    // Show details in modal or redirect
    window.location.href = '{{ base_url }}/retrocession/' + id + '/details';
}

function generateInvoice(id) {
    if (confirm('{{ __("retrocession.generate_invoice_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/retrocession/' + id + '/generate-invoice';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
/* Fix for dropdown positioning in tables */
.table-responsive .dropdown {
    position: static;
}

.table-responsive .dropdown-menu {
    position: absolute !important;
    transform: translate3d(0px, 0px, 0px) !important;
    will-change: transform !important;
}
</style>
{% endblock %}