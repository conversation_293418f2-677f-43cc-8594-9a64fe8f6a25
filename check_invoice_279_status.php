<?php
require_once 'app/config/bootstrap.php';

$invoiceId = 279;

try {
    $db = Flight::db();
    
    echo "=== Invoice #$invoiceId Status Check ===\n\n";
    
    // Get invoice details
    $stmt = $db->prepare("SELECT id, invoice_number, status, issue_date, due_date, client_id, user_id FROM invoices WHERE id = ?");
    $stmt->execute([$invoiceId]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "Invoice found:\n";
        echo "- Number: " . $invoice['invoice_number'] . "\n";
        echo "- Status: " . $invoice['status'] . "\n";
        echo "- Issue Date: " . $invoice['issue_date'] . "\n";
        echo "- Due Date: " . $invoice['due_date'] . "\n";
        echo "- Client ID: " . ($invoice['client_id'] ?? 'null') . "\n";
        echo "- User ID: " . ($invoice['user_id'] ?? 'null') . "\n\n";
        
        if ($invoice['status'] !== 'draft') {
            echo "⚠️ WARNING: This invoice has status '" . $invoice['status'] . "'\n";
            echo "Only invoices with status 'draft' can be sent.\n\n";
            echo "To fix this, you can:\n";
            echo "1. Change the invoice back to draft status\n";
            echo "2. Or remove the status check from the send() function\n";
        } else {
            echo "✓ Invoice is in draft status and can be sent\n";
        }
    } else {
        echo "Invoice not found!\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}