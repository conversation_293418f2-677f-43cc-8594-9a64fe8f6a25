<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Clean Rebuild of RET Types</h2>";
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Step 1: Backup current state
        echo "<h3>Step 1: Current State</h3>";
        $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code LIKE '%ret%' OR prefix LIKE '%RET%'");
        $backup = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Found " . count($backup) . " RET-related entries to process.</p>";
        
        // Step 2: Delete all RET-related entries
        echo "<h3>Step 2: Cleaning Up</h3>";
        $stmt = $db->prepare("
            DELETE FROM config_invoice_types 
            WHERE code LIKE '%ret%' 
               OR prefix LIKE '%RET%'
               OR code IN ('retr', 'retrocession', 'retrocession_5', 'retrocession_10', 'retrocession_25', 'retrocession_30')
        ");
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        echo "<p>Deleted $deletedCount existing RET-related entries.</p>";
        
        // Step 3: Create fresh entries
        echo "<h3>Step 3: Creating Fresh Entries</h3>";
        
        $newTypes = [
            [
                'code' => 'ret',
                'prefix' => 'RET',
                'name' => json_encode(['fr' => 'Rétrocession', 'en' => 'Retrocession']),
                'color' => '#17a2b8',
                'is_active' => 1
            ],
            [
                'code' => 'ret25',
                'prefix' => 'FAC-RET25',
                'name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%']),
                'color' => '#28a745',
                'is_active' => 1
            ],
            [
                'code' => 'ret30',
                'prefix' => 'FAC-RET30',
                'name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%']),
                'color' => '#007bff',
                'is_active' => 1
            ]
        ];
        
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
            VALUES (:code, :prefix, :name, :color, :is_active)
        ");
        
        foreach ($newTypes as $type) {
            $stmt->execute($type);
            $name = json_decode($type['name'], true);
            echo "<p style='color: green;'>✓ Created {$type['code']} - {$type['prefix']} - {$name['fr']}</p>";
        }
        
        // Step 4: Ensure invoice_types table is synchronized
        echo "<h3>Step 4: Synchronizing invoice_types Table</h3>";
        
        $invoiceTypes = [
            ['code' => 'RET', 'name' => 'Rétrocession'],
            ['code' => 'RET25', 'name' => 'Rétrocession 5%'],
            ['code' => 'RET30', 'name' => 'Rétrocession 10%']
        ];
        
        foreach ($invoiceTypes as $type) {
            // Check if exists
            $checkStmt = $db->prepare("SELECT id FROM invoice_types WHERE code = :code");
            $checkStmt->execute(['code' => $type['code']]);
            
            if (!$checkStmt->fetch()) {
                $insertStmt = $db->prepare("
                    INSERT INTO invoice_types (code, name, is_active)
                    VALUES (:code, :name, 1)
                ");
                $insertStmt->execute($type);
                echo "<p style='color: green;'>✓ Created {$type['code']} in invoice_types</p>";
            } else {
                echo "<p>✓ {$type['code']} already exists in invoice_types</p>";
            }
        }
        
        // Commit transaction
        $db->commit();
        echo "<h3 style='color: green;'>✓ Transaction Committed Successfully</h3>";
        
    } catch (Exception $e) {
        $db->rollBack();
        throw $e;
    }
    
    // Step 5: Verify final state
    echo "<h3>Step 5: Verification</h3>";
    
    $stmt = $db->query("
        SELECT cit.*, it.id as invoice_type_id
        FROM config_invoice_types cit
        LEFT JOIN invoice_types it ON it.code = UPPER(cit.code)
        WHERE cit.code IN ('ret', 'ret25', 'ret30')
        ORDER BY 
            CASE cit.code 
                WHEN 'ret' THEN 1 
                WHEN 'ret25' THEN 2 
                WHEN 'ret30' THEN 3 
            END
    ");
    $finalTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='width: 100%; max-width: 900px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>Code</th><th>Prefix</th><th>Name</th><th>Color</th><th>Invoice Type</th><th>Status</th>";
    echo "</tr>";
    
    $allGood = true;
    foreach ($finalTypes as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $status = '✓';
        $statusColor = 'green';
        
        if (!$row['invoice_type_id']) {
            $status = '⚠️ Missing in invoice_types';
            $statusColor = 'orange';
            $allGood = false;
        }
        
        $rowStyle = '';
        if ($row['code'] == 'ret25') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['code'] == 'ret30') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td><span style='display: inline-block; width: 20px; height: 20px; background-color: {$row['color']}; border: 1px solid #ccc;'></span></td>";
        echo "<td>" . ($row['invoice_type_id'] ? "ID: {$row['invoice_type_id']}" : "Not linked") . "</td>";
        echo "<td style='color: $statusColor;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test with Frank
    echo "<h3>Testing with Frank Huet</h3>";
    $stmt = $db->prepare("
        SELECT secretary_value 
        FROM user_retrocession_settings 
        WHERE user_id = 1 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    $secretaryValue = $settings['secretary_value'] ?? 10;
    
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Secretary Fee:</strong> {$secretaryValue}%</p>";
    echo "<p><strong>Expected Code:</strong> " . ($secretaryValue == 5 ? 'ret25' : 'ret30') . "</p>";
    echo "<p><strong>Expected Invoice:</strong> <span style='font-size: 1.2em; color: " . ($secretaryValue == 5 ? '#28a745' : '#007bff') . ";'>";
    echo ($secretaryValue == 5 ? 'FAC-RET25' : 'FAC-RET30') . "-2025-XXXX</span></p>";
    echo "</div>";
    
    // Final message
    if ($allGood && count($finalTypes) == 3) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Configuration Complete!</h3>";
        echo "<p>The retrocession invoice types are now properly configured:</p>";
        echo "<ul style='margin-bottom: 0;'>";
        echo "<li><strong>FAC-RET25-2025-XXXX</strong> for 5% secretary fee</li>";
        echo "<li><strong>FAC-RET30-2025-XXXX</strong> for 10% secretary fee</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p style='text-align: center;'>";
        echo "<a href='/fit/public/invoices/bulk-generation?tab=retrocession' class='btn btn-success btn-lg' style='padding: 12px 40px; font-size: 1.2em; text-decoration: none; background-color: #28a745; color: white; border-radius: 5px; display: inline-block;'>🚀 Test Bulk Generation Now</a>";
        echo "</p>";
    } else {
        echo "<p style='color: red;'>⚠️ Configuration may need additional attention. Please check the table above.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
        echo "<p style='color: red;'>Transaction rolled back.</p>";
    }
}