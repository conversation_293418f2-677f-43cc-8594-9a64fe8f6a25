<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Rebuilding RET Types</h2>";
    
    // First, check what we have
    echo "<h3>Current state in config_invoice_types:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code LIKE '%ret%' OR prefix LIKE '%RET%' ORDER BY id");
    $existing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($existing)) {
        echo "<p style='color: orange;'>No RET types found. Creating from scratch...</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
        foreach ($existing as $row) {
            $name = json_decode($row['name'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['code']}</td>";
            echo "<td>{$row['prefix']}</td>";
            echo "<td>{$displayName}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Ensure we have the three required types
    echo "<h3>Ensuring required types exist:</h3>";
    
    $requiredTypes = [
        [
            'code' => 'ret',
            'prefix' => 'RET',
            'name' => json_encode(['fr' => 'Rétrocession', 'en' => 'Retrocession']),
            'color' => '#17a2b8'
        ],
        [
            'code' => 'ret25',
            'prefix' => 'FAC-RET25',
            'name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%']),
            'color' => '#28a745'
        ],
        [
            'code' => 'ret30',
            'prefix' => 'FAC-RET30',
            'name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%']),
            'color' => '#007bff'
        ]
    ];
    
    foreach ($requiredTypes as $type) {
        // Check if exists
        $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = :code");
        $stmt->execute(['code' => $type['code']]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            // Update to ensure correct values
            $stmt = $db->prepare("
                UPDATE config_invoice_types 
                SET prefix = :prefix, name = :name, color = :color, is_active = 1
                WHERE code = :code
            ");
            $stmt->execute([
                'prefix' => $type['prefix'],
                'name' => $type['name'],
                'color' => $type['color'],
                'code' => $type['code']
            ]);
            echo "<p style='color: blue;'>✓ Updated {$type['code']} with prefix {$type['prefix']}</p>";
        } else {
            // Create new
            $stmt = $db->prepare("
                INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
                VALUES (:code, :prefix, :name, :color, 1)
            ");
            $stmt->execute($type);
            echo "<p style='color: green;'>✓ Created {$type['code']} with prefix {$type['prefix']}</p>";
        }
    }
    
    // Verify final configuration
    echo "<h3>Final Configuration:</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30') 
        ORDER BY 
            CASE code 
                WHEN 'ret' THEN 1 
                WHEN 'ret25' THEN 2 
                WHEN 'ret30' THEN 3 
            END
    ");
    $finalTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='width: 100%; max-width: 800px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>Code</th><th>Prefix</th><th>Name</th><th>Color</th><th>Purpose</th>";
    echo "</tr>";
    
    foreach ($finalTypes as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $purpose = '';
        $rowStyle = '';
        if ($row['code'] == 'ret') {
            $purpose = 'Base type (fallback)';
        } elseif ($row['code'] == 'ret25') {
            $purpose = '5% secretary fee';
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['code'] == 'ret30') {
            $purpose = '10% secretary fee';
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td><span style='display: inline-block; width: 20px; height: 20px; background-color: {$row['color']}; border: 1px solid #ccc;'></span> {$row['color']}</td>";
        echo "<td>{$purpose}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test logic
    echo "<h3>Testing Invoice Generation Logic:</h3>";
    
    // Test Frank (5%)
    echo "<div style='background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #28a745;'>";
    echo "<h4 style='margin-top: 0;'>Frank Huet (5% secretary):</h4>";
    
    $stmt = $db->prepare("SELECT prefix FROM config_invoice_types WHERE code = 'ret25'");
    $stmt->execute();
    $type = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($type) {
        echo "<p>✓ Will generate: <strong style='color: #28a745; font-size: 1.2em;'>{$type['prefix']}-2025-0001</strong></p>";
    } else {
        echo "<p style='color: red;'>✗ ERROR: ret25 not found!</p>";
    }
    echo "</div>";
    
    // Test default (10%)
    echo "<div style='background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff;'>";
    echo "<h4 style='margin-top: 0;'>User with 10% secretary (default):</h4>";
    
    $stmt = $db->prepare("SELECT prefix FROM config_invoice_types WHERE code = 'ret30'");
    $stmt->execute();
    $type = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($type) {
        echo "<p>✓ Will generate: <strong style='color: #007bff; font-size: 1.2em;'>{$type['prefix']}-2025-0001</strong></p>";
    } else {
        echo "<p style='color: red;'>✗ ERROR: ret30 not found!</p>";
    }
    echo "</div>";
    
    // Success message
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✓ Configuration Complete!</h3>";
    echo "<p style='margin-bottom: 10px;'>The retrocession invoice system is now properly configured.</p>";
    echo "<p style='margin-bottom: 0;'>Ready to generate invoices with the correct prefixes based on secretary percentage.</p>";
    echo "</div>";
    
    echo "<p style='text-align: center;'>";
    echo "<a href='/fit/public/invoices/bulk-generation?tab=retrocession' class='btn btn-primary btn-lg' style='padding: 10px 30px; font-size: 1.1em;'>🚀 Test Bulk Generation</a>";
    echo "</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}