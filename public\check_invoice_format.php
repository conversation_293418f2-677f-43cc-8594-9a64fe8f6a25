<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CHECKING INVOICE NUMBER FORMAT ===\n\n";
    
    // Get invoice document type
    $stmt = $pdo->prepare("SELECT * FROM document_types WHERE code = 'invoice'");
    $stmt->execute();
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($docType) {
        echo "Invoice Document Type:\n";
        echo "- ID: {$docType['id']}\n";
        echo "- Code: {$docType['code']}\n";
        echo "- Prefix: {$docType['prefix']}\n";
        echo "- Counter Type: {$docType['counter_type']}\n";
        echo "- Number Format: " . ($docType['number_format'] ?: '(empty - using default)') . "\n\n";
        
        if (empty($docType['number_format'])) {
            echo "DEFAULT FORMAT IS BEING USED:\n";
            echo "- For yearly counter: PREFIX-YYYY-NNNNN (5 digits)\n";
            echo "- Example: FAC-2025-00186\n\n";
            
            echo "To use 4 digits instead, we need to set a custom format.\n";
            echo "Would set number_format to: {PREFIX}-{YYYY}-{NNNN}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}