<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\UserCourse;
use App\Models\UserRetrocessionSetting;
use Flight;

class UserController extends Controller
{
    /**
     * Display users list
     */
    public function index(Request $request, Response $response)
    {
        $users = User::getAllWithGroups();
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'users/index-modern';
        
        return $this->render($viewName, [
            'title' => __('users.user_management'),
            'users' => $users
        ]);
    }
    
    /**
     * Show create user form
     */
    public function create(Request $request, Response $response)
    {
        $groups = UserGroup::getAll();
        
        $template = $this->getTemplate();
        $viewName = 'users/form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.create_user'),
            'user' => null,
            'groups' => $groups,
            'userGroups' => []
        ]);
    }
    
    /**
     * Store new user
     */
    public function store(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            $data = $_POST;
            
            // Debug: Log the incoming data
            error_log('User create - Raw POST data: ' . json_encode($data));
            
            // Validate required fields (username will be generated if empty)
            $required = ['email', 'password', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Generate username if not provided
            if (empty($data['username'])) {
                // Clean the first name
                $cleanFirstName = strtolower($data['first_name']);
                // Remove accents
                $cleanFirstName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanFirstName);
                // Remove special characters and spaces
                $cleanFirstName = preg_replace('/[^a-z0-9]/', '', $cleanFirstName);
                
                // Clean the last name for potential use
                $cleanLastName = strtolower($data['last_name']);
                $cleanLastName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanLastName);
                $cleanLastName = preg_replace('/[^a-z0-9]/', '', $cleanLastName);
                
                if (!empty($cleanFirstName)) {
                    // Try first name only
                    $username = $cleanFirstName;
                    
                    // Check if username exists
                    if (User::findByUsername($username)) {
                        // Try first name + first letter of last name
                        if (!empty($cleanLastName)) {
                            $username = $cleanFirstName . substr($cleanLastName, 0, 1);
                            
                            // If still exists, add numbers
                            $counter = 2;
                            $baseUsername = $username;
                            while (User::findByUsername($username)) {
                                $username = $baseUsername . $counter;
                                $counter++;
                            }
                        } else {
                            // No last name, just add numbers
                            $counter = 2;
                            while (User::findByUsername($username)) {
                                $username = $cleanFirstName . $counter;
                                $counter++;
                            }
                        }
                    }
                    
                    $data['username'] = $username;
                }
            }
            
            // Validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new \Exception(__('users.username_format'));
            }
            
            // Validate username length
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new \Exception(__('users.username_length'));
            }
            
            // Generate default email if not provided or not @fit-360.lu
            if (empty($data['email']) || !str_ends_with($data['email'], '@fit-360.lu')) {
                // Clean the first name
                $cleanName = strtolower($data['first_name']);
                // Remove accents
                $cleanName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanName);
                // Remove special characters and spaces
                $cleanName = preg_replace('/[^a-z0-9]/', '', $cleanName);
                
                if (!empty($cleanName)) {
                    // Check for uniqueness
                    $baseEmail = $cleanName . '@fit-360.lu';
                    $email = $baseEmail;
                    $counter = 1;
                    
                    while (User::findByEmail($email)) {
                        $counter++;
                        $email = $cleanName . $counter . '@fit-360.lu';
                    }
                    
                    $data['email'] = $email;
                }
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('users.invalid_email'));
            }
            
            // Validate password length
            if (strlen($data['password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Set defaults only if the fields are truly empty
            $data['language'] = (!empty($data['language'])) ? $data['language'] : 'fr';
            $data['timezone'] = (!empty($data['timezone'])) ? $data['timezone'] : 'Europe/Luxembourg';
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;
            
            // Only set address defaults if the fields are not provided or truly empty
            if (!isset($data['address']) || $data['address'] === '') {
                $data['address'] = '15, am Pëtz';
            }
            if (!isset($data['postal_code']) || $data['postal_code'] === '') {
                $data['postal_code'] = 'L-9579';
            }
            if (!isset($data['city']) || $data['city'] === '') {
                $data['city'] = 'Weidingen';
            }
            if (!isset($data['country']) || $data['country'] === '') {
                $data['country'] = 'LU';
            }
            
            // Debug: Log processed data
            error_log('User create - Processed data: ' . json_encode([
                'address' => $data['address'] ?? 'null',
                'postal_code' => $data['postal_code'] ?? 'null', 
                'city' => $data['city'] ?? 'null',
                'country' => $data['country'] ?? 'null'
            ]));
            
            // Handle avatar upload
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $data['avatar'] = $this->uploadAvatar($_FILES['avatar']);
            }
            
            // Create user
            $userId = User::create($data);
            
            // Assign groups
            if (!empty($data['groups'])) {
                User::syncGroups($userId, $data['groups']);
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.user_created'),
                'data' => ['id' => $userId]
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(Request $request, Response $response, $id)
    {
        $user = User::getWithGroups($id);
        
        if (!$user) {
            Flight::redirect('{{ base_url }}/users');
            return;
        }
        
        $groups = UserGroup::getAll();
        $userGroupIds = array_column($user['groups'], 'id');
        
        // Get active retrocession settings for user
        try {
            $retrocessionSettings = \App\Models\UserRetrocessionSetting::getActiveSettingsForUser($id);
        } catch (\Exception $e) {
            // Table might not exist, set to null
            $retrocessionSettings = null;
        }
        
        // Get financial obligations for user
        $financialObligations = User::getCurrentFinancialObligations($id);
        
        // Check if current user can edit financial obligations
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $canEditFinancial = User::canEditFinancialObligations($currentUserId);
        
        // Check if user is in coach group and get courses
        $coachGroupId = (string)(Flight::get('coach_group_id') ?? 24);
        $isCoach = in_array($coachGroupId, $userGroupIds);
        $courses = [];
        
        if ($isCoach) {
            $courses = UserCourse::getByUserId($id);
        }
        
        // Check if user is in practitioner group (Kiné)
        $practitionerGroupId = (string)(Flight::get('practitioner_group_id') ?? 4);
        $isPractitioner = in_array($practitionerGroupId, $userGroupIds);
        
        // Check if current user can edit retrocession settings
        $canEditRetrocession = User::isAdmin($currentUserId) || User::isManager($currentUserId);
        
        // Get monthly retrocession amounts if practitioner
        $monthlyAmounts = [];
        $monthlyInvoices = [];
        if ($isPractitioner) {
            try {
                $db = Flight::db();
                $stmt = $db->prepare("
                    SELECT month, cns_amount, patient_amount, is_active 
                    FROM user_monthly_retrocession_amounts 
                    WHERE user_id = :user_id
                    ORDER BY month
                ");
                $stmt->execute(['user_id' => $id]);
                $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                
                // Create indexed array by month
                foreach ($results as $row) {
                    $monthlyAmounts[$row['month']] = $row;
                }
                
                // Get existing retrocession invoices for current year
                $currentYear = date('Y');
                $clientStmt = $db->prepare("SELECT id FROM clients WHERE user_id = :user_id AND is_practitioner = 1 LIMIT 1");
                $clientStmt->execute(['user_id' => $id]);
                $clientData = $clientStmt->fetch(\PDO::FETCH_ASSOC);
                
                if ($clientData) {
                    $invoiceStmt = $db->prepare("
                        SELECT i.id, i.invoice_number, i.status, 
                               rde.period_month, rde.period_year
                        FROM invoices i
                        INNER JOIN retrocession_data_entry rde ON rde.invoice_id = i.id
                        WHERE i.client_id = :client_id
                        AND rde.period_year = :year
                        ORDER BY rde.period_month
                    ");
                    $invoiceStmt->execute([
                        'client_id' => $clientData['id'],
                        'year' => $currentYear
                    ]);
                    while ($invoice = $invoiceStmt->fetch(\PDO::FETCH_ASSOC)) {
                        $monthlyInvoices[$invoice['period_month']] = $invoice;
                    }
                }
            } catch (\Exception $e) {
                // Table might not exist
                error_log("Failed to get monthly amounts: " . $e->getMessage());
            }
        }
        
        $template = $this->getTemplate();
        $viewName = 'users/form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.edit_user'),
            'user' => $user,
            'groups' => $groups,
            'userGroups' => $userGroupIds,
            'retrocession_settings' => $retrocessionSettings,
            'financial_obligations' => $financialObligations,
            'can_edit_financial' => $canEditFinancial,
            'currency' => 'EUR',
            'is_coach' => $isCoach,
            'courses' => $courses,
            'is_practitioner' => $isPractitioner,
            'can_edit_retrocession' => $canEditRetrocession,
            'monthly_amounts' => $monthlyAmounts,
            'monthly_invoices' => $monthlyInvoices
        ]);
    }
    
    /**
     * Update user
     */
    public function update(Request $request, Response $response, $id)
    {
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
        }
        
        try {
            $data = $_POST;
            
            // Validate required fields
            $required = ['username', 'email', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new \Exception(__('users.username_format'));
            }
            
            // Validate username length
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new \Exception(__('users.username_length'));
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('users.invalid_email'));
            }
            
            // If password is provided, validate it
            if (!empty($data['password']) && strlen($data['password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Set defaults for update - don't override existing values with defaults
            $data['language'] = (!empty($data['language'])) ? $data['language'] : 'fr';
            $data['timezone'] = (!empty($data['timezone'])) ? $data['timezone'] : 'Europe/Luxembourg';
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;
            
            // For update, only set address fields if they are provided in the form
            // Don't set defaults as user might want to clear them
            
            // Handle avatar upload
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $data['avatar'] = $this->uploadAvatar($_FILES['avatar'], $id);
            }
            
            // Handle avatar removal
            if (isset($data['remove_avatar']) && $data['remove_avatar']) {
                $this->removeAvatar($id);
                $data['avatar'] = null;
            }
            
            // Update user
            User::update($id, $data);
            
            // Update groups
            $groups = $data['groups'] ?? [];
            User::syncGroups($id, $groups);
            
            // Handle retrocession settings if provided
            if (isset($data['retrocession']) && is_array($data['retrocession'])) {
                $retrocession = $data['retrocession'];
                
                // Validate retrocession data
                if (!empty($retrocession['valid_from'])) {
                    // First, end any existing active settings that would overlap
                    $validFrom = $retrocession['valid_from'];
                    $validFromDate = new \DateTime($validFrom);
                    $validFromDate->modify('-1 day');
                    $endDate = $validFromDate->format('Y-m-d');
                    
                    // End existing active settings
                    $db = \Flight::db();
                    $updateSql = "UPDATE user_retrocession_settings 
                                 SET valid_to = :end_date 
                                 WHERE user_id = :user_id 
                                 AND is_active = 1 
                                 AND (valid_to IS NULL OR valid_to >= :valid_from)";
                    $updateStmt = $db->prepare($updateSql);
                    $updateStmt->execute([
                        'end_date' => $endDate,
                        'user_id' => $id,
                        'valid_from' => $validFrom
                    ]);
                    
                    // Prepare retrocession data
                    $retrocessionData = [
                        'user_id' => $id,
                        'cns_type' => $retrocession['cns_type'] ?? 'percentage',
                        'cns_value' => floatval($retrocession['cns_value'] ?? 20),
                        'cns_label' => $retrocession['cns_label'] ?? 'RÉTROCESSION CNS',
                        'patient_type' => $retrocession['patient_type'] ?? 'percentage',
                        'patient_value' => floatval($retrocession['patient_value'] ?? 20),
                        'patient_label' => $retrocession['patient_label'] ?? 'RÉTROCESSION PATIENTS',
                        'secretary_type' => $retrocession['secretary_type'] ?? 'percentage',
                        'secretary_value' => floatval($retrocession['secretary_value'] ?? 10),
                        'secretary_label' => $retrocession['secretary_label'] ?? 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL',
                        'ceiling_enabled' => isset($retrocession['ceiling_enabled']) ? 1 : 0,
                        'ceiling_amount' => floatval($retrocession['ceiling_amount'] ?? 5000),
                        'valid_from' => $retrocession['valid_from'],
                        'valid_to' => $retrocession['valid_to'] ?: null,
                        'notes' => $retrocession['notes'] ?? null,
                        'created_by' => $_SESSION['user_id'],
                        'is_active' => 1
                    ];
                    
                    // Create new retrocession setting
                    \App\Models\UserRetrocessionSetting::create($retrocessionData);
                }
            }
            
            // Handle monthly retrocession amounts if provided
            if (isset($data['monthly_amounts']) && is_array($data['monthly_amounts'])) {
                $db = \Flight::db();
                
                // Delete existing monthly amounts
                $deleteStmt = $db->prepare("DELETE FROM user_monthly_retrocession_amounts WHERE user_id = :user_id");
                $deleteStmt->execute(['user_id' => $id]);
                
                // Insert new monthly amounts
                $insertStmt = $db->prepare("
                    INSERT INTO user_monthly_retrocession_amounts 
                    (user_id, month, cns_amount, patient_amount, is_active, created_by, created_at)
                    VALUES (:user_id, :month, :cns_amount, :patient_amount, :is_active, :created_by, NOW())
                ");
                
                foreach ($data['monthly_amounts'] as $month => $monthData) {
                    if (!is_numeric($month) || $month < 1 || $month > 12) {
                        continue;
                    }
                    
                    $insertStmt->execute([
                        'user_id' => $id,
                        'month' => $month,
                        'cns_amount' => floatval($monthData['cns_amount'] ?? 0),
                        'patient_amount' => floatval($monthData['patient_amount'] ?? 0),
                        'is_active' => isset($monthData['is_active']) ? 1 : 0,
                        'created_by' => $_SESSION['user_id']
                    ]);
                }
            }
            
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => __('users.user_updated')
                ]);
                exit;
            } else {
                // Set flash message and redirect back to edit page
                $_SESSION['flash']['success'] = __('users.user_updated');
                Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/edit');
                exit;
            }
        } catch (\Exception $e) {
            if ($isAjax) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                // Set flash error message and redirect back to edit form
                $_SESSION['flash']['error'] = $e->getMessage();
                Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/edit');
                exit;
            }
        }
    }
    
    /**
     * Delete user
     */
    public function delete(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Don't allow self-deletion
            if ($id == $_SESSION['user_id']) {
                throw new \Exception(__('users.cannot_delete_self'));
            }
            
            User::delete($id);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.user_deleted')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Toggle user active status
     */
    public function toggleActive(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Don't allow self-deactivation
            if ($id == $_SESSION['user_id']) {
                throw new \Exception(__('users.cannot_deactivate_self'));
            }
            
            User::toggleActive($id);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.status_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Show user profile
     */
    public function profile(Request $request, Response $response, $id = null)
    {
        // If no ID provided, show current user's profile
        if (!$id) {
            $id = $_SESSION['user_id'];
        }
        
        $user = User::getWithGroups($id);
        
        if (!$user) {
            Flight::redirect('{{ base_url }}/users');
            return;
        }
        
        $permissions = User::getPermissions($id);
        
        // Organize permissions by category
        $permissionsByCategory = [];
        foreach ($permissions as $permission) {
            $category = $permission['category'];
            if (!isset($permissionsByCategory[$category])) {
                $permissionsByCategory[$category] = [];
            }
            $permissionsByCategory[$category][] = $permission;
        }
        
        // Check if current user can edit this profile
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $currentUser = User::find($currentUserId);
        $canEdit = false;
        
        if ($currentUser) {
            // Can edit if: own profile OR current user is admin/manager
            $canEdit = ($id == $currentUserId) || 
                       User::isAdmin($currentUserId) || 
                       User::isManager($currentUserId);
        }
        
        $template = $this->getTemplate();
        $viewName = 'users/profile-modern';
        
        return $this->render($viewName, [
            'title' => __('users.user_profile'),
            'user' => $user,
            'permissions' => $permissionsByCategory,
            'isOwnProfile' => ($id == $_SESSION['user_id']),
            'canEdit' => $canEdit,
            'currentUser' => $currentUser
        ]);
    }
    
    /**
     * Upload avatar
     */
    private function uploadAvatar($file, $userId = null)
    {
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new \Exception(__('users.invalid_file_type'));
        }
        
        // Validate file size (max 5MB)
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            throw new \Exception(__('users.file_size_exceeded'));
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = dirname(__DIR__, 2) . '/public/uploads/avatars';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('avatar_') . '.' . $extension;
        $uploadPath = $uploadDir . '/' . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // If updating, remove old avatar
            if ($userId) {
                $this->removeAvatar($userId);
            }
            
            // Return the web path
            return '{{ base_url }}/uploads/avatars/' . $filename;
        } else {
            throw new \Exception(__('users.upload_failed'));
        }
    }
    
    /**
     * Remove avatar
     */
    private function removeAvatar($userId)
    {
        $user = User::find($userId);
        if ($user && $user['avatar']) {
            $filePath = dirname(__DIR__, 2) . '/public' . str_replace('{{ base_url }}', '', $user['avatar']);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
    
    /**
     * Get retrocession history for a user
     */
    public function retrocessionHistory(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check if user exists
            $user = User::find($id);
            if (!$user) {
                throw new \Exception(__('users.user_not_found'));
            }
            
            // Get retrocession history
            try {
                $history = \App\Models\UserRetrocessionSetting::getAllForUser($id);
            } catch (\Exception $e) {
                $history = [];
            }
            
            echo json_encode([
                'success' => true,
                'history' => $history
            ]);
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Show financial obligations for a user
     */
    public function financialObligations(Request $request, Response $response, $id)
    {
        $user = User::find($id);
        if (!$user) {
            Flight::redirect('/users');
            return;
        }
        
        // Check if current user can edit
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $canEdit = User::canEditFinancialObligations($currentUserId);
        
        // Get current and historical obligations
        $currentObligations = User::getCurrentFinancialObligations($id);
        $obligationsHistory = User::getFinancialObligationsHistory($id);
        
        $template = $this->getTemplate();
        $viewName = 'users/financial-obligations-modern';
        
        return $this->render($viewName, [
            'title' => __('users.financial_obligations') . ' - ' . $user['first_name'] . ' ' . $user['last_name'],
            'user' => $user,
            'currentObligations' => $currentObligations,
            'obligationsHistory' => $obligationsHistory,
            'canEdit' => $canEdit,
            'currency' => 'EUR'
        ]);
    }
    
    /**
     * Update financial obligations for a user
     */
    public function updateFinancialObligations(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $currentUserId = $_SESSION['user_id'] ?? 0;
            
            // Check permissions
            if (!User::canEditFinancialObligations($currentUserId)) {
                throw new \Exception(__('users.unauthorized_financial_edit'));
            }
            
            $data = $_POST;
            
            // Validate required fields
            $required = ['rent_amount', 'charges_amount', 'effective_date'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || $data[$field] === '') {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Calculate total (rent + charges + secretary_tvac_17 only)
            $data['total_tvac'] = floatval($data['rent_amount']) + floatval($data['charges_amount']) + 
                                  floatval($data['secretary_tvac_17'] ?? 0);
            
            // Add created_by
            $data['created_by'] = $currentUserId;
            
            // Update obligations
            User::updateFinancialObligations($id, $data, $currentUserId);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.financial_obligations_updated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Reset password for a user (admin only)
     */
    public function resetPassword(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $currentUserId = $_SESSION['user_id'] ?? 0;
            $currentUser = User::find($currentUserId);
            
            // Check permissions - only admins and managers can reset passwords
            if (!$currentUser || (!User::isAdmin($currentUserId) && !User::isManager($currentUserId))) {
                throw new \Exception(__('users.unauthorized_password_reset'));
            }
            
            // Check if trying to reset own password
            if ($id == $currentUserId) {
                throw new \Exception(__('users.use_change_password_instead'));
            }
            
            $data = $_POST;
            
            // Validate new password
            if (empty($data['new_password'])) {
                throw new \Exception(__('users.new_password_required'));
            }
            
            if (strlen($data['new_password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Validate password confirmation
            if (empty($data['confirm_password'])) {
                throw new \Exception(__('users.confirm_password_required'));
            }
            
            if ($data['new_password'] !== $data['confirm_password']) {
                throw new \Exception(__('users.password_mismatch'));
            }
            
            // Get target user
            $targetUser = User::find($id);
            if (!$targetUser) {
                throw new \Exception(__('users.user_not_found'));
            }
            
            // Update password
            User::update($id, [
                'password' => $data['new_password']
            ]);
            
            // Send email notification if requested
            if (!empty($data['send_email']) && filter_var($targetUser['email'], FILTER_VALIDATE_EMAIL)) {
                // TODO: Implement email notification
                // For now, just log the action
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.password_reset_success')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update user preferences (language, theme, etc.)
     */
    public function updatePreferences(Request $request, Response $response)
    {
        try {
            $userId = $_SESSION['user_id'] ?? 0;
            if (!$userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = $_POST;
            
            // Only allow updating specific preference fields
            $allowedFields = ['language', 'timezone', 'theme', 'email_notifications'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            // Update user preferences
            User::update($userId, $updateData);
            
            // Update session if language or theme changed
            if (isset($updateData['language'])) {
                $_SESSION['user_language'] = $updateData['language'];
            }
            if (isset($updateData['theme'])) {
                $_SESSION['template'] = $updateData['theme'];
            }
            
            // Redirect back to profile with success message
            $_SESSION['flash']['success'] = __('users.preferences_updated');
            Flight::redirect(Flight::get('flight.base_url') . '/profile');
            
        } catch (\Exception $e) {
            $_SESSION['flash']['error'] = $e->getMessage();
            Flight::redirect(Flight::get('flight.base_url') . '/profile');
        }
    }
    
    /**
     * Get courses for a user (AJAX)
     */
    public function courses(Request $request, Response $response, $id)
    {
        try {
            $courses = UserCourse::getByUserId($id);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $courses
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Get retrocession settings for a user
     */
    public function retrocessionSettings(Request $request, Response $response, $id)
    {
        try {
            // Get active settings for the user
            $settings = UserRetrocessionSetting::getActiveSettingsForUser($id);
            
            // If no settings exist, return defaults
            if (!$settings) {
                $settings = [
                    'cns_type' => 'percentage',
                    'cns_value' => 20.00,
                    'patient_type' => 'percentage',
                    'patient_value' => 20.00,
                    'secretary_type' => 'percentage',
                    'secretary_value' => 10.00,
                    'is_default' => true
                ];
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $settings
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Get financial obligations for a user (API endpoint)
     */
    public function financialObligationsApi(Request $request, Response $response, $id)
    {
        try {
            // Get current financial obligations
            $obligations = User::getCurrentFinancialObligations($id);
            
            // Return all financial obligation data for invoice creation
            // Ensure numeric values are returned
            $data = [
                'rent_amount' => $obligations ? floatval($obligations['rent_amount']) : 0,
                'charges_amount' => $obligations ? floatval($obligations['charges_amount']) : 0,
                'secretary_tvac_17' => $obligations ? floatval($obligations['secretary_tvac_17']) : 0,
                'secretary_htva' => $obligations ? floatval($obligations['secretary_htva']) : 0,
                'tva_17' => $obligations ? floatval($obligations['tva_17']) : 0
            ];
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $data
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Store a new course for a user
     */
    public function storeCourse(Request $request, Response $response, $id)
    {
        try {
            $data = $_POST;
            
            // Validate required fields
            if (empty($data['course_name'])) {
                throw new \Exception(__('courses.name_required'));
            }
            
            if (!isset($data['hourly_rate']) || $data['hourly_rate'] === '') {
                throw new \Exception(__('courses.rate_required'));
            }
            
            // Ensure numeric values
            $data['hourly_rate'] = floatval($data['hourly_rate']);
            $data['vat_rate'] = isset($data['vat_rate']) ? floatval($data['vat_rate']) : 16.0;
            $data['user_id'] = $id;
            $data['is_active'] = isset($data['is_active']) ? 1 : 1; // Default to active
            
            $course = UserCourse::createCourse($data);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.created_successfully'),
                'data' => $course
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update a course
     */
    public function updateCourse(Request $request, Response $response, $userId, $courseId)
    {
        try {
            $data = $_POST;
            
            // Validate ownership
            $course = UserCourse::find($courseId);
            if (!$course || $course->user_id != $userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            // Validate required fields
            if (empty($data['course_name'])) {
                throw new \Exception(__('courses.name_required'));
            }
            
            if (!isset($data['hourly_rate']) || $data['hourly_rate'] === '') {
                throw new \Exception(__('courses.rate_required'));
            }
            
            // Prepare update data
            $updateData = [
                'course_name' => $data['course_name'],
                'hourly_rate' => floatval($data['hourly_rate']),
                'vat_rate' => isset($data['vat_rate']) ? floatval($data['vat_rate']) : 16.0,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];
            
            UserCourse::updateCourse($courseId, $updateData);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.updated_successfully')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Delete a course
     */
    public function deleteCourse(Request $request, Response $response, $userId, $courseId)
    {
        try {
            // Validate ownership
            $course = UserCourse::find($courseId);
            if (!$course || $course->user_id != $userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            UserCourse::deleteCourse($courseId);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.deleted_successfully')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update course display order
     */
    public function updateCourseOrder(Request $request, Response $response, $id)
    {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (empty($data['order'])) {
                throw new \Exception(__('courses.invalid_order'));
            }
            
            $success = UserCourse::updateDisplayOrder($id, $data['order']);
            
            if (!$success) {
                throw new \Exception(__('courses.order_update_failed'));
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => __('courses.order_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * API endpoint to get basic user info
     */
    public function apiGetUser(Request $request, Response $response, $id)
    {
        try {
            $user = User::find($id);
            
            if (!$user) {
                throw new \Exception('User not found');
            }
            
            // Return basic user info
            $data = [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'billing_email' => $user->billing_email,
                'username' => $user->username
            ];
            
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'user' => $data]);
            exit;
        } catch (\Exception $e) {
            http_response_code(404);
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }
    
    /**
     * Generate retrocession invoice for a user
     */
    public function generateRetrocession(Request $request, Response $response, $id)
    {
        // Clear any output buffers to ensure clean JSON response
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        header('Content-Type: application/json');
        
        error_log("UserController::generateRetrocession - Starting for user ID: " . $id);
        
        try {
            // Check permissions
            $currentUserId = $_SESSION['user_id'] ?? 0;
            if (!User::isAdmin($currentUserId) && !User::isManager($currentUserId)) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            $month = $data['month'] ?? date('n');
            $year = $data['year'] ?? date('Y');
            
            // Get the client ID associated with this user
            $db = Flight::db();
            $stmt = $db->prepare("
                SELECT id FROM clients 
                WHERE user_id = :user_id 
                AND is_practitioner = 1
                LIMIT 1
            ");
            $stmt->execute(['user_id' => $id]);
            $client = $stmt->fetch();
            
            error_log("UserController::generateRetrocession - Client lookup:");
            error_log("- User ID: " . $id);
            error_log("- Client found: " . ($client ? "YES" : "NO"));
            if ($client) {
                error_log("- Client ID: " . $client['id']);
            }
            
            if (!$client) {
                throw new \Exception(__('retrocession.user_not_practitioner'));
            }
            
            $clientId = $client['id'];
            
            // Check if user has monthly amounts configured
            $stmt = $db->prepare("
                SELECT * FROM user_monthly_retrocession_amounts 
                WHERE user_id = :user_id AND month = :month AND is_active = 1
            ");
            $stmt->execute(['user_id' => $id, 'month' => $month]);
            $monthlyAmount = $stmt->fetch();
            
            error_log("UserController::generateRetrocession - Monthly amount check:");
            error_log("- User ID: " . $id);
            error_log("- Month: " . $month);
            error_log("- Monthly amount found: " . ($monthlyAmount ? "YES" : "NO"));
            if ($monthlyAmount) {
                error_log("- CNS Amount: " . $monthlyAmount['cns_amount']);
                error_log("- Patient Amount: " . $monthlyAmount['patient_amount']);
            }
            
            if (!$monthlyAmount) {
                throw new \Exception(__('retrocession.no_monthly_amount_configured'));
            }
            
            // Check if entry already exists
            $stmt = $db->prepare("
                SELECT * FROM retrocession_data_entry 
                WHERE practitioner_id = :practitioner_id 
                AND period_month = :month 
                AND period_year = :year
            ");
            $stmt->execute([
                'practitioner_id' => $clientId,
                'month' => $month,
                'year' => $year
            ]);
            $existing = $stmt->fetch();
            
            if ($existing && $existing['status'] === 'invoiced' && $existing['invoice_id']) {
                // Check if the invoice still exists
                $stmt = $db->prepare("SELECT invoice_number FROM invoices WHERE id = :id");
                $stmt->execute(['id' => $existing['invoice_id']]);
                $invoice = $stmt->fetch();
                
                if ($invoice) {
                    // Invoice exists, cannot regenerate
                    echo json_encode([
                        'success' => true,
                        'message' => __('retrocession.invoice_already_exists'),
                        'invoice_id' => $existing['invoice_id'],
                        'invoice_number' => $invoice['invoice_number'] ?? ''
                    ]);
                    exit;
                } else {
                    // Invoice was deleted, reset the status to allow regeneration
                    $stmt = $db->prepare("
                        UPDATE retrocession_data_entry 
                        SET status = 'confirmed', invoice_id = NULL 
                        WHERE id = :id
                    ");
                    $stmt->execute(['id' => $existing['id']]);
                    
                    // Update the existing record for further processing
                    $existing['status'] = 'confirmed';
                    $existing['invoice_id'] = null;
                }
            }
            
            // Create or update entry
            if (!$existing) {
                $stmt = $db->prepare("
                    INSERT INTO retrocession_data_entry (
                        practitioner_id, period_month, period_year,
                        cns_amount, patient_amount,
                        data_source, auto_generated_from,
                        status, entered_by, entered_at,
                        confirmed_by, confirmed_at
                    ) VALUES (
                        :practitioner_id, :period_month, :period_year,
                        :cns_amount, :patient_amount,
                        'auto_generated', :auto_generated_from,
                        'confirmed', :entered_by, NOW(),
                        :confirmed_by, NOW()
                    )
                ");
                
                $stmt->execute([
                    'practitioner_id' => $clientId,
                    'period_month' => $month,
                    'period_year' => $year,
                    'cns_amount' => $monthlyAmount['cns_amount'],
                    'patient_amount' => $monthlyAmount['patient_amount'],
                    'auto_generated_from' => $monthlyAmount['id'],
                    'entered_by' => $currentUserId,
                    'confirmed_by' => $currentUserId
                ]);
            } else {
                // Update existing entry (for draft or confirmed status)
                error_log("UserController::generateRetrocession - Updating existing entry with status: " . $existing['status']);
                
                $stmt = $db->prepare("
                    UPDATE retrocession_data_entry
                    SET cns_amount = :cns_amount,
                        patient_amount = :patient_amount,
                        status = 'confirmed',
                        confirmed_by = :confirmed_by,
                        confirmed_at = NOW(),
                        auto_generated_from = :auto_generated_from
                    WHERE id = :id
                ");
                $stmt->execute([
                    'cns_amount' => $monthlyAmount['cns_amount'],
                    'patient_amount' => $monthlyAmount['patient_amount'],
                    'confirmed_by' => $currentUserId,
                    'auto_generated_from' => $monthlyAmount['id'],
                    'id' => $existing['id']
                ]);
                
                error_log("UserController::generateRetrocession - Updated entry ID {$existing['id']} with amounts from monthly config");
            }
            
            // Debug: Verify the entry was created/updated
            $stmt = $db->prepare("
                SELECT * FROM retrocession_data_entry 
                WHERE practitioner_id = :practitioner_id 
                AND period_month = :month 
                AND period_year = :year
            ");
            $stmt->execute([
                'practitioner_id' => $clientId,
                'month' => $month,
                'year' => $year
            ]);
            $verifyEntry = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            error_log("UserController::generateRetrocession - After create/update:");
            error_log("- Practitioner ID: " . $clientId);
            error_log("- Month/Year: " . $month . "/" . $year);
            error_log("- Entry found: " . ($verifyEntry ? "YES" : "NO"));
            if ($verifyEntry) {
                error_log("- Entry ID: " . $verifyEntry['id']);
                error_log("- Status: " . $verifyEntry['status']);
                error_log("- CNS Amount: " . $verifyEntry['cns_amount']);
                error_log("- Patient Amount: " . $verifyEntry['patient_amount']);
            }
            
            // Log before generating invoice
            error_log("UserController::generateRetrocession - About to generate invoice for client: $clientId, month: $month, year: $year");
            
            // Verify entry exists and has correct status
            $stmt = $db->prepare("
                SELECT * FROM retrocession_data_entry 
                WHERE practitioner_id = :practitioner_id 
                AND period_month = :month 
                AND period_year = :year
            ");
            $stmt->execute([
                'practitioner_id' => $clientId,
                'month' => $month,
                'year' => $year
            ]);
            $finalEntry = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            error_log("UserController::generateRetrocession - Final entry check: " . 
                     "exists=" . ($finalEntry ? 'yes' : 'no') . 
                     ", status=" . ($finalEntry['status'] ?? 'N/A') . 
                     ", cns=" . ($finalEntry['cns_amount'] ?? 'N/A') . 
                     ", patient=" . ($finalEntry['patient_amount'] ?? 'N/A'));
            
            // Generate invoice
            try {
                $calculator = new \App\Services\RetrocessionCalculator();
                $invoice = $calculator->generateInvoice($clientId, $month, $year);
                
                echo json_encode([
                    'success' => true,
                    'message' => __('retrocession.invoice_generated_successfully'),
                    'invoice_id' => $invoice['id'],
                    'invoice_number' => $invoice['invoice_number'] ?? ''
                ]);
                exit;
            } catch (\Exception $calcError) {
                error_log("UserController::generateRetrocession - Calculator error: " . $calcError->getMessage());
                error_log("UserController::generateRetrocession - Calculator trace: " . $calcError->getTraceAsString());
                throw $calcError; // Re-throw to be caught by outer try-catch
            }
            
        } catch (\Exception $e) {
            error_log("UserController::generateRetrocession - Error: " . $e->getMessage());
            error_log("UserController::generateRetrocession - Trace: " . $e->getTraceAsString());
            
            // Clear any output that might have been generated
            while (ob_get_level()) {
                ob_end_clean();
            }
            
            http_response_code(400);
            header('Content-Type: application/json');
            
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage(),
                'debug' => [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ]);
            exit;
        } catch (\Error $e) {
            // Catch fatal errors too
            error_log("UserController::generateRetrocession - Fatal Error: " . $e->getMessage());
            
            while (ob_get_level()) {
                ob_end_clean();
            }
            
            http_response_code(500);
            header('Content-Type: application/json');
            
            echo json_encode([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage(),
                'debug' => [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ]);
            exit;
        }
    }
    
    /**
     * Save retrocession settings for a user
     */
    public function saveRetrocessionSettings(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check permissions
            $currentUserId = $_SESSION['user_id'] ?? 0;
            if (!User::isAdmin($currentUserId) && !User::isManager($currentUserId)) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            $settings = $data['retrocession'] ?? [];
            
            // Validate required fields
            if (empty($settings['valid_from'])) {
                throw new \Exception(__('validation.required', ['field' => 'valid_from']));
            }
            
            $db = Flight::db();
            
            // Check if user_retrocession_settings table exists
            $stmt = $db->query("SHOW TABLES LIKE 'user_retrocession_settings'");
            if (!$stmt->fetch()) {
                // Create table if it doesn't exist
                $createTableSql = file_get_contents(__DIR__ . '/../../database/migrations/070_create_user_retrocession_settings_table.sql');
                $db->exec($createTableSql);
                
                // Add label columns
                $db->exec("
                    ALTER TABLE user_retrocession_settings
                    ADD COLUMN IF NOT EXISTS cns_label VARCHAR(255) DEFAULT 'RÉTROCESSION CNS' AFTER cns_value,
                    ADD COLUMN IF NOT EXISTS patient_label VARCHAR(255) DEFAULT 'RÉTROCESSION PATIENTS' AFTER patient_value,
                    ADD COLUMN IF NOT EXISTS secretary_label VARCHAR(255) DEFAULT 'FRAIS SECRÉTARIAT' AFTER secretary_value
                ");
            }
            
            // Deactivate current settings for this user
            $stmt = $db->prepare("
                UPDATE user_retrocession_settings 
                SET is_active = 0, valid_to = DATE_SUB(:valid_from, INTERVAL 1 DAY)
                WHERE user_id = :user_id AND is_active = 1 AND valid_to IS NULL
            ");
            $stmt->execute([
                'user_id' => $id,
                'valid_from' => $settings['valid_from']
            ]);
            
            // Insert new settings
            $stmt = $db->prepare("
                INSERT INTO user_retrocession_settings (
                    user_id, cns_type, cns_value, cns_label,
                    patient_type, patient_value, patient_label,
                    secretary_type, secretary_value, secretary_label,
                    ceiling_enabled, ceiling_amount,
                    valid_from, valid_to, is_active, notes,
                    created_by, approved_by, approved_at
                ) VALUES (
                    :user_id, :cns_type, :cns_value, :cns_label,
                    :patient_type, :patient_value, :patient_label,
                    :secretary_type, :secretary_value, :secretary_label,
                    :ceiling_enabled, :ceiling_amount,
                    :valid_from, :valid_to, 1, :notes,
                    :created_by, :approved_by, NOW()
                )
            ");
            
            $stmt->execute([
                'user_id' => $id,
                'cns_type' => $settings['cns_type'] ?? 'percentage',
                'cns_value' => $settings['cns_value'] ?? 20,
                'cns_label' => $settings['cns_label'] ?? 'RÉTROCESSION CNS',
                'patient_type' => $settings['patient_type'] ?? 'percentage',
                'patient_value' => $settings['patient_value'] ?? 20,
                'patient_label' => $settings['patient_label'] ?? 'RÉTROCESSION PATIENTS',
                'secretary_type' => $settings['secretary_type'] ?? 'percentage',
                'secretary_value' => $settings['secretary_value'] ?? 10,
                'secretary_label' => $settings['secretary_label'] ?? 'FRAIS SECRÉTARIAT',
                'ceiling_enabled' => $settings['ceiling_enabled'] ?? 0,
                'ceiling_amount' => $settings['ceiling_amount'] ?? 5000,
                'valid_from' => $settings['valid_from'],
                'valid_to' => $settings['valid_to'] ?: null,
                'notes' => $settings['notes'] ?? null,
                'created_by' => $currentUserId,
                'approved_by' => $currentUserId
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.retrocession_settings_saved')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
}