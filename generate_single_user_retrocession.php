<?php
/**
 * Generate retrocession entry and invoice for a single user
 * Usage: php generate_single_user_retrocession.php [user_id] [month] [year]
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Create database connection
$dsn = sprintf(
    'mysql:host=%s;dbname=%s;charset=utf8mb4',
    $_ENV['DB_HOST'],
    $_ENV['DB_DATABASE']
);

$db = new PDO($dsn, $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD'], [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
]);

// Get command line arguments
$userId = isset($argv[1]) ? intval($argv[1]) : null;
$month = isset($argv[2]) ? intval($argv[2]) : date('n');
$year = isset($argv[3]) ? intval($argv[3]) : date('Y');

if (!$userId) {
    echo "Usage: php generate_single_user_retrocession.php [user_id] [month] [year]\n";
    echo "\nAvailable practitioners:\n";
    
    // Show list of practitioners
    $stmt = $db->query("
        SELECT u.id, u.first_name, u.last_name, u.email
        FROM users u
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        INNER JOIN user_groups ug ON ugm.group_id = ug.id
        WHERE ug.slug IN ('practitioners', 'coaches')
        AND u.is_active = 1
        ORDER BY u.first_name, u.last_name
    ");
    
    foreach ($stmt->fetchAll() as $user) {
        echo sprintf("  ID: %d - %s %s (%s)\n", 
            $user['id'], 
            $user['first_name'], 
            $user['last_name'], 
            $user['email']
        );
    }
    exit(1);
}

echo "Generating retrocession for user ID $userId for " . date('F Y', strtotime("$year-$month-01")) . "...\n\n";

try {
    // Check if monthly amount exists
    $stmt = $db->prepare("
        SELECT * FROM user_monthly_retrocession_amounts 
        WHERE user_id = :user_id AND month = :month AND is_active = 1
    ");
    $stmt->execute(['user_id' => $userId, 'month' => $month]);
    $monthlyAmount = $stmt->fetch();
    
    if (!$monthlyAmount) {
        echo "Error: No active monthly amount configured for this user and month.\n";
        exit(1);
    }
    
    // Check if entry already exists
    $stmt = $db->prepare("
        SELECT * FROM retrocession_data_entry 
        WHERE practitioner_id = :practitioner_id 
        AND period_month = :month 
        AND period_year = :year
    ");
    $stmt->execute([
        'practitioner_id' => $userId,
        'month' => $month,
        'year' => $year
    ]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "Entry already exists with status: " . $existing['status'] . "\n";
        
        if ($existing['status'] === 'invoiced') {
            echo "Invoice already generated - Invoice ID: " . $existing['invoice_id'] . "\n";
            exit(0);
        }
        
        echo "Would you like to generate an invoice for this entry? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        if (trim($line) !== 'y') {
            echo "Cancelled.\n";
            exit(0);
        }
        
        $dataEntryId = $existing['id'];
    } else {
        // Create new entry
        $stmt = $db->prepare("
            INSERT INTO retrocession_data_entry (
                practitioner_id, period_month, period_year,
                cns_amount, patient_amount,
                data_source, auto_generated_from,
                status, entered_by, entered_at
            ) VALUES (
                :practitioner_id, :period_month, :period_year,
                :cns_amount, :patient_amount,
                'auto_generated', :auto_generated_from,
                'confirmed', 1, NOW()
            )
        ");
        
        $stmt->execute([
            'practitioner_id' => $userId,
            'period_month' => $month,
            'period_year' => $year,
            'cns_amount' => $monthlyAmount['cns_amount'],
            'patient_amount' => $monthlyAmount['patient_amount'],
            'auto_generated_from' => $monthlyAmount['id']
        ]);
        
        $dataEntryId = $db->lastInsertId();
        echo "✓ Created retrocession entry\n";
    }
    
    // Generate invoice using the service
    require_once __DIR__ . '/app/config/bootstrap.php';
    
    $calculator = new \App\Services\RetrocessionCalculator();
    $invoice = $calculator->generateInvoice($userId, $month, $year);
    
    echo "✓ Generated invoice ID: " . $invoice['id'] . "\n";
    echo "  Invoice number: " . $invoice['invoice_number'] . "\n";
    echo "  Total amount: €" . number_format($invoice['total'], 2) . "\n";
    echo "\nView invoice at: http://localhost/fit/public/invoices/" . $invoice['id'] . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}