name: Database Import Fix
description: Fixes foreign key constraint errors during SQL import by cleaning up orphaned references
version: 1.0.0
author: Claude-Flow

inputs:
  - name: sql_file
    type: string
    description: Path to SQL file to import
    required: true
    default: ""
    
  - name: fix_mode
    type: string
    description: How to fix issues (analyze, clean, force)
    required: false
    default: "analyze"
    
  - name: database_name
    type: string
    description: Target database name
    required: false
    default: "fitapp"

env:
  mysql_host: "${DB_HOST:-localhost}"
  mysql_user: "${DB_USER:-root}"
  mysql_pass: "${DB_PASS:-}"

steps:
  - name: Analyze Import File
    type: shell
    command: |
      echo "=== Analyzing SQL Import File ==="
      
      if [ ! -f "${inputs.sql_file}" ]; then
        echo "Error: SQL file not found: ${inputs.sql_file}"
        exit 1
      fi
      
      # Check file size
      file_size=$(stat -c%s "${inputs.sql_file}" 2>/dev/null || stat -f%z "${inputs.sql_file}" 2>/dev/null)
      echo "SQL file size: $(numfmt --to=iec-i --suffix=B $file_size 2>/dev/null || echo "$file_size bytes")"
      
      # Check for foreign key constraints in file
      echo -e "\nForeign key constraints found in file:"
      grep -i "foreign key" "${inputs.sql_file}" | wc -l

  - name: Check Current Database State
    type: shell
    command: |
      php -r "
      echo \"\\n=== Checking Current Database State ===\\n\";
      
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      try {
          \$pdo = new PDO(
              'mysql:host=' . (\$_ENV['DB_HOST'] ?? 'localhost') . ';dbname=${inputs.database_name}',
              \$_ENV['DB_USER'] ?? 'root',
              \$_ENV['DB_PASS'] ?? ''
          );
          
          // Check if tables exist
          echo \"Checking existing tables...\\n\";
          \$tables = ['invoices', 'config_payment_terms', 'document_types', 'users', 'clients'];
          
          foreach (\$tables as \$table) {
              \$stmt = \$pdo->query(\"SHOW TABLES LIKE '\$table'\");
              if (\$stmt->rowCount() > 0) {
                  \$count = \$pdo->query(\"SELECT COUNT(*) FROM \$table\")->fetchColumn();
                  echo \"  ✓ \$table exists with \$count records\\n\";
              } else {
                  echo \"  ✗ \$table does not exist\\n\";
              }
          }
          
      } catch (Exception \$e) {
          echo \"Database connection failed: \" . \$e->getMessage() . \"\\n\";
      }
      "

  - name: Analyze Foreign Key Issues
    type: conditional
    condition: "{{ inputs.fix_mode == 'analyze' || inputs.fix_mode == 'clean' }}"
    steps:
      - name: Find Orphaned References
        type: shell
        command: |
          php -r "
          echo \"\\n=== Analyzing Foreign Key Issues ===\\n\";
          
          require_once 'vendor/autoload.php';
          \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
          \$dotenv->load();
          
          \$pdo = new PDO(
              'mysql:host=' . (\$_ENV['DB_HOST'] ?? 'localhost') . ';dbname=${inputs.database_name}',
              \$_ENV['DB_USER'] ?? 'root',
              \$_ENV['DB_PASS'] ?? '',
              [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
          );
          
          \$issues = [];
          
          // Check payment_term_id references
          try {
              \$stmt = \$pdo->query(\"
                  SELECT DISTINCT i.payment_term_id, COUNT(*) as count
                  FROM invoices i
                  LEFT JOIN config_payment_terms cpt ON i.payment_term_id = cpt.id
                  WHERE i.payment_term_id IS NOT NULL 
                  AND cpt.id IS NULL
                  GROUP BY i.payment_term_id
              \");
              
              \$orphaned = \$stmt->fetchAll(PDO::FETCH_ASSOC);
              if (!empty(\$orphaned)) {
                  echo \"\\n⚠️  Found orphaned payment_term_id references:\\n\";
                  foreach (\$orphaned as \$row) {
                      echo \"   ID \" . \$row['payment_term_id'] . \" (\" . \$row['count'] . \" invoices)\\n\";
                      \$issues['payment_term_id'][] = \$row['payment_term_id'];
                  }
              }
          } catch (Exception \$e) {
              echo \"Could not check payment_term_id: \" . \$e->getMessage() . \"\\n\";
          }
          
          // Check other foreign keys
          \$fkChecks = [
              ['invoices', 'document_type_id', 'document_types', 'id'],
              ['invoices', 'user_id', 'users', 'id'],
              ['invoices', 'client_id', 'clients', 'id'],
              ['invoices', 'type_id', 'config_invoice_types', 'id']
          ];
          
          foreach (\$fkChecks as \$check) {
              list(\$table, \$column, \$refTable, \$refColumn) = \$check;
              
              try {
                  \$stmt = \$pdo->query(\"
                      SELECT DISTINCT t.\$column, COUNT(*) as count
                      FROM \$table t
                      LEFT JOIN \$refTable r ON t.\$column = r.\$refColumn
                      WHERE t.\$column IS NOT NULL 
                      AND r.\$refColumn IS NULL
                      GROUP BY t.\$column
                  \");
                  
                  \$orphaned = \$stmt->fetchAll(PDO::FETCH_ASSOC);
                  if (!empty(\$orphaned)) {
                      echo \"\\n⚠️  Found orphaned \$column references in \$table:\\n\";
                      foreach (\$orphaned as \$row) {
                          echo \"   ID \" . \$row[\$column] . \" (\" . \$row['count'] . \" records)\\n\";
                          \$issues[\$column][] = \$row[\$column];
                      }
                  }
              } catch (Exception \$e) {
                  // Table might not exist yet
              }
          }
          
          if (empty(\$issues)) {
              echo \"\\n✓ No orphaned foreign key references found\\n\";
          } else {
              file_put_contents('fk_issues.json', json_encode(\$issues, JSON_PRETTY_PRINT));
              echo \"\\nIssues saved to fk_issues.json\\n\";
          }
          "

  - name: Clean Orphaned References
    type: conditional
    condition: "{{ inputs.fix_mode == 'clean' }}"
    steps:
      - name: Fix Invalid References
        type: shell
        command: |
          php -r "
          echo \"\\n=== Cleaning Orphaned References ===\\n\";
          
          require_once 'vendor/autoload.php';
          \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
          \$dotenv->load();
          
          \$pdo = new PDO(
              'mysql:host=' . (\$_ENV['DB_HOST'] ?? 'localhost') . ';dbname=${inputs.database_name}',
              \$_ENV['DB_USER'] ?? 'root',
              \$_ENV['DB_PASS'] ?? ''
          );
          
          // Load issues if exists
          \$issues = [];
          if (file_exists('fk_issues.json')) {
              \$issues = json_decode(file_get_contents('fk_issues.json'), true);
          }
          
          // Create backup of affected records
          echo \"Creating backup of affected records...\\n\";
          
          // Fix payment_term_id
          if (isset(\$issues['payment_term_id'])) {
              \$ids = implode(',', \$issues['payment_term_id']);
              
              // Backup
              \$stmt = \$pdo->query(\"SELECT * FROM invoices WHERE payment_term_id IN (\$ids)\");
              \$backup = \$stmt->fetchAll(PDO::FETCH_ASSOC);
              file_put_contents('invoices_backup_' . date('YmdHis') . '.json', json_encode(\$backup, JSON_PRETTY_PRINT));
              
              // Set to NULL
              \$stmt = \$pdo->prepare(\"UPDATE invoices SET payment_term_id = NULL WHERE payment_term_id IN (\$ids)\");
              \$stmt->execute();
              echo \"✓ Set \" . \$stmt->rowCount() . \" invalid payment_term_id values to NULL\\n\";
          }
          
          // Fix other columns similarly
          \$nullableColumns = ['document_type_id', 'user_id', 'type_id'];
          
          foreach (\$nullableColumns as \$column) {
              if (isset(\$issues[\$column])) {
                  \$ids = implode(',', \$issues[\$column]);
                  \$stmt = \$pdo->prepare(\"UPDATE invoices SET \$column = NULL WHERE \$column IN (\$ids)\");
                  \$stmt->execute();
                  echo \"✓ Set \" . \$stmt->rowCount() . \" invalid \$column values to NULL\\n\";
              }
          }
          
          echo \"\\nCleaning completed. You can now retry the import.\\n\";
          "

  - name: Force Import Mode
    type: conditional
    condition: "{{ inputs.fix_mode == 'force' }}"
    steps:
      - name: Import with FK Checks Disabled
        type: shell
        command: |
          echo -e "\n=== Importing with Foreign Key Checks Disabled ==="
          
          # Create import script
          cat > temp_import.sql << 'EOF'
          SET FOREIGN_KEY_CHECKS = 0;
          SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
          SET AUTOCOMMIT = 0;
          START TRANSACTION;
          
          SOURCE ${inputs.sql_file};
          
          SET FOREIGN_KEY_CHECKS = 1;
          COMMIT;
          EOF
          
          # Run import
          mysql -h ${env.mysql_host} -u ${env.mysql_user} -p${env.mysql_pass} ${inputs.database_name} < temp_import.sql
          
          if [ $? -eq 0 ]; then
              echo "✓ Import completed successfully"
          else
              echo "✗ Import failed"
          fi
          
          rm temp_import.sql

  - name: Post-Import Validation
    type: shell
    command: |
      php -r "
      echo \"\\n=== Post-Import Validation ===\\n\";
      
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      \$pdo = new PDO(
          'mysql:host=' . (\$_ENV['DB_HOST'] ?? 'localhost') . ';dbname=${inputs.database_name}',
          \$_ENV['DB_USER'] ?? 'root',
          \$_ENV['DB_PASS'] ?? ''
      );
      
      // Check final state
      \$tables = ['invoices', 'config_payment_terms', 'document_types', 'users', 'clients'];
      
      foreach (\$tables as \$table) {
          try {
              \$count = \$pdo->query(\"SELECT COUNT(*) FROM \$table\")->fetchColumn();
              echo \"✓ \$table: \$count records\\n\";
          } catch (Exception \$e) {
              echo \"✗ \$table: Not found or error\\n\";
          }
      }
      
      // Check for any remaining FK issues
      echo \"\\nChecking foreign key integrity...\\n\";
      
      try {
          \$stmt = \$pdo->query(\"
              SELECT COUNT(*) 
              FROM invoices i
              LEFT JOIN config_payment_terms cpt ON i.payment_term_id = cpt.id
              WHERE i.payment_term_id IS NOT NULL AND cpt.id IS NULL
          \");
          
          \$orphaned = \$stmt->fetchColumn();
          if (\$orphaned > 0) {
              echo \"⚠️  Still have \$orphaned invoices with invalid payment_term_id\\n\";
          } else {
              echo \"✓ All payment_term_id references are valid\\n\";
          }
      } catch (Exception \$e) {
          echo \"Could not validate: \" . \$e->getMessage() . \"\\n\";
      }
      
      echo \"\\nImport validation completed.\\n\";
      "

  - name: Generate Fix Report
    type: shell
    command: |
      echo -e "\n=== Import Fix Summary ==="
      echo "SQL File: ${inputs.sql_file}"
      echo "Database: ${inputs.database_name}"
      echo "Fix Mode: ${inputs.fix_mode}"
      echo ""
      
      if [ "${inputs.fix_mode}" == "analyze" ]; then
          echo "Analysis complete. To fix issues, run with:"
          echo "  --fix_mode=clean   : Clean orphaned references"
          echo "  --fix_mode=force   : Import with FK checks disabled"
      fi
      
      # Cleanup
      rm -f fk_issues.json

on_error:
  - name: Log Import Error
    type: shell
    command: |
      echo "Import fix error: {{ error_message }}" >> storage/logs/import-errors.log
      echo "Check the output above for specific issues"

on_success:
  - name: Log Success
    type: shell
    command: |
      echo "Import fix workflow completed at $(date)" >> storage/logs/import-success.log