<?php
// Fix invoice 263 total from 794.97 to 930.00

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Fix Invoice 263 Total</h1>";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $invoiceId = $_POST['invoice_id'] ?? 263;
    
    try {
        $db->beginTransaction();
        
        switch ($action) {
            case 'add_adjustment':
                // Add adjustment line to reach 930.00
                $stmt = $db->prepare("SELECT total FROM invoices WHERE id = ?");
                $stmt->execute([$invoiceId]);
                $currentTotal = $stmt->fetchColumn();
                
                $adjustmentAmount = 930.00 - $currentTotal;
                
                // Add adjustment line
                $stmt = $db->prepare("
                    INSERT INTO invoice_lines (
                        invoice_id, line_type, description, quantity, unit_price, 
                        vat_rate, line_total, sort_order
                    ) VALUES (?, 'adjustment', 'Adjustment to correct total', 1, ?, 0, ?, 999)
                ");
                $stmt->execute([$invoiceId, $adjustmentAmount, $adjustmentAmount]);
                
                // Update invoice total
                $stmt = $db->prepare("UPDATE invoices SET total = 930.00, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$invoiceId]);
                
                echo "<p style='color: green;'>✅ Added adjustment line of " . number_format($adjustmentAmount, 2) . " € to reach 930.00 €</p>";
                break;
                
            case 'add_patient_retrocession':
                // Add patient retrocession line (assuming this was excluded)
                $stmt = $db->prepare("SELECT total FROM invoices WHERE id = ?");
                $stmt->execute([$invoiceId]);
                $currentTotal = $stmt->fetchColumn();
                
                $patientAmount = 930.00 - $currentTotal;
                
                // Add patient retrocession line
                $stmt = $db->prepare("
                    INSERT INTO invoice_lines (
                        invoice_id, line_type, description, quantity, unit_price, 
                        vat_rate, line_total, sort_order
                    ) VALUES (?, 'patient_part', 'Part Patient', 1, ?, 0, ?, 2)
                ");
                $stmt->execute([$invoiceId, $patientAmount, $patientAmount]);
                
                // Update invoice total
                $stmt = $db->prepare("UPDATE invoices SET total = 930.00, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$invoiceId]);
                
                echo "<p style='color: green;'>✅ Added patient retrocession line of " . number_format($patientAmount, 2) . " € to reach 930.00 €</p>";
                break;
                
            case 'recalculate_totals':
                // Recalculate based on existing lines
                $stmt = $db->prepare("
                    SELECT 
                        SUM(quantity * unit_price) as subtotal,
                        SUM(quantity * unit_price * (vat_rate / 100)) as vat_amount,
                        SUM(line_total) as total
                    FROM invoice_lines 
                    WHERE invoice_id = ?
                ");
                $stmt->execute([$invoiceId]);
                $totals = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Update invoice with calculated totals
                $stmt = $db->prepare("
                    UPDATE invoices 
                    SET subtotal = ?, vat_amount = ?, total = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([
                    round($totals['subtotal'], 2),
                    round($totals['vat_amount'], 2),
                    round($totals['total'], 2),
                    $invoiceId
                ]);
                
                echo "<p style='color: green;'>✅ Recalculated totals based on existing lines</p>";
                echo "<p>New total: " . number_format($totals['total'], 2) . " €</p>";
                break;
                
            case 'set_exact_total':
                // Simply set the total to 930.00 without adding lines
                $stmt = $db->prepare("UPDATE invoices SET total = 930.00, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$invoiceId]);
                
                echo "<p style='color: green;'>✅ Set total to exactly 930.00 €</p>";
                break;
                
            default:
                echo "<p style='color: red;'>❌ Unknown action: $action</p>";
        }
        
        $db->commit();
        echo "<p><a href='http://localhost/fit/public/invoices/263'>View Updated Invoice 263</a></p>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

// Show current state
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = 263");
$stmt->execute();
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if ($invoice) {
    echo "<h2>Current Invoice 263 State</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
    echo "<tr><td>Current Total</td><td><strong>" . number_format($invoice['total'], 2) . " €</strong></td></tr>";
    echo "<tr><td>Target Total</td><td><strong>930.00 €</strong></td></tr>";
    echo "<tr><td>Difference</td><td><strong>" . number_format(930.00 - $invoice['total'], 2) . " €</strong></td></tr>";
    echo "</table>";
    
    if ($invoice['total'] != 930.00) {
        echo "<h2>Fix Options</h2>";
        
        echo "<form method='post' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='invoice_id' value='263'>";
        echo "<p><strong>Option 1:</strong> Add adjustment line to reach exactly 930.00 €</p>";
        echo "<button type='submit' name='action' value='add_adjustment' onclick='return confirm(\"Add adjustment line of " . number_format(930.00 - $invoice['total'], 2) . " €?\");'>Add Adjustment Line</button>";
        echo "</form>";
        
        echo "<form method='post' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='invoice_id' value='263'>";
        echo "<p><strong>Option 2:</strong> Add patient retrocession line (if this was excluded)</p>";
        echo "<button type='submit' name='action' value='add_patient_retrocession' onclick='return confirm(\"Add patient retrocession line of " . number_format(930.00 - $invoice['total'], 2) . " €?\");'>Add Patient Retrocession</button>";
        echo "</form>";
        
        echo "<form method='post' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='invoice_id' value='263'>";
        echo "<p><strong>Option 3:</strong> Recalculate based on existing lines</p>";
        echo "<button type='submit' name='action' value='recalculate_totals' onclick='return confirm(\"Recalculate totals based on invoice lines?\");'>Recalculate Totals</button>";
        echo "</form>";
        
        echo "<form method='post' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='invoice_id' value='263'>";
        echo "<p><strong>Option 4:</strong> Simply set total to 930.00 € (quick fix)</p>";
        echo "<button type='submit' name='action' value='set_exact_total' onclick='return confirm(\"Set total to exactly 930.00 €?\");'>Set Exact Total</button>";
        echo "</form>";
    } else {
        echo "<p style='color: green;'>✅ Invoice 263 total is already correct at 930.00 €</p>";
    }
    
    // Show current lines
    echo "<h2>Current Invoice Lines</h2>";
    $stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 263 ORDER BY sort_order, id");
    $stmt->execute();
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($lines)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
        
        $totalLines = 0;
        foreach ($lines as $line) {
            $totalLines += $line['line_total'];
            echo "<tr>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>" . number_format($line['unit_price'], 2) . " €</td>";
            echo "<td>{$line['vat_rate']}%</td>";
            echo "<td>" . number_format($line['line_total'], 2) . " €</td>";
            echo "</tr>";
        }
        
        echo "<tr style='font-weight: bold;'>";
        echo "<td colspan='4'>TOTAL FROM LINES</td>";
        echo "<td>" . number_format($totalLines, 2) . " €</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No invoice lines found!</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Invoice 263 not found!</p>";
}

echo "<p><a href='debug_invoice_263_actual.php'>Back to Debug Analysis</a></p>";
?>