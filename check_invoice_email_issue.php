<?php
/**
 * Check specific invoice email issue
 */

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

$invoiceNumber = $_GET['invoice'] ?? 'FAC-DIV-2025-0190';

echo "<!DOCTYPE html>";
echo "<html><head><title>Invoice Email Check</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".warning { color: orange; }";
echo "table { border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
echo "th { background: #f0f0f0; }";
echo "pre { background: #f5f5f5; padding: 10px; overflow: auto; }";
echo "</style></head><body>";

echo "<h1>Invoice Email Issue Check</h1>";
echo "<p>Checking invoice: <strong>$invoiceNumber</strong></p>";

// Get invoice details
$stmt = $db->prepare('
    SELECT i.*, 
           u.id as user_id, u.email as user_email,
           u.first_name, u.last_name,
           c.id as client_id, c.email as client_email, c.name as client_name
    FROM invoices i
    LEFT JOIN users u ON i.user_id = u.id
    LEFT JOIN clients c ON i.client_id = c.id
    WHERE i.invoice_number = :invoice_number
');
$stmt->execute([':invoice_number' => $invoiceNumber]);
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$invoice) {
    echo "<p class='error'>Invoice not found!</p>";
    echo "<form method='get'>";
    echo "Enter invoice number: <input type='text' name='invoice' value=''>";
    echo "<button type='submit'>Check</button>";
    echo "</form>";
    die("</body></html>");
}

echo "<h2>Invoice Details</h2>";
echo "<table>";
echo "<tr><th>Field</th><th>Value</th></tr>";
echo "<tr><td>Invoice ID</td><td>{$invoice['id']}</td></tr>";
echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
echo "<tr><td>Status</td><td class='" . ($invoice['status'] === 'sent' ? 'success' : 'warning') . "'>{$invoice['status']}</td></tr>";
echo "<tr><td>Created</td><td>{$invoice['created_at']}</td></tr>";
echo "<tr><td>Updated</td><td>{$invoice['updated_at']}</td></tr>";
echo "</table>";

echo "<h2>Recipient Information</h2>";
if ($invoice['user_id']) {
    echo "<p><strong>Type:</strong> User Invoice</p>";
    echo "<table>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>User ID</td><td>{$invoice['user_id']}</td></tr>";
    echo "<tr><td>Name</td><td>{$invoice['first_name']} {$invoice['last_name']}</td></tr>";
    echo "<tr><td>Email</td><td>" . ($invoice['user_email'] ?: '<span class="error">NOT SET</span>') . "</td></tr>";
    echo "</table>";
    
    $recipientEmail = $invoice['user_email'];
    if (!$recipientEmail) {
        echo "<p class='error'><strong>⚠ NO EMAIL ADDRESS FOUND - This is why the email cannot be sent!</strong></p>";
    } else {
        echo "<p class='success'>✓ Would send to: <strong>$recipientEmail</strong></p>";
    }
} elseif ($invoice['client_id']) {
    echo "<p><strong>Type:</strong> Client Invoice</p>";
    echo "<table>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Client ID</td><td>{$invoice['client_id']}</td></tr>";
    echo "<tr><td>Name</td><td>{$invoice['client_name']}</td></tr>";
    echo "<tr><td>Email</td><td>" . ($invoice['client_email'] ?: '<span class="error">NOT SET</span>') . "</td></tr>";
    echo "</table>";
    
    if (!$invoice['client_email']) {
        echo "<p class='error'><strong>⚠ NO EMAIL ADDRESS FOUND - This is why the email cannot be sent!</strong></p>";
    } else {
        echo "<p class='success'>✓ Would send to: <strong>{$invoice['client_email']}</strong></p>";
    }
}

// Check email logs
echo "<h2>Email Log History</h2>";
$stmt = $db->prepare('
    SELECT * FROM email_logs 
    WHERE invoice_id = :invoice_id 
    ORDER BY created_at DESC
');
$stmt->execute([':invoice_id' => $invoice['id']]);
$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($logs)) {
    echo "<p class='warning'>⚠ No email logs found - This invoice email was never attempted!</p>";
    echo "<p>Even though the invoice status is '<strong>{$invoice['status']}</strong>', no email was actually sent.</p>";
} else {
    echo "<table>";
    echo "<tr><th>ID</th><th>Status</th><th>Recipient</th><th>Sent At</th><th>Error</th></tr>";
    foreach ($logs as $log) {
        echo "<tr>";
        echo "<td>{$log['id']}</td>";
        echo "<td class='" . ($log['status'] === 'sent' ? 'success' : 'error') . "'>{$log['status']}</td>";
        echo "<td>{$log['recipient_email']}</td>";
        echo "<td>" . ($log['sent_at'] ?: '-') . "</td>";
        echo "<td>" . htmlspecialchars($log['error_message'] ?: '-') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check email templates
echo "<h2>Email Template Check</h2>";
$stmt = $db->query("
    SELECT id, name, email_type, invoice_type, is_active 
    FROM email_templates 
    WHERE email_type = 'new_invoice' 
    ORDER BY invoice_type, priority DESC
");
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Available email templates:</p>";
echo "<table>";
echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Invoice Type</th><th>Active</th></tr>";
foreach ($templates as $tpl) {
    $isMatch = ($tpl['invoice_type'] === 'rental' || $tpl['invoice_type'] === null);
    echo "<tr" . ($isMatch ? " style='background:#e8f5e9'" : "") . ">";
    echo "<td>{$tpl['id']}</td>";
    echo "<td>{$tpl['name']}</td>";
    echo "<td>{$tpl['email_type']}</td>";
    echo "<td>" . ($tpl['invoice_type'] ?: 'ALL') . "</td>";
    echo "<td class='" . ($tpl['is_active'] ? 'success' : 'error') . "'>" . ($tpl['is_active'] ? 'Yes' : 'No') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Summary
echo "<h2>Summary</h2>";
echo "<div style='border: 2px solid #333; padding: 15px; background: #f9f9f9;'>";

$hasEmail = false;
if ($invoice['user_id']) {
    $hasEmail = !empty($invoice['user_email']);
} else {
    $hasEmail = !empty($invoice['client_email']);
}

if (!$hasEmail) {
    echo "<p class='error'><strong>PROBLEM:</strong> The recipient has no email address configured.</p>";
    echo "<p>To fix this:</p>";
    echo "<ol>";
    if ($invoice['user_id']) {
        echo "<li>Go to Users management</li>";
        echo "<li>Edit user: {$invoice['first_name']} {$invoice['last_name']}</li>";
        echo "<li>Add an email address in the 'Email' field</li>";
    } else {
        echo "<li>Go to Clients management</li>";
        echo "<li>Edit client: {$invoice['client_name']}</li>";
        echo "<li>Add an email address</li>";
    }
    echo "</ol>";
} elseif (empty($logs)) {
    echo "<p class='warning'><strong>ISSUE:</strong> Invoice marked as '{$invoice['status']}' but no email was sent.</p>";
    echo "<p>This can happen when:</p>";
    echo "<ul>";
    echo "<li>Email service failed silently</li>";
    echo "<li>Mailhog was not running at the time</li>";
    echo "<li>PDF generation failed</li>";
    echo "</ul>";
    echo "<p><a href='test_email_standalone.php?test_invoice={$invoice['id']}'>Try sending email now</a></p>";
} else {
    $lastLog = $logs[0];
    if ($lastLog['status'] === 'sent') {
        echo "<p class='success'><strong>✓ Email was sent successfully</strong></p>";
        echo "<p>Last sent: {$lastLog['sent_at']} to {$lastLog['recipient_email']}</p>";
    } else {
        echo "<p class='error'><strong>✗ Email sending failed</strong></p>";
        echo "<p>Error: " . htmlspecialchars($lastLog['error_message']) . "</p>";
    }
}
echo "</div>";

echo "</body></html>";