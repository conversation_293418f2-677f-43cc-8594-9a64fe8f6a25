<?php
// Create a test invoice for Frank
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html><html><head><title>Create Test Invoice</title></head><body>";
    echo "<h2>Creating Test Invoice for Frank</h2>";
    
    // Get user Frank
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color: red;'>User Frank (ID 1) not found!</p>";
        exit;
    }
    
    echo "<p>User: {$user['first_name']} {$user['last_name']} (Email: {$user['email']})</p>";
    
    // Check if invoice already exists
    $stmt = $db->prepare("
        SELECT id, invoice_number 
        FROM invoices 
        WHERE user_id = 1 
        AND document_type_id = 1 
        AND invoice_type_id = 4
        ORDER BY id DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing) {
        echo "<h3>Invoice Already Exists!</h3>";
        echo "<p>Found invoice: {$existing['invoice_number']} (ID: {$existing['id']})</p>";
        
        // Add items if missing
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoice_items WHERE invoice_id = ?");
        $stmt->execute([$existing['id']]);
        $itemCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($itemCount['count'] == 0) {
            echo "<p>Adding missing invoice items...</p>";
            $stmt = $db->prepare("
                INSERT INTO invoice_items (
                    invoice_id, description, quantity, unit_price,
                    vat_rate, vat_amount
                ) VALUES 
                (:invoice_id, 'Loyer mensuel', 1, 100, 17, 17)
            ");
            $stmt->execute([':invoice_id' => $existing['id']]);
            echo "<p>✓ Invoice items added</p>";
        }
        
        $invoiceId = $existing['id'];
        $invoiceNumber = $existing['invoice_number'];
    } else {
        // Get next invoice number
        $stmt = $db->query("
            SELECT MAX(CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED)) as max_num 
            FROM invoices 
            WHERE invoice_number LIKE 'FAC-%'
        ");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $nextNum = ($result['max_num'] ?? 0) + 1;
        $invoiceNumber = sprintf('FAC-DIV-2025-%04d', $nextNum);
        
        echo "<p>Creating invoice: $invoiceNumber</p>";
    
    // Create invoice
    $stmt = $db->prepare("
        INSERT INTO invoices (
            document_type_id, invoice_type_id, invoice_number,
            issue_date, due_date, status, user_id,
            subject, period, subtotal, vat_amount, total,
            created_at, created_by, payment_term_id
        ) VALUES (
            1, 4, :invoice_number,
            CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'draft', 1,
            'LOYER + CHARGES', 'JUILLET 2025', 100.00, 17.00, 117.00,
            NOW(), 1, 1
        )
    ");
    
        $stmt->execute([':invoice_number' => $invoiceNumber]);
        $invoiceId = $db->lastInsertId();
        
        echo "<p>✓ Invoice created with ID: $invoiceId</p>";
        
        // Add invoice items
        $stmt = $db->prepare("
            INSERT INTO invoice_items (
                invoice_id, description, quantity, unit_price,
                vat_rate, vat_amount
            ) VALUES 
            (:invoice_id, 'Loyer mensuel', 1, 100, 17, 17)
        ");
        
        $stmt->execute([':invoice_id' => $invoiceId]);
        echo "<p>✓ Invoice item added</p>";
    }
    
    // Show invoice details
    echo "<h3>Invoice Created Successfully!</h3>";
    echo "<ul>";
    echo "<li>Invoice Number: $invoiceNumber</li>";
    echo "<li>User: {$user['first_name']} {$user['last_name']}</li>";
    echo "<li>Email: {$user['email']}</li>";
    echo "<li>Subject: LOYER + CHARGES</li>";
    echo "<li>Period: JUILLET 2025</li>";
    echo "<li>Total: €117.00</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<p>Now you can:</p>";
    echo "<ol>";
    echo "<li><a href='/fit/test_email_direct.php'>Run the email test with PDF</a></li>";
    echo "<li><a href='/fit/public/invoices'>View all invoices</a></li>";
    echo "<li><a href='/fit/public/invoices/{$invoiceId}'>View this invoice</a></li>";
    echo "</ol>";
    
    echo "</body></html>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}