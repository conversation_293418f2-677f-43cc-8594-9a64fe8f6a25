<?php
// Find all course data in the database

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Find All Course Data</h1>";

// 1. Find all tables with 'course' in the name
echo "<h2>1. Tables with 'course' in name:</h2>";
$stmt = $db->query("SHOW TABLES LIKE '%course%'");
$courseTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "<ul>";
foreach ($courseTables as $table) {
    echo "<li><strong>$table</strong></li>";
}
echo "</ul>";

// 2. Check each course table
foreach ($courseTables as $table) {
    echo "<h3>Table: $table</h3>";
    
    // Get table structure
    $stmt = $db->query("DESCRIBE $table");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Columns:</p>";
    echo "<table border='1' cellpadding='3' style='font-size: 0.9em;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Get sample data
    try {
        $stmt = $db->query("SELECT * FROM $table LIMIT 5");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Sample data (" . count($data) . " rows shown, ";
        $countStmt = $db->query("SELECT COUNT(*) FROM $table");
        echo $countStmt->fetchColumn() . " total):</p>";
        
        if (count($data) > 0) {
            echo "<table border='1' cellpadding='3' style='font-size: 0.9em;'>";
            echo "<tr>";
            foreach (array_keys($data[0]) as $key) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            foreach ($data as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error reading table: " . $e->getMessage() . "</p>";
    }
}

// 3. Check for course-related columns in other tables
echo "<h2>3. Tables with course-related columns:</h2>";
$tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

foreach ($tables as $table) {
    $stmt = $db->query("SHOW COLUMNS FROM $table LIKE '%course%'");
    $courseColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($courseColumns) > 0) {
        echo "<h4>$table:</h4>";
        echo "<ul>";
        foreach ($courseColumns as $col) {
            echo "<li>{$col['Field']} ({$col['Type']})</li>";
        }
        echo "</ul>";
        
        // Get sample data for these columns
        $columnNames = array_column($courseColumns, 'Field');
        $columnList = implode(', ', $columnNames);
        
        if (strpos($table, 'user') !== false) {
            $columnList = "id, username, $columnList";
        }
        
        try {
            $stmt = $db->query("SELECT $columnList FROM $table WHERE " . $columnNames[0] . " IS NOT NULL AND " . $columnNames[0] . " != '' LIMIT 5");
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($data) > 0) {
                echo "<p>Sample data with courses:</p>";
                echo "<table border='1' cellpadding='3' style='font-size: 0.9em;'>";
                echo "<tr>";
                foreach (array_keys($data[0]) as $key) {
                    echo "<th>$key</th>";
                }
                echo "</tr>";
                foreach ($data as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            // Ignore errors
        }
    }
}

// 4. Check relationship between users and courses
echo "<h2>4. Finding User-Course Relationships:</h2>";

// Check if there's a user_courses join table
if (in_array('user_courses', $courseTables)) {
    echo "<h3>user_courses table found - checking relationships:</h3>";
    
    $stmt = $db->query("
        SELECT uc.*, u.username, CONCAT(u.first_name, ' ', u.last_name) as user_name,
               c.name as course_name
        FROM user_courses uc
        JOIN users u ON uc.user_id = u.id
        LEFT JOIN courses c ON uc.course_id = c.id
        LIMIT 10
    ");
    $relationships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($relationships) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User ID</th><th>User Name</th><th>Course ID</th><th>Course Name</th></tr>";
        foreach ($relationships as $rel) {
            echo "<tr>";
            echo "<td>{$rel['user_id']}</td>";
            echo "<td>{$rel['user_name']} ({$rel['username']})</td>";
            echo "<td>{$rel['course_id']}</td>";
            echo "<td>{$rel['course_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// 5. Check coaches in group 24 with their courses
echo "<h2>5. Coaches (Group 24) and Their Courses:</h2>";

// First, let's see what course data we can join
if (in_array('user_courses', $courseTables) && in_array('courses', $courseTables)) {
    $stmt = $db->prepare("
        SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name,
               GROUP_CONCAT(c.name SEPARATOR ', ') as courses
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        LEFT JOIN user_courses uc ON u.id = uc.user_id
        LEFT JOIN courses c ON uc.course_id = c.id
        WHERE ugm.group_id = 24
        AND u.is_active = 1
        GROUP BY u.id
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute();
    $coachesWithCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Courses</th></tr>";
    foreach ($coachesWithCourses as $coach) {
        echo "<tr>";
        echo "<td>{$coach['id']}</td>";
        echo "<td>{$coach['name']}</td>";
        echo "<td>{$coach['username']}</td>";
        echo "<td>" . ($coach['courses'] ? $coach['courses'] : '<em style="color: red;">No courses</em>') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #e9e9e9; font-weight: bold; }
td, th { padding: 5px 8px; border: 1px solid #ddd; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #666; }
</style>

<hr>
<p><a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a></p>