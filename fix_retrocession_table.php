<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fixing retrocession_data_entry Table</h2>\n";
    
    // First, clean up the partial invoice
    echo "<h3>1. Cleaning up partial invoice #319</h3>\n";
    $stmt = $pdo->query("SELECT * FROM invoices WHERE id = 319");
    $invoice = $stmt->fetch();
    if ($invoice) {
        echo "Found invoice #319 - Status: {$invoice['status']}\n";
        if ($invoice['status'] == 'draft' && empty($invoice['invoice_number'])) {
            echo "<a href='?delete_invoice=319'>Delete this partial invoice</a>\n";
        }
    }
    
    if (isset($_GET['delete_invoice'])) {
        $id = $_GET['delete_invoice'];
        // Delete invoice lines first
        $pdo->exec("DELETE FROM invoice_lines WHERE invoice_id = $id");
        // Delete invoice
        $pdo->exec("DELETE FROM invoices WHERE id = $id");
        // Delete from user_generated_invoices
        $pdo->exec("DELETE FROM user_generated_invoices WHERE invoice_id = $id");
        echo "✓ Deleted invoice #$id and its lines\n";
    }
    
    // Check table structure
    echo "\n<h3>2. Current retrocession_data_entry structure:</h3>\n";
    $stmt = $pdo->query("DESCRIBE retrocession_data_entry");
    $columns = [];
    while ($row = $stmt->fetch()) {
        $columns[$row['Field']] = $row;
        echo "{$row['Field']} - {$row['Type']}\n";
    }
    
    // Check for missing columns
    $requiredColumns = [
        'cns_services_count' => 'INT DEFAULT 0',
        'patient_services_count' => 'INT DEFAULT 0',
        'secretary_services' => 'DECIMAL(10,2) DEFAULT 0',
        'data_source' => "VARCHAR(50) DEFAULT 'manual'"
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $col => $type) {
        if (!isset($columns[$col])) {
            $missingColumns[$col] = $type;
        }
    }
    
    if (!empty($missingColumns)) {
        echo "\n<h3>⚠️ Missing columns:</h3>\n";
        foreach ($missingColumns as $col => $type) {
            echo "- $col ($type)\n";
        }
        echo "\n<a href='?add_columns=1'>Click here to add all missing columns</a>\n";
    } else {
        echo "\n✓ All required columns exist\n";
    }
    
    // Handle column addition
    if (isset($_GET['add_columns'])) {
        echo "\n<h3>Adding missing columns...</h3>\n";
        
        foreach ($missingColumns as $col => $type) {
            try {
                $pdo->exec("ALTER TABLE retrocession_data_entry ADD COLUMN $col $type");
                echo "✓ Added column: $col\n";
            } catch (Exception $e) {
                echo "⚠️ Error adding $col: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n<a href='test_retrocession_generation.php'>Test retrocession generation now</a>\n";
    }
    
    // Show sample data
    echo "\n<h3>3. Sample retrocession_data_entry records:</h3>\n";
    $stmt = $pdo->query("
        SELECT r.*, c.name as practitioner_name 
        FROM retrocession_data_entry r
        LEFT JOIN clients c ON r.practitioner_id = c.id
        ORDER BY r.id DESC LIMIT 5
    ");
    
    $results = $stmt->fetchAll();
    if (count($results) > 0) {
        echo "<table border='1' style='font-size: 12px;'>\n";
        foreach ($results as $i => $row) {
            if ($i === 0) {
                echo "<tr>";
                foreach (array_keys($row) as $col) {
                    echo "<th>$col</th>";
                }
                echo "</tr>\n";
            }
            echo "<tr>";
            foreach ($row as $val) {
                echo "<td>" . htmlspecialchars(substr((string)$val, 0, 20)) . "</td>";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "No data in retrocession_data_entry table\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}