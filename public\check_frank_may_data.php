<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking <PERSON>'s Retrocession Data</h2>";
    
    // Get <PERSON>'s user ID
    $stmt = $db->prepare("SELECT id, first_name, last_name FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color: red;'>Frank Huet not found!</p>";
        exit;
    }
    
    echo "<p>User: {$user['first_name']} {$user['last_name']} (ID: {$user['id']})</p>";
    
    // Check if year column exists
    $yearCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYearColumn = $yearCheck->rowCount() > 0;
    
    // Get all monthly data for Frank
    echo "<h3>All Monthly Retrocession Amounts:</h3>";
    
    if ($hasYearColumn) {
        $stmt = $db->prepare("
            SELECT month, year, cns_amount, patient_amount, is_active 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id 
            ORDER BY year DESC, month DESC
        ");
    } else {
        $stmt = $db->prepare("
            SELECT month, cns_amount, patient_amount, is_active 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id 
            ORDER BY month DESC
        ");
    }
    
    $stmt->execute(['user_id' => $user['id']]);
    $amounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($amounts)) {
        echo "<p style='color: red;'>No monthly amounts configured for Frank!</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Month</th>" . ($hasYearColumn ? "<th>Year</th>" : "") . "<th>CNS Amount</th><th>Patient Amount</th><th>Active</th></tr>";
        
        foreach ($amounts as $amount) {
            $monthName = ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][$amount['month']];
            echo "<tr>";
            echo "<td>{$monthName} ({$amount['month']})</td>";
            if ($hasYearColumn) {
                echo "<td>" . ($amount['year'] ?? '2025') . "</td>";
            }
            echo "<td>€" . number_format($amount['cns_amount'], 2) . "</td>";
            echo "<td>€" . number_format($amount['patient_amount'], 2) . "</td>";
            echo "<td>" . ($amount['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Specifically check for May 2025
        echo "<h3>May 2025 Data:</h3>";
        $mayFound = false;
        foreach ($amounts as $amount) {
            if ($amount['month'] == 5) {
                $mayFound = true;
                echo "<p style='color: green;'>✓ May data found:</p>";
                echo "<ul>";
                echo "<li>CNS Amount: €" . number_format($amount['cns_amount'], 2) . "</li>";
                echo "<li>Patient Amount: €" . number_format($amount['patient_amount'], 2) . "</li>";
                echo "<li>Active: " . ($amount['is_active'] ? 'Yes' : 'No') . "</li>";
                echo "</ul>";
                break;
            }
        }
        
        if (!$mayFound) {
            echo "<p style='color: red;'>✗ No May data found!</p>";
            echo "<p>Need to add May 2025 data for retrocession generation to work.</p>";
        }
    }
    
    // Also check retrocession settings
    echo "<h3>Retrocession Settings:</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_retrocession_settings 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute(['user_id' => $user['id']]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<p style='color: green;'>✓ Settings found:</p>";
        echo "<ul>";
        echo "<li>CNS: {$settings['cns_label']} - {$settings['cns_value']}" . ($settings['cns_type'] == 'percentage' ? '%' : '€') . "</li>";
        echo "<li>Patient: {$settings['patient_label']} - {$settings['patient_value']}" . ($settings['patient_type'] == 'percentage' ? '%' : '€') . "</li>";
        if ($settings['secretary_value'] > 0) {
            echo "<li>Secretary: {$settings['secretary_label']} - {$settings['secretary_value']}" . ($settings['secretary_type'] == 'percentage' ? '%' : '€') . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>No custom settings found (will use defaults)</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}