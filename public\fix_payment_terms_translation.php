<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== FIXING PAYMENT TERMS TRANSLATION ISSUE ===\n\n";

// Force French language
$_SESSION['user_language'] = 'fr';
\App\Helpers\Language::setLanguage('fr');

// Reload translations to ensure they're loaded
\App\Helpers\Language::load('config', 'fr');
\App\Helpers\Language::load('common', 'fr');

echo "1. Testing translations after forced reload:\n";
echo "   config.code = " . __('config.code') . "\n";
echo "   common.name = " . __('common.name') . "\n";
echo "   config.payment_term_name_hint = " . __('config.payment_term_name_hint') . "\n\n";

// Check if there are any payment terms without proper names
try {
    $db = Flight::db();
    
    echo "2. Checking payment terms in database:\n";
    $stmt = $db->query("SELECT id, name, code, days, description FROM config_payment_terms");
    $terms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($terms as $term) {
        echo "   Term #{$term['id']}:\n";
        echo "   - Code: {$term['code']}\n";
        echo "   - Name (raw): {$term['name']}\n";
        
        // Try to decode the name
        if (!empty($term['name']) && $term['name'] !== 'null') {
            $nameData = json_decode($term['name'], true);
            if ($nameData) {
                echo "   - Name (decoded): " . json_encode($nameData) . "\n";
            } else {
                echo "   - Name decode failed!\n";
            }
        }
        echo "\n";
    }
    
    // Check if there are any terms with literal "config.code" as name
    echo "3. Checking for terms with translation keys as values:\n";
    $stmt = $db->prepare("SELECT * FROM config_payment_terms WHERE name LIKE '%config.%' OR description LIKE '%config.%'");
    $stmt->execute();
    $badTerms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($badTerms) > 0) {
        echo "   Found " . count($badTerms) . " terms with translation keys as values!\n";
        foreach ($badTerms as $term) {
            echo "   - Term #{$term['id']} (code: {$term['code']}) has bad data\n";
        }
        
        echo "\n4. Fixing bad data...\n";
        // Fix any terms with translation keys as names
        foreach ($badTerms as $term) {
            // Generate a proper name based on the code
            $properName = ucwords(str_replace(['_', '-'], ' ', $term['code']));
            $nameJson = json_encode(['fr' => $properName, 'en' => $properName]);
            
            $updateStmt = $db->prepare("UPDATE config_payment_terms SET name = ? WHERE id = ?");
            $updateStmt->execute([$nameJson, $term['id']]);
            echo "   Fixed term #{$term['id']}: {$term['code']} -> {$properName}\n";
        }
    } else {
        echo "   No terms found with translation keys as values.\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\n5. Clear any cached translations:\n";
// Clear the loaded groups to force reload
$reflection = new ReflectionClass(\App\Helpers\Language::class);
$loadedGroupsProp = $reflection->getProperty('loadedGroups');
$loadedGroupsProp->setAccessible(true);
$loadedGroupsProp->setValue([]);

$translationsProp = $reflection->getProperty('translations');
$translationsProp->setAccessible(true);
$translationsProp->setValue([]);

echo "   Translation cache cleared.\n";

echo "\nDone! Please refresh the payment terms page to see if the issue is resolved.\n";