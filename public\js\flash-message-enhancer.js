/**
 * Flash Message Enhancer - Makes success/error messages more visible
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find all flash messages
    const flashMessages = document.querySelectorAll('.alert');
    
    flashMessages.forEach(alert => {
        // Check if it's a success message about invoice creation
        if (alert.classList.contains('alert-success') && 
            (alert.textContent.includes('créée') || 
             alert.textContent.includes('created') || 
             alert.textContent.includes('envoyée') ||
             alert.textContent.includes('sent'))) {
            
            // Make it more prominent
            alert.style.fontSize = '1.1em';
            alert.style.padding = '20px';
            alert.style.marginBottom = '20px';
            alert.style.border = '2px solid #198754';
            alert.style.animation = 'slideInDown 0.5s ease-out';
            
            // Add an icon if not present
            if (!alert.querySelector('i')) {
                alert.innerHTML = '<i class="bi bi-check-circle-fill me-2" style="font-size: 1.5em;"></i>' + alert.innerHTML;
            }
            
            // Auto-hide after 10 seconds instead of default
            setTimeout(() => {
                alert.style.animation = 'fadeOut 0.5s ease-out forwards';
                setTimeout(() => alert.remove(), 500);
            }, 10000);
        }
    });
});

// Add animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }
    
    .alert-success {
        position: relative;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
`;
document.head.appendChild(style);