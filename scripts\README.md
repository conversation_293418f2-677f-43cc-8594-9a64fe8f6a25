# Scripts Directory

This directory contains maintenance and utility scripts for the Fit360 AdminDesk application.

## Database Backup Scripts

### `database-backup.php`
Creates automated MySQL database backups with compression and retention management.

**Usage:**
```bash
# Create compressed backup with 7-day retention (default)
php scripts/database-backup.php

# Create uncompressed backup with custom retention
php scripts/database-backup.php --no-compress --retention=30

# List existing backups
php scripts/database-backup.php --list
```

### `database-restore.php`
Restores database from backup files with safety features.

**Usage:**
```bash
# List available backups
php scripts/database-restore.php --list

# Restore from backup (with confirmation)
php scripts/database-restore.php storage/backups/fitapp_backup_2024-01-15_10-30-00.sql.gz

# Force restore without confirmation
php scripts/database-restore.php storage/backups/backup.sql.gz --force
```

### `backup.sh`
Shell script wrapper for easy backup execution.

**Usage:**
```bash
# Make executable
chmod +x scripts/backup.sh

# Run backup
./scripts/backup.sh

# Run with custom options
./scripts/backup.sh --no-compress --retention=14
```

## Translation Scripts

### `add-missing-translations.php`
Adds missing translation keys to language files.

### `fix-translations.php`
Fixes and validates translation files.

## Running Scripts

All PHP scripts should be run from the project root directory:

```bash
# From project root
php scripts/script-name.php

# Or make shell scripts executable and run directly
chmod +x scripts/backup.sh
./scripts/backup.sh
```

## Requirements

- PHP 8.1 or higher
- MySQL client tools (mysqldump, mysql)
- Composer dependencies installed
- Proper database configuration in `.env` file

## Documentation

For detailed information about the database backup system, see:
- `docs/DATABASE_BACKUP_SYSTEM.md`
