<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Invoice #252 Analysis</h2>\n";
    
    // Get invoice details
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 252");
    $stmt->execute();
    $invoice = $stmt->fetch();
    
    if ($invoice) {
        echo "<h3>Invoice Header:</h3>\n";
        echo "Number: {$invoice['invoice_number']}\n";
        echo "Subject: {$invoice['subject']}\n";
        echo "Status: {$invoice['status']}\n";
        echo "Total HTVA: {$invoice['total_htva']}€\n";
        echo "Total VAT: {$invoice['total_vat']}€\n";
        echo "Total: " . ($invoice['total_htva'] + $invoice['total_vat']) . "€\n";
        
        echo "\n<h3>Invoice Lines:</h3>\n";
        $stmt = $pdo->prepare("
            SELECT * FROM invoice_lines 
            WHERE invoice_id = 252 
            ORDER BY sort_order, id
        ");
        $stmt->execute();
        
        echo "<table border='1'>\n";
        echo "<tr><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>\n";
        
        while ($line = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>{$line['unit_price']}€</td>";
            echo "<td>{$line['vat_rate']}%</td>";
            echo "<td>{$line['line_total']}€</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Compare with invoice 321 (the one we just generated)
        echo "\n<h3>Invoice #321 (Just Generated) Lines:</h3>\n";
        $stmt = $pdo->prepare("
            SELECT * FROM invoice_lines 
            WHERE invoice_id = 321 
            ORDER BY sort_order, id
        ");
        $stmt->execute();
        
        echo "<table border='1'>\n";
        echo "<tr><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>\n";
        
        while ($line = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>{$line['unit_price']}€</td>";
            echo "<td>{$line['vat_rate']}%</td>";
            echo "<td>{$line['line_total']}€</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Check retrocession data for both
        echo "\n<h3>Retrocession Data Comparison:</h3>\n";
        $stmt = $pdo->prepare("
            SELECT r.*, c.name as practitioner_name
            FROM retrocession_data_entry r
            LEFT JOIN clients c ON r.practitioner_id = c.id
            WHERE r.invoice_id IN (252, 321)
            ORDER BY r.invoice_id
        ");
        $stmt->execute();
        
        while ($retro = $stmt->fetch()) {
            echo "\nInvoice #{$retro['invoice_id']}:\n";
            echo "- Practitioner: {$retro['practitioner_name']}\n";
            echo "- CNS Amount: {$retro['cns_amount']}€\n";
            echo "- Patient Amount: {$retro['patient_amount']}€\n";
            echo "- Period: {$retro['period_month']}/{$retro['period_year']}\n";
        }
        
    } else {
        echo "Invoice #252 not found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}