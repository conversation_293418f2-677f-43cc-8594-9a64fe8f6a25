<?php
// Setup coach group

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Setup Coach Group</h1>";

// Check all existing groups
echo "<h2>Current Groups:</h2>";
$stmt = $db->query("SELECT * FROM user_groups ORDER BY id");
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Description</th></tr>";
foreach ($groups as $group) {
    echo "<tr>";
    echo "<td>{$group['id']}</td>";
    echo "<td>{$group['name']}</td>";
    echo "<td>{$group['description']}</td>";
    echo "</tr>";
}
echo "</table>";

// Check if group 2 exists
$stmt = $db->prepare("SELECT COUNT(*) FROM user_groups WHERE id = 2");
$stmt->execute();
$group2Exists = $stmt->fetchColumn() > 0;

if (!$group2Exists) {
    echo "<h2>Creating Coach Group (ID: 2):</h2>";
    
    // Check if we can use ID 2
    try {
        $stmt = $db->prepare("INSERT INTO user_groups (id, name, description) VALUES (2, 'Coach', 'Coaches who provide courses')");
        $stmt->execute();
        echo "<p class='success'>✅ Coach group created successfully with ID 2</p>";
    } catch (Exception $e) {
        // If ID 2 is taken or auto-increment, create without specifying ID
        try {
            $stmt = $db->prepare("INSERT INTO user_groups (name, description) VALUES ('Coach', 'Coaches who provide courses')");
            $stmt->execute();
            $newId = $db->lastInsertId();
            echo "<p class='success'>✅ Coach group created successfully with ID $newId</p>";
            
            // Update config to use this new ID
            $stmt = $db->prepare("INSERT INTO config_settings (name, value) VALUES ('coach_group_id', ?) ON DUPLICATE KEY UPDATE value = ?");
            $stmt->execute([$newId, $newId]);
            echo "<p>Updated coach_group_id config to $newId</p>";
        } catch (Exception $e2) {
            echo "<p class='error'>❌ Error creating coach group: " . $e2->getMessage() . "</p>";
        }
    }
} else {
    echo "<p>Coach group (ID: 2) already exists</p>";
}

// Find users with courses who should be coaches
echo "<h2>Users with Courses (Should be Coaches):</h2>";
$stmt = $db->query("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, 
           u.course_name, u.is_active, u.can_be_invoiced
    FROM users u
    WHERE u.course_name IS NOT NULL AND u.course_name != ''
    ORDER BY u.first_name, u.last_name
");
$usersWithCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Found " . count($usersWithCourses) . " users with courses</p>";

if (count($usersWithCourses) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Course</th><th>Active</th><th>Can Invoice</th><th>In Coach Group?</th></tr>";
    
    // Get current coach group ID
    $coachGroupId = 2;
    $stmt = $db->prepare("SELECT value FROM config_settings WHERE name = 'coach_group_id'");
    $stmt->execute();
    $configValue = $stmt->fetchColumn();
    if ($configValue !== false) {
        $coachGroupId = intval($configValue);
    }
    
    foreach ($usersWithCourses as $user) {
        // Check if user is in coach group
        $stmt = $db->prepare("SELECT COUNT(*) FROM user_group_members WHERE user_id = ? AND group_id = ?");
        $stmt->execute([$user['id'], $coachGroupId]);
        $inCoachGroup = $stmt->fetchColumn() > 0;
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td><strong>{$user['course_name']}</strong></td>";
        echo "<td>" . ($user['is_active'] ? '✅' : '❌') . "</td>";
        echo "<td>" . ($user['can_be_invoiced'] ? '✅' : '❌') . "</td>";
        echo "<td>" . ($inCoachGroup ? '✅ Yes' : '❌ No') . "</td>";
        echo "</tr>";
        
        // Add to coach group if not already there and active
        if (!$inCoachGroup && $user['is_active'] && $user['can_be_invoiced']) {
            try {
                $stmt = $db->prepare("INSERT INTO user_group_members (user_id, group_id) VALUES (?, ?)");
                $stmt->execute([$user['id'], $coachGroupId]);
                echo "<tr><td colspan='7' style='color: green;'>➕ Added {$user['name']} to coach group</td></tr>";
            } catch (Exception $e) {
                echo "<tr><td colspan='7' style='color: red;'>❌ Error adding {$user['name']}: " . $e->getMessage() . "</td></tr>";
            }
        }
    }
    echo "</table>";
} else {
    echo "<p>No users with courses found. You may need to assign courses to your coach users.</p>";
}

// Show summary
echo "<h2>Summary:</h2>";
$stmt = $db->prepare("
    SELECT COUNT(DISTINCT u.id) as coach_count
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = ?
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
");
$stmt->execute([$coachGroupId]);
$coachCount = $stmt->fetchColumn();

echo "<p>Coach group (ID: $coachGroupId) now has <strong>$coachCount</strong> active members who can be invoiced.</p>";

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #e9e9e9; }
td, th { padding: 8px; }
</style>

<hr>
<p>
<a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a> | 
<a href='/fit/public/invoices/create?type=location'>Test Location Invoice</a>
</p>