<?php
// Quick script to set group 24 as coach group

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Update configuration
try {
    // First check which table and column to use
    $configTable = 'config_settings';
    $nameColumn = 'name';
    
    // Check if config_settings exists
    $tables = $db->query("SHOW TABLES LIKE 'config_settings'")->fetchAll();
    if (empty($tables)) {
        $configTable = 'config';
        $nameColumn = 'key';
    } else {
        // Check column name
        $columns = $db->query("SHOW COLUMNS FROM config_settings")->fetchAll(PDO::FETCH_COLUMN);
        if (in_array('key', $columns)) {
            $nameColumn = 'key';
        }
    }
    
    // Update or insert
    if ($configTable == 'config_settings') {
        $stmt = $db->prepare("
            INSERT INTO $configTable (`$nameColumn`, `value`, created_at, updated_at) 
            VALUES ('coach_group_id', '24', NOW(), NOW())
            ON DUPLICATE KEY UPDATE `value` = '24', updated_at = NOW()
        ");
    } else {
        $stmt = $db->prepare("
            INSERT INTO $configTable (`$nameColumn`, `value`) 
            VALUES ('coach_group_id', '24')
            ON DUPLICATE KEY UPDATE `value` = '24'
        ");
    }
    $stmt->execute();
    
    echo "<h1>✅ Success!</h1>";
    echo "<p>Set coach_group_id = 24</p>";
    
    // Verify
    $stmt = $db->prepare("SELECT value FROM $configTable WHERE `$nameColumn` = 'coach_group_id'");
    $stmt->execute();
    $value = $stmt->fetchColumn();
    echo "<p>Verified: coach_group_id is now $value</p>";
    
} catch (Exception $e) {
    echo "<h1>❌ Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
}

// Show coaches with courses
echo "<h2>Coaches in Group 24 with Courses:</h2>";
$stmt = $db->query("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name,
           GROUP_CONCAT(uc.course_name ORDER BY uc.display_order SEPARATOR ', ') as courses
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    JOIN user_courses uc ON u.id = uc.user_id
    WHERE ugm.group_id = 24
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
    AND uc.is_active = 1
    GROUP BY u.id
");
$coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($coaches) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Name</th><th>Username</th><th>Courses</th></tr>";
    foreach ($coaches as $coach) {
        echo "<tr>";
        echo "<td>{$coach['name']}</td>";
        echo "<td>{$coach['username']}</td>";
        echo "<td>{$coach['courses']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No coaches with courses found in group 24</p>";
}

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; margin: 20px 0; }
th { background: #e9e9e9; }
td, th { padding: 8px; }
</style>

<hr>
<p>
<a href='/fit/public/invoices/create?type=location' target='_blank' style='padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; display: inline-block; margin: 10px 0;'>
    Test Location Invoice
</a>
</p>
<p>
<a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a>
</p>