<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING DRAFT INVOICES ===\n\n";

try {
    $db = Flight::db();
    
    // Get all invoices with their statuses
    echo "1. All invoices in database:\n";
    $stmt = $db->query("
        SELECT i.id, i.invoice_number, i.status, i.issue_date, i.created_at,
               dt.code as doc_type,
               CASE 
                   WHEN c.client_type = 'individual' THEN CONCAT(c.first_name, ' ', c.last_name)
                   ELSE c.company_name 
               END as client_name
        FROM invoices i
        LEFT JOIN document_types dt ON i.document_type_id = dt.id
        LEFT JOIN clients c ON i.client_id = c.id
        ORDER BY i.created_at DESC
        LIMIT 10
    ");
    $invoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($invoices as $inv) {
        echo "- #{$inv['id']} | {$inv['invoice_number']} | Status: {$inv['status']} | Client: {$inv['client_name']} | Created: {$inv['created_at']}\n";
    }
    
    // Check specifically for draft invoices
    echo "\n2. Draft invoices:\n";
    $stmt = $db->query("
        SELECT COUNT(*) as count 
        FROM invoices 
        WHERE status = 'draft' AND COALESCE(is_archived, FALSE) = FALSE
    ");
    $result = $stmt->fetch(\PDO::FETCH_ASSOC);
    echo "Total draft invoices (not archived): " . $result['count'] . "\n";
    
    // Get draft invoices details
    $stmt = $db->query("
        SELECT i.id, i.invoice_number, i.status, i.issue_date, i.created_at, i.is_archived,
               CASE 
                   WHEN c.client_type = 'individual' THEN CONCAT(c.first_name, ' ', c.last_name)
                   ELSE c.company_name 
               END as client_name
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        WHERE i.status = 'draft'
        ORDER BY i.created_at DESC
    ");
    $drafts = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($drafts) > 0) {
        echo "\nDraft invoice details:\n";
        foreach ($drafts as $draft) {
            echo "- ID: {$draft['id']}\n";
            echo "  Number: {$draft['invoice_number']}\n";
            echo "  Client: {$draft['client_name']}\n";
            echo "  Created: {$draft['created_at']}\n";
            echo "  Archived: " . ($draft['is_archived'] ? 'YES' : 'NO') . "\n\n";
        }
    }
    
    // Check the possible status values
    echo "3. All unique invoice statuses in database:\n";
    $stmt = $db->query("SELECT DISTINCT status, COUNT(*) as count FROM invoices GROUP BY status");
    $statuses = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($statuses as $status) {
        echo "- '{$status['status']}': {$status['count']} invoices\n";
    }
    
    // Check if there's a recent invoice that might be saved with wrong status
    echo "\n4. Most recent invoice:\n";
    $stmt = $db->query("
        SELECT * FROM invoices 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $latest = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($latest) {
        echo "ID: {$latest['id']}\n";
        echo "Number: {$latest['invoice_number']}\n";
        echo "Status: '{$latest['status']}'\n";
        echo "Created: {$latest['created_at']}\n";
        echo "Total: {$latest['total']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}