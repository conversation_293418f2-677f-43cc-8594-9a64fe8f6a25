<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Invoice Sequence Analysis for RET, LOC, and LOY</h2>";
    
    // Check for invoice_number_sequences table
    echo "<h3>1. Checking for invoice_number_sequences table:</h3>";
    $sql = "SHOW TABLES LIKE 'invoice_number_sequences'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $hasSequenceTable = $stmt->fetchColumn();
    
    if ($hasSequenceTable) {
        echo "<p style='color: green;'>✅ invoice_number_sequences table exists</p>";
        
        $sql = "SELECT * FROM invoice_number_sequences WHERE prefix IN ('RET', 'LOC', 'LOY') OR invoice_type IN ('RET', 'LOC', 'LOY')";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($sequences)) {
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
            echo "<tr style='background-color: #f0f0f0;'>";
            foreach (array_keys($sequences[0]) as $key) {
                echo "<th>{$key}</th>";
            }
            echo "</tr>";
            foreach ($sequences as $seq) {
                echo "<tr>";
                foreach ($seq as $value) {
                    echo "<td>{$value}</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No sequences found for RET, LOC, or LOY</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ invoice_number_sequences table does not exist - sequences are generated differently</p>";
    }
    
    // Check actual invoices for each type
    echo "<h3>2. Latest Invoices by Type:</h3>";
    
    $types = ['RET', 'LOC', 'LOY'];
    foreach ($types as $type) {
        echo "<h4>{$type} Invoices:</h4>";
        
        // Get latest 5 invoices for this type
        $sql = "SELECT id, invoice_number, invoice_type_id, created_at 
                FROM invoices 
                WHERE invoice_number LIKE :pattern 
                ORDER BY id DESC 
                LIMIT 5";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['pattern' => "%{$type}%"]);
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($invoices)) {
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Type ID</th><th>Created</th><th>Sequence</th></tr>";
            foreach ($invoices as $inv) {
                // Extract sequence number
                $seq = '';
                if (preg_match('/(\d{4})$/', $inv['invoice_number'], $matches)) {
                    $seq = $matches[1];
                }
                echo "<tr>";
                echo "<td>{$inv['id']}</td>";
                echo "<td><strong>{$inv['invoice_number']}</strong></td>";
                echo "<td>{$inv['invoice_type_id']}</td>";
                echo "<td>{$inv['created_at']}</td>";
                echo "<td>{$seq}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Analyze pattern
            if (!empty($invoices)) {
                $numbers = [];
                foreach ($invoices as $inv) {
                    if (preg_match('/(\d{4})$/', $inv['invoice_number'], $matches)) {
                        $numbers[] = intval($matches[1]);
                    }
                }
                if (!empty($numbers)) {
                    $max = max($numbers);
                    $next = str_pad($max + 1, 4, '0', STR_PAD_LEFT);
                    echo "<p>Latest sequence: <strong>{$max}</strong> → Next: <strong>FAC-{$type}-2025-{$next}</strong></p>";
                }
            }
        } else {
            echo "<p>No invoices found for {$type}</p>";
        }
    }
    
    // Check invoice_types configuration
    echo "<h3>3. Invoice Types Configuration:</h3>";
    $sql = "SELECT * FROM invoice_types WHERE prefix IN ('RET', 'LOC', 'LOY') OR code IN ('RET', 'LOC', 'LOY')";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Description</th><th>Active</th>";
    echo "</tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['name']}</td>";
        echo "<td><strong>{$type['code']}</strong></td>";
        echo "<td><strong>{$type['prefix']}</strong></td>";
        echo "<td>{$type['description']}</td>";
        echo "<td>" . ($type['is_active'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for sequence generation in config
    echo "<h3>4. Checking Config Table for Sequence Settings:</h3>";
    $sql = "SELECT * FROM config WHERE `key` LIKE '%invoice%' OR `key` LIKE '%sequence%' OR `key` LIKE '%number%'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($configs)) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>Key</th><th>Value</th></tr>";
        foreach ($configs as $config) {
            echo "<tr>";
            echo "<td>{$config['key']}</td>";
            echo "<td>{$config['value']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No invoice-related config entries found</p>";
    }
    
    echo "<h3>5. Document Types Configuration:</h3>";
    $sql = "SELECT * FROM document_types WHERE code = 'invoice'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $docTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($docTypes)) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        foreach (array_keys($docTypes[0]) as $key) {
            echo "<th>{$key}</th>";
        }
        echo "</tr>";
        foreach ($docTypes as $type) {
            echo "<tr>";
            foreach ($type as $value) {
                echo "<td>{$value}</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check config_invoice_types table
    echo "<h3>6. Checking config_invoice_types table:</h3>";
    $sql = "SHOW TABLES LIKE 'config_invoice_types'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $hasConfigInvoiceTypes = $stmt->fetchColumn();
    
    if ($hasConfigInvoiceTypes) {
        echo "<p style='color: green;'>✅ config_invoice_types table exists</p>";
        $sql = "SELECT * FROM config_invoice_types WHERE prefix IN ('RET', 'LOC', 'LOY')";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $configTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($configTypes)) {
            echo "<table border='1' cellpadding='5'>";
            $keys = array_keys($configTypes[0]);
            echo "<tr style='background-color: #f0f0f0;'>";
            foreach ($keys as $key) {
                echo "<th>{$key}</th>";
            }
            echo "</tr>";
            foreach ($configTypes as $type) {
                echo "<tr>";
                foreach ($type as $value) {
                    echo "<td>{$value}</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ config_invoice_types table does not exist - using invoice_types table instead</p>";
    }
    
    echo "<h3>7. Summary:</h3>";
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Invoice Number Pattern:</strong> FAC-[PREFIX]-[YEAR]-[SEQUENCE]</p>";
    echo "<p><strong>Sequence Generation:</strong> Appears to be based on finding the highest existing number and incrementing</p>";
    echo "<p><strong>Table Used:</strong> The system looks for config_invoice_types first, then falls back to invoice_types</p>";
    echo "<p><strong>Document Type:</strong> Uses document_types table for number format configuration</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>