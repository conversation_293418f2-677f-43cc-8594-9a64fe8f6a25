<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CHECKING USER ADDRESS FIELDS ===\n\n";
    
    // Check if address columns exist
    $stmt = $pdo->prepare("
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'fitapp' 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME IN ('address', 'postal_code', 'city', 'country', 'vat_intercommunautaire')
        ORDER BY ORDINAL_POSITION
    ");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($columns) == 0) {
        echo "❌ Address fields are NOT present in the users table!\n\n";
        echo "You need to run the migration: 2025_01_07_add_address_vat_to_users.sql\n\n";
        
        echo "To fix this, run:\n";
        echo "1. Go to http://localhost/fit/public/database/migrate.php\n";
        echo "2. Or run manually:\n";
        echo "   mysql -u root -ptest1234 fitapp < database/migrations/2025_01_07_add_address_vat_to_users.sql\n";
    } else {
        echo "✓ Found " . count($columns) . " address columns:\n\n";
        foreach ($columns as $col) {
            echo "- {$col['COLUMN_NAME']}: {$col['DATA_TYPE']}, ";
            echo "Default: " . ($col['COLUMN_DEFAULT'] ?: 'NULL') . "\n";
        }
        
        echo "\n=== CHECKING USER DATA ===\n\n";
        
        // Check some users
        $stmt = $pdo->query("
            SELECT id, username, first_name, last_name, 
                   address, postal_code, city, country, vat_intercommunautaire 
            FROM users 
            LIMIT 5
        ");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "First 5 users:\n";
        foreach ($users as $user) {
            echo "\nUser #{$user['id']} ({$user['username']}):\n";
            echo "  Name: {$user['first_name']} {$user['last_name']}\n";
            echo "  Address: " . ($user['address'] ?: '(empty)') . "\n";
            echo "  City: " . ($user['city'] ?: '(empty)') . ", ";
            echo ($user['postal_code'] ?: '(empty)') . " ";
            echo ($user['country'] ?: '(empty)') . "\n";
            echo "  VAT: " . ($user['vat_intercommunautaire'] ?: '(none)') . "\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}