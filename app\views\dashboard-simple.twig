{% extends "base-modern.twig" %}

{% block title %}{{ title }} - {{ app_name }}{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">{{ greeting }}, {{ user.name|default('User') }}</h1>
                <p class="text-muted">Welcome back! Here's your dashboard for {{ current_month_name }}</p>
            </div>
            <div class="col-auto">
                <div class="btn-list">
                    <a href="{{ url('/invoices/create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Invoice
                    </a>
                    <a href="{{ url('/clients/create') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus"></i> Add Client
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm bg-primary-lt">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-0">{{ stats.total_clients|number_format(0) }}</h4>
                            <p class="text-muted mb-0">Total Clients</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm bg-success-lt">
                                <i class="fas fa-user-injured"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-0">{{ stats.total_patients|number_format(0) }}</h4>
                            <p class="text-muted mb-0">Total Patients</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm bg-info-lt">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-0">{{ stats.total_invoices|number_format(0) }}</h4>
                            <p class="text-muted mb-0">Total Invoices</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm bg-warning-lt">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-0">€{{ stats.revenue_this_month|number_format(2) }}</h4>
                            <p class="text-muted mb-0">Revenue This Month</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Monthly Revenue</h3>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url('/invoices/create') }}" class="btn btn-primary">
                            <i class="fas fa-file-invoice me-2"></i>Create Invoice
                        </a>
                        <a href="{{ url('/payments/create') }}" class="btn btn-success">
                            <i class="fas fa-coins me-2"></i>Record Payment
                        </a>
                        <a href="{{ url('/clients') }}" class="btn btn-info">
                            <i class="fas fa-users me-2"></i>View All Clients
                        </a>
                        <a href="{{ url('/reports') }}" class="btn btn-secondary">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Invoices -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Invoices</h3>
                    <div class="card-actions">
                        <a href="{{ url('/invoices') }}" class="btn btn-sm btn-outline-primary">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice Number</th>
                                    <th>Client</th>
                                    <th>Issue Date</th>
                                    <th>Due Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>
                                        <a href="{{ url('/invoices/view/' ~ invoice.id) }}">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.client_name }}</td>
                                    <td>{{ invoice.issue_date|date('d/m/Y') }}</td>
                                    <td>{{ invoice.due_date|date('d/m/Y') }}</td>
                                    <td>€{{ invoice.total_amount|number_format(2) }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                            <span class="badge bg-success">Paid</span>
                                        {% elseif invoice.status == 'sent' %}
                                            <span class="badge bg-info">Sent</span>
                                        {% elseif invoice.status == 'draft' %}
                                            <span class="badge bg-secondary">Draft</span>
                                        {% elseif invoice.status == 'overdue' %}
                                            <span class="badge bg-danger">Overdue</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ invoice.status|capitalize }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url('/invoices/view/' ~ invoice.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        No recent invoices found.
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Responsive Styles -->
<style>
@media (max-width: 768px) {
    .page-header .btn-list {
        margin-top: 1rem;
    }
    
    .page-header .btn-list .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .card-actions {
        margin-top: 0.5rem;
    }
}

.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 16px;
}

.bg-primary-lt {
    background-color: rgba(32, 107, 196, 0.1);
    color: #206bc4;
}

.bg-success-lt {
    background-color: rgba(47, 179, 68, 0.1);
    color: #2fb344;
}

.bg-info-lt {
    background-color: rgba(66, 153, 225, 0.1);
    color: #4299e1;
}

.bg-warning-lt {
    background-color: rgba(247, 103, 7, 0.1);
    color: #f76707;
}

.card-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.card-header {
    position: relative;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueData = {{ monthly_revenue|json_encode|raw }};
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: revenueData.map(item => item.month),
            datasets: [{
                label: 'Revenue',
                data: revenueData.map(item => item.revenue),
                borderColor: 'rgb(32, 107, 196)',
                backgroundColor: 'rgba(32, 107, 196, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '€' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}