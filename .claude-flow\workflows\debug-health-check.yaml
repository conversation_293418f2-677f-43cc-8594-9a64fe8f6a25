name: Debug Health Check
description: Comprehensive debugging workflow for Fit360 AdminDesk - checks system health, common issues, and generates diagnostic reports
version: 1.0.0
author: Claude-Flow

inputs:
  - name: check_type
    type: string
    description: Type of check to perform (all, database, cache, permissions, email, performance)
    required: false
    default: "all"
    
  - name: verbose
    type: boolean
    description: Show detailed debug information
    required: false
    default: true

  - name: test_email
    type: string
    description: Email address to test email functionality
    required: false
    default: ""

env:
  app_root: "."
  log_dir: "storage/logs"
  cache_dir: "storage/cache"

steps:
  - name: System Information
    type: shell
    command: |
      echo "=== System Information ==="
      echo "PHP Version: $(php -v | head -n 1)"
      echo "Working Directory: $(pwd)"
      echo "Current User: $(whoami)"
      echo "Timestamp: $(date)"
      echo ""

  - name: Check PHP Configuration
    type: shell
    command: |
      php -r "
      echo \"\\n=== PHP Configuration ===\\n\";
      echo 'Memory Limit: ' . ini_get('memory_limit') . \"\\n\";
      echo 'Max Execution Time: ' . ini_get('max_execution_time') . \"s\\n\";
      echo 'Post Max Size: ' . ini_get('post_max_size') . \"\\n\";
      echo 'Upload Max Filesize: ' . ini_get('upload_max_filesize') . \"\\n\";
      echo 'Error Reporting: ' . error_reporting() . \"\\n\";
      echo 'Display Errors: ' . (ini_get('display_errors') ? 'On' : 'Off') . \"\\n\";
      
      // Check required extensions
      echo \"\\n=== PHP Extensions ===\\n\";
      \$required = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'session', 'filter', 'fileinfo'];
      foreach (\$required as \$ext) {
          echo \$ext . ': ' . (extension_loaded(\$ext) ? '✓ Loaded' : '✗ Missing') . \"\\n\";
      }
      "

  - name: Environment Configuration Check
    type: shell
    command: |
      php -r "
      echo \"\\n=== Environment Configuration ===\\n\";
      
      if (!file_exists('.env')) {
          echo \"⚠️  .env file not found!\\n\";
          exit(1);
      }
      
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      // Check critical env vars (without exposing sensitive data)
      \$vars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'APP_URL', 'APP_ENV', 'APP_DEBUG'];
      foreach (\$vars as \$var) {
          if (isset(\$_ENV[\$var]) && !empty(\$_ENV[\$var])) {
              if (in_array(\$var, ['DB_USER', 'DB_PASS'])) {
                  echo \$var . ': ****** (set)\\n';
              } else {
                  echo \$var . ': ' . \$_ENV[\$var] . \"\\n\";
              }
          } else {
              echo \$var . ': ⚠️ NOT SET\\n';
          }
      }
      "

  - name: Database Connection Test
    type: conditional
    condition: "{{ inputs.check_type == 'all' || inputs.check_type == 'database' }}"
    steps:
      - name: Test Database
        type: shell
        command: |
          php -r "
          echo \"\\n=== Database Connection Test ===\\n\";
          
          require_once 'vendor/autoload.php';
          \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
          \$dotenv->load();
          
          try {
              \$pdo = new PDO(
                  'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'] . ';charset=utf8mb4',
                  \$_ENV['DB_USER'],
                  \$_ENV['DB_PASS'],
                  [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
              );
              
              echo \"✓ Database connection successful\\n\";
              echo \"Database: \" . \$_ENV['DB_NAME'] . \"\\n\";
              echo \"Host: \" . \$_ENV['DB_HOST'] . \"\\n\";
              
              // Check database size
              \$stmt = \$pdo->query(\"
                  SELECT 
                      table_schema AS 'Database',
                      ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
                  FROM information_schema.tables 
                  WHERE table_schema = '\" . \$_ENV['DB_NAME'] . \"'
                  GROUP BY table_schema
              \");
              \$size = \$stmt->fetch(PDO::FETCH_ASSOC);
              echo \"Database Size: \" . (\$size['Size (MB)'] ?? '0') . \" MB\\n\";
              
              // Check key tables
              echo \"\\n=== Table Record Counts ===\\n\";
              \$tables = ['users', 'invoices', 'clients', 'invoice_lines', 'retrocession_data_entry', 'translations'];
              
              foreach (\$tables as \$table) {
                  try {
                      \$stmt = \$pdo->query(\"SELECT COUNT(*) as count FROM \$table\");
                      \$count = \$stmt->fetchColumn();
                      echo sprintf(\"%-25s: %d records\\n\", \$table, \$count);
                  } catch (Exception \$e) {
                      echo sprintf(\"%-25s: ⚠️ Table not found\\n\", \$table);
                  }
              }
              
              // Check for slow queries
              if ('${inputs.verbose}' === 'true') {
                  echo \"\\n=== Recent Slow Queries ===\\n\";
                  try {
                      \$stmt = \$pdo->query(\"SHOW VARIABLES LIKE 'slow_query_log'\");
                      \$slowLog = \$stmt->fetch(PDO::FETCH_ASSOC);
                      echo \"Slow Query Log: \" . (\$slowLog['Value'] ?? 'OFF') . \"\\n\";
                  } catch (Exception \$e) {
                      echo \"Cannot check slow query log (insufficient privileges)\\n\";
                  }
              }
              
          } catch (Exception \$e) {
              echo \"✗ Database connection failed: \" . \$e->getMessage() . \"\\n\";
              exit(1);
          }
          "

  - name: File Permissions Check
    type: conditional
    condition: "{{ inputs.check_type == 'all' || inputs.check_type == 'permissions' }}"
    steps:
      - name: Check Directories
        type: shell
        command: |
          echo -e "\n=== Directory Permissions ==="
          
          # Directories that need to be writable
          dirs=(
            "storage/logs"
            "storage/cache"
            "storage/cache/twig"
            "storage/uploads"
            "public/uploads"
            "public/assets"
          )
          
          for dir in "${dirs[@]}"; do
            if [ -d "$dir" ]; then
              perms=$(stat -c "%a" "$dir" 2>/dev/null || stat -f "%p" "$dir" 2>/dev/null | cut -c 4-6)
              owner=$(stat -c "%U:%G" "$dir" 2>/dev/null || stat -f "%Su:%Sg" "$dir" 2>/dev/null)
              
              if [ -w "$dir" ]; then
                echo "✓ $dir - $perms ($owner) - Writable"
              else
                echo "✗ $dir - $perms ($owner) - NOT writable!"
              fi
            else
              echo "⚠️  $dir - Directory does not exist"
            fi
          done

  - name: Cache System Check
    type: conditional
    condition: "{{ inputs.check_type == 'all' || inputs.check_type == 'cache' }}"
    steps:
      - name: Check Cache
        type: shell
        command: |
          php -r "
          echo \"\\n=== Cache System Check ===\\n\";
          
          \$cacheDir = 'storage/cache/twig';
          
          if (is_dir(\$cacheDir)) {
              \$files = glob(\$cacheDir . '/*');
              echo \"Twig Cache Directory: ✓ Exists\\n\";
              echo \"Cached Templates: \" . count(\$files) . \"\\n\";
              
              if (count(\$files) > 0) {
                  \$totalSize = 0;
                  \$oldestTime = time();
                  
                  foreach (\$files as \$file) {
                      \$totalSize += filesize(\$file);
                      \$mtime = filemtime(\$file);
                      if (\$mtime < \$oldestTime) {
                          \$oldestTime = \$mtime;
                      }
                  }
                  
                  echo \"Total Cache Size: \" . round(\$totalSize / 1024 / 1024, 2) . \" MB\\n\";
                  echo \"Oldest Cache File: \" . date('Y-m-d H:i:s', \$oldestTime) . \"\\n\";
              }
          } else {
              echo \"⚠️ Twig Cache Directory not found!\\n\";
          }
          
          // Check OpCache
          if (function_exists('opcache_get_status')) {
              \$status = opcache_get_status();
              if (\$status) {
                  echo \"\\nOpCache Status: ✓ Enabled\\n\";
                  echo \"Memory Usage: \" . round(\$status['memory_usage']['used_memory'] / 1024 / 1024, 2) . \" MB\\n\";
                  echo \"Hit Rate: \" . round(\$status['opcache_statistics']['hits'] / (\$status['opcache_statistics']['hits'] + \$status['opcache_statistics']['misses']) * 100, 2) . \"%\\n\";
              } else {
                  echo \"\\nOpCache: ✗ Disabled\\n\";
              }
          }
          "

  - name: Email Configuration Test
    type: conditional
    condition: "{{ inputs.check_type == 'all' || inputs.check_type == 'email' }}"
    steps:
      - name: Check Email Settings
        type: shell
        command: |
          php -r "
          echo \"\\n=== Email Configuration Test ===\\n\";
          
          require_once 'vendor/autoload.php';
          require_once 'app/Core/bootstrap.php';
          
          // Get email configuration
          \$config = App\\Core\\Config::get('email');
          
          if (\$config) {
              echo \"SMTP Host: \" . (\$config['smtp_host'] ?? 'Not configured') . \"\\n\";
              echo \"SMTP Port: \" . (\$config['smtp_port'] ?? 'Not configured') . \"\\n\";
              echo \"SMTP Auth: \" . ((\$config['smtp_auth'] ?? false) ? 'Enabled' : 'Disabled') . \"\\n\";
              echo \"From Email: \" . (\$config['from_email'] ?? 'Not configured') . \"\\n\";
              
              if ('${inputs.test_email}' !== '') {
                  echo \"\\nSending test email to: ${inputs.test_email}\\n\";
                  
                  try {
                      \$mail = new PHPMailer\\PHPMailer\\PHPMailer(true);
                      \$mail->isSMTP();
                      \$mail->Host = \$config['smtp_host'];
                      \$mail->Port = \$config['smtp_port'];
                      \$mail->SMTPAuth = \$config['smtp_auth'] ?? true;
                      \$mail->Username = \$config['smtp_user'] ?? '';
                      \$mail->Password = \$config['smtp_pass'] ?? '';
                      \$mail->SMTPSecure = \$config['smtp_secure'] ?? 'tls';
                      
                      \$mail->setFrom(\$config['from_email'], \$config['from_name'] ?? 'Fit360');
                      \$mail->addAddress('${inputs.test_email}');
                      \$mail->Subject = 'Fit360 Debug Test Email';
                      \$mail->Body = 'This is a test email from the Claude-Flow debug workflow.';
                      
                      \$mail->send();
                      echo \"✓ Test email sent successfully!\\n\";
                  } catch (Exception \$e) {
                      echo \"✗ Email sending failed: \" . \$e->getMessage() . \"\\n\";
                  }
              }
          } else {
              echo \"⚠️ Email configuration not found in config table\\n\";
          }
          "

  - name: Performance Analysis
    type: conditional
    condition: "{{ inputs.check_type == 'all' || inputs.check_type == 'performance' }}"
    steps:
      - name: Analyze Performance
        type: shell
        command: |
          php -r "
          echo \"\\n=== Performance Analysis ===\\n\";
          
          require_once 'vendor/autoload.php';
          \$dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
          \$dotenv->load();
          
          \$pdo = new PDO(
              'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
              \$_ENV['DB_USER'],
              \$_ENV['DB_PASS']
          );
          
          // Check for missing indexes
          echo \"\\nChecking for tables without indexes...\\n\";
          \$stmt = \$pdo->query(\"
              SELECT 
                  t.TABLE_NAME,
                  t.TABLE_ROWS,
                  ROUND(((t.DATA_LENGTH + t.INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)'
              FROM information_schema.TABLES t
              LEFT JOIN information_schema.STATISTICS s ON t.TABLE_NAME = s.TABLE_NAME 
                  AND t.TABLE_SCHEMA = s.TABLE_SCHEMA
              WHERE t.TABLE_SCHEMA = '\" . \$_ENV['DB_NAME'] . \"'
                  AND t.TABLE_TYPE = 'BASE TABLE'
                  AND s.INDEX_NAME IS NULL
              GROUP BY t.TABLE_NAME
          \");
          
          \$noIndex = \$stmt->fetchAll(PDO::FETCH_ASSOC);
          if (count(\$noIndex) > 0) {
              echo \"⚠️ Tables without any indexes:\\n\";
              foreach (\$noIndex as \$table) {
                  echo \"  - \" . \$table['TABLE_NAME'] . \" (\" . \$table['TABLE_ROWS'] . \" rows)\\n\";
              }
          } else {
              echo \"✓ All tables have indexes\\n\";
          }
          
          // Check for large tables
          echo \"\\nLarge tables (>10MB):\\n\";
          \$stmt = \$pdo->query(\"
              SELECT 
                  TABLE_NAME,
                  TABLE_ROWS,
                  ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size_MB'
              FROM information_schema.TABLES
              WHERE TABLE_SCHEMA = '\" . \$_ENV['DB_NAME'] . \"'
                  AND ((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024) > 10
              ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC
          \");
          
          \$largeTables = \$stmt->fetchAll(PDO::FETCH_ASSOC);
          foreach (\$largeTables as \$table) {
              echo \"  - \" . \$table['TABLE_NAME'] . \": \" . \$table['Size_MB'] . \" MB (\" . number_format(\$table['TABLE_ROWS']) . \" rows)\\n\";
          }
          "

  - name: Check Recent Errors
    type: shell
    command: |
      echo -e "\n=== Recent Error Logs ==="
      
      # Check PHP error log
      if [ -f "storage/logs/error.log" ]; then
        echo -e "\nLast 10 PHP errors:"
        tail -n 10 storage/logs/error.log || echo "No recent errors"
      else
        echo "No PHP error log found"
      fi
      
      # Check application log
      if [ -f "storage/logs/app.log" ]; then
        echo -e "\nLast 10 application log entries:"
        tail -n 10 storage/logs/app.log || echo "No recent entries"
      else
        echo "No application log found"
      fi

  - name: Generate Debug Report
    type: shell
    command: |
      php -r "
      echo \"\\n=== Debug Report Summary ===\\n\";
      echo \"Report generated at: \" . date('Y-m-d H:i:s') . \"\\n\";
      echo \"Check Type: ${inputs.check_type}\\n\";
      
      // Generate recommendations
      echo \"\\n=== Recommendations ===\\n\";
      
      \$recommendations = [
          '1. Run \"php clear-cache.php\" if experiencing template issues',
          '2. Check storage/logs/ for detailed error information',
          '3. Ensure all directories under storage/ are writable',
          '4. Monitor database table sizes and add indexes where needed',
          '5. Enable OpCache for better performance in production'
      ];
      
      foreach (\$recommendations as \$rec) {
          echo \$rec . \"\\n\";
      }
      
      // Save report
      \$reportFile = 'storage/logs/debug-report-' . date('Y-m-d-His') . '.txt';
      echo \"\\nFull report saved to: \$reportFile\\n\";
      "

on_error:
  - name: Log Debug Error
    type: shell
    command: |
      echo "Debug workflow error: {{ error_message }}" >> storage/logs/debug-workflow-errors.log
      echo "Some checks may have failed - review the output above for details"

on_success:
  - name: Log Success
    type: shell
    command: |
      echo "Debug health check completed at $(date)" >> storage/logs/debug-workflow.log