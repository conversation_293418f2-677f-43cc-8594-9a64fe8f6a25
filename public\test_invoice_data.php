<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Please log in first.");
}

// Connect to database using .env
$db = new PDO(
    'mysql:host=' . $_ENV['DB_HOST'] . ';port=' . $_ENV['DB_PORT'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
    $_ENV['DB_USERNAME'],
    $_ENV['DB_PASSWORD'],
    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
);

// Get coach group ID
$coachGroupId = 24;
$practitionerGroupId = 4;

// Run the same query as InvoiceController
$stmt = $db->prepare("
    SELECT u.id, u.username, u.email, 
           CONCAT(u.first_name, ' ', u.last_name) as name,
           u.billing_email, u.billing_address, u.billing_city,
           u.billing_postal_code, u.billing_country, u.vat_number,
           u.is_intracommunity, u.vat_intercommunautaire,
           u.course_name, u.hourly_rate, u.hourly_vat_rate,
           uip.invoice_language, uip.discount_percentage,
           -- Flag to identify coaches (members of coach group)
           CASE WHEN ugm.user_id IS NOT NULL THEN 1 ELSE 0 END as is_coach,
           -- Flag to identify practitioners (members of practitioner group)
           CASE WHEN ugm2.user_id IS NOT NULL THEN 1 ELSE 0 END as is_practitioner
    FROM users u
    LEFT JOIN user_invoice_preferences uip ON u.id = uip.user_id
    LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.group_id = :coach_group_id
    LEFT JOIN user_group_members ugm2 ON u.id = ugm2.user_id AND ugm2.group_id = :practitioner_group_id
    WHERE u.is_active = 1 AND u.can_be_invoiced = 1
    ORDER BY u.first_name, u.last_name
");
$stmt->execute([
    'coach_group_id' => $coachGroupId,
    'practitioner_group_id' => $practitionerGroupId
]);
$users = $stmt->fetchAll(\PDO::FETCH_ASSOC);

// Separate coaches and practitioners
$coaches = array_filter($users, function($user) {
    return $user['is_coach'] == 1;
});
$coaches = array_values($coaches);

$practitioners = array_filter($users, function($user) {
    return $user['is_practitioner'] == 1;
});
$practitioners = array_values($practitioners);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Invoice Data Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 10px; background: #f5f5f5; }
        pre { background: white; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Invoice Data Test</h1>
    
    <div class="section">
        <h2>Summary</h2>
        <p>Total users: <?php echo count($users); ?></p>
        <p>Total coaches: <?php echo count($coaches); ?></p>
        <p>Total practitioners: <?php echo count($practitioners); ?></p>
    </div>
    
    <div class="section">
        <h2>Coaches Data (JSON)</h2>
        <pre><?php echo json_encode($coaches, JSON_PRETTY_PRINT); ?></pre>
    </div>
    
    <div class="section">
        <h2>Practitioners Data (JSON)</h2>
        <pre><?php echo json_encode($practitioners, JSON_PRETTY_PRINT); ?></pre>
    </div>
    
    <div class="section">
        <h2>JavaScript Test</h2>
        <p>This is what would be passed to the template:</p>
        <pre>
let coachesData = <?php echo json_encode($coaches); ?>;
let practitionersData = <?php echo json_encode($practitioners); ?>;

console.log('Coaches:', coachesData);
console.log('Practitioners:', practitionersData);
        </pre>
    </div>
    
    <div class="section">
        <h2>Test Links</h2>
        <p><a href="/fit/public/invoices/create?type=location" target="_blank">Create Location Invoice</a></p>
        <p><a href="/fit/public/invoices/create?type=retrocession_30" target="_blank">Create Retrocession Invoice</a></p>
    </div>
</body>
</html>