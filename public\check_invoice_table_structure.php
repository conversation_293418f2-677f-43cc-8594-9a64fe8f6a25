<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING INVOICE TABLE STRUCTURE ===\n\n";

try {
    $db = Flight::db();
    
    // Check the structure of the invoices table
    echo "1. Invoice table columns:\n";
    $stmt = $db->query("DESCRIBE invoices");
    $columns = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'status') {
            echo "Status column:\n";
            echo "  Type: {$col['Type']}\n";
            echo "  Null: {$col['Null']}\n";
            echo "  Default: {$col['Default']}\n";
            echo "  Extra: {$col['Extra']}\n";
            break;
        }
    }
    
    // Check if status column exists
    $hasStatus = false;
    foreach ($columns as $col) {
        if ($col['Field'] === 'status') {
            $hasStatus = true;
            break;
        }
    }
    
    if (!$hasStatus) {
        echo "\n⚠️  WARNING: 'status' column not found in invoices table!\n";
        echo "The table might be using a different column name.\n\n";
        
        // List all columns
        echo "All columns in invoices table:\n";
        foreach ($columns as $col) {
            echo "- {$col['Field']} ({$col['Type']})\n";
        }
    }
    
    // Check if there's an invoice_status column instead
    $hasInvoiceStatus = false;
    foreach ($columns as $col) {
        if ($col['Field'] === 'invoice_status') {
            $hasInvoiceStatus = true;
            echo "\n⚠️  Found 'invoice_status' column instead of 'status'!\n";
            echo "  Type: {$col['Type']}\n";
            echo "  Default: {$col['Default']}\n";
            break;
        }
    }
    
    // Sample data from invoices
    echo "\n2. Sample invoice data:\n";
    $query = "SELECT id, invoice_number, ";
    if ($hasStatus) {
        $query .= "status";
    } elseif ($hasInvoiceStatus) {
        $query .= "invoice_status as status";
    } else {
        // Try to find any status-like column
        $query .= "created_at";
    }
    $query .= " FROM invoices ORDER BY id DESC LIMIT 5";
    
    $stmt = $db->query($query);
    $samples = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($samples as $sample) {
        echo "- Invoice #{$sample['id']}: {$sample['invoice_number']} - Status: " . ($sample['status'] ?? 'N/A') . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}