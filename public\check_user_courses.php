<?php
// Check user courses

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>User Courses Analysis</h1>";

// Check if course_name column exists
echo "<h2>Checking users table structure:</h2>";
$stmt = $db->prepare("SHOW COLUMNS FROM users LIKE '%course%'");
$stmt->execute();
$courseColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($courseColumns) > 0) {
    echo "<p>Course-related columns found:</p>";
    echo "<ul>";
    foreach ($courseColumns as $col) {
        echo "<li>{$col['Field']} ({$col['Type']})</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠️ No course columns found in users table</p>";
}

// Check users in group 24 (Coach group)
echo "<h2>Users in Group 24 (Coach):</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name,
           u.is_active, u.can_be_invoiced, u.course_name
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 24
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Found " . count($coaches) . " members in group 24</p>";
if (count($coaches) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Active</th><th>Can Invoice</th><th>Course Name</th></tr>";
    foreach ($coaches as $coach) {
        echo "<tr>";
        echo "<td>{$coach['id']}</td>";
        echo "<td>{$coach['name']}</td>";
        echo "<td>{$coach['username']}</td>";
        echo "<td>" . ($coach['is_active'] ? '✅' : '❌') . "</td>";
        echo "<td>" . ($coach['can_be_invoiced'] ? '✅' : '❌') . "</td>";
        echo "<td>" . ($coach['course_name'] ? $coach['course_name'] : '<em style="color: red;">No course</em>') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check if there's a separate courses table
echo "<h2>Checking for courses-related tables:</h2>";
$stmt = $db->query("SHOW TABLES LIKE '%course%'");
$courseTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

if (count($courseTables) > 0) {
    echo "<p>Found course-related tables:</p>";
    echo "<ul>";
    foreach ($courseTables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check user_courses table if it exists
    if (in_array('user_courses', $courseTables)) {
        echo "<h3>Checking user_courses table:</h3>";
        $stmt = $db->query("
            SELECT uc.*, u.username, CONCAT(u.first_name, ' ', u.last_name) as user_name
            FROM user_courses uc
            JOIN users u ON uc.user_id = u.id
            LIMIT 10
        ");
        $userCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($userCourses) > 0) {
            echo "<p>Sample user courses:</p>";
            echo "<pre>";
            print_r($userCourses);
            echo "</pre>";
        }
    }
} else {
    echo "<p>No course-related tables found</p>";
}

// Try to set course names for coaches if they don't have them
echo "<h2>Suggested Actions:</h2>";
if (count($coaches) > 0) {
    $needsCourse = 0;
    foreach ($coaches as $coach) {
        if (!$coach['course_name']) {
            $needsCourse++;
        }
    }
    
    if ($needsCourse > 0) {
        echo "<p style='color: orange;'>⚠️ $needsCourse coaches in group 24 don't have course names set.</p>";
        echo "<p>You may need to update their course_name field for them to appear in location invoice dropdowns.</p>";
        
        echo "<h3>Sample SQL to update coach courses:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px;'>";
        foreach ($coaches as $coach) {
            if (!$coach['course_name']) {
                echo "UPDATE users SET course_name = 'Fitness Course' WHERE id = {$coach['id']}; -- {$coach['name']}\n";
            }
        }
        echo "</pre>";
    }
}

// Final recommendation
echo "<h2>Configuration Update:</h2>";
echo "<p>To use group 24 as the coach group, update the configuration:</p>";
echo "<form method='post' action='update_coach_group_config.php'>";
echo "<input type='hidden' name='group_id' value='24'>";
echo "<button type='submit' style='padding: 10px 20px; background: #4CAF50; color: white; border: none; cursor: pointer;'>";
echo "Set Group 24 (Coach) as Coach Group";
echo "</button>";
echo "</form>";

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #e9e9e9; }
td, th { padding: 8px; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style>

<hr>
<p><a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a></p>