<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Adding May 2025 Data for Frank Huet</h2>";
    
    // Get <PERSON>'s user ID
    $stmt = $db->prepare("SELECT id, first_name, last_name FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color: red;'>Frank Huet not found!</p>";
        exit;
    }
    
    echo "<p>User: {$user['first_name']} {$user['last_name']} (ID: {$user['id']})</p>";
    
    // Check if year column exists
    $yearCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYearColumn = $yearCheck->rowCount() > 0;
    
    // Check if May data already exists
    if ($hasYearColumn) {
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = 5 AND year = 2025
        ");
    } else {
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = 5
        ");
    }
    $stmt->execute(['user_id' => $user['id']]);
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing) {
        echo "<p style='color: orange;'>May data already exists:</p>";
        echo "<ul>";
        echo "<li>CNS Amount: €" . number_format($existing['cns_amount'], 2) . "</li>";
        echo "<li>Patient Amount: €" . number_format($existing['patient_amount'], 2) . "</li>";
        echo "</ul>";
        
        // Update if needed
        if ($existing['cns_amount'] == 0 && $existing['patient_amount'] == 0) {
            echo "<p>Updating with default values...</p>";
            
            if ($hasYearColumn) {
                $stmt = $db->prepare("
                    UPDATE user_monthly_retrocession_amounts 
                    SET cns_amount = 4500, patient_amount = 5000, is_active = 1
                    WHERE user_id = :user_id AND month = 5 AND year = 2025
                ");
            } else {
                $stmt = $db->prepare("
                    UPDATE user_monthly_retrocession_amounts 
                    SET cns_amount = 4500, patient_amount = 5000, is_active = 1
                    WHERE user_id = :user_id AND month = 5
                ");
            }
            $stmt->execute(['user_id' => $user['id']]);
            echo "<p style='color: green;'>✓ Updated May data with CNS: €4,500 and Patient: €5,000</p>";
        }
    } else {
        echo "<p>May data not found. Creating it...</p>";
        
        // Insert May data
        if ($hasYearColumn) {
            $stmt = $db->prepare("
                INSERT INTO user_monthly_retrocession_amounts 
                (user_id, month, year, cns_amount, patient_amount, is_active)
                VALUES (:user_id, 5, 2025, 4500, 5000, 1)
            ");
        } else {
            $stmt = $db->prepare("
                INSERT INTO user_monthly_retrocession_amounts 
                (user_id, month, cns_amount, patient_amount, is_active)
                VALUES (:user_id, 5, 4500, 5000, 1)
            ");
        }
        $stmt->execute(['user_id' => $user['id']]);
        echo "<p style='color: green;'>✓ Created May 2025 data with CNS: €4,500 and Patient: €5,000</p>";
    }
    
    // Also check if June data exists (for comparison)
    echo "<h3>Checking June 2025 Data:</h3>";
    if ($hasYearColumn) {
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = 6 AND year = 2025
        ");
    } else {
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = 6
        ");
    }
    $stmt->execute(['user_id' => $user['id']]);
    $june = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($june) {
        echo "<p>June data found:</p>";
        echo "<ul>";
        echo "<li>CNS Amount: €" . number_format($june['cns_amount'], 2) . "</li>";
        echo "<li>Patient Amount: €" . number_format($june['patient_amount'], 2) . "</li>";
        echo "</ul>";
    }
    
    // Show all months for verification
    echo "<h3>All Monthly Data for Frank:</h3>";
    if ($hasYearColumn) {
        $stmt = $db->prepare("
            SELECT month, year, cns_amount, patient_amount 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND year = 2025
            ORDER BY month
        ");
    } else {
        $stmt = $db->prepare("
            SELECT month, cns_amount, patient_amount 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id
            ORDER BY month
        ");
    }
    $stmt->execute(['user_id' => $user['id']]);
    $allData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $monthNames = ['', 'January', 'February', 'March', 'April', 'May', 'June', 
                   'July', 'August', 'September', 'October', 'November', 'December'];
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Month</th><th>CNS Amount</th><th>Patient Amount</th><th>Total</th></tr>";
    foreach ($allData as $data) {
        $total = $data['cns_amount'] + $data['patient_amount'];
        echo "<tr>";
        echo "<td>{$monthNames[$data['month']]}</td>";
        echo "<td>€" . number_format($data['cns_amount'], 2) . "</td>";
        echo "<td>€" . number_format($data['patient_amount'], 2) . "</td>";
        echo "<td>€" . number_format($total, 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<p><strong>Done!</strong> You can now generate retrocession invoices for June 2025 (which will use May 2025 data).</p>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession'>Go to Bulk Generation</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}