/* Mobile-First Responsive Design Improvements for Fit360 AdminDesk */
/* Designed for optimal experience on smaller devices */

/* ========================================
   1. MOBILE-FIRST BASE STYLES
   ======================================== */

/* Ensure proper viewport behavior */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

/* Improve scrolling performance */
html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Base mobile typography */
body {
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
}

/* ========================================
   2. FORM IMPROVEMENTS FOR MOBILE
   ======================================== */

/* Stack form fields on mobile */
@media (max-width: 767px) {
    .form-row,
    .row.g-3 {
        flex-direction: column;
    }
    
    .form-row > div,
    .row.g-3 > div {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 1rem;
    }
    
    /* Increase form control sizes for better touch targets */
    .form-control,
    .form-select,
    .btn {
        min-height: 44px;
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem 1rem;
    }
    
    /* Full-width form elements */
    .form-control,
    .form-select,
    .input-group {
        width: 100% !important;
    }
    
    /* Floating labels for better space usage */
    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
    }
    
    /* Radio and checkbox improvements */
    .form-check {
        padding: 0.5rem 0;
    }
    
    .form-check-input {
        width: 1.5rem;
        height: 1.5rem;
        margin-top: 0.125rem;
        margin-right: 0.75rem;
    }
    
    .form-check-label {
        padding-top: 0.125rem;
        font-size: 1rem;
    }
}

/* ========================================
   3. RESPONSIVE TABLE SOLUTIONS
   ======================================== */

/* Mobile table wrapper */
.table-mobile-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0 -15px;
    padding: 0 15px;
}

@media (max-width: 767px) {
    /* Card-based table layout for mobile */
    .table-mobile-card table {
        display: block;
    }
    
    .table-mobile-card thead {
        display: none;
    }
    
    .table-mobile-card tbody,
    .table-mobile-card tr,
    .table-mobile-card td {
        display: block;
        width: 100%;
    }
    
    .table-mobile-card tr {
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .table-mobile-card td {
        padding: 0.5rem 0;
        border: none;
        position: relative;
        padding-left: 40%;
    }
    
    .table-mobile-card td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 35%;
        font-weight: 600;
        text-align: left;
    }
    
    /* Priority columns - hide on mobile */
    .hide-mobile {
        display: none !important;
    }
    
    /* Sticky table header on scroll */
    .table-sticky-header {
        position: relative;
    }
    
    .table-sticky-header thead th {
        position: sticky;
        top: 0;
        background: #fff;
        z-index: 10;
        box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
    }
}

/* ========================================
   4. NAVIGATION ENHANCEMENTS
   ======================================== */

@media (max-width: 767px) {
    /* Mobile hamburger menu */
    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1100;
        width: 44px;
        height: 44px;
        background: #007bff;
        border: none;
        border-radius: 0.5rem;
        color: white;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    /* Bottom navigation bar */
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: space-around;
        padding: 0.5rem 0;
        z-index: 1000;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
    }
    
    .bottom-nav-item {
        flex: 1;
        text-align: center;
        padding: 0.5rem;
        color: #6c757d;
        text-decoration: none;
        font-size: 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }
    
    .bottom-nav-item.active {
        color: #007bff;
    }
    
    .bottom-nav-item i {
        font-size: 1.25rem;
    }
    
    /* Adjust main content for bottom nav */
    .main-content {
        padding-bottom: 4rem;
    }
    
    /* Swipeable sidebar */
    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        bottom: 0;
        width: 80%;
        max-width: 300px;
        background: #fff;
        transition: left 0.3s ease;
        z-index: 1050;
        overflow-y: auto;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }
    
    .sidebar.show {
        left: 0;
    }
    
    /* Sidebar overlay */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: none;
        z-index: 1040;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
}

/* ========================================
   5. BUTTON AND TOUCH TARGET OPTIMIZATION
   ======================================== */

@media (max-width: 767px) {
    /* Increase all button sizes */
    .btn {
        min-height: 44px;
        min-width: 44px;
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
        font-weight: 500;
    }
    
    /* Stack button groups vertically */
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        width: 100%;
        border-radius: 0.375rem !important;
        margin-bottom: 0.5rem;
    }
    
    /* Floating action button */
    .fab {
        position: fixed;
        bottom: 5rem;
        right: 1rem;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: #007bff;
        color: white;
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.2);
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
    }
    
    /* Dropdown improvements */
    .dropdown-menu {
        width: 100%;
        max-height: 50vh;
        overflow-y: auto;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
}

/* ========================================
   6. TYPOGRAPHY AND SPACING
   ======================================== */

@media (max-width: 767px) {
    /* Responsive typography */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.875rem; }
    
    /* Increase line height for readability */
    p, li {
        line-height: 1.7;
    }
    
    /* Responsive spacing */
    .container,
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Text truncation with ellipsis */
    .text-truncate-mobile {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
    }
}

/* ========================================
   7. MOBILE-SPECIFIC FEATURES
   ======================================== */

/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: fixed;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: top 0.3s ease;
    z-index: 1100;
}

.pull-to-refresh.show {
    top: 20px;
}

/* Mobile loading states */
.mobile-loader {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Mobile-optimized modals */
@media (max-width: 767px) {
    .modal-dialog {
        margin: 0;
        width: 100%;
        max-width: 100%;
        height: 100%;
    }
    
    .modal-content {
        height: 100%;
        border-radius: 0;
    }
    
    .modal-header {
        position: sticky;
        top: 0;
        background: #fff;
        z-index: 10;
        border-radius: 0;
    }
    
    .modal-body {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Mobile card layouts */
@media (max-width: 767px) {
    .mobile-card-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .mobile-card {
        background: #fff;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
}

/* ========================================
   8. PERFORMANCE OPTIMIZATIONS
   ======================================== */

/* Reduce animations on mobile for better performance */
@media (max-width: 767px) and (prefers-reduced-motion: no-preference) {
    * {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
}

/* Image optimization for mobile */
@media (max-width: 767px) {
    img {
        max-width: 100%;
        height: auto;
    }
    
    /* Lazy loading placeholder */
    .lazy-load {
        background: #f0f0f0;
        min-height: 200px;
    }
}

/* ========================================
   9. UTILITY CLASSES
   ======================================== */

/* Mobile visibility utilities */
@media (max-width: 767px) {
    .mobile-only { display: block !important; }
    .desktop-only { display: none !important; }
    .mobile-full-width { width: 100% !important; }
    .mobile-text-center { text-align: center !important; }
    .mobile-mt-3 { margin-top: 1rem !important; }
    .mobile-mb-3 { margin-bottom: 1rem !important; }
    .mobile-p-2 { padding: 0.5rem !important; }
}

/* Desktop visibility utilities */
@media (min-width: 768px) {
    .mobile-only { display: none !important; }
    .desktop-only { display: block !important; }
    .mobile-menu-toggle { display: none !important; }
    .bottom-nav { display: none !important; }
}

/* ========================================
   10. ACCESSIBILITY IMPROVEMENTS
   ======================================== */

/* Focus styles for mobile */
@media (max-width: 767px) {
    *:focus {
        outline: 3px solid #007bff;
        outline-offset: 2px;
    }
    
    /* Skip to main content link */
    .skip-link {
        position: absolute;
        top: -40px;
        left: 0;
        background: #007bff;
        color: white;
        padding: 0.5rem 1rem;
        text-decoration: none;
        border-radius: 0 0 0.5rem 0;
    }
    
    .skip-link:focus {
        top: 0;
    }
}