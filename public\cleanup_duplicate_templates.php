<?php
// Cleanup duplicate system templates
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
try {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $db = new PDO($dsn, $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Template Cleanup</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='container mt-4'>";

echo "<h1>Cleanup Duplicate System Templates</h1>";

// Step 1: Analyze duplicates
echo "<h2>Step 1: Analyzing Duplicates</h2>";

$stmt = $db->query("
    SELECT code, COUNT(*) as count, GROUP_CONCAT(id ORDER BY id) as ids
    FROM invoice_templates
    WHERE owner_type = 'system'
    GROUP BY code
    HAVING count > 1
");
$duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($duplicates)) {
    echo "<div class='alert alert-success'>No duplicate system templates found!</div>";
    echo "</body></html>";
    exit;
}

echo "<table class='table table-bordered'>";
echo "<tr><th>Template Code</th><th>Count</th><th>IDs</th><th>Keep ID</th><th>Delete IDs</th></tr>";

$toDelete = [];
$toKeep = [];

foreach ($duplicates as $dup) {
    $ids = explode(',', $dup['ids']);
    $keepId = $ids[0]; // Keep the first one
    $deleteIds = array_slice($ids, 1);
    
    $toKeep[$dup['code']] = $keepId;
    $toDelete = array_merge($toDelete, $deleteIds);
    
    echo "<tr>";
    echo "<td>{$dup['code']}</td>";
    echo "<td>{$dup['count']}</td>";
    echo "<td>{$dup['ids']}</td>";
    echo "<td class='text-success'><strong>{$keepId}</strong></td>";
    echo "<td class='text-danger'>" . implode(', ', $deleteIds) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Step 2: Check for references
echo "<h2>Step 2: Checking References</h2>";

if (!empty($toDelete)) {
    $placeholders = implode(',', array_fill(0, count($toDelete), '?'));
    
    // Check invoices
    $stmt = $db->prepare("SELECT template_id, COUNT(*) as count FROM invoices WHERE template_id IN ($placeholders) GROUP BY template_id");
    $stmt->execute($toDelete);
    $invoiceRefs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($invoiceRefs)) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>Found invoices using duplicate templates:</h5>";
        echo "<ul>";
        foreach ($invoiceRefs as $ref) {
            echo "<li>Template ID {$ref['template_id']}: {$ref['count']} invoices</li>";
        }
        echo "</ul>";
        echo "<p>These will be updated to use the kept template.</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>No invoices are using the duplicate templates.</div>";
    }
}

// Step 3: Perform cleanup
if (isset($_POST['cleanup'])) {
    echo "<h2>Step 3: Performing Cleanup</h2>";
    
    try {
        $db->beginTransaction();
        
        // Update invoice references
        foreach ($duplicates as $dup) {
            $ids = explode(',', $dup['ids']);
            $keepId = $ids[0];
            $deleteIds = array_slice($ids, 1);
            
            if (!empty($deleteIds)) {
                $placeholders = implode(',', array_fill(0, count($deleteIds), '?'));
                
                // Update invoices to use the kept template
                $stmt = $db->prepare("UPDATE invoices SET template_id = ? WHERE template_id IN ($placeholders)");
                $params = array_merge([$keepId], $deleteIds);
                $stmt->execute($params);
                $updated = $stmt->rowCount();
                
                if ($updated > 0) {
                    echo "<div class='alert alert-info'>Updated {$updated} invoices from template IDs " . implode(', ', $deleteIds) . " to {$keepId}</div>";
                }
                
                // Delete template settings
                $stmt = $db->prepare("DELETE FROM invoice_template_settings WHERE template_id IN ($placeholders)");
                $stmt->execute($deleteIds);
                
                // Delete template VAT configs
                $stmt = $db->prepare("DELETE FROM template_vat_configs WHERE template_id IN ($placeholders)");
                $stmt->execute($deleteIds);
                
                // Delete template line items
                $stmt = $db->prepare("DELETE FROM template_line_items WHERE template_id IN ($placeholders)");
                $stmt->execute($deleteIds);
                
                // Delete the duplicate templates
                $stmt = $db->prepare("DELETE FROM invoice_templates WHERE id IN ($placeholders)");
                $stmt->execute($deleteIds);
                $deleted = $stmt->rowCount();
                
                echo "<div class='alert alert-success'>Deleted {$deleted} duplicate templates for code: {$dup['code']}</div>";
            }
        }
        
        $db->commit();
        echo "<div class='alert alert-success'><h5>Cleanup completed successfully!</h5></div>";
        
        // Show remaining templates
        echo "<h3>Remaining System Templates:</h3>";
        $stmt = $db->query("SELECT * FROM invoice_templates WHERE owner_type = 'system' ORDER BY id");
        $remaining = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table class='table table-bordered'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Type</th></tr>";
        foreach ($remaining as $template) {
            echo "<tr>";
            echo "<td>{$template['id']}</td>";
            echo "<td>{$template['name']}</td>";
            echo "<td>{$template['code']}</td>";
            echo "<td>{$template['invoice_type']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<div class='alert alert-danger'>Error during cleanup: " . $e->getMessage() . "</div>";
    }
} else {
    // Show cleanup button
    echo "<form method='POST' class='mt-4'>";
    echo "<div class='alert alert-warning'>";
    echo "<h5>Ready to Clean Up?</h5>";
    echo "<p>This will:</p>";
    echo "<ul>";
    echo "<li>Update any invoices using duplicate templates to use the original template</li>";
    echo "<li>Delete all duplicate template data (settings, VAT configs, line items)</li>";
    echo "<li>Delete the duplicate template records</li>";
    echo "</ul>";
    echo "<p><strong>This action cannot be undone!</strong></p>";
    echo "</div>";
    echo "<button type='submit' name='cleanup' class='btn btn-danger' onclick='return confirm(\"Are you sure you want to clean up duplicate templates?\")'>Perform Cleanup</button>";
    echo " <a href='/fit/public/template_diagnostic.php' class='btn btn-secondary'>Cancel</a>";
    echo "</form>";
}

echo "</body></html>";
?>