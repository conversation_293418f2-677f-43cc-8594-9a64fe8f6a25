<?php
// Enhance Invoice-Course Integration Analysis
// This script analyzes how to better integrate course-specific pricing into invoices

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Invoice-Course Integration Analysis</h1>";
    
    // Check how many users have courses configured
    $stmt = $db->prepare("
        SELECT 
            COUNT(DISTINCT u.id) as users_with_courses,
            COUNT(uc.id) as total_courses,
            AVG(uc.hourly_rate) as avg_hourly_rate
        FROM users u
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        WHERE u.is_active = 1
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Course Configuration Statistics</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Users with courses</td><td>{$stats['users_with_courses']}</td></tr>";
    echo "<tr><td>Total active courses</td><td>{$stats['total_courses']}</td></tr>";
    echo "<tr><td>Average hourly rate</td><td>€ " . number_format($stats['avg_hourly_rate'], 2) . "</td></tr>";
    echo "</table>";
    
    // Show users with multiple courses
    $stmt = $db->prepare("
        SELECT 
            u.id,
            u.username,
            u.first_name,
            u.last_name,
            COUNT(uc.id) as course_count,
            GROUP_CONCAT(CONCAT(uc.course_name, ' (€', uc.hourly_rate, ')') SEPARATOR ', ') as courses
        FROM users u
        JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        WHERE u.is_active = 1
        GROUP BY u.id
        HAVING course_count > 1
        ORDER BY course_count DESC
        LIMIT 10
    ");
    $stmt->execute();
    $multiCourseUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Users with Multiple Courses</h2>";
    if (!empty($multiCourseUsers)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User</th><th>Courses</th><th>Count</th></tr>";
        
        foreach ($multiCourseUsers as $user) {
            echo "<tr>";
            echo "<td>{$user['first_name']} {$user['last_name']} ({$user['username']})</td>";
            echo "<td>{$user['courses']}</td>";
            echo "<td>{$user['course_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No users found with multiple courses</p>";
    }
    
    // Check recent invoices and their pricing structure
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.total,
            u.first_name,
            u.last_name,
            u.username,
            COUNT(uc.id) as course_count,
            COUNT(il.id) as line_count
        FROM invoices i
        JOIN users u ON i.client_id = u.id
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        LEFT JOIN invoice_lines il ON i.id = il.invoice_id
        WHERE i.status = 'sent'
        GROUP BY i.id
        ORDER BY i.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recentInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Recent Invoices vs Course Configuration</h2>";
    if (!empty($recentInvoices)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Invoice</th><th>Client</th><th>Total</th><th>Courses</th><th>Lines</th><th>Status</th></tr>";
        
        foreach ($recentInvoices as $invoice) {
            $status = $invoice['course_count'] > 0 ? 'Has Courses' : 'No Courses';
            $statusColor = $invoice['course_count'] > 0 ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td><a href='http://localhost/fit/public/invoices/{$invoice['id']}' target='_blank'>{$invoice['invoice_number']}</a></td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['course_count']}</td>";
            echo "<td>{$invoice['line_count']}</td>";
            echo "<td style='color: $statusColor;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Analyze integration gaps
    echo "<h2>Integration Analysis</h2>";
    
    // Check if InvoiceController loads course data
    $controllerFile = __DIR__ . '/app/controllers/InvoiceController.php';
    $controllerContent = file_exists($controllerFile) ? file_get_contents($controllerFile) : '';
    
    echo "<h3>InvoiceController.php Analysis</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Feature</th><th>Status</th><th>Details</th></tr>";
    
    $features = [
        'Course data loading' => [
            'check' => strpos($controllerContent, 'user_courses') !== false,
            'details' => 'Checks if controller queries user_courses table'
        ],
        'Course selection in forms' => [
            'check' => strpos($controllerContent, 'course_name') !== false,
            'details' => 'Checks if course names are used in forms'
        ],
        'Hourly rate integration' => [
            'check' => strpos($controllerContent, 'hourly_rate') !== false,
            'details' => 'Checks if hourly rates are loaded from courses'
        ],
        'VAT rate from courses' => [
            'check' => strpos($controllerContent, 'vat_rate') !== false,
            'details' => 'Checks if VAT rates come from course config'
        ]
    ];
    
    foreach ($features as $feature => $check) {
        $status = $check['check'] ? '✅ Present' : '❌ Missing';
        $color = $check['check'] ? 'green' : 'red';
        echo "<tr>";
        echo "<td>$feature</td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "<td>{$check['details']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check RetrocessionCalculator
    $calculatorFile = __DIR__ . '/app/services/RetrocessionCalculator.php';
    $calculatorContent = file_exists($calculatorFile) ? file_get_contents($calculatorFile) : '';
    
    echo "<h3>RetrocessionCalculator.php Analysis</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Feature</th><th>Status</th><th>Details</th></tr>";
    
    $calculatorFeatures = [
        'Course-based rates' => [
            'check' => strpos($calculatorContent, 'user_courses') !== false,
            'details' => 'Uses course-specific rates for calculations'
        ],
        'Multiple course support' => [
            'check' => strpos($calculatorContent, 'course_name') !== false,
            'details' => 'Supports multiple courses per user'
        ],
        'Dynamic VAT rates' => [
            'check' => strpos($calculatorContent, 'vat_rate') !== false && strpos($calculatorContent, 'user_courses') !== false,
            'details' => 'Uses VAT rates from course configuration'
        ]
    ];
    
    foreach ($calculatorFeatures as $feature => $check) {
        $status = $check['check'] ? '✅ Present' : '❌ Missing';
        $color = $check['check'] ? 'green' : 'red';
        echo "<tr>";
        echo "<td>$feature</td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "<td>{$check['details']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Recommendations
    echo "<h2>Integration Recommendations</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Priority 1: Fix Invoice 263</h3>";
    echo "<ul>";
    echo "<li>Use the <code>fix_invoice_263_courses.php</code> script to correct the current issue</li>";
    echo "<li>Add missing patient retrocession component (135.03€)</li>";
    echo "<li>Verify total reaches 930.00€</li>";
    echo "</ul>";
    
    echo "<h3>Priority 2: Enhance InvoiceController.php</h3>";
    echo "<ul>";
    echo "<li>Add course data loading in <code>create()</code> method</li>";
    echo "<li>Modify <code>store()</code> method to use course-specific rates</li>";
    echo "<li>Add JavaScript integration for course selection</li>";
    echo "</ul>";
    
    echo "<h3>Priority 3: Update RetrocessionCalculator.php</h3>";
    echo "<ul>";
    echo "<li>Replace legacy rate system with course-based calculation</li>";
    echo "<li>Support multiple courses per user</li>";
    echo "<li>Use course-specific VAT rates</li>";
    echo "</ul>";
    
    echo "<h3>Priority 4: Enhance Invoice Forms</h3>";
    echo "<ul>";
    echo "<li>Add course selection dropdown</li>";
    echo "<li>Auto-populate rates when course is selected</li>";
    echo "<li>Show course-specific pricing in real-time</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>Next Steps</h2>";
    echo "<ol>";
    echo "<li><strong>Immediate:</strong> <a href='fix_invoice_263_courses.php'>Fix Invoice 263</a></li>";
    echo "<li><strong>Short-term:</strong> Update InvoiceController to load course data</li>";
    echo "<li><strong>Medium-term:</strong> Enhance RetrocessionCalculator with course support</li>";
    echo "<li><strong>Long-term:</strong> Add full course selection UI to invoice forms</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #666; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
code { background: #f1f1f1; padding: 2px 4px; border-radius: 3px; }
</style>