<?php
require_once 'app/config/bootstrap.php';

// Ensure we have a session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "=== CSRF and Send Invoice Test ===\n\n";

// Check session
echo "1. Session Status:\n";
echo "   Session ID: " . session_id() . "\n";
echo "   Session status: " . session_status() . "\n";
echo "   Session save path: " . session_save_path() . "\n\n";

// Check CSRF token
echo "2. CSRF Token:\n";
echo "   Session CSRF: " . ($_SESSION['csrf_token'] ?? 'NOT SET') . "\n";
echo "   Length: " . strlen($_SESSION['csrf_token'] ?? '') . "\n\n";

// Check if CSRF is enabled
echo "3. CSRF Protection:\n";
$csrfEnabled = filter_var($_ENV['CSRF_PROTECTION'] ?? true, FILTER_VALIDATE_BOOLEAN);
echo "   CSRF_PROTECTION env: " . ($_ENV['CSRF_PROTECTION'] ?? 'not set (defaults to true)') . "\n";
echo "   CSRF enabled: " . ($csrfEnabled ? 'YES' : 'NO') . "\n\n";

// Test invoice data
$testInvoiceId = 279;
echo "4. Test Invoice #$testInvoiceId:\n";
try {
    $invoice = new \App\Models\Invoice();
    $invoiceData = $invoice->getById($testInvoiceId);
    
    if ($invoiceData) {
        echo "   Found: " . $invoiceData['invoice_number'] . "\n";
        echo "   Status: " . $invoiceData['status'] . "\n";
        echo "   Can send: " . ($invoiceData['status'] === 'draft' ? 'YES' : 'NO - only draft invoices can be sent') . "\n";
    } else {
        echo "   NOT FOUND\n";
    }
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}

echo "\n5. Debug Instructions:\n";
echo "   1. Open browser developer tools (F12)\n";
echo "   2. Go to Console tab\n";
echo "   3. Navigate to invoice view page\n";
echo "   4. Click the send button\n";
echo "   5. Check console for:\n";
echo "      - 'sendInvoice() called'\n";
echo "      - 'CSRF token: ...' (should not be EMPTY!)\n";
echo "      - 'Submitting form...'\n";
echo "   6. Check Network tab for the POST request\n\n";

echo "6. Common Issues:\n";
echo "   - CSRF token empty: Session not started or token not generated\n";
echo "   - 403 error: CSRF validation failed\n";
echo "   - 405 error: Wrong HTTP method (should be POST)\n";
echo "   - Invoice not draft: Can only send draft invoices\n";