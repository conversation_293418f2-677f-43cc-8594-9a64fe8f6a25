<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fixing retrocession_data_entry Columns</h2>\n";
    
    // Clean up invoice #320 first
    echo "<h3>1. Invoice #320 Status:</h3>\n";
    $stmt = $pdo->query("SELECT * FROM invoices WHERE id = 320");
    $invoice = $stmt->fetch();
    if ($invoice) {
        echo "Found partial invoice #320\n";
        echo "<a href='?delete_invoice=320'>Delete invoice #320 to retry</a>\n";
    }
    
    if (isset($_GET['delete_invoice']) && $_GET['delete_invoice'] == 320) {
        $pdo->exec("DELETE FROM invoice_lines WHERE invoice_id = 320");
        $pdo->exec("DELETE FROM user_generated_invoices WHERE invoice_id = 320");
        $pdo->exec("DELETE FROM invoices WHERE id = 320");
        echo "✓ Deleted invoice #320\n";
    }
    
    // Check current columns
    echo "\n<h3>2. Current retrocession_data_entry columns:</h3>\n";
    $stmt = $pdo->query("DESCRIBE retrocession_data_entry");
    $existingColumns = [];
    while ($row = $stmt->fetch()) {
        $existingColumns[$row['Field']] = $row['Type'];
        echo "- {$row['Field']} ({$row['Type']})\n";
    }
    
    // Define all required columns based on the INSERT query
    echo "\n<h3>3. Required columns check:</h3>\n";
    $requiredColumns = [
        'practitioner_id' => 'INT UNSIGNED',
        'period_month' => 'TINYINT',
        'period_year' => 'YEAR',
        'cns_services_count' => 'INT DEFAULT 0',
        'cns_base_amount' => 'DECIMAL(10,2) DEFAULT 0',
        'patient_services_count' => 'INT DEFAULT 0',
        'patient_base_amount' => 'DECIMAL(10,2) DEFAULT 0',
        'secretary_services' => 'DECIMAL(10,2) DEFAULT 0',
        'status' => "VARCHAR(20) DEFAULT 'draft'",
        'invoice_id' => 'INT UNSIGNED',
        'data_source' => "VARCHAR(50) DEFAULT 'manual'",
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'created_by' => 'INT UNSIGNED'
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $col => $type) {
        if (!isset($existingColumns[$col])) {
            $missingColumns[$col] = $type;
            echo "❌ Missing: $col ($type)\n";
        } else {
            echo "✓ Exists: $col\n";
        }
    }
    
    if (!empty($missingColumns)) {
        echo "\n<a href='?add_missing=1'>Add all missing columns</a>\n";
    } else {
        echo "\n✓ All required columns exist!\n";
    }
    
    // Add missing columns
    if (isset($_GET['add_missing'])) {
        echo "\n<h3>Adding missing columns...</h3>\n";
        foreach ($missingColumns as $col => $type) {
            try {
                $sql = "ALTER TABLE retrocession_data_entry ADD COLUMN `$col` $type";
                $pdo->exec($sql);
                echo "✓ Added: $col\n";
            } catch (Exception $e) {
                echo "❌ Error adding $col: " . $e->getMessage() . "\n";
            }
        }
        echo "\n<a href='test_retrocession_generation.php?user_id=1&month=7&year=2025'>Try generating again</a>\n";
    }
    
    // Show the INSERT query from UnifiedInvoiceGenerator for reference
    echo "\n<h3>4. UnifiedInvoiceGenerator INSERT query structure:</h3>\n";
    echo "<pre style='background: #f0f0f0; padding: 10px;'>";
    echo "INSERT INTO retrocession_data_entry
(practitioner_id, period_month, period_year, cns_services_count, cns_base_amount,
 patient_services_count, patient_base_amount, secretary_services, status, 
 invoice_id, data_source, created_at, created_by)
SELECT 
    c.id, :month, :year, 1, :cns_amount,
    1, :patient_amount, :secretary_amount, 'invoiced',
    :invoice_id, 'unified_generator', NOW(), :created_by
FROM users u
JOIN clients c ON ...
WHERE u.id = :user_id";
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}