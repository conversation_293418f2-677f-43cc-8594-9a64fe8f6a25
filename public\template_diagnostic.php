<?php
// Simple diagnostic script using .env
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load composer autoloader for Dotenv
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
try {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();
} catch (Exception $e) {
    die("Error loading .env file: " . $e->getMessage());
}

// Database connection using .env values
try {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $db = new PDO($dsn, $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage() . "<br>DSN: " . $dsn);
}

// Start session
session_start();

// Get current user info
$userId = $_SESSION['user_id'] ?? null;
$isAdmin = $_SESSION['user']['is_admin'] ?? false;
$userGroups = $_SESSION['user_groups'] ?? [];

// Check if user is in Administrators group (temporary fix compatibility)
if (!$isAdmin && is_string($userGroups) && stripos($userGroups, 'administrators') !== false) {
    $isAdmin = true;
}

// Get all templates with detailed info
try {
    $stmt = $db->query('
        SELECT t.*, 
               COUNT(DISTINCT i.id) as invoice_count,
               u.username as creator_username
        FROM invoice_templates t
        LEFT JOIN invoices i ON i.template_id = t.id
        LEFT JOIN users u ON u.id = t.created_by
        GROUP BY t.id
        ORDER BY t.id
    ');
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Query failed: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Invoice Templates Diagnostic</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .can-delete { background-color: #d4edda; }
        .cannot-delete { background-color: #f8d7da; }
        .reason { font-size: 0.875rem; color: #666; }
    </style>
</head>
<body>
<div class="container mt-4">
    <h1>Invoice Templates Diagnostic</h1>
    
    <div class="alert alert-info">
        <h5>Current User</h5>
        <p>
            User ID: <?= htmlspecialchars($userId ?? 'Not logged in') ?><br>
            Is Admin: <?= $isAdmin ? 'Yes' : 'No' ?><br>
            Groups: <?= is_string($userGroups) ? htmlspecialchars($userGroups) : (is_array($userGroups) ? htmlspecialchars(implode(', ', array_map(function($g) { return is_array($g) ? ($g['name'] ?? '') : $g; }, $userGroups))) : 'None') ?>
        </p>
    </div>

    <h2>Templates Analysis</h2>
    <table class="table table-bordered table-sm">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Code</th>
                <th>Type</th>
                <th>Owner</th>
                <th>Created By</th>
                <th>Used By</th>
                <th>Can Delete?</th>
                <th>Reason</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($templates as $t): 
            $canDelete = false;
            $reason = '';
            
            // Check if system template
            if ($t['owner_type'] === 'system') {
                $reason = 'System template (protected)';
            }
            // Check if used by invoices
            elseif ($t['invoice_count'] > 0) {
                $reason = "Used by {$t['invoice_count']} invoice(s)";
            }
            // Check permissions
            elseif ($isAdmin || (is_string($userGroups) && stripos($userGroups, 'administrator') !== false)) {
                $canDelete = true;
                $reason = 'Admin privileges';
            }
            elseif ($t['owner_type'] === 'user' && $t['created_by'] == $userId) {
                $canDelete = true;
                $reason = 'You created this template';
            }
            else {
                $reason = 'No permission';
            }
        ?>
            <tr class="<?= $canDelete ? 'can-delete' : 'cannot-delete' ?>">
                <td><?= htmlspecialchars($t['id']) ?></td>
                <td><?= htmlspecialchars($t['name']) ?></td>
                <td><code><?= htmlspecialchars($t['code']) ?></code></td>
                <td>
                    <span class="badge bg-<?= $t['owner_type'] === 'system' ? 'primary' : ($t['owner_type'] === 'group' ? 'info' : 'secondary') ?>">
                        <?= htmlspecialchars($t['owner_type']) ?>
                    </span>
                </td>
                <td><?= htmlspecialchars($t['owner_id'] ?? '-') ?></td>
                <td><?= htmlspecialchars($t['creator_username'] ?? 'User #' . $t['created_by']) ?></td>
                <td>
                    <?php if ($t['invoice_count'] > 0): ?>
                        <span class="badge bg-warning"><?= $t['invoice_count'] ?> invoices</span>
                    <?php else: ?>
                        <span class="text-muted">None</span>
                    <?php endif; ?>
                </td>
                <td><?= $canDelete ? '✅ Yes' : '❌ No' ?></td>
                <td class="reason"><?= htmlspecialchars($reason) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>

    <div class="mt-4">
        <h3>Legend</h3>
        <ul>
            <li><span class="badge bg-primary">system</span> - System templates cannot be deleted</li>
            <li><span class="badge bg-info">group</span> - Group templates can be deleted by group members or admins</li>
            <li><span class="badge bg-secondary">user</span> - User templates can be deleted by the creator or admins</li>
            <li><span class="badge bg-warning">X invoices</span> - Templates in use cannot be deleted</li>
        </ul>
    </div>

    <div class="mt-4">
        <h3>Summary</h3>
        <?php
        $systemCount = count(array_filter($templates, fn($t) => $t['owner_type'] === 'system'));
        $inUseCount = count(array_filter($templates, fn($t) => $t['invoice_count'] > 0));
        $totalCount = count($templates);
        ?>
        <ul>
            <li>Total templates: <?= $totalCount ?></li>
            <li>System templates (protected): <?= $systemCount ?></li>
            <li>Templates in use: <?= $inUseCount ?></li>
            <li>Templates you can delete: <?= count(array_filter($templates, function($t) use ($userId, $isAdmin, $userGroups) {
                if ($t['owner_type'] === 'system' || $t['invoice_count'] > 0) return false;
                if ($isAdmin || (is_string($userGroups) && stripos($userGroups, 'administrator') !== false)) return true;
                if ($t['owner_type'] === 'user' && $t['created_by'] == $userId) return true;
                return false;
            })) ?></li>
        </ul>
    </div>

    <div class="mt-4">
        <a href="/fit/public/config/invoice-templates" class="btn btn-primary">Back to Templates</a>
    </div>
</div>
</body>
</html>