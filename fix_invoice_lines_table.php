<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fixing invoice_lines Table</h2>\n";
    
    // Check current structure
    echo "<h3>Current invoice_lines structure:</h3>\n";
    $stmt = $pdo->query("DESCRIBE invoice_lines");
    $columns = [];
    while ($row = $stmt->fetch()) {
        $columns[$row['Field']] = $row;
        echo "{$row['Field']} - {$row['Type']}\n";
    }
    
    // Check if display_order exists
    if (!isset($columns['display_order'])) {
        echo "\n<h3>⚠️ Missing 'display_order' column</h3>\n";
        echo "<a href='?add_column=display_order'>Click here to add display_order column</a>\n";
    } else {
        echo "\n✓ display_order column already exists\n";
    }
    
    // Handle column addition
    if (isset($_GET['add_column']) && $_GET['add_column'] == 'display_order') {
        echo "\n<h3>Adding display_order column...</h3>\n";
        
        try {
            $pdo->exec("ALTER TABLE invoice_lines ADD COLUMN display_order INT DEFAULT 1 AFTER line_total");
            echo "✓ Successfully added display_order column\n";
            
            // Update existing records
            $pdo->exec("
                SET @row_number = 0;
                UPDATE invoice_lines 
                SET display_order = (@row_number:=@row_number + 1) 
                ORDER BY invoice_id, id
            ");
            echo "✓ Updated existing records with display order\n";
            
            echo "\n<a href='test_retrocession_generation.php'>Test retrocession generation now</a>\n";
        } catch (Exception $e) {
            echo "Error adding column: " . $e->getMessage() . "\n";
        }
    }
    
    // Show sample data
    echo "\n<h3>Sample invoice_lines data:</h3>\n";
    $stmt = $pdo->query("SELECT * FROM invoice_lines LIMIT 5");
    $results = $stmt->fetchAll();
    
    if (count($results) > 0) {
        echo "<table border='1'>\n<tr>";
        foreach (array_keys($results[0]) as $col) {
            echo "<th>$col</th>";
        }
        echo "</tr>\n";
        
        foreach ($results as $row) {
            echo "<tr>";
            foreach ($row as $val) {
                echo "<td>" . htmlspecialchars(substr($val, 0, 50)) . "</td>";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "No data in invoice_lines table\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}