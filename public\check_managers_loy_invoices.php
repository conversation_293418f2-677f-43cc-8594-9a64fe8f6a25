<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '\"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Managers with Financial Obligations</h2>";
    
    // Find all managers with financial obligations
    $stmt = $db->prepare("
        SELECT u.id, u.first_name, u.last_name, 
               ufo.rent_amount, ufo.charges_amount,
               ufo.secretary_tvac_17, ufo.secretary_htva, ufo.tva_17
        FROM users u
        INNER JOIN user_financial_obligations ufo ON u.id = ufo.user_id AND ufo.end_date IS NULL
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 3  -- Managers group
        ORDER BY u.last_name, u.first_name
    ");
    $stmt->execute();
    $managers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($managers) . " managers with financial obligations.</p>";
    
    echo "<table border='1' cellpadding='10' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>User</th>";
    echo "<th>Rent</th>";
    echo "<th>Secretary HT<br>(Base for 17% VAT)</th>";
    echo "<th>Expected Invoice Lines</th>";
    echo "<th>Total TTC</th>";
    echo "</tr>";
    
    foreach ($managers as $manager) {
        $rent = floatval($manager['rent_amount']);
        $secretaryHT = floatval($manager['secretary_htva']);
        $vatAmount = $secretaryHT * 0.17;
        $totalTTC = $rent + $secretaryHT + $vatAmount;
        
        echo "<tr>";
        echo "<td><strong>{$manager['first_name']} {$manager['last_name']}</strong></td>";
        echo "<td>€" . number_format($rent, 2) . "</td>";
        echo "<td>€" . number_format($secretaryHT, 2) . "</td>";
        echo "<td>";
        echo "1. Loyer mensuel: €" . number_format($rent, 2) . " (0% VAT)<br>";
        if ($secretaryHT > 0) {
            echo "2. Frais secrétariat: €" . number_format($secretaryHT, 2) . " (17% VAT)";
        }
        echo "</td>";
        echo "<td style='background-color: #e8f5e9;'><strong>€" . number_format($totalTTC, 2) . "</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<h3>Fixed Calculation Logic:</h3>";
    echo "<ul>";
    echo "<li><strong>Loyer mensuel</strong>: rent_amount with 0% VAT</li>";
    echo "<li><strong>Frais secrétariat</strong>: secretary_htva with 17% VAT</li>";
    echo "<li><strong>Total</strong>: rent + secretary_htva + (secretary_htva × 0.17)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<p><a href='/fit/public/invoices/create?type=loyer' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>Test LOY Invoice Creation</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>