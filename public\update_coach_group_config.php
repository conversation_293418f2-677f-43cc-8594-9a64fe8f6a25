<?php
// Update coach group configuration

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$groupId = $_REQUEST['group_id'] ?? null;

if (!$groupId) {
    die("No group ID provided");
}

// Verify group exists
$stmt = $db->prepare("SELECT name FROM user_groups WHERE id = ?");
$stmt->execute([$groupId]);
$groupName = $stmt->fetchColumn();

if (!$groupName) {
    die("Group $groupId not found");
}

echo "<h1>Update Coach Group Configuration</h1>";

// Update or insert config
try {
    $stmt = $db->prepare("
        INSERT INTO config_settings (name, value, created_at, updated_at) 
        VALUES ('coach_group_id', ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE value = ?, updated_at = NOW()
    ");
    $stmt->execute([$groupId, $groupId]);
    
    echo "<p style='color: green; font-size: 1.2em;'>✅ Successfully updated coach_group_id to <strong>$groupId</strong> ($groupName)</p>";
    
    // Show coaches in this group
    $stmt = $db->prepare("
        SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, u.course_name
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = ?
        AND u.is_active = 1 AND u.can_be_invoiced = 1
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute([$groupId]);
    $coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Coaches in $groupName (Group $groupId):</h2>";
    echo "<p>Total: " . count($coaches) . " active coaches who can be invoiced</p>";
    
    if (count($coaches) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Course</th></tr>";
        foreach ($coaches as $coach) {
            echo "<tr>";
            echo "<td>{$coach['id']}</td>";
            echo "<td>{$coach['name']}</td>";
            echo "<td>{$coach['username']}</td>";
            echo "<td>{$coach['course_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error updating configuration: " . $e->getMessage() . "</p>";
}

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #e9e9e9; }
td, th { padding: 8px; }
</style>

<hr>
<h2>Test the Configuration:</h2>
<p>
<a href='/fit/public/invoices/create?type=location' target='_blank' style='padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; display: inline-block;'>
    Test Location Invoice (Should show coaches)
</a>
</p>
<p style='margin-top: 20px;'>
<a href='/fit/public/check_all_groups.php'>Back to Groups</a> | 
<a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a>
</p>