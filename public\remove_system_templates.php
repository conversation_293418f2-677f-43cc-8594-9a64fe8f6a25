<?php
// Remove system templates - USE WITH CAUTION
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
try {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $db = new PDO($dsn, $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Remove System Templates</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='container mt-4'>";

echo "<h1>Remove System Templates</h1>";

// Get system templates
$stmt = $db->query("
    SELECT t.*, COUNT(i.id) as invoice_count
    FROM invoice_templates t
    LEFT JOIN invoices i ON i.template_id = t.id
    WHERE t.owner_type = 'system'
    GROUP BY t.id
    ORDER BY t.id
");
$systemTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($systemTemplates)) {
    echo "<div class='alert alert-success'>No system templates found!</div>";
    echo "<a href='/fit/public/config/invoice-templates' class='btn btn-primary'>Back to Templates</a>";
    echo "</body></html>";
    exit;
}

// Display system templates
echo "<div class='alert alert-warning'>";
echo "<h5>⚠️ Warning: System Templates</h5>";
echo "<p>System templates are core application templates that are meant to be permanent. Removing them may affect the application's functionality.</p>";
echo "</div>";

echo "<h2>Current System Templates:</h2>";
echo "<table class='table table-bordered'>";
echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Type</th><th>Used By</th><th>Status</th></tr>";

$hasInvoices = false;
foreach ($systemTemplates as $template) {
    $rowClass = $template['invoice_count'] > 0 ? 'table-danger' : '';
    if ($template['invoice_count'] > 0) $hasInvoices = true;
    
    echo "<tr class='{$rowClass}'>";
    echo "<td>{$template['id']}</td>";
    echo "<td>{$template['name']}</td>";
    echo "<td><code>{$template['code']}</code></td>";
    echo "<td>{$template['invoice_type']}</td>";
    echo "<td>" . ($template['invoice_count'] > 0 ? "<strong>{$template['invoice_count']} invoices</strong>" : "None") . "</td>";
    echo "<td>" . ($template['invoice_count'] > 0 ? "❌ In Use" : "✅ Can Remove") . "</td>";
    echo "</tr>";
}
echo "</table>";

if ($hasInvoices) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>Cannot Remove Templates in Use</h5>";
    echo "<p>Some system templates are being used by invoices. You must delete or reassign those invoices first.</p>";
    echo "</div>";
}

// Removal options
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        $db->beginTransaction();
        
        if ($action === 'convert') {
            // Convert to user templates
            echo "<h2>Converting System Templates to User Templates...</h2>";
            
            $stmt = $db->prepare("
                UPDATE invoice_templates 
                SET owner_type = 'user', 
                    owner_id = ?,
                    description = CONCAT('(Former System Template) ', COALESCE(description, ''))
                WHERE owner_type = 'system'
            ");
            $stmt->execute([$_SESSION['user_id'] ?? 1]);
            $updated = $stmt->rowCount();
            
            $db->commit();
            echo "<div class='alert alert-success'>Successfully converted {$updated} system templates to user templates. You can now delete them through the UI.</div>";
            
        } elseif ($action === 'delete' && !$hasInvoices) {
            // Force delete (only if not in use)
            echo "<h2>Removing System Templates...</h2>";
            
            // Get IDs of templates not in use
            $stmt = $db->query("
                SELECT t.id 
                FROM invoice_templates t
                LEFT JOIN invoices i ON i.template_id = t.id
                WHERE t.owner_type = 'system' AND i.id IS NULL
            ");
            $deleteIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($deleteIds)) {
                $placeholders = implode(',', array_fill(0, count($deleteIds), '?'));
                
                // Delete related data
                $stmt = $db->prepare("DELETE FROM template_line_items WHERE template_id IN ($placeholders)");
                $stmt->execute($deleteIds);
                
                $stmt = $db->prepare("DELETE FROM invoice_template_settings WHERE template_id IN ($placeholders)");
                $stmt->execute($deleteIds);
                
                $stmt = $db->prepare("DELETE FROM template_vat_configs WHERE template_id IN ($placeholders)");
                $stmt->execute($deleteIds);
                
                // Delete templates
                $stmt = $db->prepare("DELETE FROM invoice_templates WHERE id IN ($placeholders)");
                $stmt->execute($deleteIds);
                $deleted = $stmt->rowCount();
                
                $db->commit();
                echo "<div class='alert alert-success'>Successfully removed {$deleted} system templates.</div>";
            } else {
                echo "<div class='alert alert-info'>No templates to delete.</div>";
            }
        }
        
        echo "<a href='/fit/public/config/invoice-templates' class='btn btn-primary'>Back to Templates</a>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    }
    
} else {
    // Show options
    echo "<h2>Choose an Option:</h2>";
    
    echo "<div class='row mt-4'>";
    
    // Option 1: Convert to user templates
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>Option 1: Convert to User Templates</h5>";
    echo "<p class='card-text'>Convert system templates to user templates. This allows you to delete them through the normal UI.</p>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='action' value='convert' class='btn btn-warning' onclick='return confirm(\"Convert all system templates to user templates?\")'>Convert to User Templates</button>";
    echo "</form>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Option 2: Force delete
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>Option 2: Force Delete</h5>";
    echo "<p class='card-text'>Permanently delete system templates that are not in use. Templates used by invoices cannot be deleted.</p>";
    if ($hasInvoices) {
        echo "<button class='btn btn-danger' disabled>Cannot Delete (Templates in Use)</button>";
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='action' value='delete' class='btn btn-danger' onclick='return confirm(\"Permanently delete all unused system templates? This cannot be undone!\")'>Force Delete</button>";
        echo "</form>";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<a href='/fit/public/config/invoice-templates' class='btn btn-secondary'>Cancel</a>";
    echo "</div>";
}

echo "</body></html>";
?>