<?php
/**
 * Invoice Module Routes
 */

use App\Controllers\InvoiceController;
use App\Controllers\RetrocessionController;
use App\Controllers\VoucherController;

// Main Invoice routes
Flight::route('GET /invoices', [new InvoiceController(), 'index']);
Flight::route('GET /invoices/create', [new InvoiceController(), 'create']);
Flight::route('POST /invoices', [new InvoiceController(), 'store']);
Flight::route('GET /invoices/@id:[0-9]+', [new InvoiceController(), 'show']);
Flight::route('GET /invoices/@id:[0-9]+/edit', [new InvoiceController(), 'edit']);
Flight::route('PUT /invoices/@id:[0-9]+', [new InvoiceController(), 'update']);
Flight::route('GET /invoices/@id:[0-9]+/delete', [new InvoiceController(), 'confirmDelete']);
Flight::route('DELETE /invoices/@id:[0-9]+', [new InvoiceController(), 'delete']);
Flight::route('POST /invoices/@id:[0-9]+/delete', [new InvoiceController(), 'delete']);

// Invoice actions
Flight::route('POST /invoices/@id:[0-9]+/send', [new InvoiceController(), 'send']);
Flight::route('POST /invoices/@id:[0-9]+/cancel', [new InvoiceController(), 'cancel']);
Flight::route('POST /invoices/@id:[0-9]+/lock', [new InvoiceController(), 'lock']);
Flight::route('POST /invoices/@id:[0-9]+/unlock', [new InvoiceController(), 'unlock']);
Flight::route('GET /invoices/@id:[0-9]+/download', [new InvoiceController(), 'download']);
Flight::route('GET /invoices/@id:[0-9]+/print', [new InvoiceController(), 'print']);
Flight::route('GET /invoices/@id:[0-9]+/email-preview', [new InvoiceController(), 'emailPreview']);
Flight::route('POST /invoices/@id:[0-9]+/payment', [new InvoiceController(), 'recordPayment']);
Flight::route('GET /invoices/@id:[0-9]+/payment', function($id) {
    Flight::json(['error' => 'Payment recording requires POST method. Please submit the form from the invoice page.'], 405);
});
Flight::route('POST /invoices/@id:[0-9]+/items', [new InvoiceController(), 'updateInvoiceItems']);

// Credit notes
Flight::route('GET /invoices/@id:[0-9]+/credit-note', [new InvoiceController(), 'createCreditNote']);
Flight::route('POST /invoices/@id:[0-9]+/credit-note', [new InvoiceController(), 'storeCreditNote']);

// Bulk operations
Flight::route('POST /invoices/bulk-send', [new InvoiceController(), 'bulkSend']);
Flight::route('POST /invoices/bulk-export', [new InvoiceController(), 'bulkExport']);
Flight::route('POST /invoices/bulk-delete', [new InvoiceController(), 'bulkDelete']);
Flight::route('GET /invoices/bulk-delete', [new InvoiceController(), 'bulkDelete']);
Flight::route('POST /invoices/bulk-archive', [new InvoiceController(), 'bulkArchive']);

// Archive routes
Flight::route('GET /invoices/archive', [new InvoiceController(), 'archive']);
Flight::route('POST /invoices/@id:[0-9]+/archive', [new InvoiceController(), 'archiveInvoice']);
Flight::route('POST /invoices/@id:[0-9]+/restore', [new InvoiceController(), 'restoreInvoice']);

// Invoice templates
Flight::route('GET /invoices/templates', [new InvoiceController(), 'templates']);
Flight::route('GET /invoices/templates/@id:[0-9]+/edit', [new InvoiceController(), 'editTemplate']);
Flight::route('PUT /invoices/templates/@id:[0-9]+', [new InvoiceController(), 'updateTemplate']);

// AJAX routes
Flight::route('GET /invoices/search-billable', [new InvoiceController(), 'searchBillable']);
Flight::route('GET /invoices/generate-number', [new InvoiceController(), 'generateNumber']);
Flight::route('POST /invoices/calculate-line', [new InvoiceController(), 'calculateLine']);
Flight::route('GET /invoices/check-voucher', [new InvoiceController(), 'checkVoucher']);
Flight::route('GET /invoices/count-all', [new InvoiceController(), 'countAllInvoices']);
Flight::route('POST /invoices/delete-all', [new InvoiceController(), 'deleteAllInvoices']);

// API routes for invoice templates
Flight::route('GET /api/invoice-templates', [new InvoiceController(), 'getInvoiceTemplatesApi']);
Flight::route('GET /api/invoice-templates/@id:[0-9]+/details', [new InvoiceController(), 'getInvoiceTemplateDetails']);

// Redirects for old URLs to unified bulk generation
Flight::route('GET /invoices/generate-monthly', function() {
    Flight::redirect('/invoices/bulk-generation');
});
Flight::route('GET /invoices/bulk-loyer', function() {
    Flight::redirect('/invoices/bulk-generation?tab=loyer');
});

// Unified bulk invoice generation
Flight::route('GET /invoices/bulk-generation', [new InvoiceController(), 'bulkGenerationView']);
Flight::route('POST /invoices/bulk-generation/generate', [new InvoiceController(), 'bulkGenerate']);

// Retrocession routes
Flight::route('GET /retrocession', [new RetrocessionController(), 'index']);
Flight::route('GET /retrocession/@practitionerId:[0-9]+/data-entry', [new RetrocessionController(), 'dataEntry']);
Flight::route('POST /retrocession/@practitionerId:[0-9]+/data-entry', [new RetrocessionController(), 'saveDataEntry']);
Flight::route('POST /retrocession/@practitionerId:[0-9]+/confirm', [new RetrocessionController(), 'confirmEntry']);
Flight::route('POST /retrocession/@practitionerId:[0-9]+/generate-invoice', [new RetrocessionController(), 'generateInvoice']);
Flight::route('POST /retrocession/bulk-generate', [new RetrocessionController(), 'bulkGenerate']);
// Redirect old retrocession bulk monthly to unified generation
Flight::route('GET /retrocession/bulk-monthly', function() {
    Flight::redirect('/invoices/bulk-generation?tab=retrocession');
});
Flight::route('POST /retrocession/calculate-preview', [new RetrocessionController(), 'calculatePreview']);

// Rate profiles
Flight::route('GET /retrocession/rate-profiles', [new RetrocessionController(), 'rateProfiles']);
Flight::route('GET /retrocession/rate-profiles/create', [new RetrocessionController(), 'createProfile']);
Flight::route('POST /retrocession/rate-profiles', [new RetrocessionController(), 'storeProfile']);
Flight::route('GET /retrocession/rate-profiles/@id:[0-9]+/edit', [new RetrocessionController(), 'editProfile']);
Flight::route('PUT /retrocession/rate-profiles/@id:[0-9]+', [new RetrocessionController(), 'updateProfile']);
Flight::route('POST /retrocession/rate-profiles/assign', [new RetrocessionController(), 'assignProfile']);

// Redirect billing wizard to unified generation  
Flight::route('GET /billing-wizard', function() {
    Flight::redirect('/invoices/bulk-generation');
});

// Voucher routes
Flight::route('GET /vouchers', [new VoucherController(), 'index']);
Flight::route('GET /vouchers/create', [new VoucherController(), 'create']);
Flight::route('POST /vouchers', [new VoucherController(), 'store']);
Flight::route('GET /vouchers/@id:[0-9]+', [new VoucherController(), 'show']);
Flight::route('POST /vouchers/@id:[0-9]+/cancel', [new VoucherController(), 'cancel']);
Flight::route('GET /vouchers/@id:[0-9]+/print', [new VoucherController(), 'print']);
Flight::route('POST /vouchers/@id:[0-9]+/email', [new VoucherController(), 'email']);

// Voucher operations
Flight::route('POST /vouchers/use', [new VoucherController(), 'use']);
Flight::route('GET /vouchers/check', [new VoucherController(), 'check']);
Flight::route('GET /vouchers/expiring', [new VoucherController(), 'expiring']);
Flight::route('POST /vouchers/send-expiry-reminders', [new VoucherController(), 'sendExpiryReminders']);
Flight::route('GET /vouchers/statistics', [new VoucherController(), 'statistics']);

// CNS Import routes
Flight::route('GET /cns/import', [new \App\Controllers\CnsImportController(), 'index']);
Flight::route('GET /cns/import/upload', [new \App\Controllers\CnsImportController(), 'upload']);
Flight::route('POST /cns/import/upload', [new \App\Controllers\CnsImportController(), 'processUpload']);
Flight::route('GET /cns/import/@id:[0-9]+', [new \App\Controllers\CnsImportController(), 'view']);
Flight::route('GET /cns/import/@id:[0-9]+/ocr', [new \App\Controllers\CnsImportController(), 'ocr']);
Flight::route('POST /cns/import/@id:[0-9]+/ocr', [new \App\Controllers\CnsImportController(), 'processOcr']);
Flight::route('GET /cns/import/@id:[0-9]+/review', [new \App\Controllers\CnsImportController(), 'reviewOcr']);
Flight::route('POST /cns/import/@id:[0-9]+/review', [new \App\Controllers\CnsImportController(), 'saveOcrReview']);
Flight::route('GET /cns/import/@id:[0-9]+/process', [new \App\Controllers\CnsImportController(), 'process']);
Flight::route('POST /cns/import/@id:[0-9]+/execute', [new \App\Controllers\CnsImportController(), 'executeImport']);
Flight::route('DELETE /cns/import/@id:[0-9]+', [new \App\Controllers\CnsImportController(), 'delete']);
Flight::route('GET /cns/import/template/@type', [new \App\Controllers\CnsImportController(), 'downloadTemplate']);