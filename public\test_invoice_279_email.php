<?php
/**
 * Test Email Sending for Invoice 279 with Full Debugging
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\EmailService;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Invoice 279 Email</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .debug { background: #e9ecef; padding: 10px; margin: 5px 0; font-family: monospace; font-size: 12px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        h3 { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Test Email Sending for Invoice 279 (FAC-DIV-2025-0190)</h1>
    
    <?php
    $invoiceId = 279;
    $testEmail = $_POST['test_email'] ?? '<EMAIL>';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<div class='info'><h3>Starting Email Send Test...</h3></div>";
        
        try {
            // Step 1: Load invoice
            echo "<div class='debug'>Step 1: Loading invoice #$invoiceId...</div>";
            $invoice = Invoice::find($invoiceId);
            
            if (!$invoice) {
                throw new Exception("Invoice not found!");
            }
            
            echo "<div class='success'>✓ Invoice loaded: " . $invoice->invoice_number . "</div>";
            
            // Step 2: Initialize EmailService
            echo "<div class='debug'>Step 2: Initializing EmailService...</div>";
            $emailService = new EmailService();
            echo "<div class='success'>✓ EmailService initialized</div>";
            
            // Step 3: Check database connection
            echo "<div class='debug'>Step 3: Checking database connection...</div>";
            $db = Flight::db();
            $testQuery = $db->query("SELECT 1");
            if ($testQuery) {
                echo "<div class='success'>✓ Database connection OK</div>";
            }
            
            // Step 4: Send email with detailed logging
            echo "<div class='debug'>Step 4: Calling sendInvoiceEmail($invoiceId, '$testEmail')...</div>";
            
            // Capture any output
            ob_start();
            $result = $emailService->sendInvoiceEmail($invoiceId, $testEmail);
            $output = ob_get_clean();
            
            if ($output) {
                echo "<div class='warning'>Output during send: <pre>" . htmlspecialchars($output) . "</pre></div>";
            }
            
            // Step 5: Display result
            echo "<div class='info'><h3>Email Send Result:</h3>";
            echo "<pre>" . print_r($result, true) . "</pre>";
            echo "</div>";
            
            if (isset($result['success']) && $result['success']) {
                echo "<div class='success'>✅ Email reportedly sent successfully!</div>";
            } else {
                echo "<div class='error'>❌ Email send failed: " . ($result['message'] ?? 'Unknown error') . "</div>";
            }
            
            // Step 6: Check if log was created
            echo "<div class='debug'>Step 6: Checking email_logs table...</div>";
            $stmt = $db->prepare("SELECT * FROM email_logs WHERE invoice_id = :id ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([':id' => $invoiceId]);
            $log = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($log) {
                echo "<div class='success'>✓ Email log created:</div>";
                echo "<pre>" . print_r($log, true) . "</pre>";
            } else {
                echo "<div class='error'>❌ No email log found!</div>";
                
                // Try to manually insert a test log
                echo "<div class='debug'>Attempting manual log insert...</div>";
                try {
                    $stmt = $db->prepare("
                        INSERT INTO email_logs (invoice_id, recipient_email, subject, status, created_at)
                        VALUES (:invoice_id, :email, :subject, :status, NOW())
                    ");
                    $stmt->execute([
                        ':invoice_id' => $invoiceId,
                        ':email' => $testEmail,
                        ':subject' => 'Test log for invoice ' . $invoice->invoice_number,
                        ':status' => 'sent'
                    ]);
                    echo "<div class='success'>✓ Manual log insert successful</div>";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Manual log insert failed: " . $e->getMessage() . "</div>";
                }
            }
            
            // Step 7: Check error logs
            echo "<div class='debug'>Step 7: Checking PHP error log...</div>";
            $errorLog = ini_get('error_log');
            if ($errorLog && file_exists($errorLog)) {
                $recentErrors = array_slice(file($errorLog), -10);
                if ($recentErrors) {
                    echo "<div class='warning'>Recent errors:</div>";
                    echo "<pre>" . htmlspecialchars(implode("", $recentErrors)) . "</pre>";
                }
            }
            
            // Step 8: Test email configuration
            echo "<div class='info'><h3>Email Configuration:</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Setting</th><th>Value</th></tr>";
            echo "<tr><td>MAIL_DRIVER</td><td>" . ($_ENV['MAIL_DRIVER'] ?? 'not set') . "</td></tr>";
            echo "<tr><td>MAIL_HOST</td><td>" . ($_ENV['MAIL_HOST'] ?? 'not set') . "</td></tr>";
            echo "<tr><td>MAIL_PORT</td><td>" . ($_ENV['MAIL_PORT'] ?? 'not set') . "</td></tr>";
            echo "<tr><td>MAIL_FROM_ADDRESS</td><td>" . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'not set') . "</td></tr>";
            echo "</table></div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h3>Exception caught:</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
            echo "</div>";
        }
        
    } else {
        // Show test form
        ?>
        <form method="POST">
            <div class='info'>
                <h3>Test Email Send</h3>
                <p>This will attempt to send invoice 279 via email and show detailed debugging information.</p>
                
                <label for="test_email">Send to email:</label><br>
                <input type="email" id="test_email" name="test_email" value="<EMAIL>" style="width: 300px; padding: 5px;"><br><br>
                
                <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Run Email Test
                </button>
            </div>
        </form>
        
        <div class='warning'>
            <h3>Current Status:</h3>
            <?php
            $db = Flight::db();
            $stmt = $db->prepare("SELECT * FROM invoices WHERE id = 279");
            $stmt->execute();
            $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p>Invoice Number: <strong>" . $invoice['invoice_number'] . "</strong></p>";
            echo "<p>Status: <strong>" . $invoice['status'] . "</strong></p>";
            echo "<p>Sent At: <strong>" . ($invoice['sent_at'] ?: 'NULL') . "</strong></p>";
            
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM email_logs WHERE invoice_id = 279");
            $stmt->execute();
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>Email logs for this invoice: <strong>" . $count['count'] . "</strong></p>";
            ?>
        </div>
        <?php
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/check_email_logs_table.php">Check Email Logs</a> |
        <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Email Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>