<?php
/**
 * Debug script for bulk Loyer invoice generation
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Clear any opcache if available
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✓ Opcache cleared\n\n";
}

// Database connection
try {
    $dsn = sprintf(
        "mysql:host=%s;dbname=%s;charset=utf8mb4",
        $_ENV['DB_HOST'] ?? 'localhost',
        $_ENV['DB_DATABASE'] ?? 'healthcenter_billing'
    );
    
    $db = new PDO($dsn, $_ENV['DB_USERNAME'] ?? 'root', $_ENV['DB_PASSWORD'] ?? '');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Connected to database successfully\n\n";
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage() . "\n");
}

// Set HTML header
header('Content-Type: text/plain; charset=utf-8');

echo "=== DEBUGGING INVOICE GENERATION ===\n\n";

// Check the actual code at line 500
$file = file_get_contents(__DIR__ . '/app/services/MonthlyInvoiceGenerator.php');
$lines = explode("\n", $file);
echo "Line 499: " . trim($lines[498]) . "\n";
echo "Line 500: " . trim($lines[499]) . "\n";
echo "Line 501: " . trim($lines[500]) . "\n\n";

// Check if LOC invoice type exists
echo "=== CHECKING LOC INVOICE TYPE ===\n";
$stmt = $db->prepare("SELECT * FROM invoice_types WHERE code = 'LOC'");
$stmt->execute();
$locType = $stmt->fetch(PDO::FETCH_ASSOC);

if ($locType) {
    echo "✓ LOC type found:\n";
    echo "  ID: " . $locType['id'] . "\n";
    echo "  Name: " . $locType['name'] . "\n";
    echo "  Code: " . $locType['code'] . "\n";
    echo "  Active: " . ($locType['is_active'] ? 'Yes' : 'No') . "\n";
} else {
    echo "✗ LOC type NOT FOUND\n";
}

echo "\n=== TESTING Invoice::find() METHOD ===\n";

// Test the Invoice model's find method
try {
    // First, let's get a valid invoice ID
    $stmt = $db->query("SELECT id FROM invoices ORDER BY id DESC LIMIT 1");
    $lastInvoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($lastInvoice) {
        echo "Testing with invoice ID: " . $lastInvoice['id'] . "\n";
        
        // Test what Invoice::find() returns
        $invoice = \App\Models\Invoice::find($lastInvoice['id']);
        echo "Type of returned value: " . gettype($invoice) . "\n";
        
        if (is_object($invoice)) {
            echo "Class: " . get_class($invoice) . "\n";
            echo "Invoice number: " . (property_exists($invoice, 'invoice_number') ? $invoice->invoice_number : 'Property not found') . "\n";
            
            // Check if we can access as array
            if ($invoice instanceof ArrayAccess) {
                echo "Can access as array: Yes\n";
                echo "Invoice number via array: " . $invoice['invoice_number'] . "\n";
            } else {
                echo "Can access as array: No\n";
            }
        }
    }
} catch (Exception $e) {
    echo "Error testing Invoice::find(): " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== CHECKING FILE MODIFICATION TIME ===\n";
$filePath = __DIR__ . '/app/services/MonthlyInvoiceGenerator.php';
echo "File: " . $filePath . "\n";
echo "Last modified: " . date('Y-m-d H:i:s', filemtime($filePath)) . "\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\n";

echo "\n=== PHP OPCACHE STATUS ===\n";
if (function_exists('opcache_get_status')) {
    $status = opcache_get_status();
    echo "Opcache enabled: " . ($status['opcache_enabled'] ? 'Yes' : 'No') . "\n";
    if ($status['opcache_enabled']) {
        echo "Cache hits: " . $status['opcache_statistics']['hits'] . "\n";
        echo "Cache misses: " . $status['opcache_statistics']['misses'] . "\n";
    }
} else {
    echo "Opcache not available\n";
}