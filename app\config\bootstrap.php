<?php
/**
 * Bootstrap file - Initialize Flight and core services
 */

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use App\Helpers\Language;

// Load environment variables FIRST
if (file_exists(dirname(__DIR__, 2) . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__, 2));
    $dotenv->load();
}

// Load helper functions
require_once __DIR__ . '/../helpers/functions.php';
require_once __DIR__ . '/../helpers/menu-config.php';
require_once __DIR__ . '/../helpers/column_helper.php';

// Set timezone
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Europe/Luxembourg');

// Define PUBLIC_PATH constant for file operations
if (!defined('PUBLIC_PATH')) {
    define('PUBLIC_PATH', dirname(__DIR__, 2) . '/public');
}

// Session configuration - start session early (before accessing $_SESSION)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize language system
// Check for user language preference in session
$userLanguage = $_SESSION['user_language'] ?? $_SESSION['language'] ?? 'fr';
Language::setLanguage($userLanguage);
Language::setFallbackLanguage('en');

// Set base path for Flight
$baseUrl = $_ENV['APP_BASE_URL'] ?? '';
Flight::set('flight.base_url', $baseUrl);

// Register flash message handler
Flight::map('flash', function($type, $message) {
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message
    ];
});

// Register flash message getter
Flight::map('getFlash', function() {
    if (!isset($_SESSION['flash_messages'])) {
        return [];
    }
    $messages = $_SESSION['flash_messages'];
    unset($_SESSION['flash_messages']);
    
    // Remove duplicate messages and keep only the most recent of each type
    $uniqueMessages = [];
    $seen = [];
    
    // Process messages in reverse order (most recent first)
    foreach (array_reverse($messages) as $msg) {
        $key = $msg['type'] . '::' . $msg['message'];
        if (!isset($seen[$key])) {
            $seen[$key] = true;
            array_unshift($uniqueMessages, $msg);
        }
    }
    
    return $uniqueMessages;
});

// Error reporting
$debug = isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true';
if ($debug) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Initialize Logger
$log = new Logger('app');
$log->pushHandler(new StreamHandler(__DIR__ . '/../../storage/logs/app.log', Logger::WARNING));
Flight::set('log', $log);

// Database configuration
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

Flight::register('db', PDO::class, [
    "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4",
    $dbUser,
    $dbPass,
    [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_PERSISTENT => true, // Enable persistent connections
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
    ]
]);

// Register cache service
Flight::register('cache', 'App\Services\CacheService');

// Twig template engine with optimizations
$loader = new \Twig\Loader\FilesystemLoader(__DIR__ . '/../views');
$twig = new \Twig\Environment($loader, [
    'cache' => $debug ? false : __DIR__ . '/../../storage/cache/twig',
    'debug' => $debug,
    'auto_reload' => $debug, // Only check for updates in debug mode
    'optimizations' => -1, // Enable all optimizations
]);

// Add global variables to Twig
$twig->addGlobal('app_name', $_ENV['APP_NAME'] ?? 'Fit360 AdminDesk');
$twig->addGlobal('app_url', $_ENV['APP_URL'] ?? 'http://localhost:8000');
$twig->addGlobal('base_url', Flight::get('flight.base_url'));
// Add app object to prevent undefined variable errors
$twig->addGlobal('app', new \App\Core\TwigArrayWrapper([
    'language' => $_SESSION['user_language'] ?? 'fr',
    'name' => $_ENV['APP_NAME'] ?? 'Fit360 AdminDesk'
]));
// Re-enable session global but wrap it to prevent array to string conversion
if (!empty($_SESSION)) {
    $twig->addGlobal('session', new \App\Core\TwigArrayWrapper($_SESSION));
}
$twig->addGlobal('currency', '€'); // Default currency for Luxembourg

// Generate CSRF token if not exists
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$twig->addGlobal('csrf_token', $_SESSION['csrf_token']);

// Add translation function to Twig
$twig->addFunction(new \Twig\TwigFunction('__', function($key, $params = []) {
    return __($key, $params);
}));

// Add language helper functions to Twig
$twig->addFunction(new \Twig\TwigFunction('current_language', function() {
    return current_language();
}));

// Add CSRF field function to Twig
$twig->addFunction(new \Twig\TwigFunction('csrf_field', function() {
    return '<input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">';
}, ['is_safe' => ['html']]));

// Add url function to Twig
$twig->addFunction(new \Twig\TwigFunction('url', function($path = '') {
    $baseUrl = Flight::get('flight.base_url');
    // Remove trailing slash from base URL
    $baseUrl = rtrim($baseUrl, '/');
    // Ensure path starts with /
    if ($path && $path[0] !== '/') {
        $path = '/' . $path;
    }
    return $baseUrl . $path;
}));

// Add custom Twig extensions
$twig->addExtension(new \App\Core\TwigExtensions());

Flight::set('view', $twig);

// Custom error handler
Flight::map('error', function($ex) use ($debug) {
    $log = Flight::get('log');
    if ($log) {
        $log->error($ex->getMessage(), ['exception' => $ex]);
    }
    
    if ($debug) {
        echo '<h1>Error</h1>';
        echo '<pre>' . $ex->getMessage() . '</pre>';
        echo '<pre>' . $ex->getTraceAsString() . '</pre>';
    } else {
        $view = Flight::get('view');
        if ($view) {
            echo $view->render('errors/500.twig');
        } else {
            echo 'Internal Server Error';
        }
    }
});

// 404 handler
Flight::map('notFound', function(){
    $view = Flight::get('view');
    if ($view) {
        echo $view->render('errors/404.twig');
    } else {
        header('HTTP/1.0 404 Not Found');
        echo '404 Not Found';
    }
});

// Performance monitoring (only in debug mode)
if ($debug) {
    Flight::before('start', function() {
        $_SERVER['REQUEST_TIME_FLOAT'] = microtime(true);
        $_SERVER['REQUEST_MEMORY_START'] = memory_get_usage(true);
    });
    
    Flight::after('start', function() {
        $time = microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'];
        $memory = memory_get_peak_usage(true) / 1024 / 1024;
        $memoryUsed = (memory_get_peak_usage(true) - $_SERVER['REQUEST_MEMORY_START']) / 1024 / 1024;
        
        // Log performance metrics
        error_log(sprintf(
            "[PERF] %s | Time: %.3fs | Memory Peak: %.2fMB | Memory Used: %.2fMB",
            $_SERVER['REQUEST_URI'] ?? 'CLI',
            $time,
            $memory,
            $memoryUsed
        ));
    });
}

// Load routes - THIS IS CRITICAL!
require_once __DIR__ . '/routes.php';