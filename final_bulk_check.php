<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Final Bulk Generation Readiness Check</h2>\n";
    
    // Check <PERSON>'s June 2025 data
    echo "<h3>1. June 2025 Data (needed for July invoice):</h3>\n";
    $stmt = $pdo->prepare("
        SELECT u.id, u.first_name, u.last_name, uma.month, uma.year, uma.cns_amount, uma.patient_amount
        FROM users u
        LEFT JOIN user_monthly_retrocession_amounts uma ON u.id = uma.user_id
        WHERE u.id = 1 AND uma.month = 6 AND uma.year = 2025
    ");
    $stmt->execute();
    $june_data = $stmt->fetch();
    
    if ($june_data) {
        echo "✅ Frank Huet has June 2025 data:\n";
        echo "- CNS: {$june_data['cns_amount']}€\n";
        echo "- Patient: {$june_data['patient_amount']}€\n";
    } else {
        echo "❌ Frank Huet has NO June 2025 data!\n";
        echo "<a href='add_june_data.php'>Add June data</a>\n";
    }
    
    // Check for blocking entries
    echo "\n<h3>2. Checking for blocking entries:</h3>\n";
    
    // Check retrocession_data_entry
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM retrocession_data_entry 
        WHERE practitioner_id = 135 AND period_month = 7 AND period_year = 2025
    ");
    $stmt->execute();
    $retro_count = $stmt->fetchColumn();
    
    echo "- Retrocession entries for July 2025: " . ($retro_count > 0 ? "❌ $retro_count found (blocking)" : "✅ None (good)") . "\n";
    
    // Check user_generated_invoices
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM user_generated_invoices 
        WHERE user_id = 1 AND period_month = 7 AND period_year = 2025 AND invoice_type = 'RET'
    ");
    $stmt->execute();
    $ugi_count = $stmt->fetchColumn();
    
    echo "- User generated invoice entries for July 2025: " . ($ugi_count > 0 ? "❌ $ugi_count found (blocking)" : "✅ None (good)") . "\n";
    
    // Final status
    echo "\n<h3>3. Ready to Generate?</h3>\n";
    $ready = $june_data && $retro_count == 0 && $ugi_count == 0;
    
    if ($ready) {
        echo "<p style='color: green; font-size: 1.2em;'>✅ <strong>YES! Ready to generate July 2025 invoice</strong></p>\n";
        echo "<p><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation' style='font-size: 1.2em;'>Go to Bulk Generation</a></p>\n";
    } else {
        echo "<p style='color: red; font-size: 1.2em;'>❌ <strong>NOT READY - Fix issues above</strong></p>\n";
        
        if ($retro_count > 0 || $ugi_count > 0) {
            echo "<p><a href='fix_retrocession_duplicate.php'>Clean duplicate entries</a></p>\n";
        }
        if (!$june_data) {
            echo "<p><a href='add_june_data.php'>Add June 2025 data</a></p>\n";
        }
    }
    
    echo "\n<hr>\n";
    echo "<h4>Remember:</h4>\n";
    echo "<ul>\n";
    echo "<li>Select <strong>July 2025</strong> as the invoice month</li>\n";
    echo "<li>The system will automatically use <strong>June 2025</strong> data</li>\n";
    echo "<li>Make sure Frank Huet's checkbox is enabled (not grayed out)</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}