<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing RET Type Codes</h2>";
    
    // First, show what we have
    echo "<h3>Current RET-related types:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code LIKE '%ret%' OR prefix LIKE '%RET%'
        ORDER BY id
    ");
    $existing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($existing as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['code']}</td>";
        echo "<td>{$row['prefix']}</td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Update ret2 to ret25
    echo "<h3>Updating codes:</h3>";
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret25' WHERE code = 'ret2'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Updated 'ret2' to 'ret25'</p>";
    }
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret30' WHERE code = 'ret3'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Updated 'ret3' to 'ret30'</p>";
    }
    
    // Now ensure correct prefixes
    echo "<h3>Ensuring correct prefixes:</h3>";
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET prefix = 'FAC-RET25' WHERE code = 'ret25'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Updated ret25 prefix to FAC-RET25</p>";
    }
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET prefix = 'FAC-RET30' WHERE code = 'ret30'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Updated ret30 prefix to FAC-RET30</p>";
    }
    
    // Show final state
    echo "<h3>Final configuration:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30')
        ORDER BY code
    ");
    $final = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($final as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $rowStyle = '';
        if ($row['code'] == 'ret25') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['code'] == 'ret30') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Get the ret25 ID for the next step
    $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = 'ret25'");
    $stmt->execute();
    $ret25 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($ret25) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>The RET types are now properly configured.</p>";
        echo "<p>ret25 has ID: <strong>{$ret25['id']}</strong></p>";
        echo "<p><a href='/fit/public/fix_frank_invoice.php' style='color: #155724; font-weight: bold;'>→ Now fix Frank's invoice</a></p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}