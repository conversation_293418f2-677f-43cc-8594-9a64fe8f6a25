<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Invoice Type Display Issues</h2>";
    
    // Check invoices with RET prefixes
    echo "<h3>Retrocession Invoices:</h3>";
    $stmt = $db->query("
        SELECT i.id, i.invoice_number, i.type_id, i.client_id,
               cit.code as type_code, cit.prefix, cit.name as type_name,
               c.name as client_name
        FROM invoices i
        LEFT JOIN config_invoice_types cit ON cit.id = i.type_id
        LEFT JOIN clients c ON c.id = i.client_id
        WHERE i.invoice_number LIKE '%RET%'
        ORDER BY i.id DESC
        LIMIT 20
    ");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>ID</th><th>Invoice Number</th><th>type_id</th><th>Type Code</th><th>Type Name (JSON)</th><th>Client</th><th>Issues</th>";
    echo "</tr>";
    
    foreach ($invoices as $invoice) {
        $issues = [];
        
        // Check if type_id is NULL
        if (empty($invoice['type_id'])) {
            $issues[] = "type_id is NULL";
        }
        
        // Parse the type name
        $typeName = 'N/A';
        if (!empty($invoice['type_name'])) {
            $nameData = json_decode($invoice['type_name'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($nameData)) {
                $typeName = $nameData['fr'] ?? $nameData['en'] ?? $invoice['type_name'];
            } else {
                $typeName = $invoice['type_name'];
            }
        }
        
        $rowStyle = '';
        if (!empty($issues)) {
            $rowStyle = 'background-color: #ffcccc;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$invoice['id']}</td>";
        echo "<td><strong>{$invoice['invoice_number']}</strong></td>";
        echo "<td>" . ($invoice['type_id'] ?? 'NULL') . "</td>";
        echo "<td>{$invoice['type_code']}</td>";
        echo "<td>{$typeName}</td>";
        echo "<td>{$invoice['client_name']}</td>";
        echo "<td style='color: red;'>" . implode(', ', $issues) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check config_invoice_types
    echo "<h3>Available Invoice Types (config_invoice_types):</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name
        FROM config_invoice_types
        WHERE code LIKE '%ret%'
        ORDER BY id
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>ID</th><th>Code</th><th>Prefix</th><th>Name (French)</th>";
    echo "</tr>";
    
    foreach ($types as $type) {
        $nameData = json_decode($type['name'], true);
        $frenchName = is_array($nameData) ? ($nameData['fr'] ?? $type['name']) : $type['name'];
        
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['code']}</td>";
        echo "<td>{$type['prefix']}</td>";
        echo "<td>{$frenchName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Summary
    echo "<h3>Summary of Issues:</h3>";
    echo "<ol>";
    echo "<li><strong>NULL type_id:</strong> Some invoices have NULL type_id, which causes 'N/A' to display</li>";
    echo "<li><strong>Name display:</strong> The names in config_invoice_types show percentages (5%, 10%) not '25%'</li>";
    echo "</ol>";
    
    echo "<h3>Recommended Fixes:</h3>";
    echo "<ol>";
    echo "<li>Update invoices with NULL type_id to use the correct type based on their prefix</li>";
    echo "<li>If you want 'Rétrocession 25%' instead of 'Rétrocession 5%', update the name in config_invoice_types</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}