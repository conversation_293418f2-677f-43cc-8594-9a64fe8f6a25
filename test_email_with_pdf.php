<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\Invoice;
use App\Models\User;
use App\Services\EmailService;
use App\Services\PdfService;

$db = Flight::db();

echo "<!DOCTYPE html><html><head><title>Email Test with PDF</title></head><body>";
echo "<h2>Testing Email with PDF Attachment</h2>";

try {
    // Get user Frank
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color: red;'>User Frank not found!</p>";
        exit;
    }
    
    echo "<p>User: {$user['first_name']} {$user['last_name']} (Email: {$user['email']})</p>";
    
    // Find or create a DIV invoice
    $invoice = new Invoice();
    $invoices = $invoice->findBy([
        'user_id' => 1,
        'document_type_id' => 1,
        'invoice_type_id' => 4
    ], ['order' => 'id DESC', 'limit' => 1]);
    
    if ($invoices->count() > 0) {
        $invoiceData = $invoices->first();
        $invoiceId = $invoiceData['id'];
        echo "<p>Found invoice: {$invoiceData['invoice_number']} (ID: {$invoiceId})</p>";
    } else {
        echo "<p>No DIV invoice found for Frank. Please create one first.</p>";
        exit;
    }
    
    // Get complete invoice data with all relations
    $completeInvoiceData = $invoice->getInvoiceWithDetails($invoiceId);
    
    if (!$completeInvoiceData) {
        echo "<p style='color: red;'>Could not load invoice details!</p>";
        exit;
    }
    
    echo "<h3>Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$completeInvoiceData['invoice_number']}</li>";
    echo "<li>Type: {$completeInvoiceData['invoice_type_name']} ({$completeInvoiceData['invoice_type_code']})</li>";
    echo "<li>Subject: {$completeInvoiceData['subject']}</li>";
    echo "<li>Period: {$completeInvoiceData['period']}</li>";
    echo "<li>Total: €" . number_format($completeInvoiceData['total'], 2) . "</li>";
    echo "<li>Status: {$completeInvoiceData['status']}</li>";
    echo "</ul>";
    
    // Show invoice items
    if (!empty($completeInvoiceData['items'])) {
        echo "<h4>Invoice Items:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Description</th><th>Qty</th><th>Price</th><th>VAT</th><th>Total</th></tr>";
        foreach ($completeInvoiceData['items'] as $item) {
            $itemTotal = $item['quantity'] * $item['unit_price'] * (1 + $item['vat_rate'] / 100);
            echo "<tr>";
            echo "<td>{$item['description']}</td>";
            echo "<td>{$item['quantity']}</td>";
            echo "<td>€{$item['unit_price']}</td>";
            echo "<td>{$item['vat_rate']}%</td>";
            echo "<td>€" . number_format($itemTotal, 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test PDF generation
    echo "<h3>Testing PDF Generation...</h3>";
    
    $pdfService = new PdfService();
    $pdfContent = $pdfService->generateInvoicePdf($completeInvoiceData);
    
    if ($pdfContent) {
        $pdfSize = strlen($pdfContent);
        echo "<p>✓ PDF generated successfully (Size: " . number_format($pdfSize) . " bytes)</p>";
        
        // Save PDF temporarily for verification
        $tempPdfPath = __DIR__ . '/storage/temp_invoice_' . $invoiceId . '.pdf';
        file_put_contents($tempPdfPath, $pdfContent);
        echo "<p>PDF saved temporarily at: <a href='/fit/storage/temp_invoice_{$invoiceId}.pdf' target='_blank'>View PDF</a></p>";
    } else {
        echo "<p style='color: red;'>✗ PDF generation failed!</p>";
        exit;
    }
    
    // Test email sending with PDF
    echo "<h3>Testing Email Send with PDF Attachment...</h3>";
    
    $emailService = new EmailService();
    $result = $emailService->sendInvoiceEmail($invoiceId);
    
    if ($result['success']) {
        echo "<p style='color: green;'>✓ Email sent successfully!</p>";
        if (isset($result['subject'])) {
            echo "<p>Subject: " . htmlspecialchars($result['subject']) . "</p>";
        }
        if (isset($result['message'])) {
            echo "<p>Message: " . htmlspecialchars($result['message']) . "</p>";
        }
        
        echo "<h4>What to check:</h4>";
        echo "<ol>";
        echo "<li>Open Mailhog at <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></li>";
        echo "<li>Look for an email to: {$user['email']}</li>";
        echo "<li>Check that the PDF is attached</li>";
        echo "<li>Download and open the PDF to verify it's correct</li>";
        echo "</ol>";
        
        // Check email logs
        $stmt = $db->prepare("
            SELECT * FROM email_logs 
            WHERE invoice_id = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$invoiceId]);
        $log = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($log) {
            echo "<h4>Email Log Entry:</h4>";
            echo "<ul>";
            echo "<li>Status: {$log['status']}</li>";
            echo "<li>Sent at: {$log['sent_at']}</li>";
            echo "<li>Recipient: {$log['recipient_email']}</li>";
            if ($log['error_message']) {
                echo "<li>Error: {$log['error_message']}</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Email failed: " . htmlspecialchars($result['message']) . "</p>";
        
        // Check error logs
        $errorLog = @file_get_contents('/mnt/c/wamp64/logs/php_error.log');
        if ($errorLog) {
            $lines = explode("\n", $errorLog);
            $recent = array_slice($lines, -20);
            echo "<h4>Recent PHP Errors:</h4>";
            echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto;'>";
            foreach ($recent as $line) {
                if (strpos($line, 'EmailService') !== false || strpos($line, 'PdfService') !== false) {
                    echo htmlspecialchars($line) . "\n";
                }
            }
            echo "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/fit/public/invoices'>Back to Invoices</a> | ";
echo "<a href='/fit/public/invoices/create?type=rental&user_id=1'>Create New DIV Invoice</a></p>";
echo "</body></html>";