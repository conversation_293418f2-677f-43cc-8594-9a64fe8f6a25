<?php
// Delete Invoice 263 (FAC-LOC-2025-0196)
// This will permanently remove the invoice and all its lines

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Delete Invoice 263</h1>";
    
    // First, get invoice details for confirmation
    $stmt = $db->prepare("
        SELECT i.*, u.first_name, u.last_name 
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        WHERE i.id = 263
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>❌ Invoice 263 not found!</p>";
        echo "<p>The invoice may have already been deleted or doesn't exist in the database.</p>";
        exit;
    }
    
    echo "<h2>Invoice Details</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>ID</td><td>{$invoice['id']}</td></tr>";
    echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
    echo "<tr><td>Client</td><td>{$invoice['first_name']} {$invoice['last_name']}</td></tr>";
    echo "<tr><td>Total</td><td>€ " . number_format($invoice['total'], 2) . "</td></tr>";
    echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
    echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
    echo "<tr><td>Created</td><td>{$invoice['created_at']}</td></tr>";
    echo "</table>";
    
    // Get invoice lines count
    $stmt = $db->prepare("SELECT COUNT(*) FROM invoice_lines WHERE invoice_id = 263");
    $stmt->execute();
    $linesCount = $stmt->fetchColumn();
    
    echo "<p><strong>Invoice Lines:</strong> $linesCount line(s)</p>";
    
    // Show invoice lines
    if ($linesCount > 0) {
        $stmt = $db->prepare("
            SELECT * FROM invoice_lines 
            WHERE invoice_id = 263 
            ORDER BY sort_order ASC, id ASC
        ");
        $stmt->execute();
        $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Invoice Lines to be Deleted</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Line Type</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>Line Total</th></tr>";
        
        foreach ($lines as $line) {
            echo "<tr>";
            echo "<td>{$line['line_type']}</td>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>€ " . number_format($line['unit_price'], 2) . "</td>";
            echo "<td>€ " . number_format($line['line_total'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check for related records
    $relatedTables = [
        'invoice_payments' => 'invoice_id',
        'invoice_attachments' => 'invoice_id',
        'invoice_history' => 'invoice_id'
    ];
    
    $hasRelated = false;
    foreach ($relatedTables as $table => $column) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) FROM $table WHERE $column = 263");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                echo "<p><strong>Related records in $table:</strong> $count</p>";
                $hasRelated = true;
            }
        } catch (PDOException $e) {
            // Table might not exist, ignore
        }
    }
    
    if ($hasRelated) {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ Warning</h3>";
        echo "<p>This invoice has related records that will also be deleted.</p>";
        echo "</div>";
    }
    
    // Deletion confirmation
    if (!isset($_GET['confirm'])) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ Deletion Confirmation Required</h3>";
        echo "<p>This action will permanently delete:</p>";
        echo "<ul>";
        echo "<li>Invoice 263 (FAC-LOC-2025-0196)</li>";
        echo "<li>All $linesCount invoice lines</li>";
        if ($hasRelated) {
            echo "<li>All related records (payments, attachments, history)</li>";
        }
        echo "</ul>";
        echo "<p><strong>This action cannot be undone!</strong></p>";
        echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⚠️ DELETE INVOICE 263</a></p>";
        echo "<p><a href='#' onclick='history.back()' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Cancel</a></p>";
        echo "</div>";
    } else {
        // Perform deletion
        try {
            $db->beginTransaction();
            
            // Delete related records first
            $deletedRecords = [];
            
            // Delete invoice lines
            $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = 263");
            $stmt->execute();
            $deletedRecords[] = "Invoice lines: " . $stmt->rowCount();
            
            // Delete related records
            foreach ($relatedTables as $table => $column) {
                try {
                    $stmt = $db->prepare("DELETE FROM $table WHERE $column = 263");
                    $stmt->execute();
                    $deleted = $stmt->rowCount();
                    if ($deleted > 0) {
                        $deletedRecords[] = "$table: $deleted";
                    }
                } catch (PDOException $e) {
                    // Table might not exist, ignore
                }
            }
            
            // Delete the invoice itself
            $stmt = $db->prepare("DELETE FROM invoices WHERE id = 263");
            $stmt->execute();
            $deletedRecords[] = "Invoice record: " . $stmt->rowCount();
            
            $db->commit();
            
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>✅ Invoice 263 Deleted Successfully</h3>";
            echo "<p>The following records were deleted:</p>";
            echo "<ul>";
            foreach ($deletedRecords as $record) {
                echo "<li>$record</li>";
            }
            echo "</ul>";
            echo "<p><strong>Invoice 263 (FAC-LOC-2025-0196) has been permanently removed from the system.</strong></p>";
            echo "</div>";
            
            // Verify deletion
            $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE id = 263");
            $stmt->execute();
            $exists = $stmt->fetchColumn();
            
            if ($exists == 0) {
                echo "<p style='color: green;'>✅ Verification: Invoice 263 no longer exists in the database</p>";
            } else {
                echo "<p style='color: red;'>❌ Warning: Invoice 263 still exists in the database</p>";
            }
            
        } catch (Exception $e) {
            $db->rollBack();
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>❌ Deletion Failed</h3>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #666; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>