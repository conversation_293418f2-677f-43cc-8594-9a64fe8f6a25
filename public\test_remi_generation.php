<?php
require_once '../vendor/autoload.php';

// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing Rémi's Invoice Generation Data</h2>";
    
    // Simulate what UnifiedInvoiceGenerator does
    $userId = 18; // <PERSON><PERSON><PERSON>'s user ID
    $month = 7; // July (current)
    $year = 2025;
    
    // For retrocession, we use previous month
    $dataMonth = 6; // June
    $dataYear = 2025;
    
    echo "<p>User ID: $userId</p>";
    echo "<p>Generating in: $month/$year</p>";
    echo "<p>For data from: $dataMonth/$dataYear</p>";
    
    // Check year column
    $yearCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYearColumn = $yearCheck->rowCount() > 0;
    
    // Get monthly amounts - exact same query as UnifiedInvoiceGenerator
    if ($hasYearColumn) {
        $stmt = $db->prepare("
            SELECT cns_amount, patient_amount 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month AND year = :year
        ");
        $stmt->execute(['user_id' => $userId, 'month' => $dataMonth, 'year' => $dataYear]);
    } else {
        $stmt = $db->prepare("
            SELECT cns_amount, patient_amount 
            FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month
        ");
        $stmt->execute(['user_id' => $userId, 'month' => $dataMonth]);
    }
    $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Query Result:</h3>";
    if ($amounts) {
        echo "<p style='color: green;'>✓ Found data:</p>";
        echo "<ul>";
        echo "<li>CNS Amount: " . number_format($amounts['cns_amount'], 2) . " €</li>";
        echo "<li>Patient Amount: " . number_format($amounts['patient_amount'], 2) . " €</li>";
        echo "</ul>";
        
        // Check the condition that causes null return
        if (!$amounts || ($amounts['cns_amount'] == 0 && $amounts['patient_amount'] == 0)) {
            echo "<p style='color: red;'>Would return NULL because both amounts are 0</p>";
        } else {
            echo "<p style='color: green;'>Would proceed with invoice generation</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ No data found - query returned no results</p>";
    }
    
    // Debug: Show exact SQL being executed
    echo "<h3>Debug - Exact Query:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px;'>";
    if ($hasYearColumn) {
        echo "SELECT cns_amount, patient_amount 
FROM user_monthly_retrocession_amounts 
WHERE user_id = $userId AND month = $dataMonth AND year = $dataYear";
    } else {
        echo "SELECT cns_amount, patient_amount 
FROM user_monthly_retrocession_amounts 
WHERE user_id = $userId AND month = $dataMonth";
    }
    echo "</pre>";
    
    // Let's also check if this is a bulk generation issue
    echo "<h3>Checking Bulk Generation Context:</h3>";
    
    // In bulk generation, the system might be checking for non-invoiced entries
    $stmt = $db->prepare("
        SELECT rde.*, c.name as client_name
        FROM retrocession_data_entry rde
        JOIN clients c ON c.id = rde.practitioner_id
        WHERE c.user_id = :user_id
        AND rde.period_month = :month
        AND rde.period_year = :year
        AND (rde.invoice_id IS NULL OR rde.invoice_id = 0)
    ");
    $stmt->execute(['user_id' => $userId, 'month' => $dataMonth, 'year' => $dataYear]);
    $retrocessionEntry = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($retrocessionEntry) {
        echo "<p>Found retrocession_data_entry that could be invoiced</p>";
    } else {
        echo "<p style='color: orange;'>No retrocession_data_entry found for bulk generation</p>";
        echo "<p>This might be why bulk generation shows 'No data'</p>";
    }
    
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Possible Solutions:</h3>";
    echo "<ol>";
    echo "<li>Generate the invoice from Rémi's user profile page (individual generation)</li>";
    echo "<li>Create the retrocession_data_entry manually first</li>";
    echo "<li>Check if the bulk generation is using different criteria</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}