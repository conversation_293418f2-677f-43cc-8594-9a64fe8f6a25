<?php
/**
 * Test Email Without PDF - Invoice 279
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Services\EmailService;
use App\Models\Invoice;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Email Without PDF</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Email Without PDF Attachment</h1>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $invoiceId = 279;
            $recipientEmail = '<EMAIL>';
            
            // Get invoice data
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Initialize EmailService and use reflection to call private methods
            $emailService = new EmailService();
            $reflection = new ReflectionClass($emailService);
            
            // Select template
            $selectMethod = $reflection->getMethod('selectInvoiceTemplate');
            $selectMethod->setAccessible(true);
            $template = $selectMethod->invoke($emailService, $invoiceData);
            
            if (!$template) {
                throw new Exception('No email template found');
            }
            
            echo '<div class="info">';
            echo '<p>Template selected: ' . htmlspecialchars($template['name']) . '</p>';
            echo '</div>';
            
            // Render template
            $renderMethod = $reflection->getMethod('renderTemplate');
            $renderMethod->setAccessible(true);
            $emailContent = $renderMethod->invoke($emailService, $template, $invoiceData);
            
            echo '<div class="info">';
            echo '<h3>Email Content:</h3>';
            echo '<p><strong>Subject:</strong> ' . htmlspecialchars($emailContent['subject']) . '</p>';
            echo '<p><strong>Body Preview:</strong></p>';
            echo '<pre>' . htmlspecialchars(substr($emailContent['body_text'], 0, 300)) . '...</pre>';
            echo '</div>';
            
            // Send email without PDF
            $sendMethod = $reflection->getMethod('send');
            $sendMethod->setAccessible(true);
            
            $result = $sendMethod->invoke($emailService, [
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [] // No PDF attachment
            ]);
            
            echo '<div class="info">';
            echo '<h3>Send Result:</h3>';
            echo '<pre>' . print_r($result, true) . '</pre>';
            echo '</div>';
            
            if ($result['success']) {
                echo '<div class="success">';
                echo '<h2>✅ Email sent successfully (without PDF)!</h2>';
                echo '<p>Check Mailhog at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a></p>';
                echo '</div>';
                
                // Log the email
                $logMethod = $reflection->getMethod('logEmail');
                $logMethod->setAccessible(true);
                $logMethod->invoke($emailService, $invoiceId, $template['id'] ?? null, $recipientEmail, $result);
                
                echo '<div class="info">Email has been logged in the database.</div>';
            } else {
                echo '<div class="error">❌ Failed to send email: ' . htmlspecialchars($result['message']) . '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">';
            echo '<h3>Error:</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
            echo '</div>';
        }
    } else {
        ?>
        <div class="info">
            <p>This will send the invoice email WITHOUT the PDF attachment to test if the email system works.</p>
            <p>The PDF generation is failing due to a Config::get() method issue.</p>
            
            <form method="POST">
                <button type="submit">Send Email Without PDF</button>
            </form>
        </div>
        <?php
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Email Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>