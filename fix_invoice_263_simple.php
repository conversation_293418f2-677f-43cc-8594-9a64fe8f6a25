<?php
// Simple fix for Invoice 263 - Add missing patient retrocession line
// Run this script through a web browser: http://localhost/fit/fix_invoice_263_simple.php

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Fix Invoice 263 - Simple Version</h1>";
    
    // Get current invoice state
    $stmt = $db->prepare("SELECT id, invoice_number, status, total FROM invoices WHERE id = 263");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        die("<p style='color: red;'>Invoice 263 not found!</p>");
    }
    
    echo "<h2>Current State</h2>";
    echo "<p>Invoice Number: {$invoice['invoice_number']}</p>";
    echo "<p>Status: {$invoice['status']}</p>";
    echo "<p>Current Total: <strong>{$invoice['total']} €</strong></p>";
    echo "<p>Target Total: <strong>930.00 €</strong></p>";
    echo "<p>Missing Amount: <strong>" . number_format(930.00 - $invoice['total'], 2) . " €</strong></p>";
    
    // Check if already fixed
    if ($invoice['total'] == 930.00) {
        echo "<p style='color: green;'>✅ Invoice 263 is already correct at 930.00 €</p>";
        exit;
    }
    
    // Auto-fix: Add patient retrocession line
    if (!isset($_GET['action']) || $_GET['action'] !== 'fix') {
        echo "<p><a href='?action=fix' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Click here to fix Invoice 263</a></p>";
        exit;
    }
    
    // Perform the fix
    $missingAmount = 930.00 - $invoice['total'];
    
    $db->beginTransaction();
    
    try {
        // Add patient retrocession line
        $stmt = $db->prepare("
            INSERT INTO invoice_lines (
                invoice_id, line_type, description, quantity, unit_price, 
                vat_rate, line_total, sort_order
            ) VALUES (?, 'patient_part', 'Part Patient', 1, ?, 0, ?, 2)
        ");
        $stmt->execute([263, $missingAmount, $missingAmount]);
        
        // Update invoice total
        $stmt = $db->prepare("UPDATE invoices SET total = 930.00, updated_at = NOW() WHERE id = 263");
        $stmt->execute();
        
        $db->commit();
        
        echo "<p style='color: green;'>✅ Invoice 263 has been fixed!</p>";
        echo "<p>Added patient retrocession line: " . number_format($missingAmount, 2) . " €</p>";
        echo "<p>New total: 930.00 €</p>";
        echo "<p><a href='http://localhost/fit/public/invoices/263' target='_blank'>View updated invoice</a></p>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}
?>