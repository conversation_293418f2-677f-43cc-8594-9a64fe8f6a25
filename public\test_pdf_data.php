<?php
// Simple test to see what data the PDF is getting
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$invoiceId = $_GET['id'] ?? 238;
$db = Flight::db();

// Same query as PDF
$stmt = $db->prepare("
    SELECT id, invoice_id, description, quantity, unit_price, 
           vat_rate, sort_order
    FROM invoice_lines 
    WHERE invoice_id = :id
    ORDER BY sort_order ASC, id ASC
");
$stmt->execute(['id' => $invoiceId]);
$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Invoice Lines for Invoice ID: $invoiceId</h3>";
echo "<pre>";
echo "Total lines found: " . count($items) . "\n\n";

foreach ($items as $i => $item) {
    echo "Line " . ($i + 1) . ":\n";
    echo "  ID: {$item['id']}\n";
    echo "  Description: {$item['description']}\n";
    echo "  Quantity: {$item['quantity']} x {$item['unit_price']}€\n";
    echo "  VAT: {$item['vat_rate']}%\n";
    echo "  Sort Order: " . ($item['sort_order'] ?? 'NULL') . "\n\n";
}

// Check visible columns config
$stmt = $db->prepare("SELECT document_type_id FROM invoices WHERE id = ?");
$stmt->execute([$invoiceId]);
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

$documentTypeId = $invoice['document_type_id'] ?? null;
$visibleColumns = [];

if ($documentTypeId) {
    $visibleColumns = \App\Models\DocumentTypeColumnConfig::getVisibleInvoiceColumns($documentTypeId);
}

if (empty($visibleColumns)) {
    $visibleColumns = [
        'description' => ['visible' => true, 'order' => 1],
        'quantity' => ['visible' => true, 'order' => 2],
        'unit_price' => ['visible' => true, 'order' => 3],
        'vat_rate' => ['visible' => true, 'order' => 4],
        'total' => ['visible' => true, 'order' => 5]
    ];
}

echo "\nVisible columns:\n";
foreach ($visibleColumns as $col => $config) {
    echo "- $col (order: {$config['order']})\n";
}
echo "</pre>";