<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Final Fix for RET Types</h2>";
    
    // First, let's see exactly what we have
    echo "<h3>Before changes:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret2', 'ret3', 'ret25', 'ret30') OR prefix LIKE '%RET%'
        ORDER BY id
    ");
    $before = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($before);
    echo "</pre>";
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Update ret2 to ret25
        $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret25' WHERE id = 29 AND code = 'ret2'");
        $result = $stmt->execute();
        echo "<p>Update ret2 to ret25: " . ($stmt->rowCount() > 0 ? "✓ Success" : "No rows updated") . "</p>";
        
        // Update ret3 to ret30
        $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret30' WHERE id = 30 AND code = 'ret3'");
        $result = $stmt->execute();
        echo "<p>Update ret3 to ret30: " . ($stmt->rowCount() > 0 ? "✓ Success" : "No rows updated") . "</p>";
        
        // Commit
        $db->commit();
        echo "<p style='color: green;'>✓ Transaction committed</p>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Transaction rolled back</p>";
    }
    
    // Show after state
    echo "<h3>After changes:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret2', 'ret3', 'ret25', 'ret30') OR prefix LIKE '%RET%'
        ORDER BY id
    ");
    $after = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($after as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $rowStyle = '';
        if ($row['code'] == 'ret25' || $row['prefix'] == 'FAC-RET25') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['code'] == 'ret30' || $row['prefix'] == 'FAC-RET30') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if ret25 exists
    $stmt = $db->prepare("SELECT id, code, prefix FROM config_invoice_types WHERE code = 'ret25' OR (id = 29 AND prefix = 'FAC-RET25')");
    $stmt->execute();
    $ret25 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($ret25) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ RET25 Found!</h3>";
        echo "<p>ID: {$ret25['id']}, Code: {$ret25['code']}, Prefix: {$ret25['prefix']}</p>";
        echo "<p><a href='/fit/public/fix_frank_invoice_v2.php' style='color: #155724; font-weight: bold;'>→ Now fix Frank's invoice</a></p>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>Note: ret25 code not found, but we can use ID 29 with prefix FAC-RET25</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}