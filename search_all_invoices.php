<?php
// Search for all invoices to find the one that needs fixing
// This will help identify the correct invoice that should be 930.00€

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Search All Invoices</h1>";
    
    // Search for various patterns
    $searchPatterns = [
        'FAC-LOC-2025-0196',
        'FAC-LOC-2025%',
        'FAC-2025-0196',
        '%0196%',
        'LOC-2025%',
        'FAC%2025%'
    ];
    
    foreach ($searchPatterns as $pattern) {
        echo "<h2>Search Pattern: '$pattern'</h2>";
        
        $stmt = $db->prepare("
            SELECT i.id, i.invoice_number, i.total, i.status, i.created_at,
                   u.first_name, u.last_name, u.username
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.invoice_number LIKE ?
            ORDER BY i.id DESC
            LIMIT 10
        ");
        $stmt->execute([$pattern]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($results)) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Status</th><th>Created</th><th>Action</th></tr>";
            
            foreach ($results as $invoice) {
                $highlight = (abs($invoice['total'] - 930.00) < 200) ? 'background-color: #ffffcc;' : '';
                echo "<tr style='$highlight'>";
                echo "<td>{$invoice['id']}</td>";
                echo "<td>{$invoice['invoice_number']}</td>";
                echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
                echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
                echo "<td>{$invoice['status']}</td>";
                echo "<td>{$invoice['created_at']}</td>";
                echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}'>Fix</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No results found for '$pattern'</p>";
        }
    }
    
    // Search for invoices around 930.00€
    echo "<h2>Invoices Around 930.00€ (±50€)</h2>";
    $stmt = $db->prepare("
        SELECT i.id, i.invoice_number, i.total, i.status, i.created_at,
               u.first_name, u.last_name, u.username
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        WHERE i.total BETWEEN 880 AND 980
        ORDER BY i.id DESC
        LIMIT 15
    ");
    $stmt->execute();
    $around930 = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($around930)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Diff from 930€</th><th>Status</th><th>Action</th></tr>";
        
        foreach ($around930 as $invoice) {
            $difference = $invoice['total'] - 930.00;
            $diffColor = ($difference > 0) ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td>{$invoice['invoice_number']}</td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td style='color: $diffColor;'>" . number_format($difference, 2) . "€</td>";
            echo "<td>{$invoice['status']}</td>";
            echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}'>Fix</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Search for invoices around 795€ (the mentioned 794.97€)
    echo "<h2>Invoices Around 795€ (±20€)</h2>";
    $stmt = $db->prepare("
        SELECT i.id, i.invoice_number, i.total, i.status, i.created_at,
               u.first_name, u.last_name, u.username
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        WHERE i.total BETWEEN 775 AND 815
        ORDER BY i.id DESC
        LIMIT 15
    ");
    $stmt->execute();
    $around795 = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($around795)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Missing to 930€</th><th>Status</th><th>Action</th></tr>";
        
        foreach ($around795 as $invoice) {
            $missing = 930.00 - $invoice['total'];
            
            echo "<tr style='background-color: #ffeeee;'>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td>{$invoice['invoice_number']}</td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td style='color: red;'>€ " . number_format($missing, 2) . "</td>";
            echo "<td>{$invoice['status']}</td>";
            echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}&target=930'>Fix to 930€</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Search for recent invoices by date
    echo "<h2>Most Recent Invoices</h2>";
    $stmt = $db->prepare("
        SELECT i.id, i.invoice_number, i.total, i.status, i.created_at,
               u.first_name, u.last_name, u.username
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        ORDER BY i.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($recent)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Status</th><th>Created</th><th>Action</th></tr>";
        
        foreach ($recent as $invoice) {
            $highlight = (abs($invoice['total'] - 930.00) < 200) ? 'background-color: #ffffcc;' : '';
            echo "<tr style='$highlight'>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td>{$invoice['invoice_number']}</td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['status']}</td>";
            echo "<td>{$invoice['created_at']}</td>";
            echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}'>Fix</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Search for invoices with users who have courses configured
    echo "<h2>Invoices with Users Having Courses (Individuel/Collectif)</h2>";
    $stmt = $db->prepare("
        SELECT i.id, i.invoice_number, i.total, i.status, i.created_at,
               u.first_name, u.last_name, u.username,
               COUNT(uc.id) as course_count,
               GROUP_CONCAT(CONCAT(uc.course_name, '(€', uc.hourly_rate, ')') SEPARATOR ', ') as courses
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        WHERE i.status IN ('sent', 'draft')
        GROUP BY i.id
        HAVING course_count > 0
        ORDER BY i.created_at DESC
        LIMIT 15
    ");
    $stmt->execute();
    $withCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($withCourses)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Courses</th><th>Status</th><th>Action</th></tr>";
        
        foreach ($withCourses as $invoice) {
            $highlight = (abs($invoice['total'] - 930.00) < 200) ? 'background-color: #ffffcc;' : '';
            echo "<tr style='$highlight'>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td>{$invoice['invoice_number']}</td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['courses']}</td>";
            echo "<td>{$invoice['status']}</td>";
            echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}&target=930'>Fix to 930€</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Database statistics
    echo "<h2>Database Statistics</h2>";
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            MAX(id) as max_id,
            MIN(id) as min_id,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count
        FROM invoices
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Total Invoices</td><td>{$stats['total_invoices']}</td></tr>";
    echo "<tr><td>Max ID</td><td>{$stats['max_id']}</td></tr>";
    echo "<tr><td>Min ID</td><td>{$stats['min_id']}</td></tr>";
    echo "<tr><td>Sent Invoices</td><td>{$stats['sent_count']}</td></tr>";
    echo "<tr><td>Draft Invoices</td><td>{$stats['draft_count']}</td></tr>";
    echo "</table>";
    
    echo "<h2>Manual Search</h2>";
    echo "<form method='get'>";
    echo "<p>Search by Invoice Number: <input type='text' name='search' placeholder='Enter invoice number or pattern'> ";
    echo "<input type='submit' value='Search'></p>";
    echo "</form>";
    
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $searchTerm = '%' . $_GET['search'] . '%';
        $stmt = $db->prepare("
            SELECT i.id, i.invoice_number, i.total, i.status, i.created_at,
                   u.first_name, u.last_name, u.username
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.invoice_number LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?
            ORDER BY i.id DESC
            LIMIT 20
        ");
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        $searchResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($searchResults)) {
            echo "<h3>Search Results for '{$_GET['search']}'</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Status</th><th>Action</th></tr>";
            
            foreach ($searchResults as $invoice) {
                echo "<tr>";
                echo "<td>{$invoice['id']}</td>";
                echo "<td>{$invoice['invoice_number']}</td>";
                echo "<td>{$invoice['first_name']} {$invoice['last_name']}</td>";
                echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
                echo "<td>{$invoice['status']}</td>";
                echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}&target=930'>Fix to 930€</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No results found for '{$_GET['search']}'</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
form { margin: 10px 0; }
input[type="text"] { padding: 5px; margin: 2px; }
</style>