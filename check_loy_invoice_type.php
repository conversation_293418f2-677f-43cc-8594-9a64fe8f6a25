<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Type Review</h2>";
    
    // Check invoice types table
    echo "<h3>1. LOY Type Configuration in invoice_types table:</h3>";
    $sql = "SELECT * FROM invoice_types WHERE prefix = 'LOY' OR name LIKE '%LOY%' OR name LIKE '%loyalty%'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($types)) {
        echo "<p style='color: orange;'>⚠️ No LOY invoice type found in invoice_types table</p>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Prefix</th><th>Color</th><th>VAT Rate</th><th>Active</th><th>Created</th>";
        echo "</tr>";
        foreach ($types as $type) {
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td><strong>{$type['prefix']}</strong></td>";
            echo "<td style='background-color: {$type['color']}; color: white;'>{$type['color']}</td>";
            echo "<td>{$type['default_vat_rate']}%</td>";
            echo "<td>" . ($type['is_active'] ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td>{$type['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check LOY invoices
    echo "<h3>2. Recent LOY Invoices:</h3>";
    $sql = "SELECT i.*, it.name as type_name, it.prefix, c.name as client_name, p.first_name, p.last_name
            FROM invoices i
            LEFT JOIN invoice_types it ON i.invoice_type_id = it.id
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN patients p ON i.patient_id = p.id
            WHERE i.invoice_number LIKE '%LOY%' OR it.prefix = 'LOY'
            ORDER BY i.id DESC
            LIMIT 10";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($invoices)) {
        echo "<p style='color: orange;'>⚠️ No LOY invoices found</p>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Number</th><th>Type</th><th>Client/Patient</th><th>Total</th><th>Status</th><th>Created</th>";
        echo "</tr>";
        foreach ($invoices as $invoice) {
            $clientName = $invoice['patient_id'] ? 
                $invoice['first_name'] . ' ' . $invoice['last_name'] : 
                $invoice['client_name'];
            
            echo "<tr>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td><a href='/fit/public/invoices/{$invoice['id']}'>{$invoice['invoice_number']}</a></td>";
            echo "<td>{$invoice['type_name']} ({$invoice['prefix']})</td>";
            echo "<td>{$clientName}</td>";
            echo "<td>€" . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['status']}</td>";
            echo "<td>{$invoice['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check invoice number sequences
    echo "<h3>3. LOY Invoice Number Sequence:</h3>";
    $sql = "SELECT * FROM invoice_number_sequences WHERE prefix = 'LOY' OR invoice_type LIKE '%LOY%'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sequences)) {
        echo "<p style='color: orange;'>⚠️ No LOY sequence found in invoice_number_sequences table</p>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Prefix</th><th>Invoice Type</th><th>Current Number</th><th>Year</th><th>Pattern</th><th>Last Updated</th>";
        echo "</tr>";
        foreach ($sequences as $seq) {
            echo "<tr>";
            echo "<td>{$seq['id']}</td>";
            echo "<td><strong>{$seq['prefix']}</strong></td>";
            echo "<td>{$seq['invoice_type']}</td>";
            echo "<td>{$seq['current_number']}</td>";
            echo "<td>{$seq['year']}</td>";
            echo "<td>{$seq['number_pattern']}</td>";
            echo "<td>{$seq['updated_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check if LOY uses TTC
    echo "<h3>4. LOY Invoice TTC Configuration:</h3>";
    $helperPath = __DIR__ . '/app/Helpers/InvoiceTTCHelper.php';
    if (file_exists($helperPath)) {
        $content = file_get_contents($helperPath);
        if (strpos($content, "'LOY'") !== false) {
            echo "<p style='color: green;'>✅ LOY is configured to use TTC (Tax Included) calculation</p>";
            
            // Show the relevant code
            preg_match('/\$ttcTypes\s*=\s*\[(.*?)\]/s', $content, $matches);
            if ($matches) {
                echo "<pre style='background-color: #f0f0f0; padding: 10px;'>";
                echo "TTC Types: " . $matches[1];
                echo "</pre>";
            }
        } else {
            echo "<p style='color: red;'>❌ LOY is NOT configured for TTC calculation</p>";
        }
    }
    
    // Check sample invoice items
    echo "<h3>5. Sample LOY Invoice Items:</h3>";
    $sql = "SELECT ii.*, i.invoice_number 
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            WHERE i.invoice_number LIKE '%LOY%'
            ORDER BY ii.id DESC
            LIMIT 5";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($items)) {
        echo "<p style='color: orange;'>⚠️ No LOY invoice items found</p>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>Invoice</th><th>Description</th><th>Qty</th><th>Unit Price</th><th>VAT Rate</th><th>Total</th>";
        echo "</tr>";
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>{$item['invoice_number']}</td>";
            echo "<td>{$item['description']}</td>";
            echo "<td>{$item['quantity']}</td>";
            echo "<td>€" . number_format($item['unit_price'], 2) . "</td>";
            echo "<td>{$item['vat_rate']}%</td>";
            echo "<td>€" . number_format($item['total_amount'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Summary
    echo "<h3>6. LOY Invoice Type Summary:</h3>";
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>LOY (Loyalty) Invoice Type:</strong></p>";
    echo "<ul>";
    echo "<li>Used for loyalty/bonus invoices</li>";
    echo "<li>Should use TTC (Tax Included) pricing</li>";
    echo "<li>VAT is extracted from total, not added</li>";
    echo "<li>Typically used for special customer rewards or loyalty programs</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>