<?php
// Debug PDF output issue
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

$invoiceId = 238; // or use $_GET['id']
$db = Flight::db();

// Get invoice
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
$stmt->execute([$invoiceId]);
$invoice = $stmt->fetch(\PDO::FETCH_ASSOC);

echo "=== DEBUGGING PDF OUTPUT FOR INVOICE {$invoice['invoice_number']} ===\n\n";

// Get visible columns configuration
$documentTypeId = $invoice['document_type_id'] ?? null;
$visibleColumns = [];

if ($documentTypeId) {
    $visibleColumns = \App\Models\DocumentTypeColumnConfig::getVisibleInvoiceColumns($documentTypeId);
}

// Default columns if no configuration
if (empty($visibleColumns)) {
    $visibleColumns = [
        'description' => ['visible' => true, 'order' => 1],
        'quantity' => ['visible' => true, 'order' => 2],
        'unit_price' => ['visible' => true, 'order' => 3],
        'vat_rate' => ['visible' => true, 'order' => 4],
        'total' => ['visible' => true, 'order' => 5]
    ];
}

// Sort by order
uasort($visibleColumns, function($a, $b) {
    return ($a['order'] ?? 999) - ($b['order'] ?? 999);
});

echo "Visible columns in order:\n";
$columnOrder = [];
foreach ($visibleColumns as $col => $config) {
    $columnOrder[] = $col;
    echo "- $col (order: {$config['order']})\n";
}

// Get invoice lines
$stmt = $db->prepare("
    SELECT * FROM invoice_lines 
    WHERE invoice_id = ?
    ORDER BY sort_order ASC, id ASC
");
$stmt->execute([$invoiceId]);
$lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo "\nProcessing invoice lines:\n";
foreach ($lines as $i => $item) {
    echo "\nLine " . ($i + 1) . ": {$item['description']}\n";
    echo "Values in column order:\n";
    
    $total = $item['quantity'] * $item['unit_price'];
    
    foreach ($columnOrder as $columnId) {
        $value = '';
        
        switch ($columnId) {
            case 'description':
                $value = $item['description'];
                break;
            case 'quantity':
                $value = number_format($item['quantity'], 2, ',', '.');
                break;
            case 'unit_price':
                $value = number_format($item['unit_price'], 2, ',', '.') . '€';
                break;
            case 'vat_rate':
                $value = number_format($item['vat_rate'], 2, ',', '.') . '%';
                break;
            case 'total':
                // Calculate total with VAT included (like in the web view)
                $totalWithVat = $total * (1 + $item['vat_rate'] / 100);
                $value = number_format($totalWithVat, 2, ',', '.') . '€';
                break;
        }
        
        echo "  $columnId: $value\n";
    }
    
    echo "Output row: " . implode(' | ', array_map(function($col) use ($item, $total) {
        switch ($col) {
            case 'description':
                return $item['description'];
            case 'quantity':
                return number_format($item['quantity'], 2, ',', '.');
            case 'unit_price':
                return number_format($item['unit_price'], 2, ',', '.') . '€';
            case 'vat_rate':
                return number_format($item['vat_rate'], 2, ',', '.') . '%';
            case 'total':
                $totalWithVat = $total * (1 + $item['vat_rate'] / 100);
                return number_format($totalWithVat, 2, ',', '.') . '€';
        }
    }, $columnOrder)) . "\n";
}