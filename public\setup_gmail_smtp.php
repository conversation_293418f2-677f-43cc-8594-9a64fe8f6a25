<?php
/**
 * Gmail SMTP Configuration Helper
 * This script helps configure the application to use Gmail SMTP
 */

require_once __DIR__ . '/../vendor/autoload.php';

echo "<h1>Gmail SMTP Configuration</h1>";
echo "<pre>";

// Check if .env file exists
$envPath = dirname(__DIR__) . '/.env';
if (!file_exists($envPath)) {
    die("Error: .env file not found at $envPath");
}

// Read current .env content
$envContent = file_get_contents($envPath);

if (isset($_POST['update'])) {
    // Update .env file with Gmail settings
    $gmail = $_POST['gmail'];
    $password = $_POST['password'];
    
    if (empty($gmail) || empty($password)) {
        echo "Error: Gmail address and app password are required!\n";
    } else {
        // Update MAIL_* settings
        $envContent = preg_replace('/^MAIL_MAILER=.*/m', 'MAIL_MAILER=smtp', $envContent);
        $envContent = preg_replace('/^MAIL_HOST=.*/m', 'MAIL_HOST=smtp.gmail.com', $envContent);
        $envContent = preg_replace('/^MAIL_PORT=.*/m', 'MAIL_PORT=587', $envContent);
        $envContent = preg_replace('/^MAIL_USERNAME=.*/m', 'MAIL_USERNAME=' . $gmail, $envContent);
        $envContent = preg_replace('/^MAIL_PASSWORD=.*/m', 'MAIL_PASSWORD=' . $password, $envContent);
        $envContent = preg_replace('/^MAIL_ENCRYPTION=.*/m', 'MAIL_ENCRYPTION=tls', $envContent);
        $envContent = preg_replace('/^MAIL_FROM_ADDRESS=.*/m', 'MAIL_FROM_ADDRESS=' . $gmail, $envContent);
        
        // Write updated content
        file_put_contents($envPath, $envContent);
        
        echo "✓ Gmail SMTP configuration updated successfully!\n\n";
        echo "New configuration:\n";
        echo "MAIL_HOST=smtp.gmail.com\n";
        echo "MAIL_PORT=587\n";
        echo "MAIL_USERNAME=$gmail\n";
        echo "MAIL_PASSWORD=********\n";
        echo "MAIL_ENCRYPTION=tls\n";
        echo "MAIL_FROM_ADDRESS=$gmail\n\n";
        
        echo "You can now test email sending!\n";
        echo '<a href="test_email_diagnostic.php?send=true&email=' . $gmail . '">Send Test Email</a>';
    }
} else {
    ?>
    </pre>
    
    <h2>Configure Gmail SMTP</h2>
    <p><strong>Important:</strong> You need to use an App Password, not your regular Gmail password!</p>
    
    <h3>How to get a Gmail App Password:</h3>
    <ol>
        <li>Go to your Google Account settings: <a href="https://myaccount.google.com/security" target="_blank">https://myaccount.google.com/security</a></li>
        <li>Enable 2-Step Verification if not already enabled</li>
        <li>Go to "2-Step Verification" → "App passwords"</li>
        <li>Select app: "Mail" and device: "Other (Custom name)"</li>
        <li>Enter name: "Fit360 AdminDesk"</li>
        <li>Copy the generated 16-character password</li>
    </ol>
    
    <form method="POST">
        <table>
            <tr>
                <td><label for="gmail">Gmail Address:</label></td>
                <td><input type="email" name="gmail" id="gmail" required placeholder="<EMAIL>" size="30"></td>
            </tr>
            <tr>
                <td><label for="password">App Password:</label></td>
                <td><input type="text" name="password" id="password" required placeholder="16-character app password" size="30"></td>
            </tr>
            <tr>
                <td colspan="2">
                    <button type="submit" name="update" value="true">Update Gmail Configuration</button>
                </td>
            </tr>
        </table>
    </form>
    
    <h3>Alternative: Manual Configuration</h3>
    <p>You can also manually update your .env file with these settings:</p>
    <pre>
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
    </pre>
    <?php
}
?>