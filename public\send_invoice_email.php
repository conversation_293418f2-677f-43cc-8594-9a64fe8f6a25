<?php
/**
 * Send Invoice Email
 * This script allows sending or resending invoice emails
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\EmailService;

$invoiceId = $_GET['id'] ?? '';
$action = $_POST['action'] ?? '';

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Send Invoice Email</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; }
        .success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .warning { color: orange; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-section { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
        input[type="email"] { width: 100%; padding: 8px; margin: 5px 0; box-sizing: border-box; }
        button { padding: 10px 20px; font-size: 16px; cursor: pointer; margin: 5px; border-radius: 4px; border: 1px solid #ccc; }
        .btn-primary { background: #007bff; color: white; border-color: #007bff; }
        .btn-primary:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; color: white; border-color: #6c757d; }
        .btn-danger { background: #dc3545; color: white; border-color: #dc3545; }
        .preview { background: white; border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Send Invoice Email</h1>
    
    <?php
    if (!$invoiceId) {
        echo '<div class="error">No invoice ID provided!</div>';
        echo '<p><a href="/fit/public/check_invoice_email_status.php">Go to Email Status Check</a></p>';
        exit;
    }
    
    try {
        $db = Flight::db();
        
        // Load invoice with related data
        $invoice = Invoice::find($invoiceId);
        
        if (!$invoice) {
            echo '<div class="error">Invoice not found!</div>';
            exit;
        }
        
        // Get additional invoice data
        $stmt = $db->prepare("
            SELECT 
                i.*,
                c.name as client_name,
                c.email as client_email,
                u.first_name,
                u.last_name,
                u.email as user_email,
                u.billing_email as user_billing_email,
                it.name as invoice_type_name
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN users u ON i.user_id = u.id
            LEFT JOIN invoice_types it ON i.invoice_type = it.code
            WHERE i.id = :id
        ");
        $stmt->execute([':id' => $invoiceId]);
        $invoiceData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Display invoice info
        echo '<div class="info">';
        echo '<h2>Invoice Details</h2>';
        echo '<table>';
        echo '<tr><th>Invoice Number</th><td><strong>' . htmlspecialchars($invoice->invoice_number) . '</strong></td></tr>';
        echo '<tr><th>Status</th><td>' . htmlspecialchars($invoice->status) . '</td></tr>';
        echo '<tr><th>Total</th><td>' . number_format($invoice->total, 2) . ' €</td></tr>';
        echo '<tr><th>Issue Date</th><td>' . htmlspecialchars($invoice->issue_date) . '</td></tr>';
        echo '<tr><th>Due Date</th><td>' . htmlspecialchars($invoice->due_date) . '</td></tr>';
        
        // Determine recipient
        $recipientEmail = '';
        $recipientName = '';
        
        if ($invoiceData['client_id']) {
            $recipientEmail = $invoiceData['client_email'];
            $recipientName = $invoiceData['client_name'];
        } elseif ($invoiceData['user_id']) {
            $recipientEmail = $invoiceData['user_billing_email'] ?: $invoiceData['user_email'];
            $recipientName = $invoiceData['first_name'] . ' ' . $invoiceData['last_name'];
        }
        
        echo '<tr><th>Recipient</th><td>' . htmlspecialchars($recipientName ?: 'Unknown') . '</td></tr>';
        echo '<tr><th>Email</th><td>' . htmlspecialchars($recipientEmail ?: 'No email') . '</td></tr>';
        echo '</table>';
        echo '</div>';
        
        // Check if can be sent
        $canSend = true;
        $messages = [];
        
        if (!$recipientEmail) {
            $canSend = false;
            $messages[] = '<div class="error">❌ No recipient email address available!</div>';
        }
        
        if ($invoice->status === 'cancelled') {
            $canSend = false;
            $messages[] = '<div class="error">❌ Cannot send cancelled invoices!</div>';
        }
        
        if ($invoice->status === 'paid') {
            $messages[] = '<div class="warning">⚠️ This invoice is already marked as paid.</div>';
        }
        
        if ($invoice->status === 'sent' && $invoice->sent_at) {
            $messages[] = '<div class="warning">⚠️ This invoice was already sent on ' . htmlspecialchars($invoice->sent_at) . '</div>';
        }
        
        // Display messages
        foreach ($messages as $msg) {
            echo $msg;
        }
        
        // Handle form submission
        if ($action === 'send' && $canSend) {
            try {
                $emailService = new EmailService();
                
                // Override recipient if provided
                $sendToEmail = $_POST['recipient_email'] ?? $recipientEmail;
                
                echo '<div class="info">';
                echo '<h3>Sending Email...</h3>';
                echo '<p>To: ' . htmlspecialchars($sendToEmail) . '</p>';
                
                // Send the email
                $result = $emailService->sendInvoiceEmail($invoiceId, $sendToEmail);
                
                if ($result) {
                    echo '<div class="success">✅ Email sent successfully!</div>';
                    
                    // Update invoice status if it was draft
                    if ($invoice->status === 'draft') {
                        $invoice->status = 'sent';
                        $invoice->sent_at = date('Y-m-d H:i:s');
                        $invoice->save();
                        echo '<p>Invoice status updated to SENT.</p>';
                    }
                    
                    echo '<p><a href="/fit/public/check_invoice_email_status.php?invoice=' . urlencode($invoice->invoice_number) . '">Check Email Status</a></p>';
                } else {
                    echo '<div class="error">❌ Failed to send email!</div>';
                    echo '<p>Please check the email configuration and try again.</p>';
                }
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<div class="error">';
                echo '<h3>Error sending email:</h3>';
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '</div>';
            }
        }
        
        // Show send form if can send
        if ($canSend && $action !== 'send') {
            ?>
            <div class="form-section">
                <h3>Send Invoice Email</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="send">
                    
                    <label for="recipient_email">Recipient Email:</label>
                    <input type="email" id="recipient_email" name="recipient_email" 
                           value="<?php echo htmlspecialchars($recipientEmail); ?>" required>
                    
                    <div style="margin-top: 15px;">
                        <button type="submit" class="btn-primary">Send Email</button>
                        <button type="button" onclick="window.location.href='/fit/public/invoices/<?php echo $invoiceId; ?>'" class="btn-secondary">View Invoice</button>
                    </div>
                </form>
                
                <div style="margin-top: 20px;">
                    <h4>What will happen:</h4>
                    <ul>
                        <li>The invoice PDF will be generated automatically</li>
                        <li>An email will be sent to the recipient with the PDF attached</li>
                        <li>The email will be logged in the system</li>
                        <?php if ($invoice->status === 'draft'): ?>
                        <li>The invoice status will be updated to SENT</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            <?php
        }
        
        // Show recent email logs for this invoice
        echo '<h3>Email History</h3>';
        $stmt = $db->prepare("
            SELECT * FROM email_logs 
            WHERE invoice_id = :invoice_id
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute([':invoice_id' => $invoiceId]);
        $emailLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($emailLogs) {
            echo '<table>';
            echo '<tr><th>Date</th><th>To</th><th>Status</th><th>Error</th></tr>';
            foreach ($emailLogs as $log) {
                $statusClass = $log['status'] === 'sent' ? 'success' : 'error';
                echo '<tr>';
                echo '<td>' . htmlspecialchars($log['created_at']) . '</td>';
                echo '<td>' . htmlspecialchars($log['recipient_email']) . '</td>';
                echo '<td class="' . $statusClass . '">' . htmlspecialchars($log['status']) . '</td>';
                echo '<td>' . htmlspecialchars($log['error_message'] ?: '-') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<p>No email history for this invoice.</p>';
        }
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<h3>Error:</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/check_invoice_email_status.php?invoice=<?php echo urlencode($invoice->invoice_number ?? ''); ?>">Check Email Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>