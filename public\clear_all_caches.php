<?php
// Load composer autoloader first
require __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Now load bootstrap which depends on autoloader and env vars
require_once __DIR__ . '/../app/config/bootstrap.php';

echo "<h2>Clearing All Caches</h2>";

// Clear Twig cache
$twigCachePath = __DIR__ . '/../storage/cache/twig';
if (is_dir($twigCachePath)) {
    $files = glob($twigCachePath . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    echo "<p>✓ Twig cache cleared</p>";
} else {
    echo "<p>- Twig cache directory not found</p>";
}

// Clear any other caches
$cacheDirs = [
    __DIR__ . '/../storage/cache',
    __DIR__ . '/../storage/logs',
    __DIR__ . '/../storage/sessions'
];

foreach ($cacheDirs as $dir) {
    if (is_dir($dir)) {
        echo "<p>✓ Found cache directory: " . basename($dir) . "</p>";
    }
}

// Clear browser cache instruction
echo "<h3>Browser Cache</h3>";
echo "<p>To ensure you see the latest changes, please also clear your browser cache:</p>";
echo "<ul>";
echo "<li><strong>Chrome/Edge:</strong> Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)</li>";
echo "<li><strong>Firefox:</strong> Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)</li>";
echo "<li><strong>Safari:</strong> Press Cmd+Option+E</li>";
echo "</ul>";
echo "<p>Or do a hard refresh: <strong>Ctrl+F5</strong> (or Cmd+Shift+R on Mac)</p>";

echo "<h3>Test Invoice Formatting</h3>";
echo "<p><a href='/fit/public/test_invoice_formatting.php'>Test Invoice Formatting</a></p>";
echo "<p><a href='/fit/public/invoices'>View Invoices List</a></p>";