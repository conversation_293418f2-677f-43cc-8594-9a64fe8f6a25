<?php
/**
 * Check email sending status for invoice FAC-DIV-2025-0190
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Check Invoice FAC-DIV-2025-0190 Email Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Email Status Check for Invoice FAC-DIV-2025-0190</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // 1. Find the invoice
        echo "<h2>1. Invoice Details</h2>";
        $stmt = $db->prepare("
            SELECT 
                i.*,
                c.name as client_name,
                c.email as client_email,
                u.first_name,
                u.last_name,
                u.email as user_email,
                u.billing_email as user_billing_email,
                it.name as invoice_type_name
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN users u ON i.user_id = u.id
            LEFT JOIN invoice_types it ON i.invoice_type = it.code
            WHERE i.invoice_number = :invoice_number
        ");
        $stmt->execute([':invoice_number' => 'FAC-DIV-2025-0190']);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invoice) {
            echo "<p class='error'>❌ Invoice FAC-DIV-2025-0190 not found!</p>";
            
            // Check for similar invoices
            echo "<h3>Similar Invoices:</h3>";
            $stmt = $db->query("
                SELECT id, invoice_number, status, created_at, invoice_type
                FROM invoices 
                WHERE invoice_number LIKE '%190%' OR invoice_number LIKE '%DIV%'
                ORDER BY id DESC 
                LIMIT 20
            ");
            $similar = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($similar) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Invoice Number</th><th>Type</th><th>Status</th><th>Created</th></tr>";
                foreach ($similar as $inv) {
                    echo "<tr>";
                    echo "<td>{$inv['id']}</td>";
                    echo "<td>{$inv['invoice_number']}</td>";
                    echo "<td>{$inv['invoice_type']}</td>";
                    echo "<td>{$inv['status']}</td>";
                    echo "<td>{$inv['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            exit;
        }
        
        // Display invoice details
        echo "<div class='info'>";
        echo "<p class='success'>✓ Invoice found!</p>";
        echo "<table>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$invoice['id']}</td></tr>";
        echo "<tr><td>Number</td><td><strong>{$invoice['invoice_number']}</strong></td></tr>";
        echo "<tr><td>Type</td><td>{$invoice['invoice_type']} - {$invoice['invoice_type_name']}</td></tr>";
        echo "<tr><td>Status</td><td><strong>{$invoice['status']}</strong></td></tr>";
        echo "<tr><td>Total</td><td>" . number_format($invoice['total'] ?? 0, 2) . " €</td></tr>";
        echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
        echo "<tr><td>Due Date</td><td>{$invoice['due_date']}</td></tr>";
        echo "<tr><td>Sent At</td><td>" . ($invoice['sent_at'] ?: '<span class="error">Not sent</span>') . "</td></tr>";
        echo "<tr><td>Created</td><td>{$invoice['created_at']}</td></tr>";
        echo "<tr><td>Updated</td><td>{$invoice['updated_at']}</td></tr>";
        echo "</table>";
        
        echo "<h3>Recipient Information:</h3>";
        if ($invoice['client_id']) {
            echo "<p>Client: {$invoice['client_name']}<br>";
            echo "Email: " . ($invoice['client_email'] ?: '<span class="error">No email</span>') . "</p>";
        } elseif ($invoice['user_id']) {
            echo "<p>User: {$invoice['first_name']} {$invoice['last_name']}<br>";
            echo "Email: " . ($invoice['user_email'] ?: '<span class="error">No email</span>') . "<br>";
            echo "Billing Email: " . ($invoice['user_billing_email'] ?: '<span class="warning">Not set</span>') . "</p>";
        } else {
            echo "<p class='error'>No recipient found!</p>";
        }
        echo "</div>";
        
        // 2. Check email logs
        echo "<h2>2. Email Logs</h2>";
        
        // Check if table exists
        $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
        if (!$stmt->fetch()) {
            echo "<p class='error'>❌ email_logs table does not exist!</p>";
            echo "<p>Run this SQL to create it:</p>";
            echo "<pre>";
            echo file_get_contents(__DIR__ . '/../database/migrations/083_create_email_logs_table.sql');
            echo "</pre>";
        } else {
            // Get email logs
            $stmt = $db->prepare("
                SELECT * FROM email_logs 
                WHERE invoice_id = :invoice_id
                ORDER BY created_at DESC
            ");
            $stmt->execute([':invoice_id' => $invoice['id']]);
            $emailLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($emailLogs)) {
                echo "<p class='error'>❌ No email logs found for this invoice.</p>";
                echo "<div class='info'>";
                echo "<p>This means one of the following:</p>";
                echo "<ul>";
                echo "<li>The invoice has never been emailed</li>";
                echo "<li>Email logging was not implemented when it was sent</li>";
                echo "<li>The email failed before logging could occur</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                echo "<p class='success'>✓ Found " . count($emailLogs) . " email log(s)</p>";
                
                echo "<table>";
                echo "<tr><th>ID</th><th>To</th><th>Subject</th><th>Status</th><th>Sent At</th><th>Error</th><th>Created</th></tr>";
                foreach ($emailLogs as $log) {
                    $statusClass = $log['status'] === 'sent' ? 'success' : 'error';
                    echo "<tr>";
                    echo "<td>{$log['id']}</td>";
                    echo "<td>{$log['recipient_email']}</td>";
                    echo "<td>{$log['subject']}</td>";
                    echo "<td class='$statusClass'>{$log['status']}</td>";
                    echo "<td>" . ($log['sent_at'] ?: '-') . "</td>";
                    echo "<td>" . ($log['error_message'] ?: '-') . "</td>";
                    echo "<td>{$log['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Show details of last log
                $lastLog = $emailLogs[0];
                echo "<h3>Last Email Attempt Details:</h3>";
                echo "<div class='info'>";
                foreach ($lastLog as $key => $value) {
                    if ($value && !in_array($key, ['body_preview'])) {
                        echo "<strong>$key:</strong> ";
                        if ($key === 'attachments_sent' && $value) {
                            $attachments = json_decode($value, true);
                            if ($attachments) {
                                echo "<br>";
                                foreach ($attachments as $att) {
                                    echo "&nbsp;&nbsp;- {$att['name']} ({$att['type']})<br>";
                                }
                            } else {
                                echo htmlspecialchars($value);
                            }
                        } else {
                            echo htmlspecialchars($value);
                        }
                        echo "<br>";
                    }
                }
                if ($lastLog['body_preview']) {
                    echo "<strong>Body Preview:</strong><br>";
                    echo "<pre>" . htmlspecialchars(substr($lastLog['body_preview'], 0, 300)) . "...</pre>";
                }
                echo "</div>";
            }
        }
        
        // 3. Check email configuration
        echo "<h2>3. Email Configuration</h2>";
        echo "<div class='info'>";
        echo "<table>";
        echo "<tr><th>Setting</th><th>Value</th></tr>";
        echo "<tr><td>Mail Driver</td><td>" . ($_ENV['MAIL_DRIVER'] ?? '<span class="error">Not set</span>') . "</td></tr>";
        
        if (($_ENV['MAIL_DRIVER'] ?? '') === 'smtp') {
            echo "<tr><td>SMTP Host</td><td>" . ($_ENV['MAIL_HOST'] ?? '<span class="error">Not set</span>') . "</td></tr>";
            echo "<tr><td>SMTP Port</td><td>" . ($_ENV['MAIL_PORT'] ?? '<span class="error">Not set</span>') . "</td></tr>";
            echo "<tr><td>SMTP User</td><td>" . ($_ENV['MAIL_USERNAME'] ?? '<span class="error">Not set</span>') . "</td></tr>";
            echo "<tr><td>SMTP Encryption</td><td>" . ($_ENV['MAIL_ENCRYPTION'] ?? '<span class="error">Not set</span>') . "</td></tr>";
        }
        
        echo "<tr><td>From Address</td><td>" . ($_ENV['MAIL_FROM_ADDRESS'] ?? '<span class="error">Not set</span>') . "</td></tr>";
        echo "<tr><td>From Name</td><td>" . ($_ENV['MAIL_FROM_NAME'] ?? '<span class="error">Not set</span>') . "</td></tr>";
        echo "</table>";
        echo "</div>";
        
        // 4. Summary
        echo "<h2>Summary</h2>";
        echo "<div class='info'>";
        
        // Check for inconsistencies
        $invoiceStatusSent = ($invoice['status'] === 'sent');
        $hasSentDate = !empty($invoice['sent_at']);
        $hasEmailLogs = !empty($emailLogs);
        $hasSuccessfulEmail = false;
        
        if ($hasEmailLogs) {
            $successfulEmails = array_filter($emailLogs, function($log) { return $log['status'] === 'sent'; });
            $hasSuccessfulEmail = !empty($successfulEmails);
        }
        
        // Determine actual email status
        if ($hasSuccessfulEmail) {
            $lastSuccess = reset($successfulEmails);
            echo "<p class='success'>✅ <strong>Invoice WAS SENT via EMAIL</strong></p>";
            echo "<p>Successfully emailed on: {$lastSuccess['sent_at']} to {$lastSuccess['recipient_email']}</p>";
        } elseif ($hasEmailLogs && !$hasSuccessfulEmail) {
            echo "<p class='error'>❌ <strong>All email attempts FAILED</strong></p>";
            $lastLog = $emailLogs[0];
            echo "<p>Last attempt on: {$lastLog['created_at']}</p>";
            echo "<p>Error: {$lastLog['error_message']}</p>";
        } elseif ($invoiceStatusSent && !$hasEmailLogs) {
            echo "<p class='warning'>⚠️ <strong>Invoice marked as SENT but NO EMAIL RECORDS found</strong></p>";
            echo "<p>Possible reasons:</p>";
            echo "<ul>";
            echo "<li>Invoice was sent before email logging was implemented</li>";
            echo "<li>Invoice was marked as sent manually without actually emailing</li>";
            echo "<li>Invoice was sent through a different method (printed, etc.)</li>";
            echo "</ul>";
        } else {
            echo "<p class='error'>❌ <strong>Invoice has NEVER BEEN SENT via email</strong></p>";
            
            if (!$invoice['client_email'] && !$invoice['user_email'] && !$invoice['user_billing_email']) {
                echo "<p class='warning'>⚠️ No recipient email address available!</p>";
            } else {
                $recipientEmail = $invoice['client_email'] ?: ($invoice['user_billing_email'] ?: $invoice['user_email']);
                echo "<p>Ready to send to: {$recipientEmail}</p>";
            }
        }
        
        echo "<h3>Actions:</h3>";
        echo "<ul>";
        echo "<li><a href='/fit/public/invoices/{$invoice['id']}'>View Invoice Details</a></li>";
        echo "<li><a href='/fit/public/send_invoice_email.php?id={$invoice['id']}'>Send/Resend Email</a></li>";
        echo "<li><a href='/fit/public/test_email_service.php'>Test Email Service</a></li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    }
    ?>
    
    <hr>
    <p><a href="/fit/public/">Back to Dashboard</a></p>
</body>
</html>