<?php
require_once '../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

// Database connection using .env
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Bulk Generation Issues</h2>";
    
    // 1. Check if is_active column exists
    echo "<h3>1. Checking is_active column:</h3>";
    $stmt = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'is_active'");
    $hasIsActive = $stmt->rowCount() > 0;
    
    if (!$hasIsActive) {
        echo "<p>Adding is_active column...</p>";
        $db->exec("ALTER TABLE user_monthly_retrocession_amounts ADD COLUMN is_active TINYINT(1) DEFAULT 1");
        echo "<p style='color: green;'>✓ Added is_active column</p>";
    } else {
        echo "<p>✓ is_active column exists</p>";
    }
    
    // 2. Fix all is_active = 0 records
    echo "<h3>2. Fixing inactive records:</h3>";
    $stmt = $db->exec("UPDATE user_monthly_retrocession_amounts SET is_active = 1 WHERE is_active = 0");
    echo "<p>Updated $stmt records to active</p>";
    
    // 3. Check Rémi's specific data
    echo "<h3>3. Checking Rémi's June 2025 data:</h3>";
    
    // Get Rémi's user ID
    $stmt = $db->prepare("SELECT id FROM users WHERE first_name = 'Rémi' AND last_name = 'Heine'");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        $userId = $user['id'];
        echo "<p>Found Rémi (User ID: $userId)</p>";
        
        // Check his June 2025 data
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = 6 AND year = 2025
        ");
        $stmt->execute(['user_id' => $userId]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($data) {
            echo "<p>✓ Found June 2025 data:</p>";
            echo "<ul>";
            echo "<li>CNS Amount: " . number_format($data['cns_amount'], 2) . " €</li>";
            echo "<li>Patient Amount: " . number_format($data['patient_amount'], 2) . " €</li>";
            echo "<li>Is Active: " . ($data['is_active'] ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
            
            if (!$data['is_active']) {
                $stmt = $db->prepare("
                    UPDATE user_monthly_retrocession_amounts 
                    SET is_active = 1 
                    WHERE id = :id
                ");
                $stmt->execute(['id' => $data['id']]);
                echo "<p style='color: green;'>✓ Activated Rémi's June data</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ No June 2025 data found for Rémi</p>";
        }
        
        // Check if Rémi has a linked client record
        echo "<h4>Checking practitioner link:</h4>";
        $stmt = $db->prepare("
            SELECT c.* FROM clients c
            WHERE c.user_id = :user_id AND c.is_practitioner = 1
        ");
        $stmt->execute(['user_id' => $userId]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($client) {
            echo "<p>✓ Found linked practitioner record (Client ID: {$client['id']})</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No linked practitioner record found</p>";
            
            // Try to find by email or name
            $stmt = $db->prepare("
                SELECT c.* FROM clients c
                JOIN users u ON u.id = :user_id
                WHERE (c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name)
                AND c.is_practitioner = 1
            ");
            $stmt->execute(['user_id' => $userId]);
            $client = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($client) {
                echo "<p>Found practitioner by email/name match (Client ID: {$client['id']})</p>";
                
                // Update user_id link
                $stmt = $db->prepare("UPDATE clients SET user_id = :user_id WHERE id = :client_id");
                $stmt->execute(['user_id' => $userId, 'client_id' => $client['id']]);
                echo "<p style='color: green;'>✓ Linked client to user</p>";
            }
        }
    }
    
    // 4. Test the bulk generation query
    echo "<h3>4. Testing bulk generation query:</h3>";
    
    // This is the query that bulk generation uses
    $stmt = $db->prepare("
        SELECT 
            u.id as user_id,
            CONCAT(u.first_name, ' ', u.last_name) as name,
            uma.cns_amount,
            uma.patient_amount,
            uma.is_active,
            c.id as client_id,
            c.is_practitioner
        FROM users u
        INNER JOIN user_monthly_retrocession_amounts uma ON uma.user_id = u.id
        LEFT JOIN clients c ON c.user_id = u.id
        WHERE uma.month = 6 
        AND uma.year = 2025
        AND uma.is_active = 1
        AND (uma.cns_amount > 0 OR uma.patient_amount > 0)
        ORDER BY name
    ");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($results) . " users with June 2025 data</p>";
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>User</th><th>CNS</th><th>Patient</th><th>Has Client</th><th>Is Practitioner</th></tr>";
    foreach ($results as $row) {
        $highlight = (strpos($row['name'], 'Rémi') !== false) ? "style='background-color: #d4edda;'" : "";
        echo "<tr $highlight>";
        echo "<td>{$row['name']}</td>";
        echo "<td>" . number_format($row['cns_amount'], 2) . "</td>";
        echo "<td>" . number_format($row['patient_amount'], 2) . "</td>";
        echo "<td>" . ($row['client_id'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($row['is_practitioner'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 5. Summary
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>Summary:</h3>";
    echo "<ol>";
    echo "<li>All monthly amounts are now active</li>";
    echo "<li>Rémi's June 2025 data is confirmed active</li>";
    echo "<li>Client-User links have been verified</li>";
    echo "<li>Bulk generation should now work properly</li>";
    echo "</ol>";
    echo "<p><strong>Next step:</strong> Try bulk generation again for June 2025</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}