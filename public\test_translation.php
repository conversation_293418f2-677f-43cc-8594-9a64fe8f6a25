<?php
// Test Translation System
require_once __DIR__ . '/../vendor/autoload.php';

// Initialize session
session_start();

// Set language
$_SESSION['user_language'] = 'en';

// Test direct translation function
function __($key, $params = []) {
    // Simple translation test
    $translations = [
        'invoices.template' => 'Invoice Template',
        'invoices.no_template' => 'No Template',
        'invoices.template_hint' => 'Select a template to pre-fill invoice lines'
    ];
    
    return $translations[$key] ?? $key;
}

// Test output
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Translation Test</title>
</head>
<body>
    <h1>Translation Test</h1>
    
    <h2>Direct Translation Function</h2>
    <ul>
        <li>invoices.template = <?php echo __('invoices.template'); ?></li>
        <li>invoices.no_template = <?php echo __('invoices.no_template'); ?></li>
        <li>invoices.template_hint = <?php echo __('invoices.template_hint'); ?></li>
    </ul>
    
    <h2>HTML Output Test</h2>
    <div class="col-md-4">
        <label for="template_id" class="form-label"><?php echo __('invoices.template'); ?></label>
        <select class="form-select" id="template_id" name="template_id">
            <option value=""><?php echo __('invoices.no_template'); ?></option>
            <option value="3">System Monthly Rental</option>
        </select>
        <small class="text-muted"><?php echo __('invoices.template_hint'); ?></small>
    </div>
    
    <hr>
    <p><a href="/fit/public/invoices/create?duplicate=246">Back to Invoice Create</a></p>
</body>
</html>