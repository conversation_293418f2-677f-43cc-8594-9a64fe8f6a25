<?php
/**
 * Run only the migrations needed for the Monthly Course Tracking feature
 * 
 * This will run:
 * - 101_create_user_monthly_course_counts.sql
 * - 102_add_cours_invoice_type.sql
 */

// Load environment variables
require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load .env file
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection from .env
$host = $_ENV['DB_HOST'];
$dbname = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Connected to database successfully.\n\n";
    
    // Define the specific migrations for this feature
    $migrations = [
        '101_create_user_monthly_course_counts.sql',
        '102_add_cours_invoice_type.sql'
    ];
    
    $migrationsPath = __DIR__ . '/database/migrations/';
    $successCount = 0;
    
    foreach ($migrations as $migrationFile) {
        $fullPath = $migrationsPath . $migrationFile;
        
        echo "Processing: $migrationFile\n";
        
        if (!file_exists($fullPath)) {
            echo "  ✗ File not found: $fullPath\n\n";
            continue;
        }
        
        try {
            // Read the SQL file
            $sql = file_get_contents($fullPath);
            
            // Split by semicolons (simple approach for these specific files)
            $statements = array_filter(array_map('trim', preg_split('/;\s*$/m', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    // Show a preview of the statement
                    $preview = substr(preg_replace('/\s+/', ' ', $statement), 0, 60);
                    echo "  → Executing: $preview...\n";
                    
                    $pdo->exec($statement);
                }
            }
            
            echo "  ✓ Successfully completed: $migrationFile\n\n";
            $successCount++;
            
        } catch (PDOException $e) {
            echo "  ✗ Error: " . $e->getMessage() . "\n\n";
            
            // If it's a duplicate entry error, it might be okay
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "  ℹ Note: This might mean the migration was already partially applied.\n\n";
            }
        }
    }
    
    echo str_repeat('-', 50) . "\n";
    echo "Summary: $successCount out of " . count($migrations) . " migrations completed successfully.\n\n";
    
    // Verify the changes
    echo "Verifying changes:\n";
    
    // Check if table exists
    try {
        $result = $pdo->query("SHOW TABLES LIKE 'user_monthly_course_counts'");
        if ($result->rowCount() > 0) {
            echo "  ✓ Table 'user_monthly_course_counts' exists\n";
            
            // Show table structure
            $columns = $pdo->query("SHOW COLUMNS FROM user_monthly_course_counts");
            echo "    Columns: ";
            $cols = [];
            while ($col = $columns->fetch(PDO::FETCH_ASSOC)) {
                $cols[] = $col['Field'];
            }
            echo implode(', ', $cols) . "\n";
        } else {
            echo "  ✗ Table 'user_monthly_course_counts' not found\n";
        }
    } catch (PDOException $e) {
        echo "  ✗ Could not verify table: " . $e->getMessage() . "\n";
    }
    
    // Check if invoice type exists
    try {
        $stmt = $pdo->prepare("SELECT * FROM invoice_types WHERE code = 'COURS'");
        $stmt->execute();
        $invoiceType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoiceType) {
            echo "  ✓ Invoice type 'COURS' exists (ID: {$invoiceType['id']}, Prefix: {$invoiceType['prefix']})\n";
        } else {
            echo "  ✗ Invoice type 'COURS' not found\n";
        }
    } catch (PDOException $e) {
        echo "  ✗ Could not verify invoice type: " . $e->getMessage() . "\n";
    }
    
    echo "\n✓ Migration process completed!\n";
    echo "\nYou can now use the Monthly Course Tracking feature for coaches.\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}