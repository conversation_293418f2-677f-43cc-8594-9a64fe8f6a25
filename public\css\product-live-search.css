/**
 * Product Live Search Styles
 * Dropdown styles for live product search functionality
 */

/* Main dropdown container */
.product-search-dropdown {
    position: absolute;
    z-index: 1050;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    min-width: 400px;
    margin-top: 2px;
}

/* Individual product item */
.product-search-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.15s ease-in-out;
}

.product-search-item:last-child {
    border-bottom: none;
}

.product-search-item:hover,
.product-search-item.selected {
    background-color: #f8f9fa;
}

.product-search-item.selected {
    background-color: #e9ecef;
}

/* Product name */
.product-search-item-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: #212529;
    font-size: 0.95rem;
}

/* Product details */
.product-search-item-details {
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Price styling */
.product-search-item-details .float-end {
    font-weight: 500;
    color: #495057;
}

/* No results message */
.product-search-no-results {
    padding: 1rem;
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

/* Loading indicator */
.product-search-loading {
    padding: 1rem;
    text-align: center;
    color: #6c757d;
}

/* Highlight matching text */
.product-search-highlight {
    background-color: #fff3cd;
    font-weight: 600;
}

/* Scrollbar styling */
.product-search-dropdown::-webkit-scrollbar {
    width: 8px;
}

.product-search-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0 0.375rem 0.375rem 0;
}

.product-search-dropdown::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.product-search-dropdown::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .product-search-dropdown {
        min-width: 100%;
        max-width: 90vw;
        left: 5vw !important;
        right: 5vw !important;
        width: auto !important;
    }
    
    .product-search-item {
        padding: 0.5rem 0.75rem;
    }
    
    .product-search-item-name {
        font-size: 0.9rem;
    }
    
    .product-search-item-details {
        font-size: 0.8rem;
    }
}

/* Dark mode support (if theme supports it) */
@media (prefers-color-scheme: dark) {
    .theme-dark .product-search-dropdown {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .theme-dark .product-search-item {
        border-bottom-color: #4a5568;
    }
    
    .theme-dark .product-search-item:hover,
    .theme-dark .product-search-item.selected {
        background-color: #4a5568;
    }
    
    .theme-dark .product-search-item-name {
        color: #e2e8f0;
    }
    
    .theme-dark .product-search-item-details {
        color: #a0aec0;
    }
}

/* Input field styling when search is active */
.item-description:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Category badge in search results */
.product-search-category {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    color: #fff;
    background-color: #6c757d;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.product-search-category.misc {
    background-color: #ffc107;
    color: #212529;
}