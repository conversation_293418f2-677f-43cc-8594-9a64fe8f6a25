<?php
require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "Connected to database: {$_ENV['DB_DATABASE']}\n\n";
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage() . "\n");
}

// 1. List all groups
echo "=== ALL USER GROUPS ===\n";
$stmt = $pdo->query("SELECT * FROM user_groups ORDER BY name");
$groups = $stmt->fetchAll();

foreach ($groups as $group) {
    echo "ID: {$group['id']} | Name: {$group['name']} | Active: " . ($group['is_active'] ? 'Yes' : 'No') . "\n";
    echo "  Description: {$group['description']}\n";
    
    // Count members
    $countStmt = $pdo->prepare("SELECT COUNT(*) as member_count FROM user_group_members WHERE group_id = ?");
    $countStmt->execute([$group['id']]);
    $count = $countStmt->fetchColumn();
    echo "  Members: {$count}\n";
}

// 2. Look specifically for Medical and Managers groups
echo "\n=== SEARCHING FOR MEDICAL AND MANAGERS GROUPS ===\n";
$stmt = $pdo->prepare("SELECT * FROM user_groups WHERE name LIKE '%Medical%' OR name LIKE '%Manager%' OR name LIKE '%Médical%' OR name LIKE '%Gérant%'");
$stmt->execute();
$specialGroups = $stmt->fetchAll();

if ($specialGroups) {
    foreach ($specialGroups as $group) {
        echo "Found: ID {$group['id']} - {$group['name']}\n";
        
        // List members of this group
        $membersStmt = $pdo->prepare("
            SELECT u.id, u.username, u.first_name, u.last_name 
            FROM user_group_members ugm
            JOIN users u ON ugm.user_id = u.id
            WHERE ugm.group_id = ?
        ");
        $membersStmt->execute([$group['id']]);
        $members = $membersStmt->fetchAll();
        
        if ($members) {
            echo "  Members:\n";
            foreach ($members as $member) {
                echo "    - {$member['username']} ({$member['first_name']} {$member['last_name']})\n";
            }
        }
    }
} else {
    echo "No Medical or Managers groups found.\n";
}

// 3. Check User 6 information
echo "\n=== USER 6 (LOY) INFORMATION ===\n";
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = 6");
$stmt->execute();
$user = $stmt->fetch();

if ($user) {
    echo "Username: {$user['username']}\n";
    echo "First Name: {$user['first_name']}\n";
    echo "Last Name: {$user['last_name']}\n";
    echo "Email: {$user['email']}\n";
    echo "Status: " . ($user['is_active'] ? 'Active' : 'Inactive') . "\n";
    echo "Created: {$user['created_at']}\n";
    
    // Check user's groups
    echo "\n--- User 6's Groups ---\n";
    $stmt = $pdo->prepare("
        SELECT ug.id, ug.name, ug.description
        FROM user_group_members ugm
        JOIN user_groups ug ON ugm.group_id = ug.id
        WHERE ugm.user_id = 6
    ");
    $stmt->execute();
    $userGroups = $stmt->fetchAll();
    
    if ($userGroups) {
        foreach ($userGroups as $group) {
            echo "Group ID {$group['id']}: {$group['name']}\n";
            echo "  Description: {$group['description']}\n";
        }
    } else {
        echo "User 6 is not in any groups.\n";
    }
    
    // Check financial obligations
    echo "\n--- User 6's Financial Obligations ---\n";
    
    // Check user_financial_obligations table
    $stmt = $pdo->prepare("SELECT * FROM user_financial_obligations WHERE user_id = 6");
    $stmt->execute();
    $obligations = $stmt->fetchAll();
    
    if ($obligations) {
        echo "Financial Obligations from user_financial_obligations table:\n";
        foreach ($obligations as $obligation) {
            echo "  Rent: €" . number_format($obligation['rent_amount'] ?? 0, 2) . "\n";
            echo "  Charges: €" . number_format($obligation['charges_amount'] ?? 0, 2) . "\n";
            echo "  Secretary TVAC 17%: €" . number_format($obligation['secretary_tvac_17'] ?? 0, 2) . "\n";
            echo "  Total TVAC: €" . number_format($obligation['total_tvac'] ?? 0, 2) . "\n";
            echo "  Effective Date: {$obligation['effective_date']}\n";
            if ($obligation['end_date']) {
                echo "  End Date: {$obligation['end_date']}\n";
            }
        }
    }
    
    // Check as client (invoices received)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as invoice_count, 
               SUM(total) as total_amount,
               SUM(CASE WHEN status = 'paid' THEN total ELSE 0 END) as paid_amount,
               SUM(CASE WHEN status IN ('sent', 'partial') THEN total - COALESCE(paid_amount, 0) ELSE 0 END) as outstanding_amount
        FROM invoices 
        WHERE client_id = 6
    ");
    $stmt->execute();
    $clientInvoices = $stmt->fetch();
    
    echo "\nAs Client (Invoices Received):\n";
    echo "  Total Invoices: {$clientInvoices['invoice_count']}\n";
    echo "  Total Amount: €" . number_format($clientInvoices['total_amount'] ?? 0, 2) . "\n";
    echo "  Paid Amount: €" . number_format($clientInvoices['paid_amount'] ?? 0, 2) . "\n";
    echo "  Outstanding: €" . number_format($clientInvoices['outstanding_amount'] ?? 0, 2) . "\n";
    
    // Check recent invoices
    echo "\n  Recent Invoices (last 5):\n";
    $stmt = $pdo->prepare("
        SELECT invoice_number, issue_date, due_date, total, status 
        FROM invoices 
        WHERE client_id = 6
        ORDER BY issue_date DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recentInvoices = $stmt->fetchAll();
    
    foreach ($recentInvoices as $invoice) {
        echo "    {$invoice['invoice_number']} | {$invoice['issue_date']} | Due: {$invoice['due_date']} | €" . 
             number_format($invoice['total'], 2) . " | Status: {$invoice['status']}\n";
    }
    
    // Check as practitioner (if applicable)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as invoice_count,
               SUM(total) as total_amount
        FROM invoices 
        WHERE user_id = 6
    ");
    $stmt->execute();
    $practitionerInvoices = $stmt->fetch();
    
    if ($practitionerInvoices['invoice_count'] > 0) {
        echo "\nAs Practitioner:\n";
        echo "  Total Invoices Created: {$practitionerInvoices['invoice_count']}\n";
        echo "  Total Amount: €" . number_format($practitionerInvoices['total_amount'] ?? 0, 2) . "\n";
    }
    
    // Check retrocession settings
    echo "\n--- User 6's Retrocession Settings ---\n";
    $stmt = $pdo->prepare("
        SELECT * FROM user_retrocession_settings 
        WHERE user_id = 6
    ");
    $stmt->execute();
    $settings = $stmt->fetchAll();
    
    if ($settings) {
        foreach ($settings as $setting) {
            echo "Setting ID: {$setting['id']}\n";
            if (!empty($setting['retrocession_rate'])) {
                echo "  Retrocession Rate: {$setting['retrocession_rate']}%\n";
            }
            if (!empty($setting['vat_rate'])) {
                echo "  VAT Rate: {$setting['vat_rate']}%\n";
            }
            if (!empty($setting['exclude_patient_line'])) {
                echo "  Exclude Patient Line: " . ($setting['exclude_patient_line'] ? 'Yes' : 'No') . "\n";
            }
            echo "  Active: " . ($setting['is_active'] ? 'Yes' : 'No') . "\n";
            echo "  Created: {$setting['created_at']}\n";
        }
    } else {
        echo "No retrocession settings configured.\n";
    }
    
} else {
    echo "User 6 not found.\n";
}

// 4. Check group configurations
echo "\n=== GROUP CONFIGURATIONS ===\n";
// First check if group_config table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'group_config'");
if ($stmt->rowCount() > 0) {
    $stmt = $pdo->query("
        SELECT gc.*, ug.name as group_name 
        FROM group_config gc
        LEFT JOIN user_groups ug ON gc.group_id = ug.id
        ORDER BY ug.name
    ");
    $configs = $stmt->fetchAll();

    if ($configs) {
        foreach ($configs as $config) {
            echo "\nGroup: {$config['group_name']} (ID: {$config['group_id']})\n";
            echo "  Config Key: {$config['config_key']}\n";
            echo "  Config Value: {$config['config_value']}\n";
        }
    } else {
        echo "No group configurations found.\n";
    }
} else {
    echo "Group config table does not exist.\n";
}

// 5. Check if there's a specific LOY-related configuration
echo "\n=== LOY-SPECIFIC CONFIGURATIONS ===\n";
$stmt = $pdo->prepare("
    SELECT * FROM config 
    WHERE `key` LIKE '%loy%' OR `value` LIKE '%loy%' OR `value` LIKE '%LOY%'
");
$stmt->execute();
$loyConfigs = $stmt->fetchAll();

if ($loyConfigs) {
    foreach ($loyConfigs as $config) {
        echo "Key: {$config['key']} | Value: {$config['value']}\n";
    }
} else {
    echo "No LOY-specific configurations found.\n";
}

// 6. Check all users in the system for better context
echo "\n=== ALL USERS IN SYSTEM ===\n";
$stmt = $pdo->query("SELECT id, username, first_name, last_name, is_active FROM users ORDER BY id");
$users = $stmt->fetchAll();

foreach ($users as $u) {
    $active = $u['is_active'] ? 'Active' : 'Inactive';
    echo "ID {$u['id']}: {$u['username']} - {$u['first_name']} {$u['last_name']} ({$active})\n";
}

echo "\n=== END OF REPORT ===\n";