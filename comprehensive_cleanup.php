<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Comprehensive Invoice Generation Cleanup</h2>\n";
    
    // 1. Find all orphaned user_generated_invoices records
    echo "<h3>1. Orphaned User Generated Invoice Records</h3>\n";
    $stmt = $pdo->query("
        SELECT ugi.*, u.first_name, u.last_name, u.email,
               i.id as invoice_exists, i.invoice_number
        FROM user_generated_invoices ugi
        LEFT JOIN invoices i ON ugi.invoice_id = i.id
        JOIN users u ON ugi.user_id = u.id
        ORDER BY ugi.user_id, ugi.period_year, ugi.period_month
    ");
    
    $records = $stmt->fetchAll();
    $orphaned = array_filter($records, function($r) { return !$r['invoice_exists']; });
    
    if (count($orphaned) > 0) {
        echo "<p style='color: red;'>Found " . count($orphaned) . " orphaned records (invoice deleted but tracking remains)</p>\n";
        echo "<table border='1'>\n";
        echo "<tr><th>User</th><th>Type</th><th>Period</th><th>Invoice ID</th><th>Status</th></tr>\n";
        
        foreach ($orphaned as $record) {
            echo "<tr>";
            echo "<td>{$record['first_name']} {$record['last_name']}</td>";
            echo "<td>{$record['invoice_type']}</td>";
            echo "<td>{$record['period_month']}/{$record['period_year']}</td>";
            echo "<td>{$record['invoice_id']} (DELETED)</td>";
            echo "<td><span style='color:red'>Orphaned</span></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: green;'>✅ No orphaned user_generated_invoices records found</p>\n";
    }
    
    // 2. Check retrocession_data_entry orphans
    echo "\n<h3>2. Retrocession Data Entry Status</h3>\n";
    $stmt = $pdo->query("
        SELECT rde.*, i.id as invoice_exists, i.invoice_number,
               c.name as practitioner_name
        FROM retrocession_data_entry rde
        LEFT JOIN invoices i ON rde.invoice_id = i.id
        LEFT JOIN clients c ON rde.practitioner_id = c.id
        WHERE rde.invoice_id IS NOT NULL
        ORDER BY rde.period_year DESC, rde.period_month DESC
    ");
    
    $retro_records = $stmt->fetchAll();
    $retro_orphaned = array_filter($retro_records, function($r) { return !$r['invoice_exists']; });
    
    if (count($retro_orphaned) > 0) {
        echo "<p style='color: orange;'>Found " . count($retro_orphaned) . " retrocession entries pointing to deleted invoices</p>\n";
        echo "<table border='1'>\n";
        echo "<tr><th>Practitioner</th><th>Period</th><th>Invoice ID</th><th>Status</th></tr>\n";
        
        foreach ($retro_orphaned as $record) {
            echo "<tr>";
            echo "<td>{$record['practitioner_name']}</td>";
            echo "<td>{$record['period_month']}/{$record['period_year']}</td>";
            echo "<td>{$record['invoice_id']} (DELETED)</td>";
            echo "<td><span style='color:orange'>Points to deleted invoice</span></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // 3. Summary and cleanup options
    echo "\n<h3>3. Cleanup Options</h3>\n";
    
    if (count($orphaned) > 0 || count($retro_orphaned) > 0) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>\n";
        echo "<p><strong>Cleanup Actions Available:</strong></p>\n";
        
        if (count($orphaned) > 0) {
            echo "<form method='post' style='display:inline;'>\n";
            echo "<button type='submit' name='action' value='clean_ugi' onclick='return confirm(\"Delete all orphaned user_generated_invoices records?\")'>Clean Orphaned Tracking Records</button>\n";
            echo "</form>\n";
        }
        
        if (count($retro_orphaned) > 0) {
            echo "<form method='post' style='display:inline; margin-left: 10px;'>\n";
            echo "<button type='submit' name='action' value='clean_retro' onclick='return confirm(\"Reset invoice_id to NULL for orphaned retrocession entries?\")'>Fix Retrocession Entries</button>\n";
            echo "</form>\n";
        }
        
        echo "<form method='post' style='display:inline; margin-left: 10px;'>\n";
        echo "<button type='submit' name='action' value='clean_all' style='background-color: #dc3545; color: white;' onclick='return confirm(\"Perform ALL cleanup actions?\")'>Clean Everything</button>\n";
        echo "</form>\n";
        echo "</div>\n";
    } else {
        echo "<p style='color: green;'>✅ No cleanup needed - database is clean!</p>\n";
    }
    
    // Handle cleanup actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        echo "\n<h3>Cleanup Results:</h3>\n";
        
        switch ($_POST['action']) {
            case 'clean_ugi':
                $count = $pdo->exec("
                    DELETE ugi FROM user_generated_invoices ugi
                    LEFT JOIN invoices i ON ugi.invoice_id = i.id
                    WHERE i.id IS NULL
                ");
                echo "<p style='color: green;'>✅ Deleted $count orphaned user_generated_invoices records</p>\n";
                break;
                
            case 'clean_retro':
                $count = $pdo->exec("
                    UPDATE retrocession_data_entry rde
                    LEFT JOIN invoices i ON rde.invoice_id = i.id
                    SET rde.invoice_id = NULL
                    WHERE rde.invoice_id IS NOT NULL AND i.id IS NULL
                ");
                echo "<p style='color: green;'>✅ Fixed $count retrocession_data_entry records</p>\n";
                break;
                
            case 'clean_all':
                // Clean user_generated_invoices
                $count1 = $pdo->exec("
                    DELETE ugi FROM user_generated_invoices ugi
                    LEFT JOIN invoices i ON ugi.invoice_id = i.id
                    WHERE i.id IS NULL
                ");
                
                // Fix retrocession entries
                $count2 = $pdo->exec("
                    UPDATE retrocession_data_entry rde
                    LEFT JOIN invoices i ON rde.invoice_id = i.id
                    SET rde.invoice_id = NULL
                    WHERE rde.invoice_id IS NOT NULL AND i.id IS NULL
                ");
                
                echo "<p style='color: green;'>✅ Complete cleanup done:</p>\n";
                echo "<ul>\n";
                echo "<li>Deleted $count1 orphaned tracking records</li>\n";
                echo "<li>Fixed $count2 retrocession entries</li>\n";
                echo "</ul>\n";
                break;
        }
        
        echo "<p><a href='comprehensive_cleanup.php'>Refresh to see current status</a></p>\n";
    }
    
    echo "\n<hr>\n";
    echo "<p><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation' class='btn'>Return to Bulk Generation</a> | ";
    echo "<a href='{$_ENV['APP_BASE_URL']}/invoices' class='btn'>Go to Invoice List</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}