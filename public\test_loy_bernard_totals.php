<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '\"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Totals Test - Bernard Heens</h2>";
    
    // Check Bernard Heens financial obligations
    $stmt = $db->prepare("
        SELECT * FROM user_financial_obligations 
        WHERE user_id = 6 AND end_date IS NULL
        LIMIT 1
    ");
    $stmt->execute();
    $bernard = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($bernard) {
        echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px;'>";
        echo "<h3>Financial Obligations from Database:</h3>";
        echo "<ul>";
        echo "<li>Rent: €" . number_format($bernard['rent_amount'], 2) . "</li>";
        echo "<li>Charges: €" . number_format($bernard['charges_amount'], 2) . "</li>";
        echo "<li>Secretary TVAC 17%: €" . number_format($bernard['secretary_tvac_17'], 2) . "</li>";
        echo "<li>Secretary HTVA: €" . number_format($bernard['secretary_htva'], 2) . "</li>";
        echo "<li>TVA 17%: €" . number_format($bernard['tva_17'], 2) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Calculate expected totals
        $rentAmount = floatval($bernard['rent_amount']);
        $chargesAmount = floatval($bernard['charges_amount']);
        $secretaryTvac17 = floatval($bernard['secretary_tvac_17']);
        $secretaryHtva = floatval($bernard['secretary_htva']);
        $tva17 = floatval($bernard['tva_17']);
        
        // Total without any VAT calculation
        $simpleTotal = $rentAmount + $chargesAmount + $secretaryTvac17 + $secretaryHtva + $tva17;
        
        echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>Expected Invoice Lines:</h3>";
        echo "<table border='1' cellpadding='10' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ddd;'>";
        echo "<th>Description</th><th>Amount</th><th>VAT Rate</th><th>VAT Amount</th>";
        echo "</tr>";
        
        $totalHT = 0;
        $totalVAT = 0;
        
        // Rent - 0% VAT
        if ($rentAmount > 0) {
            echo "<tr>";
            echo "<td>Loyer mensuel</td>";
            echo "<td>€" . number_format($rentAmount, 2) . "</td>";
            echo "<td>0%</td>";
            echo "<td>€0.00</td>";
            echo "</tr>";
            $totalHT += $rentAmount;
        }
        
        // Charges - 0% VAT
        if ($chargesAmount > 0) {
            echo "<tr>";
            echo "<td>Charges location</td>";
            echo "<td>€" . number_format($chargesAmount, 2) . "</td>";
            echo "<td>0%</td>";
            echo "<td>€0.00</td>";
            echo "</tr>";
            $totalHT += $chargesAmount;
        }
        
        // Secretary TVAC 17% - This is INCLUSIVE of VAT
        if ($secretaryTvac17 > 0) {
            $htAmount = $secretaryTvac17 / 1.17; // Extract HT from TTC
            $vatAmount = $secretaryTvac17 - $htAmount;
            echo "<tr>";
            echo "<td>Secrétariat TVAC 17%</td>";
            echo "<td>€" . number_format($secretaryTvac17, 2) . " (TTC)</td>";
            echo "<td>17%</td>";
            echo "<td>€" . number_format($vatAmount, 2) . "</td>";
            echo "</tr>";
            $totalHT += $htAmount;
            $totalVAT += $vatAmount;
        }
        
        // Secretary HTVA - 0% VAT
        if ($secretaryHtva > 0) {
            echo "<tr>";
            echo "<td>Secrétariat HTVA</td>";
            echo "<td>€" . number_format($secretaryHtva, 2) . "</td>";
            echo "<td>0%</td>";
            echo "<td>€0.00</td>";
            echo "</tr>";
            $totalHT += $secretaryHtva;
        }
        
        // TVA 17% - This is pure VAT
        if ($tva17 > 0) {
            echo "<tr>";
            echo "<td>TVA 17%</td>";
            echo "<td>€" . number_format($tva17, 2) . "</td>";
            echo "<td>17%</td>";
            echo "<td>€" . number_format($tva17, 2) . "</td>";
            echo "</tr>";
            $totalVAT += $tva17;
        }
        
        echo "</table>";
        
        echo "<h4 style='margin-top: 20px;'>Expected Totals:</h4>";
        echo "<p><strong>Sub-total HT:</strong> €" . number_format($totalHT, 2) . "</p>";
        echo "<p><strong>VAT Amount:</strong> €" . number_format($totalVAT, 2) . "</p>";
        echo "<p style='font-size: 1.2em;'><strong>Total TTC:</strong> €" . number_format($simpleTotal, 2) . "</p>";
        echo "</div>";
        
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>Important Notes:</h3>";
        echo "<ul>";
        echo "<li><strong>Simple Total Calculation:</strong> All amounts should be added as-is: €2,840.00</li>";
        echo "<li><strong>Secretary TVAC 17%:</strong> This amount (€1,170) already includes VAT</li>";
        echo "<li><strong>TVA 17%:</strong> This is a pure VAT amount (€170)</li>";
        echo "<li>The invoice should NOT add additional VAT on top of TVAC amounts</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>Test Steps:</h3>";
        echo "<ol>";
        echo "<li>Go to: <a href='/fit/public/invoices/create?type=loyer' target='_blank'>/invoices/create?type=loyer</a></li>";
        echo "<li>Select <strong>Bernard Heens</strong></li>";
        echo "<li>Verify the invoice total is exactly <strong>€2,840.00</strong></li>";
        echo "<li>Check that VAT rates are correctly applied (0% for rent/charges/HTVA, 17% for TVAC/TVA)</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ No financial obligations found for Bernard Heens</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>