{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.edit_invoice') }} - {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.edit_invoice') }}</h1>
            <p class="text-muted mb-0">{{ invoice.invoice_number }}</p>
        </div>
        <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    {% if invoice.status != 'draft' %}
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        {{ __('invoices.cannot_edit_sent_invoice') }}
    </div>
    
    {% if isAdmin %}
    <!-- Admin Delete Button for Sent Invoices -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <h5 class="text-danger mb-3">{{ __('common.admin_actions') }}</h5>
            <p class="text-muted mb-3">{{ __('invoices.admin_can_delete_sent') }}</p>
            <button type="button" class="btn btn-danger" onclick="confirmDeleteSentInvoice()">
                <i class="bi bi-trash me-2"></i>{{ __('common.delete_invoice') }}
            </button>
        </div>
    </div>
    
    <!-- Delete Form (hidden) -->
    <form id="deleteForm" method="POST" action="{{ base_url }}/invoices/{{ invoice.id }}/delete" style="display: none;">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    </form>
    {% endif %}
    
    {% else %}

    <form method="POST" action="{{ base_url }}/invoices/{{ invoice.id }}" id="invoiceForm" class="needs-validation" novalidate>
        <input type="hidden" name="_method" value="PUT">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Invoice Details -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>{{ __('invoices.invoice_details') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="type_id" class="form-label">{{ __('invoices.invoice_type') }} *</label>
                                <select class="form-select" id="type_id" name="type_id" required>
                                    {% for type in invoice_types %}
                                        <option value="{{ type.id }}" {{ invoice.type_id == type.id ? 'selected' : '' }}>
                                            {{ type.display_name|default(type.name) }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="invoice_number" class="form-label">{{ __('invoices.invoice_number') }} *</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                       value="{{ invoice.invoice_number }}" required {{ invoice.status != 'draft' ? 'readonly' : '' }}>
                                {% if invoice.status == 'draft' %}
                                <small class="text-muted">{{ __('invoices.can_edit_number_draft') | default('You can edit the invoice number while in draft status') }}</small>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <label for="issue_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                       value="{{ invoice.issue_date|date('Y-m-d') }}" required>
                            </div>
                            
                            <div class="col-md-12">
                                <label class="form-label">{{ __('invoices.bill_to') }}</label>
                                <div class="p-3 bg-light rounded">
                                    {% if invoice.user_id %}
                                        <i class="bi bi-person text-primary me-2"></i>
                                        <strong>{{ invoice.user.full_name|default(invoice.user.username) }}</strong>
                                        <span class="text-muted ms-2">({{ __('users.user') }})</span>
                                    {% elseif invoice.client_id %}
                                        <i class="bi bi-building text-info me-2"></i>
                                        <strong>
                                            {% if invoice.client.client_type == 'individual' %}
                                                {{ invoice.client.first_name }} {{ invoice.client.last_name }}
                                            {% else %}
                                                {{ invoice.client.company_name }}
                                            {% endif %}
                                        </strong>
                                        <span class="text-muted ms-2">({{ __('clients.client') }})</span>
                                    {% else %}
                                        <span class="text-muted">{{ __('invoices.no_recipient') }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>{{ __('invoices.invoice_items') }}</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-light me-2" id="searchProductBtn">
                                <i class="bi bi-search me-1"></i>{{ __('invoices.search_product') | default('Search Product') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-light" id="addItemBtn">
                                <i class="bi bi-plus-circle me-1"></i>{{ __('invoices.add_item') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {# Check if this is a retrocession invoice #}
                        {% set invoiceTypeCode = invoice.invoice_type|default('') %}
                        {% set invoiceNumber = invoice.invoice_number|default('') %}
                        {% set isRetrocession = (invoiceTypeCode in ['retrocession_30', 'retrocession_25']) or 
                                               (invoiceNumber matches '/RET30|RET25/') %}
                        
                        <div class="table-responsive">
                            <table class="table" id="itemsTable">
                                <thead>
                                    <tr id="itemsTableHeader">
                                        {% if isRetrocession %}
                                            {# Retrocession columns: OBJET | Montant Base | TOTAL | HTVA | % TVA #}
                                            <th width="40%">{{ __('invoices.object')|default('OBJET') }}</th>
                                            <th width="15%">{{ __('invoices.base_amount')|default('Montant Base') }}</th>
                                            <th width="15%">{{ __('invoices.total')|default('TOTAL') }}</th>
                                            <th width="15%">{{ __('invoices.amount_excl_vat')|default('HTVA') }}</th>
                                            <th width="10%">{{ __('invoices.vat_rate')|default('% TVA') }}</th>
                                            <th width="5%"></th>
                                        {% else %}
                                            {# Standard columns #}
                                            <th width="40%">{{ __('invoices.description') }}</th>
                                            <th width="15%">{{ __('invoices.quantity') }}</th>
                                            <th width="15%">{{ __('invoices.unit_price') }}</th>
                                            <th width="15%">{{ __('invoices.vat_rate') }}</th>
                                            <th width="15%">{{ __('invoices.total') }}</th>
                                            <th width="50"></th>
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody id="itemsBody">
                                    {% for item in invoice.items %}
                                    <tr class="invoice-item" data-line-type="{{ item.description starts with 'RÉTROCESSION CNS' ? 'cns' : (item.description starts with 'RÉTROCESSION PATIENTS' ? 'patient' : (item.description starts with 'FRAIS SECRÉTARIAT' ? 'secretary' : '')) }}">
                                        {% if isRetrocession %}
                                            {# Retrocession line edit - OBJET first, then Montant Base #}
                                            <td>
                                                <input type="hidden" name="items[{{ loop.index0 }}][id]" value="{{ item.id }}">
                                                <input type="hidden" name="items[{{ loop.index0 }}][item_id]" value="{{ item.item_id }}" class="item-id">
                                                <input type="hidden" name="items[{{ loop.index0 }}][quantity]" value="1" class="item-quantity">
                                                <input type="text" class="form-control form-control-sm item-description" 
                                                       name="items[{{ loop.index0 }}][description]" value="{{ item.description }}" 
                                                       readonly required>
                                            </td>
                                            <td>
                                                {% if item.description starts with 'RÉTROCESSION CNS' %}
                                                    <input type="number" class="form-control form-control-sm retrocession-base-amount" 
                                                           id="cns_amount" placeholder="Montant CNS"
                                                           min="0" step="0.01" value="{{ (item.unit_price / 0.20)|number_format(2, '.', '') }}">
                                                {% elseif item.description starts with 'RÉTROCESSION PATIENTS' %}
                                                    <input type="number" class="form-control form-control-sm retrocession-base-amount" 
                                                           id="patient_amount" placeholder="Montant Patients"
                                                           min="0" step="0.01" value="{{ (item.unit_price / 0.20)|number_format(2, '.', '') }}">
                                                {% elseif item.description starts with 'FRAIS SECRÉTARIAT' %}
                                                    {# Calculate the base total from existing CNS and Patient lines #}
                                                    {% set cns_base = 0 %}
                                                    {% set patient_base = 0 %}
                                                    {% for other_item in invoice.items %}
                                                        {% if other_item.description starts with 'RÉTROCESSION CNS' %}
                                                            {% set cns_base = other_item.unit_price / 0.20 %}
                                                        {% elseif other_item.description starts with 'RÉTROCESSION PATIENTS' %}
                                                            {% set patient_base = other_item.unit_price / 0.20 %}
                                                        {% endif %}
                                                    {% endfor %}
                                                    <input type="text" class="form-control form-control-sm" id="secretary_total"
                                                           value="€{{ (cns_base + patient_base)|number_format(2, ',', '.') }}" readonly
                                                           style="background-color: #f8f9fa; font-weight: 500;">
                                                {% else %}
                                                    <input type="text" class="form-control form-control-sm" value="-" readonly>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm item-total" readonly 
                                                       value="{{ (item.quantity * item.unit_price * (1 + item.vat_rate / 100))|number_format(2, ',', '.') }} €">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control form-control-sm item-price" 
                                                       name="items[{{ loop.index0 }}][unit_price]" value="{{ item.unit_price }}" 
                                                       min="0" step="0.01" readonly>
                                            </td>
                                            <td>
                                                <select class="form-select form-select-sm item-vat" name="items[{{ loop.index0 }}][vat_rate]" 
                                                        required {{ item.description starts with 'FRAIS SECRÉTARIAT' ? '' : 'disabled' }}>
                                                    {% for vat in vat_rates %}
                                                        <option value="{{ vat.rate }}" data-rate="{{ vat.rate }}" 
                                                                {{ item.vat_rate == vat.rate ? 'selected' : '' }}>
                                                            {{ vat.rate|number_format(0) }} %
                                                        </option>
                                                    {% endfor %}
                                                </select>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger remove-item" disabled>
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        {% else %}
                                            {# Standard line edit #}
                                            <td>
                                                <input type="hidden" name="items[{{ loop.index0 }}][id]" value="{{ item.id }}">
                                                <input type="hidden" name="items[{{ loop.index0 }}][item_id]" value="{{ item.item_id }}" class="item-id">
                                                <input type="text" class="form-control form-control-sm item-description" 
                                                       name="items[{{ loop.index0 }}][description]" value="{{ item.description }}" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control form-control-sm item-quantity" 
                                                       name="items[{{ loop.index0 }}][quantity]" value="{{ item.quantity }}" 
                                                       min="1" step="0.01" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control form-control-sm item-price" 
                                                       name="items[{{ loop.index0 }}][unit_price]" value="{{ item.unit_price }}" 
                                                       min="0" step="0.01" required>
                                            </td>
                                            <td>
                                                <select class="form-select form-select-sm item-vat" name="items[{{ loop.index0 }}][vat_rate]" required>
                                                    {% for vat in vat_rates %}
                                                        <option value="{{ vat.rate }}" data-rate="{{ vat.rate }}" 
                                                                {{ item.vat_rate == vat.rate ? 'selected' : '' }}>
                                                            {{ vat.rate|number_format(vat.rate == 0 ? 0 : (vat.rate == vat.rate|round ? 0 : 2), ',', '.') }} %
                                                        </option>
                                                    {% endfor %}
                                                </select>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm item-total" readonly 
                                                       value="{{ item.line_total|number_format(2, ',', '.') }} €">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger remove-item">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                                {% if not item.item_id %}
                                                <button type="button" class="btn btn-sm btn-success save-to-product ms-1" title="{{ __('invoices.save_as_product')|default('Save as product') }}">
                                                    <i class="bi bi-plus-circle"></i>
                                                </button>
                                                {% endif %}
                                            </td>
                                        {% endif %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                        <td colspan="2">
                                            <strong id="subtotal">{{ invoice.subtotal|number_format(2, ',', '.') }} €</strong>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                        <td colspan="2">
                                            <strong id="vatAmount">{{ invoice.vat_amount|number_format(2, ',', '.') }} €</strong>
                                        </td>
                                    </tr>
                                    {% if invoice.discount_amount > 0 %}
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.discount') }}:</strong></td>
                                        <td colspan="2">
                                            <strong class="text-success">-{{ invoice.discount_amount|number_format(2, ',', '.') }} €</strong>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.total') }}:</strong></td>
                                        <td colspan="2">
                                            <strong id="total" class="text-primary">{{ invoice.total|number_format(2, ',', '.') }} €</strong>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('invoices.additional_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="notes" class="form-label">{{ __('invoices.notes') }}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ invoice.notes }}</textarea>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="internal_notes" class="form-label">{{ __('invoices.internal_notes') }}</label>
                                <textarea class="form-control" id="internal_notes" name="internal_notes" rows="2">{{ invoice.internal_notes }}</textarea>
                                <small class="text-muted">{{ __('invoices.internal_notes_hint') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Status -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.status') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-3">
                            <span class="badge bg-secondary fs-6">{{ __('invoices.status.draft') }}</span>
                        </div>
                        <dl class="row mb-0 small">
                            <dt class="col-6">{{ __('common.created_at') }}:</dt>
                            <dd class="col-6 text-end">{{ invoice.created_at|date('d/m/Y H:i') }}</dd>
                            
                            <dt class="col-6">{{ __('common.updated_at') }}:</dt>
                            <dd class="col-6 text-end">{{ invoice.updated_at|date('d/m/Y H:i') }}</dd>
                        </dl>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>{{ __('invoices.payment_terms') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="payment_term_id" class="form-label">{{ __('invoices.payment_terms') }}</label>
                            <select class="form-select" id="payment_term_id" name="payment_term_id">
                                <option value="">{{ __('common.select') }}</option>
                                {% for term in paymentTerms %}
                                    <option value="{{ term.id }}" data-days="{{ term.days }}" 
                                            {% if term.id == invoice.payment_term_id %}selected{% endif %}>
                                        {{ term.display_name }}
                                        {% if term.display_description %}
                                            ({{ term.display_description }})
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">{{ __('config.payment_term_description_hint') }}</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="due_date" class="form-label">{{ __('invoices.due_date') }}</label>
                            <input type="date" class="form-control" id="due_date" name="due_date" 
                                   value="{{ invoice.due_date ? invoice.due_date|date('Y-m-d') : '' }}">
                            <small class="text-muted">{{ __('invoices.leave_empty_immediate') }}</small>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>{{ __('common.save_changes') }}
                            </button>
                            <button type="submit" name="action" value="save_and_send" class="btn btn-success">
                                <i class="bi bi-send me-2"></i>{{ __('invoices.save_and_send') }}
                            </button>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                            </button>
                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Delete Form (hidden) -->
    <form id="deleteForm" method="POST" action="{{ base_url }}/invoices/{{ invoice.id }}/delete" style="display: none;">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    </form>

    {% endif %}
</div>

<!-- Product Search Modal -->
<div class="modal fade" id="productSearchModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.search_product') | default('Search Product') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="productSearchInput" 
                           placeholder="{{ __('invoices.search_by_name_or_code') | default('Search by name or code...') }}">
                </div>
                <div id="productSearchResults" style="max-height: 400px; overflow-y: auto;">
                    <!-- Search results will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Item Template -->
<template id="itemTemplate">
    <tr class="invoice-item">
        <td>
            <input type="hidden" name="items[INDEX][item_id]" class="item-id">
            <input type="text" class="form-control form-control-sm item-description" name="items[INDEX][description]" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-quantity" name="items[INDEX][quantity]" value="1" min="1" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-price" name="items[INDEX][unit_price]" min="0" step="0.01" required>
        </td>
        <td>
            <select class="form-select form-select-sm item-vat" name="items[INDEX][vat_rate]" required>
                {% for vat in vat_rates %}
                    <option value="{{ vat.rate }}" data-rate="{{ vat.rate }}" {{ vat.is_default ? 'selected' : '' }}>
                        {{ vat.rate|number_format(0) }} %
                    </option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm item-total" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger remove-item">
                <i class="bi bi-trash"></i>
            </button>
            <button type="button" class="btn btn-sm btn-success save-to-product ms-1" title="{{ __('invoices.save_as_product')|default('Save as product') }}">
                <i class="bi bi-plus-circle"></i>
            </button>
        </td>
    </tr>
</template>

<script>
let itemIndex = {{ invoice.items|length }};
const currency = '€';

document.addEventListener('DOMContentLoaded', function() {
    // Add item button
    document.getElementById('addItemBtn').addEventListener('click', addItem);
    
    // Search product button
    document.getElementById('searchProductBtn').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('productSearchModal'));
        modal.show();
        document.getElementById('productSearchInput').focus();
    });
    
    // Product search input
    let searchTimeout;
    document.getElementById('productSearchInput').addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        const term = e.target.value.trim();
        
        if (term.length < 2) {
            document.getElementById('productSearchResults').innerHTML = '';
            return;
        }
        
        searchTimeout = setTimeout(() => searchProducts(term), 300);
    });
    
    // Initialize existing items
    document.querySelectorAll('.invoice-item').forEach(row => {
        row.querySelector('.item-quantity').addEventListener('input', () => calculateItemTotal(row));
        row.querySelector('.item-price').addEventListener('input', () => calculateItemTotal(row));
        
        // Special handling for VAT changes
        const vatSelect = row.querySelector('.item-vat');
        if (vatSelect) {
            vatSelect.addEventListener('change', function() {
                // For secretary line in retrocession, ensure VAT changes are properly handled
                if (row.dataset.lineType === 'secretary') {
                    // Recalculate with new VAT rate
                    calculateRetrocessionTotals();
                } else {
                    calculateItemTotal(row);
                }
            });
        }
        
        row.querySelector('.item-description').addEventListener('input', () => checkDuplicateDescriptions());
        row.querySelector('.remove-item').addEventListener('click', () => removeItem(row));
        
        const saveBtn = row.querySelector('.save-to-product');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => saveLineAsProduct(row));
        }
        
        // Add listeners for retrocession base amount inputs
        const baseAmountInput = row.querySelector('.retrocession-base-amount');
        if (baseAmountInput) {
            baseAmountInput.addEventListener('input', () => updateRetrocessionCalculations());
        }
    });
    
    // Handle payment term change
    document.getElementById('payment_term_id').addEventListener('change', updateDueDate);
    
    // Handle issue date change
    document.getElementById('issue_date').addEventListener('change', updateDueDate);
    
    // Initial calculation
    calculateTotals();
    
    // Initialize save product modal after a short delay to ensure DOM is ready
    setTimeout(() => {
        initSaveProductModal();
    }, 100);
    
    // Add retrocession-specific event listeners
    initializeRetrocessionHandlers();
});

function addItem() {
    const template = document.getElementById('itemTemplate');
    const clone = template.content.cloneNode(true);
    
    // Replace INDEX with actual index
    clone.querySelectorAll('[name*="INDEX"]').forEach(element => {
        element.name = element.name.replace('INDEX', itemIndex);
    });
    
    // Add event listeners
    const row = clone.querySelector('tr');
    row.querySelector('.item-quantity').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-price').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-vat').addEventListener('change', () => calculateItemTotal(row));
    row.querySelector('.item-description').addEventListener('input', () => checkDuplicateDescriptions());
    row.querySelector('.remove-item').addEventListener('click', () => removeItem(row));
    row.querySelector('.save-to-product').addEventListener('click', () => saveLineAsProduct(row));
    
    document.getElementById('itemsBody').appendChild(clone);
    itemIndex++;
}

function removeItem(row) {
    if (document.querySelectorAll('.invoice-item').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('{{ __("invoices.at_least_one_item") }}');
    }
}

function calculateItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex].dataset.rate) || 0;
    
    const subtotal = quantity * price;
    const vat = subtotal * (vatRate / 100);
    const total = subtotal + vat;
    
    row.querySelector('.item-total').value = total.toFixed(2).replace('.', ',') + currency;
    calculateTotals();
}

function calculateTotals() {
    // Check if this is a retrocession invoice
    const isRetrocession = checkIfRetrocession();
    
    if (isRetrocession) {
        calculateRetrocessionTotals();
    } else {
        calculateStandardTotals();
    }
}

function calculateStandardTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    document.querySelectorAll('.invoice-item').forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const vatSelect = row.querySelector('.item-vat');
        const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex].dataset.rate) || 0;
        
        const itemSubtotal = quantity * price;
        const itemVat = itemSubtotal * (vatRate / 100);
        
        subtotal += itemSubtotal;
        vatAmount += itemVat;
    });
    
    const discount = {{ invoice.discount_amount|default(0) }};
    const secretaryFee = parseFloat(document.getElementById('secretary_fee_amount').value) || 0;
    const total = subtotal + vatAmount - discount + secretaryFee;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2).replace('.', ',') + currency;
    document.getElementById('vatAmount').textContent = vatAmount.toFixed(2).replace('.', ',') + currency;
    document.getElementById('total').textContent = total.toFixed(2).replace('.', ',') + currency;
    
    // Check for duplicate descriptions
    checkDuplicateDescriptions();
}

function calculateRetrocessionTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    document.querySelectorAll('.invoice-item').forEach(row => {
        const priceInput = row.querySelector('.item-price');
        const vatSelect = row.querySelector('.item-vat');
        const price = parseFloat(priceInput.value) || 0;
        const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex].dataset.rate) || 0;
        
        const itemSubtotal = price; // For retrocession, quantity is always 1
        const itemVat = itemSubtotal * (vatRate / 100);
        const itemTotal = itemSubtotal + itemVat;
        
        subtotal += itemSubtotal;
        vatAmount += itemVat;
        
        // Update the total display for this row
        const totalInput = row.querySelector('.item-total');
        if (totalInput) {
            totalInput.value = itemTotal.toFixed(2).replace('.', ',') + ' €';
        }
    });
    
    const discount = {{ invoice.discount_amount|default(0) }};
    const total = subtotal + vatAmount - discount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2).replace('.', ',') + ' €';
    document.getElementById('vatAmount').textContent = vatAmount.toFixed(2).replace('.', ',') + ' €';
    document.getElementById('total').textContent = total.toFixed(2).replace('.', ',') + ' €';
}

function checkDuplicateDescriptions() {
    const descriptions = [];
    const descriptionElements = document.querySelectorAll('.item-description');
    
    // Reset all warnings
    descriptionElements.forEach(input => {
        input.classList.remove('border-warning');
        const existingWarning = input.parentElement.querySelector('.duplicate-warning');
        if (existingWarning) {
            existingWarning.remove();
        }
    });
    
    // Collect all descriptions
    descriptionElements.forEach(input => {
        if (input.value.trim()) {
            descriptions.push({
                element: input,
                value: input.value.trim()
            });
        }
    });
    
    // Find duplicates
    const descriptionCounts = {};
    descriptions.forEach(item => {
        descriptionCounts[item.value] = (descriptionCounts[item.value] || 0) + 1;
    });
    
    // Mark duplicates
    descriptions.forEach(item => {
        if (descriptionCounts[item.value] > 1) {
            item.element.classList.add('border-warning');
            if (!item.element.parentElement.querySelector('.duplicate-warning')) {
                const warning = document.createElement('small');
                warning.className = 'text-warning duplicate-warning';
                warning.textContent = '{{ __("invoices.duplicate_description_warning") | default("This description appears multiple times") }}';
                item.element.parentElement.appendChild(warning);
            }
        }
    });
}


function confirmDelete() {
    if (confirm('{{ __("invoices.delete_confirm") }}')) {
        document.getElementById('deleteForm').submit();
    }
}

function updateDueDate() {
    const paymentTermSelect = document.getElementById('payment_term_id');
    const issueDate = document.getElementById('issue_date').value;
    const dueDateInput = document.getElementById('due_date');
    
    if (!paymentTermSelect.value || !issueDate) {
        return;
    }
    
    const selectedOption = paymentTermSelect.options[paymentTermSelect.selectedIndex];
    const days = parseInt(selectedOption.getAttribute('data-days')) || 0;
    
    // Calculate due date
    const date = new Date(issueDate);
    date.setDate(date.getDate() + days);
    
    // Format as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}`;
}

// Search products
function searchProducts(term) {
    fetch(`{{ base_url }}/api/products/search?term=${encodeURIComponent(term)}`, {
        credentials: 'same-origin',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Session expired. Please refresh the page and log in again.');
                }
                throw new Error('Network response was not ok');
            }
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Response is not JSON');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayProductSearchResults(data.items);
            } else if (data.error === 'Unauthorized') {
                alert('Your session has expired. Please refresh the page and log in again.');
            }
        })
        .catch(error => {
            console.error('Error searching products:', error);
            displayProductSearchResults([]);
            if (error.message.includes('Session expired')) {
                alert(error.message);
            }
        });
}

// Display product search results
function displayProductSearchResults(products) {
    const container = document.getElementById('productSearchResults');
    
    if (products.length === 0) {
        container.innerHTML = '<div class="p-3 text-muted text-center">{{ __("products.no_products_found") | default("No products found") }}</div>';
        return;
    }
    
    container.innerHTML = products.map(product => `
        <div class="list-group-item list-group-item-action" style="cursor: pointer;" onclick="addProductToInvoice(${JSON.stringify(product).replace(/"/g, '&quot;')})">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${product.name}</strong>
                    <span class="text-muted">(${product.code})</span>
                    <br>
                    <small class="text-muted">${product.category || ''}</small>
                </div>
                <div class="text-end">
                    <div class="fw-bold">${currency}${product.price_formatted}</div>
                    <small class="text-muted">{{ __("common.vat") }}: ${product.vat_rate}%</small>
                </div>
            </div>
        </div>
    `).join('');
}

// Add product to invoice
function addProductToInvoice(product) {
    // Add a new row with product data
    const template = document.getElementById('itemTemplate');
    const clone = template.content.cloneNode(true);
    
    // Replace INDEX with actual index
    clone.querySelectorAll('[name*="INDEX"]').forEach(element => {
        element.name = element.name.replace('INDEX', itemIndex);
    });
    
    // Set product data
    const row = clone.querySelector('tr');
    row.querySelector('.item-id').value = product.id;
    row.querySelector('.item-description').value = product.name;
    row.querySelector('.item-quantity').value = 1;
    row.querySelector('.item-price').value = product.price;
    
    // Set VAT rate
    const vatSelect = row.querySelector('.item-vat');
    const vatOption = Array.from(vatSelect.options).find(opt => 
        parseFloat(opt.dataset.rate) === parseFloat(product.vat_rate)
    );
    if (vatOption) {
        vatSelect.value = vatOption.value;
    }
    
    // Add event listeners
    row.querySelector('.item-quantity').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-price').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-vat').addEventListener('change', () => calculateItemTotal(row));
    row.querySelector('.remove-item').addEventListener('click', () => removeItem(row));
    
    document.getElementById('itemsBody').appendChild(clone);
    itemIndex++;
    
    // Calculate totals
    calculateItemTotal(document.getElementById('itemsBody').lastElementChild);
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('productSearchModal')).hide();
    document.getElementById('productSearchInput').value = '';
    document.getElementById('productSearchResults').innerHTML = '';
}

// Generate intelligent product code
function generateProductCode(description) {
    let codeBase = '';
    
    // Clean and split description into words
    const cleanDesc = description.trim();
    const words = cleanDesc.split(/[\s\-\_]+/).filter(w => w.length > 0);
    
    if (words.length >= 2) {
        // Multi-word: use first letter of each significant word
        codeBase = words
            .filter(w => w.length > 2 || words.length <= 3) // Skip small words unless only few words
            .map(w => w.charAt(0))
            .join('')
            .substring(0, 4)
            .toUpperCase();
    } else if (words.length === 1) {
        // Single word: use first 3-4 characters
        const word = words[0].replace(/[^a-zA-Z0-9]/g, '');
        if (word.length >= 6) {
            // Long word: try to find consonants for abbreviation
            codeBase = (word.charAt(0) + word.replace(/[aeiouAEIOU]/g, '').substring(1, 4)).toUpperCase();
        } else {
            codeBase = word.substring(0, 4).toUpperCase();
        }
    }
    
    // Ensure minimum length
    if (codeBase.length < 3) {
        codeBase = codeBase.padEnd(3, 'X');
    }
    
    // Add numeric suffix - sequential based on timestamp for better uniqueness
    const timestamp = Date.now();
    const suffix = (timestamp % 1000).toString().padStart(3, '0');
    
    return codeBase + suffix;
}

// Check product code uniqueness and suggest alternatives
function checkAndSetProductCode(code) {
    const codeInput = document.getElementById('productCode');
    codeInput.value = code;
    
    // Skip API call if code is empty
    if (!code.trim()) {
        const existingHelp = document.getElementById('codeHelp');
        if (existingHelp) existingHelp.remove();
        return;
    }
    
    // Add a small delay to avoid too many API calls
    clearTimeout(window.codeCheckTimeout);
    window.codeCheckTimeout = setTimeout(() => {
        // For now, just skip the API call since it's having issues
        // We'll let the server validate on save
        console.log('Skipping code check API call for now');
        
        // Remove any existing help text
        const existingHelp = document.getElementById('codeHelp');
        if (existingHelp) existingHelp.remove();
    }, 300);
}

// Save line as product
function saveLineAsProduct(row) {
    // Get line data
    const description = row.querySelector('.item-description').value;
    const price = row.querySelector('.item-price').value;
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = vatSelect.options[vatSelect.selectedIndex].dataset.rate;
    const vatRateId = vatSelect.value;
    
    // Generate intelligent product code
    const suggestedCode = generateProductCode(description);
    
    // Populate modal
    document.getElementById('productName').value = description;
    document.getElementById('productPrice').value = price;
    
    // Set VAT rate
    const productVatSelect = document.getElementById('productVatRate');
    for (let option of productVatSelect.options) {
        if (option.value == vatRateId) {
            option.selected = true;
            break;
        }
    }
    
    // Store row reference for later
    window.currentInvoiceRow = row;
    
    // Check code uniqueness and set it
    checkAndSetProductCode(suggestedCode);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('saveProductModal'));
    modal.show();
}

// Initialize save product functionality
function initSaveProductModal() {
    try {
        // Save product button
        const saveBtn = document.getElementById('saveProductBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
            const btn = this;
            const originalText = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}...';
            
            const data = new FormData();
            data.append('code', document.getElementById('productCode').value);
            data.append('name', document.getElementById('productName').value);
            data.append('category_id', document.getElementById('productCategory').value);
            data.append('item_type', document.getElementById('productType').value);
            data.append('unit_price', document.getElementById('productPrice').value);
            data.append('vat_rate_id', document.getElementById('productVatRate').value);
            data.append('is_stockable', document.getElementById('productStockable').checked ? 1 : 0);
            data.append('is_active', 1);
            
            fetch('{{ base_url }}/api/products/quick-create', {
                method: 'POST',
                body: data,
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Save product response status:', response.status);
                console.log('Save product response headers:', response.headers.get('content-type'));
                
                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('Session expired. Please refresh the page and log in again.');
                    }
                    throw new Error('Network response was not ok: ' + response.status);
                }
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    // Try to get the response text to see what we're getting
                    return response.text().then(text => {
                        console.error('Non-JSON response:', text.substring(0, 500));
                        throw new Error('Response is not JSON - Session may have expired');
                    });
                }
                return response.json();
            })
            .then(data => {
                btn.disabled = false;
                btn.innerHTML = originalText;
                
                if (data.success) {
                    // Update the row with product ID
                    if (window.currentInvoiceRow) {
                        const itemIdInput = window.currentInvoiceRow.querySelector('.item-id');
                        if (itemIdInput) {
                            itemIdInput.value = data.product.id;
                        }
                        
                        // Hide the save button
                        const saveBtn = window.currentInvoiceRow.querySelector('.save-to-product');
                        if (saveBtn) {
                            saveBtn.style.display = 'none';
                        }
                    }
                    
                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById('saveProductModal')).hide();
                    
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: '{{ __("products.product_created") | default("Product created successfully") }}',
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else if (data.error === 'Unauthorized') {
                    Swal.fire({
                        icon: 'error',
                        title: '{{ __("common.session_expired") | default("Session Expired") }}',
                        text: '{{ __("common.please_login_again") | default("Please refresh the page and log in again.") }}'
                    });
                } else {
                    // Show error
                    let errorMsg = data.message || '{{ __("common.error_occurred") }}';
                    if (data.errors) {
                        errorMsg = Object.values(data.errors).join('<br>');
                    }
                    
                    Swal.fire({
                        icon: 'error',
                        title: '{{ __("common.error") }}',
                        html: errorMsg
                    });
                }
            })
            .catch(error => {
                btn.disabled = false;
                btn.innerHTML = originalText;
                console.error('Error:', error);
                
                if (error.message.includes('Session expired') || error.message.includes('not JSON')) {
                    Swal.fire({
                        icon: 'error',
                        title: '{{ __("common.session_expired") | default("Session Expired") }}',
                        text: error.message
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '{{ __("common.error") }}',
                        text: '{{ __("common.error_occurred") }}'
                    });
                }
            });
        });
    }
    
    // Check code on manual input
    const codeInput = document.getElementById('productCode');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            const code = this.value.trim();
            if (code) {
                checkAndSetProductCode(code);
            }
        });
    }
    } catch (error) {
        console.error('Error initializing save product modal:', error);
    }
}

// Delete confirmation
function confirmDelete() {
    Swal.fire({
        title: '{{ __("common.are_you_sure") }}',
        text: '{{ __("invoices.delete_warning") }}',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.delete") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            // Submit the hidden delete form
            document.getElementById('deleteForm').submit();
        }
    });
}

// Delete confirmation for sent invoices (admin only)
function confirmDeleteSentInvoice() {
    Swal.fire({
        title: '{{ __("common.are_you_sure") }}',
        text: '{{ __("invoices.admin_delete_sent_warning") | default("Warning: You are about to delete a sent invoice. This action cannot be undone and may affect accounting records.") }}',
        icon: 'error',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.yes_delete") | default("Yes, Delete It") }}',
        cancelButtonText: '{{ __("common.cancel") }}',
        footer: '<p class="text-danger mb-0">{{ __("invoices.admin_only_action") | default("This is an admin-only action") }}</p>'
    }).then((result) => {
        if (result.isConfirmed) {
            // Submit the hidden delete form
            document.getElementById('deleteForm').submit();
        }
    });
}

// Retrocession-specific functions
function checkIfRetrocession() {
    const invoiceTypeSelect = document.getElementById('type_id');
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    
    // Also check invoice number pattern
    const invoiceNumber = document.getElementById('invoice_number').value;
    
    return typeName.includes('rétrocession') || 
           typeName.includes('retrocession') ||
           invoiceNumber.includes('RET30') ||
           invoiceNumber.includes('RET25');
}

function initializeRetrocessionHandlers() {
    const cnsInput = document.getElementById('cns_amount');
    const patientInput = document.getElementById('patient_amount');
    
    if (cnsInput) {
        cnsInput.addEventListener('input', updateRetrocessionCalculations);
    }
    if (patientInput) {
        patientInput.addEventListener('input', updateRetrocessionCalculations);
    }
}

function updateRetrocessionCalculations() {
    const cnsInput = document.getElementById('cns_amount');
    const patientInput = document.getElementById('patient_amount');
    
    const cnsAmount = parseFloat(cnsInput?.value || 0);
    const patientAmount = parseFloat(patientInput?.value || 0);
    
    // Calculate secretary fee (10% of CNS + Patient amounts)
    const secretaryBase = cnsAmount + patientAmount;
    const secretaryFee = secretaryBase * 0.10;
    
    // Update secretary display with Euro symbol first
    const secretaryTotalInput = document.getElementById('secretary_total');
    if (secretaryTotalInput) {
        secretaryTotalInput.value = '€' + secretaryBase.toFixed(2).replace('.', ',');
    }
    
    // Update unit prices in hidden fields
    document.querySelectorAll('.invoice-item').forEach(row => {
        const lineType = row.dataset.lineType;
        const priceInput = row.querySelector('.item-price');
        
        if (lineType === 'cns' && cnsAmount > 0) {
            // CNS line: unit_price = base * 0.20
            priceInput.value = (cnsAmount * 0.20).toFixed(2);
        } else if (lineType === 'patient' && patientAmount > 0) {
            // Patient line: unit_price = base * 0.20
            priceInput.value = (patientAmount * 0.20).toFixed(2);
        } else if (lineType === 'secretary' && secretaryFee > 0) {
            // Secretary line: unit_price = base * 0.10
            priceInput.value = secretaryFee.toFixed(2);
        }
    });
    
    // Recalculate totals
    calculateTotals();
}
</script>

<!-- Save Product Modal -->
<div class="modal fade" id="saveProductModal" tabindex="-1" aria-labelledby="saveProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="saveProductModalLabel">{{ __('products.save_as_product')|default('Enregistrer comme produit') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="productCode" class="form-label">{{ __('products.code') }} *</label>
                    <input type="text" class="form-control" id="productCode" required>
                </div>
                
                <div class="mb-3">
                    <label for="productName" class="form-label">{{ __('products.name') }} *</label>
                    <input type="text" class="form-control" id="productName" required>
                </div>
                
                <div class="mb-3">
                    <label for="productCategory" class="form-label">{{ __('products.category') }} *</label>
                    <select class="form-select" id="productCategory" required>
                        <option value="">{{ __('common.select') }}</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {{ category.name == 'Services' ? 'selected' : '' }}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="productType" class="form-label">{{ __('products.type') }}</label>
                    <select class="form-select" id="productType">
                        <option value="service" selected>{{ __('products.service') }}</option>
                        <option value="product">{{ __('products.product') }}</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="productPrice" class="form-label">{{ __('products.price') }} *</label>
                    <input type="number" class="form-control" id="productPrice" step="0.01" required>
                </div>
                
                <div class="mb-3">
                    <label for="productVatRate" class="form-label">{{ __('products.vat_rate') }}</label>
                    <select class="form-select" id="productVatRate">
                        {% for vat in vat_rates %}
                            <option value="{{ vat.id }}">
                                {{ vat.rate|number_format(vat.rate == 0 ? 0 : (vat.rate == vat.rate|round ? 0 : 2), ',', '.') }} %
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="productStockable">
                    <label class="form-check-label" for="productStockable">
                        {{ __('products.is_stockable')|default('Track stock for this product') }}
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                <button type="button" class="btn btn-primary" id="saveProductBtn">{{ __('common.save') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}