<?php
/**
 * Check Database Email Columns
 * This script checks what email-related columns actually exist in the database
 */

require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Core\Database;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Database Email Columns</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Database Email Columns Check</h1>
        
        <?php
        try {
            $db = Database::getInstance()->getConnection();
            
            // Check users table columns
            echo '<h2 class="mt-4">1. Users Table Columns</h2>';
            $stmt = $db->prepare("SHOW COLUMNS FROM users WHERE Field LIKE '%email%' OR Field IN ('id', 'first_name', 'last_name')");
            $stmt->execute();
            $userColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($userColumns) {
                echo '<table class="table table-bordered">';
                echo '<thead><tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr></thead>';
                echo '<tbody>';
                foreach ($userColumns as $col) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($col['Field']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Type']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Null']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            }
            
            // Check clients table columns
            echo '<h2 class="mt-4">2. Clients Table Columns</h2>';
            $stmt = $db->prepare("SHOW COLUMNS FROM clients WHERE Field LIKE '%email%' OR Field IN ('id', 'first_name', 'last_name', 'company_name')");
            $stmt->execute();
            $clientColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($clientColumns) {
                echo '<table class="table table-bordered">';
                echo '<thead><tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr></thead>';
                echo '<tbody>';
                foreach ($clientColumns as $col) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($col['Field']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Type']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Null']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            }
            
            // Check invoices table structure
            echo '<h2 class="mt-4">3. Invoices Table Structure (related to users/clients)</h2>';
            $stmt = $db->prepare("SHOW COLUMNS FROM invoices WHERE Field IN ('id', 'invoice_number', 'client_id', 'user_id', 'billable_type', 'billable_id', 'sent_at')");
            $stmt->execute();
            $invoiceColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($invoiceColumns) {
                echo '<table class="table table-bordered">';
                echo '<thead><tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr></thead>';
                echo '<tbody>';
                foreach ($invoiceColumns as $col) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($col['Field']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Type']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Null']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            }
            
            // Check email_logs table
            echo '<h2 class="mt-4">4. Email Logs Table</h2>';
            $stmt = $db->prepare("SHOW COLUMNS FROM email_logs");
            $stmt->execute();
            $emailLogColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($emailLogColumns) {
                echo '<table class="table table-bordered">';
                echo '<thead><tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr></thead>';
                echo '<tbody>';
                foreach ($emailLogColumns as $col) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($col['Field']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Type']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Null']) . '</td>';
                    echo '<td>' . htmlspecialchars($col['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            } else {
                echo '<div class="alert alert-warning">Email logs table not found. You may need to run migration 083.</div>';
            }
            
            // Sample query to show how to get invoice emails
            echo '<h2 class="mt-4">5. Sample Invoice with Email</h2>';
            $sampleQuery = "
                SELECT 
                    i.id,
                    i.invoice_number,
                    i.client_id,
                    i.user_id,
                    i.billable_type,
                    i.billable_id,
                    CASE 
                        WHEN i.client_id IS NOT NULL THEN c.email
                        WHEN i.user_id IS NOT NULL THEN COALESCE(u.billing_email, u.email)
                        ELSE NULL
                    END as recipient_email,
                    CASE 
                        WHEN i.client_id IS NOT NULL THEN CONCAT(c.first_name, ' ', c.last_name)
                        WHEN i.user_id IS NOT NULL THEN CONCAT(u.first_name, ' ', u.last_name)
                        ELSE NULL
                    END as recipient_name
                FROM invoices i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN users u ON i.user_id = u.id
                WHERE i.invoice_number = 'FAC-DIV-2025-0190'
            ";
            
            echo '<pre class="bg-light p-3">' . htmlspecialchars($sampleQuery) . '</pre>';
            
            $stmt = $db->prepare($sampleQuery);
            $stmt->execute();
            $sample = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($sample) {
                echo '<h3>Result:</h3>';
                echo '<pre class="bg-light p-3">' . print_r($sample, true) . '</pre>';
            } else {
                echo '<div class="alert alert-info">Invoice FAC-DIV-2025-0190 not found in database.</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<strong>Error:</strong> ' . htmlspecialchars($e->getMessage());
            echo '</div>';
        }
        ?>
        
        <div class="mt-5">
            <a href="/fit/public" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
</body>
</html>