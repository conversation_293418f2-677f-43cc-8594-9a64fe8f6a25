<?php
// Check Invoice Columns Script
require_once __DIR__ . '/../app/config/bootstrap.php';

// Initialize output
header('Content-Type: text/html; charset=UTF-8');

try {
    // Get database connection
    $db = Flight::db();
    
    echo "<h1>Invoice Table Structure</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffffcc; }
        .error { color: red; }
        .success { color: green; }
    </style>";
    
    // Check DESCRIBE command
    echo "<h2>Table Columns (DESCRIBE invoices)</h2>";
    $stmt = $db->prepare("DESCRIBE invoices");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasPatientId = false;
    $hasBillableColumns = false;
    
    foreach ($columns as $column) {
        $highlight = '';
        if (strpos($column['Field'], 'patient') !== false) {
            $highlight = 'highlight';
            $hasPatientId = true;
        }
        if (strpos($column['Field'], 'billable') !== false) {
            $highlight = 'highlight';
            $hasBillableColumns = true;
        }
        if (in_array($column['Field'], ['client_id', 'user_id'])) {
            $highlight = 'highlight';
        }
        
        echo "<tr class='$highlight'>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Summary
    echo "<h2>Summary</h2>";
    echo "<ul>";
    echo "<li>Total columns: " . count($columns) . "</li>";
    echo "<li>Has patient_id column: " . ($hasPatientId ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</li>";
    echo "<li>Has billable columns: " . ($hasBillableColumns ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</li>";
    echo "</ul>";
    
    // Check a sample invoice
    echo "<h2>Sample Invoice Data (ID: 246)</h2>";
    $stmt = $db->prepare("SELECT * FROM invoices WHERE id = 246");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<table>";
        echo "<tr><th>Column</th><th>Value</th></tr>";
        foreach ($invoice as $key => $value) {
            $highlight = '';
            if (strpos($key, 'patient') !== false || strpos($key, 'billable') !== false || in_array($key, ['client_id', 'user_id'])) {
                $highlight = 'highlight';
            }
            echo "<tr class='$highlight'>";
            echo "<td>$key</td>";
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>Invoice ID 246 not found.</p>";
    }
    
    // Check for related tables
    echo "<h2>Related Tables Check</h2>";
    
    // Check if patients table exists
    $stmt = $db->prepare("SHOW TABLES LIKE 'patients'");
    $stmt->execute();
    $patientsTableExists = $stmt->fetch() !== false;
    echo "<p>Patients table exists: " . ($patientsTableExists ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
    
    // Show foreign key constraints
    echo "<h3>Foreign Key Constraints</h3>";
    $stmt = $db->prepare("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE 
            TABLE_NAME = 'invoices' 
            AND TABLE_SCHEMA = DATABASE()
            AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $stmt->execute();
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($constraints) > 0) {
        echo "<table>";
        echo "<tr><th>Constraint Name</th><th>Column</th><th>References Table</th><th>References Column</th></tr>";
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
            echo "<td>{$constraint['COLUMN_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No foreign key constraints found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='/fit/public/'>Back to Application</a></p>";
?>