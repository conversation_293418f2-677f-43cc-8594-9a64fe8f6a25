<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user ID from parameter
    $userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
    
    if (!$userId) {
        echo "Please provide user_id parameter\n";
        exit;
    }
    
    echo "<h2>Retrocession Settings for User ID: $userId</h2>";
    
    // Check existing active settings
    $sql = "SELECT * FROM user_retrocession_settings 
            WHERE user_id = :user_id 
            AND is_active = 1
            ORDER BY valid_from DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute(['user_id' => $userId]);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($settings)) {
        echo "<p>No active retrocession settings found for this user.</p>";
    } else {
        echo "<h3>Active Settings:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Valid From</th><th>Valid To</th><th>CNS</th><th>Patient</th><th>Secretary</th><th>Created</th></tr>";
        
        foreach ($settings as $setting) {
            echo "<tr>";
            echo "<td>{$setting['id']}</td>";
            echo "<td>{$setting['valid_from']}</td>";
            echo "<td>" . ($setting['valid_to'] ?: 'No end date') . "</td>";
            echo "<td>{$setting['cns_value']}%</td>";
            echo "<td>{$setting['patient_value']}%</td>";
            echo "<td>{$setting['secretary_value']}%</td>";
            echo "<td>{$setting['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Option to deactivate all existing settings
        if (isset($_GET['deactivate']) && $_GET['deactivate'] == '1') {
            echo "<h3>Deactivating all existing settings...</h3>";
            $updateSql = "UPDATE user_retrocession_settings 
                         SET is_active = 0 
                         WHERE user_id = :user_id 
                         AND is_active = 1";
            $updateStmt = $db->prepare($updateSql);
            $updateStmt->execute(['user_id' => $userId]);
            $affected = $updateStmt->rowCount();
            echo "<p>Deactivated $affected settings.</p>";
        } else {
            echo "<p><a href='?user_id=$userId&deactivate=1'>Click here to deactivate all existing settings</a></p>";
        }
    }
    
    // Check if table exists
    $tableCheck = "SHOW TABLES LIKE 'user_retrocession_settings'";
    $tableStmt = $db->query($tableCheck);
    if ($tableStmt->rowCount() == 0) {
        echo "<p style='color: red;'>Warning: user_retrocession_settings table does not exist!</p>";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>