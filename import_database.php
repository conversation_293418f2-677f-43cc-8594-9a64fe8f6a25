<?php
/**
 * Database Import Script with Foreign Key Fix
 * Handles the fitapp(5).sql import with foreign key constraint issues
 */

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Detect if running on Windows or WSL
$sqlFile = PHP_OS_FAMILY === 'Windows' 
    ? 'C:\\Users\\<USER>\\Downloads\\fitapp(5).sql'
    : '/mnt/c/Users/<USER>/Downloads/fitapp(5).sql';
$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp'; // Target database
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

echo "=== Database Import Script ===\n\n";

try {
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    echo "Creating database if not exists...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$dbname`");
    
    // Disable foreign key checks
    echo "Disabling foreign key checks...\n";
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // Read and execute SQL file
    echo "Importing SQL file: $sqlFile\n";
    echo "This may take a few minutes...\n";
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("Cannot read SQL file: $sqlFile");
    }
    
    // Split by delimiter and execute
    $queries = preg_split('/;\s*$/m', $sql);
    $totalQueries = count($queries);
    $executed = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query)) {
            try {
                $pdo->exec($query);
                $executed++;
                
                // Show progress
                if ($executed % 100 == 0) {
                    echo "Progress: $executed/$totalQueries queries executed...\n";
                }
            } catch (PDOException $e) {
                // Log error but continue
                echo "Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\nImport completed: $executed queries executed\n";
    
    // Add missing payment terms
    echo "\nAdding missing payment terms...\n";
    $pdo->exec("
        INSERT IGNORE INTO `config_payment_terms` (`id`, `name`, `code`, `days`, `description`, `is_default`, `is_active`, `sort_order`) VALUES
        (1, '{\"fr\":\"Dès réception\",\"en\":\"Due on receipt\",\"de\":\"Sofort fällig\"}', 'immediate', 0, '{\"fr\":\"Paiement dès réception de la facture\",\"en\":\"Payment due upon receipt of invoice\",\"de\":\"Zahlung sofort nach Rechnungserhalt\"}', 1, 1, 0)
    ");
    
    // Re-enable foreign key checks
    echo "Re-enabling foreign key checks...\n";
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Verify import
    echo "\n=== Import Verification ===\n";
    
    $tables = ['invoices', 'config_payment_terms', 'users', 'clients'];
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            echo "Table $table: $count records\n";
        } catch (PDOException $e) {
            echo "Table $table: Not found or error\n";
        }
    }
    
    echo "\n✓ Import completed successfully!\n";
    
} catch (Exception $e) {
    echo "\n✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}