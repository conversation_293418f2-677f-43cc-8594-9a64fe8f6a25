<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing RET30 Duplicate Issue</h2>";
    
    // First, check what's using codes starting with 'ret'
    echo "<h3>Existing entries with 'ret' codes:</h3>";
    $stmt = $db->query("SELECT id, code, prefix, name FROM config_invoice_types WHERE code LIKE 'ret%' ORDER BY id");
    $existing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Action</th></tr>";
    foreach ($existing as $row) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['code']}</td>";
        echo "<td>{$row['prefix']}</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>";
        
        // Identify problematic entries
        if ($row['code'] == 'retr' && $row['prefix'] == 'RET30') {
            echo "Update to 'ret30'";
        } elseif ($row['code'] == 'retrocession_5' && $row['prefix'] == 'RET25') {
            echo "Update to 'ret25'";
        } elseif ($row['code'] == 'retrocession_10' && $row['prefix'] == 'RET30') {
            echo "Update to 'ret30'";
        } else {
            echo "Keep";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Fix the codes
    echo "<h3>Applying fixes:</h3>";
    
    // Update entries with wrong codes
    $updates = [
        ['old_code' => 'retr', 'new_code' => 'ret30', 'prefix' => 'FAC-RET30'],
        ['old_code' => 'retrocession_5', 'new_code' => 'ret25', 'prefix' => 'FAC-RET25'],
        ['old_code' => 'retrocession_10', 'new_code' => 'ret30', 'prefix' => 'FAC-RET30']
    ];
    
    foreach ($updates as $update) {
        $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = :code");
        $stmt->execute(['code' => $update['old_code']]);
        if ($row = $stmt->fetch()) {
            // Check if target code already exists
            $checkStmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = :code AND id != :id");
            $checkStmt->execute(['code' => $update['new_code'], 'id' => $row['id']]);
            
            if ($checkStmt->fetch()) {
                // Target exists, delete this duplicate
                $deleteStmt = $db->prepare("DELETE FROM config_invoice_types WHERE id = :id");
                $deleteStmt->execute(['id' => $row['id']]);
                echo "<p style='color: orange;'>Deleted duplicate entry with code '{$update['old_code']}'</p>";
            } else {
                // Update to new code
                $updateStmt = $db->prepare("
                    UPDATE config_invoice_types 
                    SET code = :new_code, prefix = :prefix 
                    WHERE id = :id
                ");
                $updateStmt->execute([
                    'new_code' => $update['new_code'],
                    'prefix' => $update['prefix'],
                    'id' => $row['id']
                ]);
                echo "<p style='color: green;'>✓ Updated code from '{$update['old_code']}' to '{$update['new_code']}'</p>";
            }
        }
    }
    
    // Ensure we have RET25 and RET30 with correct settings
    $requiredTypes = [
        [
            'code' => 'ret25',
            'prefix' => 'FAC-RET25',
            'name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%']),
            'color' => '#17a2b8'
        ],
        [
            'code' => 'ret30',
            'prefix' => 'FAC-RET30',
            'name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%']),
            'color' => '#17a2b8'
        ]
    ];
    
    echo "<h3>Ensuring required types exist:</h3>";
    foreach ($requiredTypes as $type) {
        $stmt = $db->prepare("SELECT id, prefix FROM config_invoice_types WHERE code = :code");
        $stmt->execute(['code' => $type['code']]);
        
        if ($existing = $stmt->fetch()) {
            // Update prefix if needed
            if ($existing['prefix'] !== $type['prefix']) {
                $updateStmt = $db->prepare("
                    UPDATE config_invoice_types 
                    SET prefix = :prefix, name = :name 
                    WHERE code = :code
                ");
                $updateStmt->execute([
                    'prefix' => $type['prefix'],
                    'name' => $type['name'],
                    'code' => $type['code']
                ]);
                echo "<p style='color: green;'>✓ Updated {$type['code']} prefix to {$type['prefix']}</p>";
            } else {
                echo "<p>✓ {$type['code']} already configured correctly</p>";
            }
        } else {
            // Create new
            $insertStmt = $db->prepare("
                INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
                VALUES (:code, :prefix, :name, :color, 1)
            ");
            $insertStmt->execute($type);
            echo "<p style='color: green;'>✓ Created {$type['code']} with prefix {$type['prefix']}</p>";
        }
    }
    
    // Show final state
    echo "<h3>Final state of RET types:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code IN ('ret', 'ret25', 'ret30') ORDER BY code");
    $final = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Code</th><th>Prefix</th><th>Name</th><th>Active</th></tr>";
    foreach ($final as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        echo "<tr>";
        echo "<td>{$row['code']}</td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<p><strong>Done!</strong> The system is now configured to use:</p>";
    echo "<ul>";
    echo "<li>FAC-RET25-2025-XXXX for 5% secretary fee</li>";
    echo "<li>FAC-RET30-2025-XXXX for 10% secretary fee</li>";
    echo "</ul>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession'>Test Bulk Generation</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}