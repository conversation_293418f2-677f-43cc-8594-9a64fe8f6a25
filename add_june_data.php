<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Add June 2025 Data for Retrocession Invoice Generation</h2>\n";
    
    // Check current data
    echo "<h3>Current Monthly Data for Frank Huet:</h3>\n";
    $stmt = $pdo->prepare("
        SELECT month, year, cns_amount, patient_amount 
        FROM user_monthly_retrocession_amounts 
        WHERE user_id = 1 AND year = 2025
        ORDER BY month
    ");
    $stmt->execute();
    
    echo "<table border='1'>\n";
    echo "<tr><th>Month</th><th>Year</th><th>CNS</th><th>Patient</th></tr>\n";
    while ($row = $stmt->fetch()) {
        $monthName = date('F', mktime(0, 0, 0, $row['month'], 1));
        echo "<tr><td>{$monthName}</td><td>{$row['year']}</td><td>{$row['cns_amount']}€</td><td>{$row['patient_amount']}€</td></tr>\n";
    }
    echo "</table>\n";
    
    // Check if June 2025 exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM user_monthly_retrocession_amounts 
        WHERE user_id = 1 AND month = 6 AND year = 2025
    ");
    $stmt->execute();
    $exists = $stmt->fetchColumn() > 0;
    
    if ($exists) {
        echo "\n<p>✓ June 2025 data already exists for Frank Huet</p>\n";
    } else {
        echo "\n<p>⚠️ June 2025 data is missing</p>\n";
        echo "<p><a href='?action=copy_from_july'>Copy July 2025 data to June 2025</a> | ";
        echo "<a href='?action=add_default'>Add default amounts (4500€ CNS, 5000€ Patient)</a></p>\n";
    }
    
    // Handle actions
    if (isset($_GET['action'])) {
        if ($_GET['action'] == 'copy_from_july') {
            // Copy July data to June
            $stmt = $pdo->prepare("
                INSERT INTO user_monthly_retrocession_amounts 
                (user_id, month, year, cns_amount, patient_amount, is_active, created_by, created_at)
                SELECT user_id, 6, year, cns_amount, patient_amount, is_active, created_by, NOW()
                FROM user_monthly_retrocession_amounts
                WHERE user_id = 1 AND month = 7 AND year = 2025
            ");
            $stmt->execute();
            echo "\n<p style='color: green;'>✓ Successfully copied July 2025 data to June 2025</p>\n";
            
        } elseif ($_GET['action'] == 'add_default') {
            // Add default amounts
            $stmt = $pdo->prepare("
                INSERT INTO user_monthly_retrocession_amounts 
                (user_id, month, year, cns_amount, patient_amount, is_active, created_by, created_at)
                VALUES (1, 6, 2025, 4500.00, 5000.00, 1, 1, NOW())
            ");
            $stmt->execute();
            echo "\n<p style='color: green;'>✓ Successfully added June 2025 data with default amounts</p>\n";
        }
        
        echo "<p><a href='add_june_data.php'>Refresh</a></p>\n";
    }
    
    // Add data for other users too
    echo "\n<h3>Other Users Needing June 2025 Data:</h3>\n";
    $stmt = $pdo->query("
        SELECT DISTINCT u.id, u.first_name, u.last_name
        FROM users u
        INNER JOIN user_monthly_retrocession_amounts uma ON u.id = uma.user_id
        WHERE uma.year = 2025 AND uma.month = 7
        AND NOT EXISTS (
            SELECT 1 FROM user_monthly_retrocession_amounts uma2
            WHERE uma2.user_id = u.id AND uma2.month = 6 AND uma2.year = 2025
        )
    ");
    
    $users = $stmt->fetchAll();
    if (count($users) > 0) {
        echo "<ul>\n";
        foreach ($users as $user) {
            echo "<li>{$user['first_name']} {$user['last_name']} (ID: {$user['id']}) - ";
            echo "<a href='?action=copy_user&user_id={$user['id']}'>Copy July→June data</a></li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>No other users need June 2025 data.</p>\n";
    }
    
    // Handle user copy
    if (isset($_GET['action']) && $_GET['action'] == 'copy_user' && isset($_GET['user_id'])) {
        $userId = intval($_GET['user_id']);
        $stmt = $pdo->prepare("
            INSERT INTO user_monthly_retrocession_amounts 
            (user_id, month, year, cns_amount, patient_amount, is_active, created_by, created_at)
            SELECT user_id, 6, year, cns_amount, patient_amount, is_active, created_by, NOW()
            FROM user_monthly_retrocession_amounts
            WHERE user_id = :user_id AND month = 7 AND year = 2025
        ");
        $stmt->execute(['user_id' => $userId]);
        echo "\n<p style='color: green;'>✓ Successfully copied data for user ID $userId</p>\n";
        echo "<p><a href='add_june_data.php'>Refresh</a></p>\n";
    }
    
    echo "\n<hr>\n";
    echo "<p><strong>After adding June data:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation'>Go to Bulk Invoice Generation</a></li>\n";
    echo "<li>Select July 2025 as the invoice month</li>\n";
    echo "<li>The system will use June 2025 data for retrocession calculations</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}