<?php
/**
 * Simple test for retrocession generation
 */

// Set up environment
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';
$_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';

// Start output buffering
ob_start();

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

// Simulate session
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['language'] = 'fr';

// Test parameters
$userId = 1;
$month = 7;
$year = 2025;

echo "=== Testing Retrocession Generation ===\n\n";

try {
    // Create a temporary input stream with JSON data
    $jsonData = json_encode([
        'month' => $month,
        'year' => $year
    ]);
    
    // Create a memory stream and write JSON data to it
    $stream = fopen('php://memory', 'r+');
    fwrite($stream, $jsonData);
    rewind($stream);
    
    // Replace the input stream temporarily
    $originalInput = fopen('php://input', 'r');
    stream_wrapper_unregister('php');
    stream_wrapper_register('php', 'App\TestPhpStreamWrapper');
    App\TestPhpStreamWrapper::setInputStream($stream);
    
    // Clear output buffer
    ob_end_clean();
    ob_start();
    
    // Call the controller directly
    $controller = new App\Controllers\UserController();
    $request = new App\Core\Request();
    $response = new App\Core\Response();
    
    $controller->generateRetrocession($request, $response, $userId);
    
    $output = ob_get_clean();
    
    echo "Raw output:\n";
    echo "---START---\n";
    echo $output;
    echo "\n---END---\n";
    
    // Try to parse as JSON
    $json = json_decode($output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "\nJSON parsed successfully:\n";
        print_r($json);
    } else {
        echo "\nJSON parse error: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

// Restore original stream wrapper
stream_wrapper_restore('php');

// Test stream wrapper class
namespace App;

class TestPhpStreamWrapper {
    private static $inputStream;
    private $position = 0;
    private $data = '';
    
    public static function setInputStream($stream) {
        self::$inputStream = $stream;
    }
    
    public function stream_open($path, $mode, $options, &$opened_path) {
        if ($path === 'php://input' && self::$inputStream) {
            $this->data = stream_get_contents(self::$inputStream);
            rewind(self::$inputStream);
            return true;
        }
        return false;
    }
    
    public function stream_read($count) {
        $result = substr($this->data, $this->position, $count);
        $this->position += strlen($result);
        return $result;
    }
    
    public function stream_eof() {
        return $this->position >= strlen($this->data);
    }
    
    public function stream_stat() {
        return [];
    }
    
    public function stream_tell() {
        return $this->position;
    }
}