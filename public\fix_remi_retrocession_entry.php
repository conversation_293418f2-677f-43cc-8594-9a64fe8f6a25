<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Rémi's Retrocession Entry</h2>";
    
    // Find Rémi
    $stmt = $db->prepare("
        SELECT u.id as user_id, u.first_name, u.last_name, c.id as client_id
        FROM users u
        LEFT JOIN clients c ON c.user_id = u.id
        WHERE u.first_name = '<PERSON><PERSON><PERSON>' AND u.last_name = 'Heine'
    ");
    $stmt->execute();
    $remi = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Ré<PERSON> Heine - User ID: {$remi['user_id']}, Client ID: {$remi['client_id']}</p>";
    
    // Get June data
    $stmt = $db->prepare("
        SELECT cns_amount, patient_amount 
        FROM user_monthly_retrocession_amounts 
        WHERE user_id = :user_id AND month = 6 AND year = 2025
    ");
    $stmt->execute(['user_id' => $remi['user_id']]);
    $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>June 2025 amounts: CNS = " . number_format($amounts['cns_amount'], 2) . " €</p>";
    
    // Get retrocession settings
    $stmt = $db->prepare("
        SELECT secretary_value 
        FROM user_retrocession_settings 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute(['user_id' => $remi['user_id']]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    $secretaryPercent = $settings['secretary_value'] ?? 10;
    
    echo "<p>Secretary fee: {$secretaryPercent}%</p>";
    
    // Calculate secretary amount
    $cnsRetrocession = $amounts['cns_amount'] * 0.20; // 20% retrocession
    $secretaryAmount = $cnsRetrocession * ($secretaryPercent / 100);
    
    echo "<p>CNS retrocession (20%): " . number_format($cnsRetrocession, 2) . " €</p>";
    echo "<p>Secretary fee ({$secretaryPercent}%): " . number_format($secretaryAmount, 2) . " €</p>";
    
    // Check if entry exists
    $stmt = $db->prepare("
        SELECT id, status, invoice_id 
        FROM retrocession_data_entry 
        WHERE practitioner_id = :client_id 
        AND period_year = 2025 
        AND period_month = 6
    ");
    $stmt->execute(['client_id' => $remi['client_id']]);
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing) {
        echo "<p style='color: orange;'>Entry already exists (ID: {$existing['id']}, Status: {$existing['status']})</p>";
        
        if ($existing['invoice_id']) {
            echo "<p>Already invoiced with invoice ID: {$existing['invoice_id']}</p>";
        } else {
            // Update to confirmed status
            $stmt = $db->prepare("
                UPDATE retrocession_data_entry 
                SET status = 'confirmed',
                    cns_amount = :cns_amount,
                    patient_amount = :patient_amount,
                    secretary_services = :secretary_amount,
                    total_amount = :total_amount,
                    updated_at = NOW()
                WHERE id = :id
            ");
            $stmt->execute([
                'cns_amount' => $amounts['cns_amount'],
                'patient_amount' => $amounts['patient_amount'],
                'secretary_amount' => $secretaryAmount,
                'total_amount' => $amounts['cns_amount'] + $amounts['patient_amount'],
                'id' => $existing['id']
            ]);
            echo "<p style='color: green;'>✓ Updated entry to confirmed status</p>";
        }
    } else {
        // Create new entry
        echo "<h3>Creating new retrocession_data_entry:</h3>";
        
        $stmt = $db->prepare("
            INSERT INTO retrocession_data_entry 
            (practitioner_id, period_month, period_year, 
             cns_amount, patient_amount, total_amount,
             secretary_services, cns_services_count, patient_services_count,
             status, data_source, entered_by, entered_at, created_at, updated_at)
            VALUES 
            (:practitioner_id, 6, 2025,
             :cns_amount, :patient_amount, :total_amount,
             :secretary_services, 1, 0,
             'confirmed', 'manual', :user_id, NOW(), NOW(), NOW())
        ");
        
        $stmt->execute([
            'practitioner_id' => $remi['client_id'], // Actually client_id
            'cns_amount' => $amounts['cns_amount'],
            'patient_amount' => $amounts['patient_amount'],
            'total_amount' => $amounts['cns_amount'] + $amounts['patient_amount'],
            'secretary_services' => $secretaryAmount,
            'user_id' => $remi['user_id']
        ]);
        
        echo "<p style='color: green;'>✓ Created retrocession_data_entry for June 2025</p>";
        echo "<p>Entry ID: " . $db->lastInsertId() . "</p>";
    }
    
    // Verify
    echo "<h3>Verification:</h3>";
    $stmt = $db->prepare("
        SELECT * FROM retrocession_data_entry 
        WHERE practitioner_id = :client_id 
        AND period_year = 2025 
        AND period_month = 6
    ");
    $stmt->execute(['client_id' => $remi['client_id']]);
    $verify = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($verify) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$verify['id']}</td></tr>";
        echo "<tr><td>Status</td><td><strong>{$verify['status']}</strong></td></tr>";
        echo "<tr><td>CNS Amount</td><td>" . number_format($verify['cns_amount'], 2) . " €</td></tr>";
        echo "<tr><td>Secretary Services</td><td>" . number_format($verify['secretary_services'], 2) . " €</td></tr>";
        echo "<tr><td>Invoice ID</td><td>" . ($verify['invoice_id'] ?? 'NULL') . "</td></tr>";
        echo "</table>";
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✓ Ready for Invoice Generation!</h3>";
    echo "<p>The retrocession_data_entry has been created/updated for Rémi Heine - June 2025.</p>";
    echo "<p>You can now generate the invoice through bulk generation.</p>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession' style='color: #155724; font-weight: bold;'>→ Go to Bulk Generation</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}