<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Type Setup</h2>";
    
    // Check if LOY exists
    echo "<h3>1. Checking for LOY Invoice Type:</h3>";
    $sql = "SELECT * FROM invoice_types WHERE prefix = 'LOY' OR code = 'LOY' OR name LIKE '%LOY%' OR name LIKE '%Loyer%'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $loyTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($loyTypes)) {
        echo "<p>Found potential LOY invoice types:</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Prefix</th><th>Code</th><th>Active</th>";
        echo "</tr>";
        foreach ($loyTypes as $type) {
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td><strong>{$type['prefix']}</strong></td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>" . ($type['is_active'] ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if we need to update the prefix
        $needsUpdate = true;
        foreach ($loyTypes as $type) {
            if ($type['prefix'] === 'LOY') {
                $needsUpdate = false;
                echo "<p style='color: green;'>✅ LOY prefix is correctly configured!</p>";
                break;
            }
        }
        
        if ($needsUpdate && !empty($loyTypes)) {
            $loyType = $loyTypes[0]; // Take the first one
            echo "<p style='color: orange;'>⚠️ Found Loyer type but prefix is not 'LOY'. Updating...</p>";
            
            $updateSql = "UPDATE invoice_types SET prefix = 'LOY', code = 'LOY' WHERE id = :id";
            $updateStmt = $db->prepare($updateSql);
            $updateStmt->execute(['id' => $loyType['id']]);
            echo "<p style='color: green;'>✅ Updated invoice type ID {$loyType['id']} to use LOY prefix!</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No LOY invoice type found. Creating one...</p>";
        
        // Insert LOY invoice type
        $insertSql = "INSERT INTO invoice_types (name, prefix, code, color, `order`, is_active, description, created_at) 
                      VALUES (:name, :prefix, :code, :color, :order, :is_active, :description, NOW())";
        
        $insertStmt = $db->prepare($insertSql);
        $insertStmt->execute([
            'name' => 'Loyer',
            'prefix' => 'LOY',
            'code' => 'LOY',
            'color' => '#FF9800', // Orange color
            'order' => 5,
            'is_active' => 1,
            'description' => 'Facture de loyer et charges'
        ]);
        
        $newId = $db->lastInsertId();
        echo "<p style='color: green;'>✅ Created LOY invoice type with ID: {$newId}</p>";
    }
    
    // Show all invoice types
    echo "<h3>2. All Invoice Types:</h3>";
    $sql = "SELECT * FROM invoice_types ORDER BY id";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $allTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Name</th><th>Prefix</th><th>Code</th><th>Color</th><th>Order</th><th>Active</th>";
    echo "</tr>";
    foreach ($allTypes as $type) {
        $highlight = ($type['prefix'] === 'LOY') ? "style='background-color: #ffffcc;'" : "";
        echo "<tr {$highlight}>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['name']}</td>";
        echo "<td><strong>{$type['prefix']}</strong></td>";
        echo "<td>{$type['code']}</td>";
        echo "<td style='background-color: {$type['color']}; color: white;'>{$type['color']}</td>";
        echo "<td>{$type['order']}</td>";
        echo "<td>" . ($type['is_active'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check existing LOY invoices
    echo "<h3>3. Existing LOY Invoices:</h3>";
    $sql = "SELECT COUNT(*) as count FROM invoices WHERE invoice_number LIKE '%LOY%'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    echo "<p>Found <strong>{$count}</strong> LOY invoices in the system.</p>";
    
    if ($count > 0) {
        $sql = "SELECT invoice_number, created_at FROM invoices 
                WHERE invoice_number LIKE '%LOY%' 
                ORDER BY id DESC LIMIT 5";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Recent LOY invoices:</p>";
        echo "<ul>";
        foreach ($invoices as $inv) {
            echo "<li>{$inv['invoice_number']} - Created: {$inv['created_at']}</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>4. Next Steps:</h3>";
    echo "<p><a href='/fit/public/invoices/create' class='btn' style='background: blue; color: white; padding: 10px; text-decoration: none;'>Create New LOY Invoice</a></p>";
    echo "<p><a href='/fit/public/test_loy_invoice_autofill.php' class='btn' style='background: green; color: white; padding: 10px; text-decoration: none;'>Run Auto-fill Test Again</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>