<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

// Ensure we're using French
$_SESSION['user_language'] = 'fr';
\App\Helpers\Language::setLanguage('fr');
\App\Helpers\Language::setFallbackLanguage('fr');

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test and Fix Translations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Translation System Diagnostic and Fix</h1>
        
        <div class="alert alert-info">
            <h5>Problem:</h5>
            <p>User reports seeing "config.code" instead of proper labels in payment terms configuration.</p>
        </div>
        
        <?php
        // Test 1: Basic translation test
        echo '<div class="card mb-3">';
        echo '<div class="card-header"><h5>Test 1: Basic Translation Function</h5></div>';
        echo '<div class="card-body">';
        
        $tests = [
            'config.code' => 'Code',
            'common.name' => 'Nom',
            'config.payment_days' => 'Jours de paiement',
            'config.payment_term_name_hint' => 'ex: Net 30, Paiement à réception'
        ];
        
        echo '<table class="table">';
        echo '<tr><th>Key</th><th>Result</th><th>Expected</th><th>Status</th></tr>';
        foreach ($tests as $key => $expected) {
            $result = __($key);
            $status = ($result === $expected) ? '<span class="text-success">✓</span>' : '<span class="text-danger">✗</span>';
            echo "<tr><td><code>$key</code></td><td>$result</td><td>$expected</td><td>$status</td></tr>";
        }
        echo '</table>';
        echo '</div></div>';
        
        // Test 2: Check Language files
        echo '<div class="card mb-3">';
        echo '<div class="card-header"><h5>Test 2: Language Files Check</h5></div>';
        echo '<div class="card-body">';
        
        $langFiles = [
            'config' => __DIR__ . '/../app/lang/fr/config.php',
            'common' => __DIR__ . '/../app/lang/fr/common.php'
        ];
        
        foreach ($langFiles as $name => $file) {
            if (file_exists($file)) {
                $translations = include $file;
                echo "<p><strong>$name.php:</strong> ";
                echo "✓ File exists, " . count($translations) . " translations loaded";
                
                // Check specific keys
                if ($name === 'config') {
                    echo "<br>- 'code' key: " . (isset($translations['code']) ? "✓ Found ('{$translations['code']}')" : "✗ Missing");
                    echo "<br>- 'payment_days' key: " . (isset($translations['payment_days']) ? "✓ Found" : "✗ Missing");
                }
                echo "</p>";
            } else {
                echo "<p><strong>$name.php:</strong> <span class='text-danger'>✗ File not found!</span></p>";
            }
        }
        echo '</div></div>';
        
        // Test 3: Check database for bad data
        echo '<div class="card mb-3">';
        echo '<div class="card-header"><h5>Test 3: Database Payment Terms Check</h5></div>';
        echo '<div class="card-body">';
        
        try {
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config_payment_terms ORDER BY id");
            $terms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            if (count($terms) > 0) {
                echo '<table class="table table-sm">';
                echo '<tr><th>ID</th><th>Code</th><th>Name (Raw)</th><th>Name (Decoded)</th><th>Status</th></tr>';
                
                $hasIssues = false;
                foreach ($terms as $term) {
                    $status = '<span class="text-success">OK</span>';
                    $decodedName = '-';
                    
                    if (strpos($term['name'], 'config.') !== false || strpos($term['name'], 'common.') !== false) {
                        $status = '<span class="text-danger">Contains translation key!</span>';
                        $hasIssues = true;
                    } else if (!empty($term['name']) && $term['name'] !== 'null') {
                        $nameData = json_decode($term['name'], true);
                        if ($nameData) {
                            $decodedName = $nameData['fr'] ?? $nameData['en'] ?? '-';
                        } else {
                            $status = '<span class="text-warning">Invalid JSON</span>';
                        }
                    }
                    
                    echo "<tr>";
                    echo "<td>{$term['id']}</td>";
                    echo "<td><code>{$term['code']}</code></td>";
                    echo "<td><small>" . htmlspecialchars(substr($term['name'], 0, 50)) . "</small></td>";
                    echo "<td>$decodedName</td>";
                    echo "<td>$status</td>";
                    echo "</tr>";
                }
                echo '</table>';
                
                if ($hasIssues) {
                    echo '<div class="alert alert-danger mt-3">';
                    echo '<strong>Issues found!</strong> Some payment terms have translation keys stored as values.';
                    echo '<br><a href="fix_payment_terms_data.php" class="btn btn-danger mt-2">Fix Database Issues</a>';
                    echo '</div>';
                }
            } else {
                echo '<p>No payment terms found in database.</p>';
            }
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Database error: ' . $e->getMessage() . '</div>';
        }
        echo '</div></div>';
        
        // Test 4: Twig Integration
        echo '<div class="card mb-3">';
        echo '<div class="card-header"><h5>Test 4: Twig Template Engine Integration</h5></div>';
        echo '<div class="card-body">';
        
        try {
            $twig = Flight::view();
            $template = $twig->createTemplate('{{ __("config.code") }} | {{ __("common.name") }}');
            $result = $template->render([]);
            echo "<p>Template test result: <code>$result</code></p>";
            
            if ($result === "Code | Nom") {
                echo '<p class="text-success">✓ Twig translation function working correctly</p>';
            } else {
                echo '<p class="text-danger">✗ Twig translation function not working properly</p>';
            }
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Twig error: ' . $e->getMessage() . '</div>';
        }
        echo '</div></div>';
        
        // Recommendations
        echo '<div class="card mb-3 border-primary">';
        echo '<div class="card-header bg-primary text-white"><h5>Recommendations</h5></div>';
        echo '<div class="card-body">';
        echo '<ol>';
        echo '<li>Clear browser cache and cookies</li>';
        echo '<li>Make sure session language is set to "fr"</li>';
        echo '<li>Check if JavaScript console shows any errors</li>';
        echo '<li>If payment terms contain translation keys as values, fix the database</li>';
        echo '</ol>';
        echo '<a href="/fit/public/config/payment-terms" class="btn btn-primary">Go to Payment Terms Page</a>';
        echo '</div></div>';
        ?>
    </div>
</body>
</html>