<?php
// Simple test without bootstrap to avoid session issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html><html><head><title>Email Test with PDF</title></head><body>";
    echo "<h2>Testing Email with PDF Attachment</h2>";
    
    // Get invoice data
    $stmt = $db->prepare("
        SELECT i.*, 
               it.name as invoice_type_name,
               it.code as invoice_type_code,
               u.first_name, u.last_name, u.email
        FROM invoices i
        LEFT JOIN invoice_types it ON i.invoice_type_id = it.id
        LEFT JOIN users u ON i.user_id = u.id
        WHERE i.user_id = 1 
        AND i.document_type_id = 1 
        AND i.invoice_type_id = 4
        ORDER BY i.id DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>No DIV invoice found for Frank!</p>";
        echo "<p><a href='/fit/public/invoices/create?type=rental&user_id=1'>Create one here</a></p>";
        exit;
    }
    
    echo "<h3>Invoice Found:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>User: {$invoice['first_name']} {$invoice['last_name']}</li>";
    echo "<li>Email: {$invoice['email']}</li>";
    echo "<li>Total: €" . number_format($invoice['total'], 2) . "</li>";
    echo "</ul>";
    
    // Get invoice items
    $stmt = $db->prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
    $stmt->execute([$invoice['id']]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($items) {
        echo "<h4>Items:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Description</th><th>Qty</th><th>Price</th><th>VAT</th></tr>";
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>{$item['description']}</td>";
            echo "<td>{$item['quantity']}</td>";
            echo "<td>€{$item['unit_price']}</td>";
            echo "<td>{$item['vat_rate']}%</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>Sending Email Test...</h3>";
    
    // Load required classes
    require_once __DIR__ . '/vendor/autoload.php';
    
    // Initialize Flight DB connection for the services
    // Register as a callback that returns the existing PDO instance
    Flight::register('db', function() use ($db) {
        return $db;
    });
    
    // Create services
    $emailService = new App\Services\EmailService();
    
    try {
        // Send email with invoice ID
        $result = $emailService->sendInvoiceEmail($invoice['id']);
        
        if ($result['success']) {
            echo "<p style='color: green;'>✓ Email sent successfully!</p>";
            if (isset($result['subject'])) {
                echo "<p>Subject: " . htmlspecialchars($result['subject']) . "</p>";
            }
            echo "<p><strong>Check Mailhog at <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></strong></p>";
            echo "<p>The email should have a PDF attachment.</p>";
        } else {
            echo "<p style='color: red;'>✗ Email failed: " . htmlspecialchars($result['message']) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    
    echo "<hr>";
    echo "<p><a href='/fit/public/invoices'>Back to Invoices</a></p>";
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "Database Error: " . $e->getMessage();
}