<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing RET Codes Final</h2>";
    
    // Update ret2 to ret25
    echo "<h3>Updating codes:</h3>";
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret25' WHERE code = 'ret2'");
    $stmt->execute();
    $count = $stmt->rowCount();
    if ($count > 0) {
        echo "<p style='color: green;'>✓ Updated 'ret2' to 'ret25' ($count rows)</p>";
    }
    
    $stmt = $db->prepare("UPDATE config_invoice_types SET code = 'ret30' WHERE code = 'ret3'");
    $stmt->execute();
    $count = $stmt->rowCount();
    if ($count > 0) {
        echo "<p style='color: green;'>✓ Updated 'ret3' to 'ret30' ($count rows)</p>";
    }
    
    // Clean up duplicates - keep only the ones with FAC- prefix
    echo "<h3>Cleaning up duplicates:</h3>";
    
    // Delete RT25 (id 15) as we have ret25
    $stmt = $db->prepare("DELETE FROM config_invoice_types WHERE id = 15");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: orange;'>Deleted RT25 duplicate</p>";
    }
    
    // Delete duplicate 'ret' entries - keep id 14
    $stmt = $db->prepare("DELETE FROM config_invoice_types WHERE code = 'ret' AND id != 14");
    $stmt->execute();
    $count = $stmt->rowCount();
    if ($count > 0) {
        echo "<p style='color: orange;'>Deleted $count duplicate 'ret' entries</p>";
    }
    
    // Verify final state - Show ALL RET-related entries
    echo "<h3>Final Configuration:</h3>";
    
    // First, show what codes we're looking for
    echo "<p>Looking for codes: 'ret', 'ret25', 'ret30'</p>";
    
    // Check if these codes exist
    $checkStmt = $db->query("
        SELECT code, COUNT(*) as count 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30') 
        GROUP BY code
    ");
    $codeCounts = $checkStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found codes: ";
    foreach ($codeCounts as $cc) {
        echo "{$cc['code']} ({$cc['count']}), ";
    }
    echo "</p>";
    
    // Get all RET-related entries, not just specific codes
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code LIKE '%ret%' OR prefix LIKE '%RET%'
        ORDER BY id
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total RET-related entries found: " . count($types) . "</p>";
    
    echo "<table border='1' cellpadding='5' style='margin-bottom: 20px;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Status</th></tr>";
    
    $hasRet25 = false;
    $hasRet30 = false;
    
    foreach ($types as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $status = '✓';
        $rowStyle = '';
        
        if ($row['code'] == 'ret25' && $row['prefix'] == 'FAC-RET25') {
            $hasRet25 = true;
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['code'] == 'ret30' && $row['prefix'] == 'FAC-RET30') {
            $hasRet30 = true;
            $rowStyle = 'background-color: #d4edda;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td style='color: green;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test with Frank's settings
    echo "<h3>Testing with Frank Huet:</h3>";
    $stmt = $db->prepare("
        SELECT u.id, u.first_name, u.last_name, urs.secretary_value 
        FROM users u
        LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
        WHERE u.email = '<EMAIL>'
        ORDER BY urs.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $frank = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($frank && $frank['secretary_value']) {
        $secretaryValue = $frank['secretary_value'];
        $selectedCode = ($secretaryValue == 5) ? 'ret25' : 'ret30';
        
        echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>User:</strong> {$frank['first_name']} {$frank['last_name']}</p>";
        echo "<p><strong>Secretary Fee:</strong> {$secretaryValue}%</p>";
        echo "<p><strong>Invoice Type Code:</strong> $selectedCode</p>";
        
        $stmt = $db->prepare("SELECT prefix FROM config_invoice_types WHERE code = :code");
        $stmt->execute(['code' => $selectedCode]);
        $type = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($type) {
            echo "<p><strong>Invoice Number Format:</strong> <span style='color: green; font-size: 1.2em;'>{$type['prefix']}-2025-XXXX</span></p>";
        }
        echo "</div>";
    }
    
    // Final summary
    echo "<hr>";
    if ($hasRet25 && $hasRet30) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Configuration Complete!</h3>";
        echo "<p style='margin-bottom: 0;'>The system is now properly configured to generate:</p>";
        echo "<ul style='margin-bottom: 0;'>";
        echo "<li><strong>FAC-RET25-2025-XXXX</strong> for users with 5% secretary fee</li>";
        echo "<li><strong>FAC-RET30-2025-XXXX</strong> for users with 10% secretary fee</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession' class='btn btn-success btn-lg'>Test Bulk Generation Now</a></p>";
    } else {
        echo "<p style='color: red;'>Configuration still incomplete. Please check the table above.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}