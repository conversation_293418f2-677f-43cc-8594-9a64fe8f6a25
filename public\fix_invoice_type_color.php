<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Config Invoice Types</h2>";
    
    // Check config_invoice_types
    $stmt = $db->query("SELECT * FROM config_invoice_types ORDER BY id");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Current Color</th><th>Preview</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . htmlspecialchars($type['name']) . "</td>";
        echo "<td>" . htmlspecialchars($type['code']) . "</td>";
        echo "<td>" . htmlspecialchars($type['prefix']) . "</td>";
        echo "<td>" . htmlspecialchars($type['color']) . "</td>";
        echo "<td><span style='background-color: " . htmlspecialchars($type['color']) . "; padding: 5px 10px; color: white;'>" . htmlspecialchars($type['name']) . "</span></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check which one is used for retrocession
    echo "<h3>Find Retrocession Type</h3>";
    $stmt = $db->prepare("
        SELECT * FROM config_invoice_types 
        WHERE name LIKE '%retro%' OR prefix LIKE '%RET%' OR code LIKE '%RET%'
    ");
    $stmt->execute();
    $retTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($retTypes) {
        echo "<p>Found retrocession types:</p>";
        foreach ($retTypes as $type) {
            echo "<p>ID: " . $type['id'] . " - Name: " . htmlspecialchars($type['name']) . " - Color: " . htmlspecialchars($type['color']) . "</p>";
            
            // Update color if it's black
            if ($type['color'] === '#000000') {
                echo "<p style='color: red;'>This type has black color! Updating to #17a2b8...</p>";
                
                $updateStmt = $db->prepare("UPDATE config_invoice_types SET color = '#17a2b8' WHERE id = :id");
                $updateStmt->execute(['id' => $type['id']]);
                
                echo "<p style='color: green;'>Updated successfully!</p>";
            }
        }
    }
    
    // Also check invoice_types table
    echo "<h3>Invoice Types Table</h3>";
    $stmt = $db->query("SELECT * FROM invoice_types WHERE name LIKE '%retro%' OR code LIKE '%RET%'");
    $invTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($invTypes) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th></tr>";
        foreach ($invTypes as $type) {
            echo "<tr>";
            echo "<td>" . $type['id'] . "</td>";
            echo "<td>" . htmlspecialchars($type['name']) . "</td>";
            echo "<td>" . htmlspecialchars($type['code']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<p><a href='/fit/public/invoices'>Go back to invoices</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}