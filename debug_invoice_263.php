<?php
// Debug invoice 263 rounding issue

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Debug Invoice 263 Rounding Issue</h1>";

// Get invoice details
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = 263");
$stmt->execute();
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$invoice) {
    die("Invoice 263 not found");
}

echo "<h2>Invoice Overview</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Field</th><th>Value</th></tr>";
echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
echo "<tr><td>Subtotal</td><td>{$invoice['subtotal']}</td></tr>";
echo "<tr><td>VAT Amount</td><td>{$invoice['vat_amount']}</td></tr>";
echo "<tr><td>Total</td><td><strong>{$invoice['total']}</strong></td></tr>";
echo "</table>";

// Get invoice lines
$stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 263 ORDER BY sort_order, id");
$stmt->execute();
$lines = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Invoice Lines Breakdown</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Line</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th><th>VAT Amount</th></tr>";

$calculatedSubtotal = 0;
$calculatedVatAmount = 0;
$lineNumber = 1;

foreach ($lines as $line) {
    $quantity = $line['quantity'];
    $unitPrice = $line['unit_price'];
    $vatRate = $line['vat_rate'];
    
    // Calculate line total (HT)
    $lineTotal = $quantity * $unitPrice;
    
    // Calculate VAT for this line
    $lineVat = $lineTotal * ($vatRate / 100);
    
    $calculatedSubtotal += $lineTotal;
    $calculatedVatAmount += $lineVat;
    
    echo "<tr>";
    echo "<td>$lineNumber</td>";
    echo "<td>{$line['description']}</td>";
    echo "<td>$quantity</td>";
    echo "<td>" . number_format($unitPrice, 2) . "</td>";
    echo "<td>$vatRate%</td>";
    echo "<td>" . number_format($lineTotal, 2) . "</td>";
    echo "<td>" . number_format($lineVat, 2) . "</td>";
    echo "</tr>";
    
    $lineNumber++;
}

echo "</table>";

// Show calculations
echo "<h2>Calculation Analysis</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Method</th><th>Subtotal</th><th>VAT Amount</th><th>Total</th><th>Difference</th></tr>";

// Method 1: Sum of lines (current system)
$method1Total = $calculatedSubtotal + $calculatedVatAmount;
echo "<tr>";
echo "<td>Method 1: Sum of lines</td>";
echo "<td>" . number_format($calculatedSubtotal, 2) . "</td>";
echo "<td>" . number_format($calculatedVatAmount, 2) . "</td>";
echo "<td>" . number_format($method1Total, 2) . "</td>";
echo "<td>" . number_format($method1Total - $invoice['total'], 2) . "</td>";
echo "</tr>";

// Method 2: Database values
echo "<tr>";
echo "<td>Method 2: Database stored</td>";
echo "<td>" . number_format($invoice['subtotal'], 2) . "</td>";
echo "<td>" . number_format($invoice['vat_amount'], 2) . "</td>";
echo "<td><strong>" . number_format($invoice['total'], 2) . "</strong></td>";
echo "<td>0.00 (reference)</td>";
echo "</tr>";

// Method 3: Rounded calculations
$roundedSubtotal = round($calculatedSubtotal, 2);
$roundedVatAmount = round($calculatedVatAmount, 2);
$method3Total = $roundedSubtotal + $roundedVatAmount;
echo "<tr>";
echo "<td>Method 3: Rounded per step</td>";
echo "<td>" . number_format($roundedSubtotal, 2) . "</td>";
echo "<td>" . number_format($roundedVatAmount, 2) . "</td>";
echo "<td>" . number_format($method3Total, 2) . "</td>";
echo "<td>" . number_format($method3Total - $invoice['total'], 2) . "</td>";
echo "</tr>";

// Method 4: TTC-first (desired)
$desiredTotal = 930.00; // What user wants
$backCalculatedVat = $desiredTotal - $roundedSubtotal;
echo "<tr>";
echo "<td>Method 4: TTC-first (desired)</td>";
echo "<td>" . number_format($roundedSubtotal, 2) . "</td>";
echo "<td>" . number_format($backCalculatedVat, 2) . "</td>";
echo "<td><strong>" . number_format($desiredTotal, 2) . "</strong></td>";
echo "<td>" . number_format($desiredTotal - $invoice['total'], 2) . "</td>";
echo "</tr>";

echo "</table>";

// Show the problem
echo "<h2>Problem Analysis</h2>";
echo "<p><strong>Issue:</strong> The total is {$invoice['total']} but should be 930.00</p>";
echo "<p><strong>Discrepancy:</strong> " . number_format($invoice['total'] - 930.00, 2) . "</p>";

echo "<h3>Likely Cause:</h3>";
echo "<ul>";
echo "<li>Multiple rounding steps accumulate small errors</li>";
echo "<li>Each line's VAT is calculated and rounded separately</li>";
echo "<li>The sum of rounded values doesn't equal the rounded sum</li>";
echo "</ul>";

echo "<h3>Solution Options:</h3>";
echo "<ol>";
echo "<li><strong>TTC-first calculation:</strong> Start with desired total (930.00) and work backwards</li>";
echo "<li><strong>Bulk rounding:</strong> Calculate all VAT first, then round the total</li>";
echo "<li><strong>Adjustment line:</strong> Add a small adjustment line to reach exact total</li>";
echo "</ol>";

// Check for similar issues
echo "<h2>Similar Issues Check</h2>";
$stmt = $db->prepare("
    SELECT id, invoice_number, total, 
           (subtotal + vat_amount) as calculated_total,
           ROUND((subtotal + vat_amount) - total, 2) as difference
    FROM invoices 
    WHERE ABS((subtotal + vat_amount) - total) > 0.001
    AND status = 'sent'
    ORDER BY ABS((subtotal + vat_amount) - total) DESC
    LIMIT 10
");
$stmt->execute();
$similarIssues = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($similarIssues) > 0) {
    echo "<p>Found " . count($similarIssues) . " invoices with similar rounding issues:</p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice ID</th><th>Invoice Number</th><th>Stored Total</th><th>Calculated Total</th><th>Difference</th></tr>";
    
    foreach ($similarIssues as $issue) {
        echo "<tr>";
        echo "<td>{$issue['id']}</td>";
        echo "<td>{$issue['invoice_number']}</td>";
        echo "<td>" . number_format($issue['total'], 2) . "</td>";
        echo "<td>" . number_format($issue['calculated_total'], 2) . "</td>";
        echo "<td>" . number_format($issue['difference'], 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No other invoices found with similar rounding issues.</p>";
}

echo "<p><a href='http://localhost/fit/public/invoices/263'>View Invoice 263</a></p>";
?>