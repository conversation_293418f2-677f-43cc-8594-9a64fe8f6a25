<?php
/**
 * Debug script to test translation system
 */

require_once 'vendor/autoload.php';
require_once 'app/Core/bootstrap.php';

use App\Helpers\Language;

echo "=== Translation System Debug ===\n\n";

// Test 1: Check current language
echo "1. Current Language: " . Language::getCurrentLanguage() . "\n\n";

// Test 2: Load common translations
echo "2. Loading 'common' translations...\n";
$loaded = Language::load('common');
echo "Result: " . ($loaded ? "Success" : "Failed") . "\n\n";

// Test 3: Test direct translation
echo "3. Testing direct translations:\n";
$testKeys = [
    'common.month.1',
    'common.month.7',
    'common.month.12',
    'common.email',
    'common.name'
];

foreach ($testKeys as $key) {
    $translation = __($key);
    echo "  $key => $translation\n";
}

// Test 4: Check if translations are loaded in memory
echo "\n4. Checking Language class translations array...\n";

// Use reflection to access private property
$reflection = new ReflectionClass('App\Helpers\Language');
$translationsProperty = $reflection->getProperty('translations');
$translationsProperty->setAccessible(true);
$translations = $translationsProperty->getValue();

echo "Loaded languages: " . implode(', ', array_keys($translations)) . "\n";

if (isset($translations['fr']['common'])) {
    echo "Common translations loaded for French\n";
    
    // Show a few examples
    if (isset($translations['fr']['common']['month.1'])) {
        echo "  month.1 = " . $translations['fr']['common']['month.1'] . "\n";
    }
    if (isset($translations['fr']['common']['email'])) {
        echo "  email = " . $translations['fr']['common']['email'] . "\n";
    }
} else {
    echo "Common translations NOT loaded for French\n";
}

// Test 5: Check the Language::get method directly
echo "\n5. Testing Language::get() directly:\n";
$directTest = Language::get('common.month.7');
echo "Language::get('common.month.7') = $directTest\n";

// Test 6: Check file existence
echo "\n6. Checking translation files:\n";
$langDir = __DIR__ . '/app/lang/fr/';
$commonFile = $langDir . 'common.php';

if (file_exists($commonFile)) {
    echo "✓ common.php exists\n";
    
    // Load and check content
    $content = include $commonFile;
    if (is_array($content)) {
        echo "✓ File returns an array with " . count($content) . " keys\n";
        
        // Check for month keys
        $monthKeys = array_filter(array_keys($content), function($key) {
            return strpos($key, 'month.') === 0;
        });
        
        echo "✓ Found " . count($monthKeys) . " month keys\n";
        
        // Show first few month translations
        for ($i = 1; $i <= 3; $i++) {
            if (isset($content["month.$i"])) {
                echo "  month.$i = " . $content["month.$i"] . "\n";
            }
        }
    }
} else {
    echo "✗ common.php NOT found at: $commonFile\n";
}

echo "\n=== End Debug ===\n";