<?php
/**
 * Check Invoice Sent Status
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Check Invoice Sent Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Invoice FAC-DIV-2025-0190 Sent Status</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // Get current invoice status
        $stmt = $db->prepare("SELECT id, invoice_number, status, sent_at, created_at, updated_at FROM invoices WHERE id = 279");
        $stmt->execute();
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "<h2>Current Status:</h2>";
            echo "<table>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            echo "<tr><td>ID</td><td>" . $invoice['id'] . "</td></tr>";
            echo "<tr><td>Invoice Number</td><td><strong>" . $invoice['invoice_number'] . "</strong></td></tr>";
            echo "<tr><td>Status</td><td><strong>" . $invoice['status'] . "</strong></td></tr>";
            echo "<tr><td>Sent At</td><td>" . ($invoice['sent_at'] ? "<span class='success'>" . $invoice['sent_at'] . "</span>" : "<span class='error'>NULL</span>") . "</td></tr>";
            echo "<tr><td>Created</td><td>" . $invoice['created_at'] . "</td></tr>";
            echo "<tr><td>Updated</td><td>" . $invoice['updated_at'] . "</td></tr>";
            echo "</table>";
            
            // If sent_at is null, provide option to update it
            if (!$invoice['sent_at'] && $invoice['status'] === 'sent') {
                echo "<h3>Fix Inconsistency</h3>";
                echo "<p class='error'>Invoice is marked as 'sent' but sent_at is NULL!</p>";
                
                if (isset($_POST['fix'])) {
                    $stmt = $db->prepare("UPDATE invoices SET sent_at = NOW() WHERE id = 279");
                    $stmt->execute();
                    echo "<p class='success'>✅ Updated sent_at to current timestamp!</p>";
                    echo "<p><a href=''>Refresh page</a></p>";
                } else {
                    echo "<form method='POST'>";
                    echo "<button type='submit' name='fix' value='1'>Fix: Set sent_at to NOW()</button>";
                    echo "</form>";
                }
            }
            
        } else {
            echo "<p class='error'>Invoice not found!</p>";
        }
        
        // Check if invoice was actually sent today
        echo "<h2>Email Activity Today:</h2>";
        $stmt = $db->query("
            SELECT * FROM email_logs 
            WHERE DATE(created_at) = CURDATE() 
            AND invoice_id = 279
            ORDER BY created_at DESC
        ");
        $todayLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($todayLogs) {
            echo "<table>";
            echo "<tr><th>Time</th><th>Status</th><th>Recipient</th></tr>";
            foreach ($todayLogs as $log) {
                echo "<tr>";
                echo "<td>" . $log['created_at'] . "</td>";
                echo "<td>" . $log['status'] . "</td>";
                echo "<td>" . $log['recipient_email'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No email logs found for today.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/check_email_logs_table.php">Check Email Logs Table</a> |
        <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Email Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>