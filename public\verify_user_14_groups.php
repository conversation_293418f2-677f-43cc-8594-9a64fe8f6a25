<?php
// Verify user 14's group memberships in database

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Verify User 14 - Malaurie Zéler</h1>";

// 1. Check user_group_members table
echo "<h2>1. Direct check of user_group_members table:</h2>";
$stmt = $db->prepare("
    SELECT ugm.*, ug.name as group_name 
    FROM user_group_members ugm
    JOIN user_groups ug ON ugm.group_id = ug.id
    WHERE ugm.user_id = 14
");
$stmt->execute();
$memberships = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($memberships) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>User ID</th><th>Group ID</th><th>Group Name</th><th>Member Since</th></tr>";
    foreach ($memberships as $m) {
        echo "<tr>";
        echo "<td>{$m['user_id']}</td>";
        echo "<td>{$m['group_id']}</td>";
        echo "<td>{$m['group_name']}</td>";
        echo "<td>{$m['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ User 14 is NOT a member of any groups!</p>";
}

// 2. Check all users in Coach group (24)
echo "<h2>2. All members of Coach group (24):</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 24
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Total coaches: " . count($coaches) . "</p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Username</th></tr>";
foreach ($coaches as $coach) {
    $highlight = ($coach['id'] == 14) ? " style='background: yellow;'" : "";
    echo "<tr$highlight>";
    echo "<td>{$coach['id']}</td>";
    echo "<td>{$coach['name']}</td>";
    echo "<td>{$coach['username']}</td>";
    echo "</tr>";
}
echo "</table>";

// 3. Add user to Coach group if needed
echo "<h2>3. Action needed:</h2>";
if (count($memberships) == 0 || !in_array('24', array_column($memberships, 'group_id'))) {
    echo "<p style='color: orange;'>⚠️ User 14 is NOT in the Coach group!</p>";
    echo "<form method='post' action='add_user_to_coach_group.php'>";
    echo "<input type='hidden' name='user_id' value='14'>";
    echo "<input type='hidden' name='group_id' value='24'>";
    echo "<button type='submit' style='padding: 10px 20px; background: #4CAF50; color: white; border: none; cursor: pointer;'>";
    echo "Add User 14 to Coach Group";
    echo "</button>";
    echo "</form>";
} else {
    echo "<p style='color: green;'>✅ User 14 is already in the Coach group</p>";
}

// 4. Check user_courses for user 14
echo "<h2>4. Courses for User 14:</h2>";
$stmt = $db->prepare("SELECT * FROM user_courses WHERE user_id = 14");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($courses) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Course Name</th><th>Hourly Rate</th><th>VAT Rate</th><th>Active</th></tr>";
    foreach ($courses as $course) {
        echo "<tr>";
        echo "<td>{$course['id']}</td>";
        echo "<td>{$course['course_name']}</td>";
        echo "<td>€{$course['hourly_rate']}</td>";
        echo "<td>{$course['vat_rate']}%</td>";
        echo "<td>" . ($course['is_active'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No courses found for this user</p>";
}

// 5. Debug what the controller would see
echo "<h2>5. Debug Controller Logic:</h2>";
// Simulate controller logic
$user = ['groups' => $memberships];
$userGroupIds = array_column($user['groups'], 'group_id');
$coachGroupId = '24';
$isCoach = in_array($coachGroupId, $userGroupIds);

echo "<p>userGroupIds from memberships: " . implode(', ', $userGroupIds) . "</p>";
echo "<p>coachGroupId: '$coachGroupId' (type: " . gettype($coachGroupId) . ")</p>";
echo "<p>isCoach result: " . ($isCoach ? 'TRUE' : 'FALSE') . "</p>";

?>