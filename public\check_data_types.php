<?php
// Check data types from database

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Check Data Types</h1>";

// 1. Check user_groups table structure
echo "<h2>1. user_groups table structure:</h2>";
$stmt = $db->query("DESCRIBE user_groups");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th></tr>";
foreach ($columns as $col) {
    echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td></tr>";
}
echo "</table>";

// 2. Sample data from user_groups
echo "<h2>2. Sample user_groups data:</h2>";
$stmt = $db->query("SELECT id, name FROM user_groups");
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Type</th><th>Name</th></tr>";
foreach ($groups as $group) {
    echo "<tr>";
    echo "<td>{$group['id']}</td>";
    echo "<td>" . gettype($group['id']) . "</td>";
    echo "<td>{$group['name']}</td>";
    echo "</tr>";
}
echo "</table>";

// 3. Check user 14's groups
echo "<h2>3. User 14's groups:</h2>";
$stmt = $db->prepare("
    SELECT ugm.group_id, ug.name 
    FROM user_group_members ugm
    JOIN user_groups ug ON ugm.group_id = ug.id
    WHERE ugm.user_id = 14
");
$stmt->execute();
$userGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<table border='1'>";
echo "<tr><th>Group ID</th><th>Type</th><th>Name</th></tr>";
foreach ($userGroups as $ug) {
    echo "<tr>";
    echo "<td>{$ug['group_id']}</td>";
    echo "<td>" . gettype($ug['group_id']) . "</td>";
    echo "<td>{$ug['name']}</td>";
    echo "</tr>";
}
echo "</table>";

// 4. Test array operations
echo "<h2>4. Array operation tests:</h2>";
$groupIds = array_column($userGroups, 'group_id');
echo "<p>array_column result:</p>";
echo "<pre>";
var_dump($groupIds);
echo "</pre>";

$groupIdsInt = array_map('intval', $groupIds);
echo "<p>After array_map('intval'):</p>";
echo "<pre>";
var_dump($groupIdsInt);
echo "</pre>";

// 5. Test in_array with different types
echo "<h2>5. in_array tests:</h2>";
$testId = "24";
echo "<p>Testing with string '24':</p>";
echo "in_array('24', groupIds): " . (in_array($testId, $groupIds) ? 'YES' : 'NO') . "<br>";
echo "in_array('24', groupIdsInt): " . (in_array($testId, $groupIdsInt) ? 'YES' : 'NO') . "<br>";
echo "in_array(24, groupIds): " . (in_array(24, $groupIds) ? 'YES' : 'NO') . "<br>";
echo "in_array(24, groupIdsInt): " . (in_array(24, $groupIdsInt) ? 'YES' : 'NO') . "<br>";

?>