<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

// Start session if not started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set a test user in session
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = ['id' => 1, 'username' => 'admin'];
}

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Invoice POST</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Invoice Form Submission</h1>
        
        <div class="alert alert-info">
            This form simulates the exact data your invoice form is submitting.
        </div>
        
        <form method="POST" action="/fit/public/invoices" id="testForm">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
            <input type="hidden" name="action" value="save">
            
            <!-- Hidden fields matching your form data -->
            <input type="hidden" name="document_type_id" value="1">
            <input type="hidden" name="invoice_type_id" value="1">
            <input type="hidden" name="invoice_number" value="">
            <input type="hidden" name="issue_date" value="2025-07-08">
            <input type="hidden" name="subject" value="LOYER + CHARGES">
            <input type="hidden" name="period" value="JUIN 2025">
            <input type="hidden" name="billable_type" value="user">
            <input type="hidden" name="billable_id" value="user_2">
            
            <!-- Invoice items -->
            <input type="hidden" name="items[0][quantity]" value="1">
            <input type="hidden" name="items[0][description]" value="Loyer mensuel">
            <input type="hidden" name="items[0][unit_price]" value="720">
            <input type="hidden" name="items[0][vat_rate_id]" value="26">
            
            <input type="hidden" name="items[1][quantity]" value="1">
            <input type="hidden" name="items[1][description]" value="Charges locations">
            <input type="hidden" name="items[1][unit_price]" value="160">
            <input type="hidden" name="items[1][vat_rate_id]" value="26">
            
            <!-- Other fields -->
            <input type="hidden" name="cns_base_amount" value="0.00">
            <input type="hidden" name="secretary_fee_amount" value="0.00">
            <input type="hidden" name="notes" value="">
            <input type="hidden" name="internal_notes" value="">
            <input type="hidden" name="payment_term_id" value="1">
            <input type="hidden" name="due_date" value="2025-07-08">
            
            <div class="mb-3">
                <h3>Form Data Summary:</h3>
                <ul>
                    <li>Document Type: Invoice (ID: 1)</li>
                    <li>Billable: User ID 2 (Maureen)</li>
                    <li>Issue Date: 2025-07-08</li>
                    <li>Due Date: 2025-07-08</li>
                    <li>Items:
                        <ul>
                            <li>Loyer mensuel: 720.00 (0% VAT)</li>
                            <li>Charges locations: 160.00 (0% VAT)</li>
                        </ul>
                    </li>
                    <li>Total: 880.00 (no VAT)</li>
                </ul>
            </div>
            
            <button type="submit" class="btn btn-primary">Submit Test Invoice</button>
        </form>
        
        <hr class="my-4">
        
        <h3>Alternative: Test with cURL</h3>
        <p>Copy this command and run it in terminal to test the endpoint directly:</p>
        <pre class="bg-light p-3"><code>curl -X POST http://localhost/fit/public/invoices \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "csrf_token=<?php echo $_SESSION['csrf_token'] ?? 'test'; ?>" \
  -d "action=save" \
  -d "document_type_id=1" \
  -d "invoice_type_id=1" \
  -d "billable_id=user_2" \
  -d "issue_date=2025-07-08" \
  -d "due_date=2025-07-08" \
  -d "payment_term_id=1" \
  -d "items[0][description]=Test+Item" \
  -d "items[0][quantity]=1" \
  -d "items[0][unit_price]=100" \
  -d "items[0][vat_rate_id]=26"</code></pre>
        
        <?php
        // If form was submitted, show the result
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            echo '<div class="mt-4 alert alert-warning">';
            echo '<h4>Form was submitted!</h4>';
            echo '<p>Check if you were redirected or if there was an error.</p>';
            echo '<p>Also check the database for new invoices.</p>';
            echo '</div>';
        }
        ?>
    </div>
</body>
</html>