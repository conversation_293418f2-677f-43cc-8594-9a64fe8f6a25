<?php
// Fix Location Invoice Type - Set prefix to "LOC"
// The location invoice type should have prefix "LOC" but it's showing as empty

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Fix Location Invoice Type Prefix</h1>";
    
    // Find the location invoice type
    $stmt = $db->prepare("
        SELECT * FROM config_invoice_types 
        WHERE LOWER(name) LIKE '%location%' 
        ORDER BY id ASC
    ");
    $stmt->execute();
    $locationTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Location Invoice Types</h2>";
    if (!empty($locationTypes)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Current Prefix</th><th>Code</th><th>Active</th><th>Action</th></tr>";
        
        foreach ($locationTypes as $type) {
            $prefix = $type['prefix'] ?? '';
            $prefixDisplay = empty($prefix) ? '<em>EMPTY</em>' : $prefix;
            $bgColor = empty($prefix) ? 'background-color: #ffeeee;' : '';
            
            echo "<tr style='$bgColor'>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>$prefixDisplay</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>" . ($type['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>";
            if (empty($prefix)) {
                echo "<a href='?fix_id={$type['id']}'>Set to LOC</a>";
            } else {
                echo "Already has prefix";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No location invoice types found</p>";
    }
    
    // Also check for ID 12 (from the JavaScript logs)
    echo "<h2>Check Invoice Type ID 12</h2>";
    $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = 12");
    $stmt->execute();
    $type12 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($type12) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$type12['id']}</td></tr>";
        echo "<tr><td>Name</td><td>{$type12['name']}</td></tr>";
        echo "<tr><td>Code</td><td>{$type12['code']}</td></tr>";
        echo "<tr><td>Prefix</td><td>" . ($type12['prefix'] ?? '<em>EMPTY</em>') . "</td></tr>";
        echo "<tr><td>Active</td><td>" . ($type12['is_active'] ? 'Yes' : 'No') . "</td></tr>";
        echo "</table>";
        
        if (empty($type12['prefix'])) {
            echo "<p><a href='?fix_id=12'>Fix this invoice type - Set prefix to LOC</a></p>";
        }
    } else {
        echo "<p>Invoice type ID 12 not found</p>";
    }
    
    // Show all invoice types for reference
    echo "<h2>All Invoice Types</h2>";
    $stmt = $db->prepare("SELECT * FROM config_invoice_types ORDER BY id ASC");
    $stmt->execute();
    $allTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($allTypes)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Active</th><th>Action</th></tr>";
        
        foreach ($allTypes as $type) {
            $prefix = $type['prefix'] ?? '';
            $prefixDisplay = empty($prefix) ? '<em>EMPTY</em>' : $prefix;
            $bgColor = empty($prefix) ? 'background-color: #ffeeee;' : '';
            
            echo "<tr style='$bgColor'>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>$prefixDisplay</td>";
            echo "<td>" . ($type['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>";
            if (empty($prefix)) {
                echo "<a href='?fix_id={$type['id']}'>Fix Prefix</a>";
            } else {
                echo "✓ OK";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Handle fix action
    if (isset($_GET['fix_id'])) {
        $fixId = $_GET['fix_id'];
        
        // Get the invoice type details
        $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = ?");
        $stmt->execute([$fixId]);
        $typeToFix = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($typeToFix) {
            echo "<h2>Fix Invoice Type ID $fixId</h2>";
            echo "<p><strong>Current:</strong> {$typeToFix['name']} - Prefix: " . ($typeToFix['prefix'] ?? '<em>EMPTY</em>') . "</p>";
            
            // Determine the correct prefix based on the name
            $newPrefix = 'LOC'; // Default for location types
            if (stripos($typeToFix['name'], 'retrocession') !== false) {
                $newPrefix = 'RET';
            } elseif (stripos($typeToFix['name'], 'facture') !== false) {
                $newPrefix = 'FAC';
            } elseif (stripos($typeToFix['name'], 'location') !== false) {
                $newPrefix = 'LOC';
            } elseif (stripos($typeToFix['name'], 'loyer') !== false) {
                $newPrefix = 'LOY';
            }
            
            if (!isset($_GET['confirm'])) {
                echo "<p>Suggested prefix: <strong>$newPrefix</strong></p>";
                echo "<p><a href='?fix_id=$fixId&confirm=yes&prefix=$newPrefix'>Confirm: Set prefix to $newPrefix</a></p>";
                echo "<p><a href='?'>Cancel</a></p>";
            } else {
                $prefixToSet = $_GET['prefix'] ?? $newPrefix;
                
                try {
                    $stmt = $db->prepare("UPDATE config_invoice_types SET prefix = ? WHERE id = ?");
                    $stmt->execute([$prefixToSet, $fixId]);
                    
                    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h3>✅ Invoice Type Updated</h3>";
                    echo "<p>Invoice type '{$typeToFix['name']}' (ID: $fixId) has been updated:</p>";
                    echo "<p><strong>New prefix:</strong> $prefixToSet</p>";
                    echo "</div>";
                    
                    // Verify the change
                    $stmt = $db->prepare("SELECT prefix FROM config_invoice_types WHERE id = ?");
                    $stmt->execute([$fixId]);
                    $newValue = $stmt->fetchColumn();
                    
                    if ($newValue === $prefixToSet) {
                        echo "<p style='color: green;'>✅ Verification: Prefix successfully updated to '$prefixToSet'</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Warning: Prefix verification failed</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error updating prefix: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>Invoice type ID $fixId not found</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>