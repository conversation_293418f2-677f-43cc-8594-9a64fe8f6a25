<?php
// Analyze coach courses from user_courses table

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Coach Courses Analysis</h1>";

// 1. Check users in group 24 (Coach)
echo "<h2>1. Users in Group 24 (Coach):</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name,
           u.is_active, u.can_be_invoiced
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 24
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Found " . count($coaches) . " members in Coach group</p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>User ID</th><th>Name</th><th>Username</th><th>Active</th><th>Can Invoice</th></tr>";
foreach ($coaches as $coach) {
    echo "<tr>";
    echo "<td>{$coach['id']}</td>";
    echo "<td>{$coach['name']}</td>";
    echo "<td>{$coach['username']}</td>";
    echo "<td>" . ($coach['is_active'] ? '✅' : '❌') . "</td>";
    echo "<td>" . ($coach['can_be_invoiced'] ? '✅' : '❌') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 2. Check user_courses for these coaches
echo "<h2>2. Courses for Coach Group Members:</h2>";
$coachIds = array_column($coaches, 'id');
if (count($coachIds) > 0) {
    $placeholders = str_repeat('?,', count($coachIds) - 1) . '?';
    $stmt = $db->prepare("
        SELECT uc.*, u.username, CONCAT(u.first_name, ' ', u.last_name) as user_name
        FROM user_courses uc
        JOIN users u ON uc.user_id = u.id
        WHERE uc.user_id IN ($placeholders)
        AND uc.is_active = 1
        ORDER BY u.first_name, u.last_name, uc.display_order
    ");
    $stmt->execute($coachIds);
    $coachCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($coachCourses) > 0) {
        echo "<p>Found " . count($coachCourses) . " active courses</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User</th><th>Course Name</th><th>Hourly Rate</th><th>VAT Rate</th></tr>";
        foreach ($coachCourses as $course) {
            echo "<tr>";
            echo "<td>{$course['user_name']} ({$course['username']})</td>";
            echo "<td><strong>{$course['course_name']}</strong></td>";
            echo "<td>€{$course['hourly_rate']}</td>";
            echo "<td>{$course['vat_rate']}%</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No active courses found for coaches in group 24</p>";
    }
}

// 3. Show all users who have courses (regardless of group)
echo "<h2>3. All Users with Courses:</h2>";
$stmt = $db->query("
    SELECT DISTINCT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name,
           COUNT(uc.id) as course_count,
           GROUP_CONCAT(uc.course_name ORDER BY uc.display_order SEPARATOR ', ') as courses
    FROM users u
    JOIN user_courses uc ON u.id = uc.user_id
    WHERE uc.is_active = 1
    AND u.is_active = 1
    AND u.can_be_invoiced = 1
    GROUP BY u.id
    ORDER BY u.first_name, u.last_name
");
$usersWithCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Found " . count($usersWithCourses) . " active users with courses</p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>User ID</th><th>Name</th><th>Username</th><th>Courses</th><th>In Group 24?</th></tr>";
foreach ($usersWithCourses as $user) {
    $inGroup24 = in_array($user['id'], $coachIds);
    echo "<tr" . ($inGroup24 ? " style='background-color: #e8f5e9;'" : "") . ">";
    echo "<td>{$user['id']}</td>";
    echo "<td>{$user['name']}</td>";
    echo "<td>{$user['username']}</td>";
    echo "<td>{$user['courses']}</td>";
    echo "<td>" . ($inGroup24 ? '✅ Yes' : '❌ No') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 4. Summary and recommendations
echo "<h2>4. Summary:</h2>";
$coachesWithCourses = 0;
foreach ($usersWithCourses as $user) {
    if (in_array($user['id'], $coachIds)) {
        $coachesWithCourses++;
    }
}

echo "<ul>";
echo "<li>Total coaches in group 24: " . count($coaches) . "</li>";
echo "<li>Coaches with active courses: $coachesWithCourses</li>";
echo "<li>Total users with courses: " . count($usersWithCourses) . "</li>";
echo "</ul>";

if ($coachesWithCourses == 0 && count($usersWithCourses) > 0) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ No coaches in group 24 have courses, but other users do!</p>";
    echo "<p>You should either:</p>";
    echo "<ol>";
    echo "<li>Add the users with courses to group 24, OR</li>";
    echo "<li>Change the coach_group_id configuration to use a different group</li>";
    echo "</ol>";
}

// 5. Configuration update
echo "<h2>5. Update Configuration:</h2>";
echo "<p>Set group 24 as the coach group:</p>";
echo "<form method='post' action='update_coach_group_config.php' style='display: inline;'>";
echo "<input type='hidden' name='group_id' value='24'>";
echo "<button type='submit' style='padding: 10px 20px; background: #4CAF50; color: white; border: none; cursor: pointer;'>";
echo "Use Group 24 for Coaches";
echo "</button>";
echo "</form>";

?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #e9e9e9; font-weight: bold; }
td, th { padding: 5px 8px; border: 1px solid #ddd; }
h2 { color: #333; margin-top: 30px; }
</style>

<hr>
<p><a href='/fit/public/test_all_invoice_types.html'>Back to Test Page</a></p>