<?php
require_once '../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

$pdo = new PDO(
    'mysql:host='.$_ENV['DB_HOST'].';dbname='.$_ENV['DB_DATABASE'].';charset=utf8mb4',
    $_ENV['DB_USERNAME'],
    $_ENV['DB_PASSWORD']
);

// Define a comprehensive list of distinct colors
$colors = [
    '#28a745', // Green
    '#dc3545', // Red
    '#ffc107', // Yellow/Amber
    '#17a2b8', // <PERSON>an
    '#6f42c1', // Purple
    '#fd7e14', // Orange
    '#20c997', // Teal
    '#e83e8c', // Pink
    '#6610f2', // Indigo
    '#795548', // <PERSON>
    '#607d8b', // Blue Grey
    '#9c27b0', // Deep Purple
    '#00bcd4', // Light Blue
    '#ff5722', // Deep Orange
    '#3f51b5', // Indigo Blue
    '#009688', // Teal <PERSON>
    '#ff9800', // Amber
    '#2196f3', // Blue
    '#4caf50', // Light Green
    '#f44336', // Red Shade
];

// Get all groups
$stmt = $pdo->query('SELECT id, name, color FROM user_groups ORDER BY id');
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<pre>";
echo "Current Groups and Colors:\n";
echo str_repeat('-', 60) . "\n";

$colorUsage = [];
foreach ($groups as &$group) {
    $color = $group['color'] ?? 'NULL';
    echo sprintf("ID: %3d | Name: %-30s | Color: %s\n", 
        $group['id'], 
        $group['name'], 
        $color
    );
    
    // Normalize color to lowercase for comparison
    if ($color !== 'NULL' && $color !== null) {
        $normalizedColor = strtolower($color);
        $group['normalized_color'] = $normalizedColor;
        
        if (!isset($colorUsage[$normalizedColor])) {
            $colorUsage[$normalizedColor] = [];
        }
        $colorUsage[$normalizedColor][] = ['name' => $group['name'], 'id' => $group['id']];
    }
}

// Find duplicate colors
echo "\n\nDuplicate Colors Found:\n";
echo str_repeat('-', 60) . "\n";
$hasDuplicates = false;
foreach ($colorUsage as $color => $groupData) {
    if (count($groupData) > 1) {
        $hasDuplicates = true;
        $names = array_map(function($g) { return $g['name']; }, $groupData);
        echo "Color $color used by: " . implode(', ', $names) . "\n";
    }
}

if (!$hasDuplicates) {
    echo "No duplicate colors found!\n";
}

// Update colors to ensure uniqueness
echo "\n\nUpdating Colors:\n";
echo str_repeat('-', 60) . "\n";

$usedColors = [];
$colorIndex = 0;

// First pass: collect unique colors
foreach ($groups as $group) {
    if (isset($group['normalized_color'])) {
        $normalizedColor = $group['normalized_color'];
        // Check if this color is unique (used by only one group)
        if (count($colorUsage[$normalizedColor]) === 1) {
            $usedColors[] = $normalizedColor;
            echo "Keeping {$group['name']} with color {$group['color']}\n";
        }
    }
}

// Second pass: assign new colors to groups with duplicate colors
foreach ($groups as $group) {
    if (isset($group['normalized_color'])) {
        $normalizedColor = $group['normalized_color'];
        // If this group has a duplicate color, assign a new one
        if (count($colorUsage[$normalizedColor]) > 1) {
            // Find next available color
            while ($colorIndex < count($colors) && in_array(strtolower($colors[$colorIndex]), $usedColors)) {
                $colorIndex++;
            }
            
            if ($colorIndex < count($colors)) {
                $newColor = $colors[$colorIndex];
                $stmt = $pdo->prepare('UPDATE user_groups SET color = ? WHERE id = ?');
                $stmt->execute([$newColor, $group['id']]);
                echo "Updated {$group['name']} (ID: {$group['id']}) from {$group['color']} to {$newColor}\n";
                $usedColors[] = strtolower($newColor);
                $colorIndex++;
            }
        }
    } else {
        // Group has no color, assign one
        while ($colorIndex < count($colors) && in_array(strtolower($colors[$colorIndex]), $usedColors)) {
            $colorIndex++;
        }
        
        if ($colorIndex < count($colors)) {
            $newColor = $colors[$colorIndex];
            $stmt = $pdo->prepare('UPDATE user_groups SET color = ? WHERE id = ?');
            $stmt->execute([$newColor, $group['id']]);
            echo "Assigned {$group['name']} (ID: {$group['id']}) color {$newColor}\n";
            $usedColors[] = strtolower($newColor);
            $colorIndex++;
        }
    }
}

echo "\n\nColor update completed successfully!\n";
echo "Redirecting to groups page in 3 seconds...\n";
echo "</pre>";

echo "<script>
setTimeout(function() {
    window.location.href = '/fit/public/users/groups';
}, 3000);
</script>";