<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fixing Invoice Type ID Mismatch</h2>\n";
    
    // Get both type IDs
    $stmt = $pdo->query("SELECT id, code, name FROM invoice_types WHERE code = 'RET'");
    $newType = $stmt->fetch();
    echo "invoice_types table: RET has ID {$newType['id']}\n";
    
    $stmt = $pdo->query("SELECT id, prefix, name FROM config_invoice_types WHERE prefix = 'RET'");
    $oldType = $stmt->fetch();
    echo "config_invoice_types table: RET has ID {$oldType['id']}\n\n";
    
    if ($newType['id'] != $oldType['id']) {
        echo "⚠️ ID mismatch detected!\n\n";
        
        echo "<h3>Option 1: Update config_invoice_types ID to match invoice_types</h3>\n";
        echo "This will change config_invoice_types.id from {$oldType['id']} to {$newType['id']}\n";
        echo "<a href='?action=update_config&new_id={$newType['id']}&old_id={$oldType['id']}'>Apply this fix</a>\n\n";
        
        echo "<h3>Option 2: Create mapping in code</h3>\n";
        echo "We'll need to update UnifiedInvoiceGenerator to use the correct ID based on table\n";
    } else {
        echo "✓ IDs match! No fix needed.\n";
    }
    
    if (isset($_GET['action']) && $_GET['action'] == 'update_config') {
        $newId = $_GET['new_id'];
        $oldId = $_GET['old_id'];
        
        echo "\n<h3>Applying Fix...</h3>\n";
        
        // First check if new ID already exists
        $stmt = $pdo->prepare("SELECT id FROM config_invoice_types WHERE id = :id");
        $stmt->execute(['id' => $newId]);
        if ($stmt->rowCount() > 0) {
            // Delete the duplicate
            $stmt = $pdo->prepare("DELETE FROM config_invoice_types WHERE id = :id");
            $stmt->execute(['id' => $oldId]);
            echo "✓ Removed duplicate entry\n";
        } else {
            // Update the ID
            $stmt = $pdo->prepare("UPDATE config_invoice_types SET id = :new_id WHERE id = :old_id");
            $stmt->execute(['new_id' => $newId, 'old_id' => $oldId]);
            echo "✓ Updated config_invoice_types ID from $oldId to $newId\n";
        }
        
        echo "\n<a href='test_retrocession_generation.php'>Test retrocession generation now</a>\n";
    }
    
    // Show all invoice types for reference
    echo "\n<h3>All Invoice Types</h3>\n";
    echo "<table border='1'>\n";
    echo "<tr><th>invoice_types</th><th>config_invoice_types</th></tr>\n";
    echo "<tr><td>\n";
    
    $stmt = $pdo->query("SELECT id, code, name FROM invoice_types ORDER BY id");
    while ($row = $stmt->fetch()) {
        echo "ID {$row['id']}: {$row['code']} - {$row['name']}<br>\n";
    }
    
    echo "</td><td>\n";
    
    $stmt = $pdo->query("SELECT id, prefix, name FROM config_invoice_types ORDER BY id");
    while ($row = $stmt->fetch()) {
        echo "ID {$row['id']}: {$row['prefix']} - {$row['name']}<br>\n";
    }
    
    echo "</td></tr></table>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}