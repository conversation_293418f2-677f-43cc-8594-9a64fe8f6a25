<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== FIXING DOCUMENT TYPE NAMES ===\n\n";
    
    // Check current names
    $stmt = $pdo->query("SELECT id, code, name FROM document_types");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $defaultNames = [
        'invoice' => ['fr' => 'Facture', 'en' => 'Invoice', 'de' => 'Rechnung'],
        'credit_note' => ['fr' => 'Note de crédit', 'en' => 'Credit Note', 'de' => 'Gutschrift'],
        'quote' => ['fr' => 'Devis', 'en' => 'Quote', 'de' => 'Angebot'],
        'proforma' => ['fr' => 'Facture pro-forma', 'en' => 'Pro-forma Invoice', 'de' => 'Pro-forma Rechnung'],
        'delivery_note' => ['fr' => 'Bon de livraison', 'en' => 'Delivery Note', 'de' => 'Lieferschein'],
        'receipt' => ['fr' => 'Reçu', 'en' => 'Receipt', 'de' => 'Quittung']
    ];
    
    foreach ($types as $type) {
        echo "Checking {$type['code']}...\n";
        $currentName = json_decode($type['name'], true);
        
        if (empty($currentName) || !is_array($currentName)) {
            if (isset($defaultNames[$type['code']])) {
                $newName = json_encode($defaultNames[$type['code']], JSON_UNESCAPED_UNICODE);
                $stmt = $pdo->prepare("UPDATE document_types SET name = ? WHERE id = ?");
                $stmt->execute([$newName, $type['id']]);
                echo "  - Fixed name for {$type['code']}\n";
            }
        } else {
            echo "  - Name OK: " . json_encode($currentName) . "\n";
        }
    }
    
    echo "\nDone!";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}