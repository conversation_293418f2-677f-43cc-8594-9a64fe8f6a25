<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Models\Client;
use App\Models\DocumentType;

header('Content-Type: text/plain');

echo "=== TESTING INVOICE CREATION ===\n\n";

try {
    $db = Flight::db();
    
    // Get a client to use
    echo "1. Finding a client...\n";
    $stmt = $db->query("SELECT id, first_name, last_name, company_name FROM clients WHERE is_active = 1 LIMIT 1");
    $client = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if (!$client) {
        echo "No active clients found. Creating a test client...\n";
        $stmt = $db->prepare("INSERT INTO clients (client_type, first_name, last_name, email, is_active) VALUES ('individual', 'Test', 'Client', '<EMAIL>', 1)");
        $stmt->execute();
        $clientId = $db->lastInsertId();
        echo "Created test client with ID: $clientId\n";
    } else {
        $clientId = $client['id'];
        $clientName = $client['company_name'] ?: $client['first_name'] . ' ' . $client['last_name'];
        echo "Using client: $clientName (ID: $clientId)\n";
    }
    
    // Get document type
    echo "\n2. Finding invoice document type...\n";
    $stmt = $db->query("SELECT id, code FROM document_types WHERE code = 'invoice' LIMIT 1");
    $docType = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if (!$docType) {
        echo "ERROR: No 'invoice' document type found!\n";
        exit;
    }
    echo "Using document type: {$docType['code']} (ID: {$docType['id']})\n";
    
    // Create test invoice data
    echo "\n3. Creating test invoice...\n";
    
    $invoiceData = [
        'document_type_id' => $docType['id'],
        'client_id' => $clientId,
        'status' => 'draft',
        'issue_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+30 days')),
        'subtotal' => 100.00,
        'vat_amount' => 17.00,
        'total' => 117.00,
        'currency' => 'EUR',
        'notes' => 'Test invoice created via script',
        'created_by' => $_SESSION['user']['id'] ?? 1
    ];
    
    echo "Invoice data:\n";
    foreach ($invoiceData as $key => $value) {
        echo "  $key: $value\n";
    }
    
    // Try to create using the Invoice model
    echo "\n4. Attempting to create invoice using Invoice model...\n";
    
    $invoice = new Invoice();
    
    try {
        $result = $invoice->createInvoice($invoiceData);
        
        if ($result) {
            echo "✓ SUCCESS! Invoice created with ID: " . $result['id'] . "\n";
            echo "  Invoice number: " . $result['invoice_number'] . "\n";
            echo "  Status: " . $result['status'] . "\n";
            
            // Verify it's in the database
            $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
            $stmt->execute([$result['id']]);
            $saved = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($saved) {
                echo "\n✓ Verified: Invoice is saved in database\n";
                echo "  You should now see it at: /fit/public/invoices?status=draft\n";
            } else {
                echo "\n✗ ERROR: Invoice not found in database after creation!\n";
            }
        } else {
            echo "✗ ERROR: createInvoice returned false/null\n";
        }
    } catch (Exception $e) {
        echo "✗ ERROR: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    
    // Check if invoice was saved
    echo "\n5. Checking all invoices in database:\n";
    $stmt = $db->query("SELECT id, invoice_number, status, total FROM invoices ORDER BY id DESC");
    $invoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($invoices) > 0) {
        foreach ($invoices as $inv) {
            echo sprintf("- ID: %d | Number: %s | Status: %s | Total: %.2f\n",
                $inv['id'],
                $inv['invoice_number'],
                $inv['status'],
                $inv['total']
            );
        }
    } else {
        echo "Still no invoices in database.\n";
    }
    
} catch (Exception $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}