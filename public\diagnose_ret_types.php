<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Diagnostic: All Invoice Types in Database</h2>";
    
    // Show ALL entries in config_invoice_types
    echo "<h3>ALL entries in config_invoice_types table:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types ORDER BY id");
    $allTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total entries: " . count($allTypes) . "</p>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Color</th><th>Active</th><th>Related to RET?</th>";
    echo "</tr>";
    
    foreach ($allTypes as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        // Check if related to retrocession
        $isRetRelated = (
            stripos($row['code'], 'ret') !== false || 
            stripos($row['prefix'], 'RET') !== false ||
            stripos($displayName, 'rétro') !== false ||
            stripos($displayName, 'retro') !== false
        );
        
        $rowStyle = $isRetRelated ? "background-color: #fff3cd;" : "";
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td><span style='display: inline-block; width: 20px; height: 20px; background-color: {$row['color']}; border: 1px solid #ccc;'></span></td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "<td>" . ($isRetRelated ? '<strong style="color: orange;">YES</strong>' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show specific searches
    echo "<h3>Specific Code Searches:</h3>";
    
    $searchCodes = ['ret', 'ret25', 'ret30', 'ret2', 'ret3', 'retr', 'retrocession'];
    foreach ($searchCodes as $code) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM config_invoice_types WHERE code = :code");
        $stmt->execute(['code' => $code]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Code '$code': " . ($result['count'] > 0 ? "<strong style='color: green;'>Found ({$result['count']})</strong>" : "<span style='color: red;'>Not found</span>") . "</p>";
    }
    
    // Show entries with RET in prefix
    echo "<h3>Entries with 'RET' in prefix:</h3>";
    $stmt = $db->query("SELECT id, code, prefix FROM config_invoice_types WHERE prefix LIKE '%RET%' ORDER BY prefix");
    $retPrefixes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($retPrefixes)) {
        echo "<p style='color: red;'>No entries found with RET in prefix!</p>";
    } else {
        foreach ($retPrefixes as $row) {
            echo "<p>ID: {$row['id']}, Code: <strong>{$row['code']}</strong>, Prefix: <strong>{$row['prefix']}</strong></p>";
        }
    }
    
    // Check invoice_types table too
    echo "<h3>invoice_types table (RET-related):</h3>";
    $stmt = $db->query("SELECT * FROM invoice_types WHERE code LIKE '%RET%' ORDER BY code");
    $invoiceTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>";
    foreach ($invoiceTypes as $row) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td>{$row['name']}</td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Analysis
    echo "<h3>Analysis:</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    
    $hasRet25 = false;
    $hasRet30 = false;
    $ret25Prefix = '';
    $ret30Prefix = '';
    
    foreach ($allTypes as $row) {
        if ($row['code'] == 'ret25' || $row['code'] == 'ret2') {
            $hasRet25 = true;
            $ret25Prefix = $row['prefix'];
        }
        if ($row['code'] == 'ret30' || $row['code'] == 'ret3') {
            $hasRet30 = true;
            $ret30Prefix = $row['prefix'];
        }
    }
    
    echo "<p><strong>Status:</strong></p>";
    echo "<ul>";
    echo "<li>RET25 (5% secretary): " . ($hasRet25 ? "✓ Found with prefix '$ret25Prefix'" : "✗ Missing") . "</li>";
    echo "<li>RET30 (10% secretary): " . ($hasRet30 ? "✓ Found with prefix '$ret30Prefix'" : "✗ Missing") . "</li>";
    echo "</ul>";
    
    if (!$hasRet25 || !$hasRet30 || $ret25Prefix != 'FAC-RET25' || $ret30Prefix != 'FAC-RET30') {
        echo "<p style='color: red;'><strong>Action needed:</strong> Configuration needs to be fixed.</p>";
    } else {
        echo "<p style='color: green;'><strong>Configuration looks good!</strong></p>";
    }
    
    echo "</div>";
    
    echo "<hr>";
    echo "<p><a href='/fit/public/fix_ret_codes_final.php'>Run Fix Script</a> | ";
    echo "<a href='/fit/public/rebuild_ret_types.php'>Run Rebuild Script</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}