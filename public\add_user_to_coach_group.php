<?php
// Add user to coach group

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $userId = $_POST['user_id'] ?? 0;
    $groupId = $_POST['group_id'] ?? 0;
    
    if ($userId && $groupId) {
        try {
            // Check if already member
            $stmt = $db->prepare("SELECT * FROM user_group_members WHERE user_id = ? AND group_id = ?");
            $stmt->execute([$userId, $groupId]);
            
            if ($stmt->fetch()) {
                echo "<p>User is already a member of this group!</p>";
            } else {
                // Check table structure first
                $stmt = $db->query("DESCRIBE user_group_members");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Add to group
                if (in_array('created_at', $columns)) {
                    $stmt = $db->prepare("
                        INSERT INTO user_group_members (user_id, group_id, created_at) 
                        VALUES (?, ?, NOW())
                    ");
                } else {
                    // Table doesn't have created_at column
                    $stmt = $db->prepare("
                        INSERT INTO user_group_members (user_id, group_id) 
                        VALUES (?, ?)
                    ");
                }
                $stmt->execute([$userId, $groupId]);
                
                echo "<h2>✅ Success!</h2>";
                echo "<p>User $userId has been added to group $groupId</p>";
                
                // Also add a test course
                echo "<h3>Adding test course...</h3>";
                
                // Check if user_courses table exists
                $stmt = $db->query("SHOW TABLES LIKE 'user_courses'");
                if ($stmt->fetch()) {
                    // Check table structure
                    $stmt = $db->query("DESCRIBE user_courses");
                    $courseColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    $fields = ['user_id', 'course_name', 'hourly_rate', 'vat_rate', 'is_active', 'display_order'];
                    $values = [$userId, 'Yoga Flow', 75.00, 16.00, 1, 1];
                    $placeholders = ['?', '?', '?', '?', '?', '?'];
                    
                    if (in_array('created_at', $courseColumns)) {
                        $fields[] = 'created_at';
                        $fields[] = 'updated_at';
                        $placeholders[] = 'NOW()';
                        $placeholders[] = 'NOW()';
                    }
                    
                    $sql = "INSERT INTO user_courses (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
                    $stmt = $db->prepare($sql);
                    $stmt->execute($values);
                    
                    echo "<p>Test course 'Yoga Flow' added with €75/hour TTC</p>";
                } else {
                    echo "<p style='color: orange;'>Note: user_courses table not found, skipping course creation</p>";
                }
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<p><a href='verify_user_14_groups.php'>Back to verification</a></p>";
echo "<p><a href='/fit/public/users/14/edit'>Go to User Edit Page</a></p>";
?>