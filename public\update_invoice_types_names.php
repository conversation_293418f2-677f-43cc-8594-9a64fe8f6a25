<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Updating Invoice Types Names</h2>";
    
    // Update the names
    echo "<h3>Updating names in invoice_types table:</h3>";
    
    $updates = [
        ['code' => 'RET25', 'name' => 'Rétrocession 25%'],
        ['code' => 'RET30', 'name' => 'Rétrocession 30%']
    ];
    
    foreach ($updates as $update) {
        $stmt = $db->prepare("UPDATE invoice_types SET name = :name WHERE code = :code");
        $stmt->execute($update);
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Updated {$update['code']} to '{$update['name']}'</p>";
        }
    }
    
    // Show final state
    echo "<h3>Final state of invoice_types table:</h3>";
    $stmt = $db->query("
        SELECT * FROM invoice_types 
        WHERE code LIKE '%RET%'
        ORDER BY code
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>";
    foreach ($types as $type) {
        $rowStyle = '';
        if ($type['code'] == 'RET2') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($type['code'] == 'RET3') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$type['id']}</td>";
        echo "<td><strong>{$type['code']}</strong></td>";
        echo "<td><strong>{$type['name']}</strong></td>";
        echo "<td>" . ($type['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✓ Configuration Complete!</h3>";
    echo "<p>The invoice types are now properly configured:</p>";
    echo "<ul>";
    echo "<li><strong>RET2</strong> - Used for 5% secretary fee → Displays as 'Rétrocession 25%'</li>";
    echo "<li><strong>RET3</strong> - Used for 10% secretary fee → Displays as 'Rétrocession 30%'</li>";
    echo "</ul>";
    echo "<p>You can now generate retrocession invoices successfully!</p>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession' style='color: #155724; font-weight: bold;'>→ Try bulk generation again</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}