<?php
/**
 * Test Invoice Email with PDF Attachment
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Services\EmailService;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Invoice Email with PDF</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .warning { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 4px; color: #856404; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Invoice Email with PDF Attachment</h1>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $invoiceId = 279;
        $recipientEmail = '<EMAIL>';
        
        echo '<div class="info">';
        echo '<p>Attempting to send invoice ' . $invoiceId . ' (FAC-DIV-2025-0190) to ' . htmlspecialchars($recipientEmail) . ' WITH PDF attachment...</p>';
        echo '</div>';
        
        try {
            // Clear any previous test logs
            $db = Flight::db();
            $stmt = $db->prepare("DELETE FROM email_logs WHERE invoice_id = :id AND subject LIKE '%Test%'");
            $stmt->execute([':id' => $invoiceId]);
            
            // Send invoice email (this should include PDF attachment)
            $emailService = new EmailService();
            $result = $emailService->sendInvoiceEmail($invoiceId, $recipientEmail);
            
            echo '<div class="info">';
            echo '<h2>Result:</h2>';
            echo '<pre>' . print_r($result, true) . '</pre>';
            echo '</div>';
            
            if (isset($result['success']) && $result['success']) {
                echo '<div class="success">';
                echo '<h2>✅ SUCCESS! Invoice email sent with PDF!</h2>';
                echo '<p>The email should now be in Mailhog with the PDF attachment.</p>';
                echo '<p><a href="http://localhost:8025" target="_blank" style="color: white;">Open Mailhog to check the email</a></p>';
                echo '</div>';
                
                // Check email log for attachment info
                $stmt = $db->prepare("
                    SELECT * FROM email_logs 
                    WHERE invoice_id = :id 
                    ORDER BY created_at DESC 
                    LIMIT 1
                ");
                $stmt->execute([':id' => $invoiceId]);
                $log = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($log) {
                    echo '<div class="info">';
                    echo '<h3>Email Log Details:</h3>';
                    echo '<table>';
                    echo '<tr><td>Log ID:</td><td>' . $log['id'] . '</td></tr>';
                    echo '<tr><td>Status:</td><td><strong>' . $log['status'] . '</strong></td></tr>';
                    echo '<tr><td>Sent At:</td><td>' . $log['sent_at'] . '</td></tr>';
                    echo '<tr><td>Subject:</td><td>' . htmlspecialchars($log['subject']) . '</td></tr>';
                    echo '<tr><td>Recipient:</td><td>' . htmlspecialchars($log['recipient_email']) . '</td></tr>';
                    
                    if ($log['attachments_sent']) {
                        $attachments = json_decode($log['attachments_sent'], true);
                        if ($attachments) {
                            echo '<tr><td>Attachments:</td><td>';
                            foreach ($attachments as $att) {
                                echo '📎 ' . htmlspecialchars($att['name']) . ' (' . htmlspecialchars($att['type']) . ')<br>';
                            }
                            echo '</td></tr>';
                        }
                    } else {
                        echo '<tr><td>Attachments:</td><td><span style="color: orange;">None recorded</span></td></tr>';
                    }
                    
                    echo '</table>';
                    echo '</div>';
                }
            } else {
                echo '<div class="error">';
                echo '<h2>❌ Failed to send email</h2>';
                echo '<p>Error: ' . htmlspecialchars($result['message'] ?? 'Unknown error') . '</p>';
                echo '</div>';
                
                // Show detailed error for PDF issues
                if (strpos($result['message'] ?? '', 'PDF') !== false || strpos($result['message'] ?? '', 'Config') !== false) {
                    echo '<div class="warning">';
                    echo '<p>The error appears to be PDF-related. The Config::get() issue has been fixed to Config::getValue().</p>';
                    echo '<p>If you\'re still seeing errors, there might be other issues with the PDF generation.</p>';
                    echo '</div>';
                }
            }
            
        } catch (Exception $e) {
            echo '<div class="error">';
            echo '<h2>Exception occurred:</h2>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
            echo '</div>';
        }
        
        echo '<hr>';
        echo '<p><a href="">Try Again</a> | <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Full Status</a></p>';
        
    } else {
        // Show current status
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT COUNT(*) as email_count,
                   MAX(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as has_successful
            FROM email_logs 
            WHERE invoice_id = 279
        ");
        $stmt->execute();
        $status = $stmt->fetch(PDO::FETCH_ASSOC);
        
        ?>
        <div class="info">
            <h2>Current Status</h2>
            <p>Invoice: FAC-DIV-2025-0190 (#279)</p>
            <p>Previous email attempts: <?php echo $status['email_count']; ?></p>
            <p>Successfully sent before: <?php echo $status['has_successful'] ? 'Yes' : 'No'; ?></p>
        </div>
        
        <div class="warning">
            <h2>What this test will do:</h2>
            <ol>
                <li>Generate a PDF for invoice FAC-DIV-2025-0190</li>
                <li>Create an email with the invoice details</li>
                <li>Attach the PDF to the email</li>
                <li>Send <NAME_EMAIL> via Mailhog</li>
                <li>Log the email with attachment details</li>
            </ol>
            <p><strong>Note:</strong> The Config::get() error has been fixed to use Config::getValue() instead.</p>
        </div>
        
        <form method="POST">
            <button type="submit">Send Invoice Email with PDF Attachment</button>
        </form>
        <?php
    }
    ?>
</body>
</html>