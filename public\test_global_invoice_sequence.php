<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing Global Invoice Sequence (Unified Numbering)</h2>";
    
    // Get the latest sent invoice across ALL types
    echo "<h3>1. Finding Latest SENT Invoice (Global Sequence):</h3>";
    
    $year = date('Y');
    $sql = "SELECT invoice_number, invoice_type_id, created_at 
            FROM invoices 
            WHERE invoice_number LIKE :prefix_pattern
            AND status = :status
            AND invoice_number LIKE :year_pattern
            ORDER BY 
                CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED) DESC,
                invoice_number DESC
            LIMIT 1";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':prefix_pattern' => 'FAC-%',
        ':status' => 'sent',
        ':year_pattern' => "%-$year-%"
    ]);
    
    $latestInvoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($latestInvoice) {
        echo "<div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px;'>";
        echo "<p><strong>Latest SENT Invoice:</strong> {$latestInvoice['invoice_number']}</p>";
        echo "<p><strong>Created:</strong> {$latestInvoice['created_at']}</p>";
        
        // Extract sequence number
        if (preg_match('/(\d{4})$/', $latestInvoice['invoice_number'], $matches)) {
            $currentSequence = intval($matches[1]);
            $nextSequence = $currentSequence + 1;
            $nextFormatted = str_pad($nextSequence, 4, '0', STR_PAD_LEFT);
            
            echo "<p><strong>Current Sequence:</strong> {$currentSequence}</p>";
            echo "<p><strong>Next Sequence:</strong> {$nextFormatted}</p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ No SENT invoices found for year $year</p>";
    }
    
    // Show recent invoices of all types
    echo "<h3>2. Recent Invoices (All Types) - Showing Sequence:</h3>";
    
    $sql = "SELECT i.id, i.invoice_number, i.status, it.prefix as type_prefix, i.created_at,
                   CAST(SUBSTRING(i.invoice_number, -4) AS UNSIGNED) as sequence_num
            FROM invoices i
            LEFT JOIN invoice_types it ON i.invoice_type_id = it.id
            WHERE i.invoice_number LIKE 'FAC-%'
            ORDER BY i.id DESC
            LIMIT 15";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Invoice Number</th><th>Type</th><th>Sequence</th><th>Status</th><th>Created</th>";
    echo "</tr>";
    
    foreach ($invoices as $inv) {
        $statusColor = ($inv['status'] === 'sent') ? '#28a745' : '#6c757d';
        $highlightRow = ($inv['status'] === 'sent') ? "style='background-color: #e8f5e9;'" : "";
        
        echo "<tr {$highlightRow}>";
        echo "<td>{$inv['id']}</td>";
        echo "<td><strong>{$inv['invoice_number']}</strong></td>";
        echo "<td>{$inv['type_prefix']}</td>";
        echo "<td style='text-align: center; font-weight: bold;'>{$inv['sequence_num']}</td>";
        echo "<td><span style='background-color: {$statusColor}; color: white; padding: 2px 5px; border-radius: 3px;'>{$inv['status']}</span></td>";
        echo "<td>{$inv['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test the next number for each type
    echo "<h3>3. Expected Next Numbers (With Global Sequence):</h3>";
    
    // Get the highest sequence from SENT invoices
    $sql = "SELECT MAX(CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED)) as max_sequence
            FROM invoices 
            WHERE invoice_number LIKE 'FAC-%' 
            AND status = 'sent'
            AND invoice_number LIKE :year_pattern";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([':year_pattern' => "%-$year-%"]);
    $maxSequence = $stmt->fetchColumn() ?: 0;
    $nextGlobalSequence = $maxSequence + 1;
    
    $types = ['LOY', 'LOC', 'RET30', 'RET25'];
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Global Sequence Status:</strong></p>";
    echo "<p>Highest SENT invoice sequence: <strong>{$maxSequence}</strong></p>";
    echo "<p>Next global sequence number: <strong>" . str_pad($nextGlobalSequence, 4, '0', STR_PAD_LEFT) . "</strong></p>";
    echo "<hr>";
    echo "<p><strong>Next invoice numbers should be:</strong></p>";
    echo "<ul>";
    foreach ($types as $type) {
        $nextNumber = "FAC-{$type}-{$year}-" . str_pad($nextGlobalSequence, 4, '0', STR_PAD_LEFT);
        echo "<li>{$type}: <strong>{$nextNumber}</strong></li>";
        $nextGlobalSequence++;
    }
    echo "</ul>";
    echo "</div>";
    
    // Check implementation status
    echo "<h3>4. Implementation Status:</h3>";
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>✅ Fixed:</strong> getLatestSentInvoiceNumber() now looks for FAC-% pattern globally</p>";
    echo "<p><strong>✅ Result:</strong> All invoice types (LOY, LOC, RET) will share the same sequence</p>";
    echo "<p><strong>⚠️ Note:</strong> Only SENT invoices count for the sequence (draft invoices are ignored)</p>";
    echo "</div>";
    
    echo "<h3>5. Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Create a new invoice of any type (LOY, LOC, RET)</li>";
    echo "<li>Save it as DRAFT first - number can be anything</li>";
    echo "<li>When you SEND the invoice, it will get the next sequential number</li>";
    echo "<li>The sequence continues across all types</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>