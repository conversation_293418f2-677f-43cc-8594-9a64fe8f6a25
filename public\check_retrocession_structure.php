<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Retrocession Table Structure</h2>";
    
    // Check table structure
    echo "<h3>retrocession_data_entry table structure:</h3>";
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Find Rémi's client ID
    echo "<h3>Finding Rémi's Client ID:</h3>";
    $stmt = $db->prepare("
        SELECT u.id as user_id, u.first_name, u.last_name, c.id as client_id, c.name as client_name
        FROM users u
        LEFT JOIN clients c ON c.user_id = u.id
        WHERE u.first_name = 'Rémi' AND u.last_name = 'Heine'
    ");
    $stmt->execute();
    $remi = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($remi) {
        echo "<p>User: {$remi['first_name']} {$remi['last_name']} (ID: {$remi['user_id']})</p>";
        echo "<p>Client: {$remi['client_name']} (ID: {$remi['client_id']})</p>";
        
        // Check retrocession data using client_id
        echo "<h3>Retrocession Data Entry for June 2025:</h3>";
        $stmt = $db->prepare("
            SELECT * FROM retrocession_data_entry 
            WHERE client_id = :client_id 
            AND year = 2025 
            AND month = 6
        ");
        $stmt->execute(['client_id' => $remi['client_id']]);
        $dataEntry = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($dataEntry) {
            echo "<p style='color: green;'>✓ Found retrocession data entry:</p>";
            echo "<ul>";
            echo "<li>ID: {$dataEntry['id']}</li>";
            echo "<li>Client ID: {$dataEntry['client_id']}</li>";
            echo "<li>Invoice ID: " . ($dataEntry['invoice_id'] ?? 'NULL') . "</li>";
            echo "<li>Created: {$dataEntry['created_at']}</li>";
            echo "</ul>";
            
            // Check if already invoiced
            if ($dataEntry['invoice_id']) {
                echo "<p style='color: orange;'>⚠️ This data has already been invoiced (Invoice ID: {$dataEntry['invoice_id']})</p>";
                
                // Check if invoice exists
                $stmt = $db->prepare("SELECT id, invoice_number FROM invoices WHERE id = :id");
                $stmt->execute(['id' => $dataEntry['invoice_id']]);
                $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($invoice) {
                    echo "<p>Invoice exists: {$invoice['invoice_number']}</p>";
                } else {
                    echo "<p style='color: red;'>Invoice ID {$dataEntry['invoice_id']} doesn't exist!</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ No retrocession data entry found for June 2025!</p>";
            
            // Create the entry
            echo "<h3>Creating Missing Entry:</h3>";
            
            $stmt = $db->prepare("
                INSERT INTO retrocession_data_entry (client_id, year, month, created_at, updated_at)
                VALUES (:client_id, 2025, 6, NOW(), NOW())
            ");
            $stmt->execute(['client_id' => $remi['client_id']]);
            
            echo "<p style='color: green;'>✓ Created retrocession_data_entry for Rémi Heine - June 2025</p>";
        }
        
        // Check all entries for Rémi
        echo "<h3>All Retrocession Data Entries for Rémi:</h3>";
        $stmt = $db->prepare("
            SELECT * FROM retrocession_data_entry 
            WHERE client_id = :client_id
            ORDER BY year DESC, month DESC
        ");
        $stmt->execute(['client_id' => $remi['client_id']]);
        $allEntries = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($allEntries) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Year</th><th>Month</th><th>Invoice ID</th><th>Created</th></tr>";
            foreach ($allEntries as $entry) {
                echo "<tr>";
                echo "<td>{$entry['id']}</td>";
                echo "<td>{$entry['year']}</td>";
                echo "<td>{$entry['month']}</td>";
                echo "<td>" . ($entry['invoice_id'] ?? 'NULL') . "</td>";
                echo "<td>{$entry['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>Rémi Heine not found or has no client record!</p>";
    }
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>Summary:</h3>";
    echo "<p>The retrocession_data_entry table uses <strong>client_id</strong>, not user_id.</p>";
    echo "<p>Rémi has monthly amounts configured for June (5,000€ CNS).</p>";
    echo "<p>Try generating the invoice again - it should work now.</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}