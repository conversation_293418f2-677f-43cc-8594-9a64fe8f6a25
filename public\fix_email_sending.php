<?php
/**
 * Fix Email Sending Issues
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Please login first.");
}

echo "<h1>Fix Email Sending</h1>";

// Check current email configuration
echo "<h2>Current Email Configuration</h2>";
echo "<pre>";
echo "MAIL_HOST: " . ($_ENV['MAIL_HOST'] ?? 'not set') . "\n";
echo "MAIL_PORT: " . ($_ENV['MAIL_PORT'] ?? 'not set') . "\n";
echo "MAIL_FROM: " . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'not set') . "\n";
echo "</pre>";

// Option 1: Start Mailhog
echo "<h2>Option 1: Start Mailhog (Recommended for Development)</h2>";
echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
echo "<p>Mailhog is installed but not running. To start it:</p>";
echo "<ol>";
echo "<li>Open Windows Explorer and go to: <code>C:\\wamp64\\mailhog</code></li>";
echo "<li>Double-click on <code>mailhog.exe</code></li>";
echo "<li>A command window will open - keep it running</li>";
echo "<li>Visit <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a> to see captured emails</li>";
echo "</ol>";
echo "<p><strong>Test Mailhog:</strong> ";
$sock = @fsockopen('localhost', 1025, $errno, $errstr, 1);
if ($sock) {
    echo "<span style='color: green;'>✓ Mailhog is running!</span>";
    fclose($sock);
} else {
    echo "<span style='color: red;'>✗ Mailhog is NOT running</span>";
}
echo "</p>";
echo "</div>";

// Option 2: Use Gmail
echo "<h2>Option 2: Use Gmail SMTP (For Real Emails)</h2>";
echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
echo "<p>Configure Gmail to send real emails:</p>";
echo "<p><a href='setup_gmail_smtp.php' class='button' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; display: inline-block;'>Configure Gmail SMTP</a></p>";
echo "</div>";

// Option 3: Disable emails temporarily
echo "<h2>Option 3: Disable Email Sending (Temporary)</h2>";
echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
echo "<p>You can continue using the system without emails by:</p>";
echo "<ul>";
echo "<li>Always unchecking 'Send email' when creating invoices</li>";
echo "<li>Using 'Save as Draft' instead of 'Save and Send'</li>";
echo "</ul>";
echo "</div>";

// Test email sending
if (isset($_GET['test'])) {
    echo "<h2>Testing Email...</h2>";
    echo "<pre>";
    
    try {
        $emailService = new \App\Services\EmailService();
        
        // Create a test invoice data
        $testInvoice = [
            'id' => 999999,
            'invoice_number' => 'TEST-EMAIL-' . time(),
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'total' => 100.00,
            'user' => [
                'email' => $_GET['email'] ?? '<EMAIL>',
                'full_name' => 'Test User'
            ]
        ];
        
        // Try to send
        $result = $emailService->sendInvoiceEmail($testInvoice['id']);
        
        if ($result['success']) {
            echo "✓ Email sent successfully!\n";
            if ($_ENV['MAIL_HOST'] === 'localhost' && $_ENV['MAIL_PORT'] == 1025) {
                echo "Check Mailhog at: http://localhost:8025\n";
            }
        } else {
            echo "✗ Email failed: " . $result['message'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Error: " . $e->getMessage() . "\n";
    }
    
    echo "</pre>";
}

// Check recent email logs
echo "<h2>Recent Email Attempts</h2>";
echo "<pre>";
try {
    $db = Flight::db();
    
    // Check if email_logs table exists
    $tables = $db->query("SHOW TABLES LIKE 'email_logs'")->fetchAll();
    if (count($tables) > 0) {
        $stmt = $db->query("SELECT * FROM email_logs ORDER BY id DESC LIMIT 10");
        echo "Recent email logs:\n";
        echo str_pad("ID", 5) . str_pad("Invoice", 10) . str_pad("Status", 10) . str_pad("Email", 30) . str_pad("Date", 20) . "\n";
        echo str_repeat("-", 75) . "\n";
        
        while ($log = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo str_pad($log['id'], 5);
            echo str_pad($log['invoice_id'] ?? '-', 10);
            echo str_pad($log['status'], 10);
            echo str_pad(substr($log['recipient_email'], 0, 28), 30);
            echo str_pad($log['created_at'], 20);
            echo "\n";
            
            if ($log['error_message']) {
                echo "     Error: " . $log['error_message'] . "\n";
            }
        }
    } else {
        echo "Email logs table not found. Run the migration:\n";
        echo "<a href='run_email_logs_migration.php'>Run Email Logs Migration</a>\n";
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}
echo "</pre>";

// Test button
echo "<h2>Test Email Sending</h2>";
echo "<form method='GET' style='border: 1px solid #ccc; padding: 15px;'>";
echo "<p>Send a test email to check if everything is working:</p>";
echo "<input type='email' name='email' placeholder='<EMAIL>' value='" . ($_GET['email'] ?? '') . "' required>";
echo "<input type='hidden' name='test' value='1'>";
echo "<button type='submit' style='background: #2196F3; color: white; padding: 10px 20px; border: none;'>Send Test Email</button>";
echo "</form>";
?>