<?php
/**
 * Run email logs table migration
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use Flight;

echo "<h1>Running Email Logs Migration</h1>";
echo "<pre>";

try {
    $db = Flight::db();
    
    // Read migration SQL
    $migrationFile = dirname(__DIR__) . '/database/migrations/083_create_email_logs_table.sql';
    if (!file_exists($migrationFile)) {
        die("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Execute migration
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            echo "Executing: " . substr($statement, 0, 50) . "...\n";
            $db->exec($statement);
        }
    }
    
    echo "\n✓ Email logs table created successfully!\n";
    
    // Verify table exists
    $tables = $db->query("SHOW TABLES LIKE 'email_logs'")->fetchAll();
    if ($tables) {
        echo "\n✓ Table 'email_logs' verified in database\n";
        
        // Show table structure
        echo "\nTable structure:\n";
        $columns = $db->query("DESCRIBE email_logs")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $column) {
            echo sprintf("  %-20s %-30s %s\n", 
                $column['Field'], 
                $column['Type'], 
                $column['Null'] === 'NO' ? 'NOT NULL' : 'NULL'
            );
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>