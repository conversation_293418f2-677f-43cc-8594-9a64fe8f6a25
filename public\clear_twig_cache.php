<?php
// Clear Twig Cache
header('Content-Type: text/html; charset=UTF-8');

echo "<h1>Clearing Twig Cache</h1>";

// Check cache directories
$cacheDir = __DIR__ . '/../storage/cache/twig';
$viewsCacheDir = __DIR__ . '/../storage/views';

echo "<h2>Cache Directories:</h2>";
echo "<ul>";
echo "<li>Twig cache: " . (is_dir($cacheDir) ? "EXISTS" : "NOT FOUND") . " - $cacheDir</li>";
echo "<li>Views cache: " . (is_dir($viewsCacheDir) ? "EXISTS" : "NOT FOUND") . " - $viewsCacheDir</li>";
echo "</ul>";

// Function to delete directory recursively
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    return rmdir($dir);
}

// Clear Twig cache
if (is_dir($cacheDir)) {
    $count = count(glob($cacheDir . '/*'));
    if (deleteDirectory($cacheDir)) {
        echo "<p class='success'>✓ Cleared $count files from Twig cache</p>";
    } else {
        echo "<p class='error'>✗ Failed to clear Twig cache</p>";
    }
} else {
    echo "<p>No Twig cache directory found</p>";
}

// Clear views cache if exists
if (is_dir($viewsCacheDir)) {
    $count = count(glob($viewsCacheDir . '/*'));
    if (deleteDirectory($viewsCacheDir)) {
        echo "<p class='success'>✓ Cleared $count files from views cache</p>";
    } else {
        echo "<p class='error'>✗ Failed to clear views cache</p>";
    }
}

// Also check for any other cache locations
$otherCaches = [
    __DIR__ . '/../cache',
    __DIR__ . '/../tmp/cache',
    __DIR__ . '/../var/cache'
];

foreach ($otherCaches as $cache) {
    if (is_dir($cache)) {
        echo "<p>Found cache directory: $cache</p>";
    }
}

echo "<hr>";
echo "<p><a href='/fit/public/invoices/create?duplicate=246'>Try invoice duplication again</a></p>";

?>
<style>
.success { color: green; }
.error { color: red; }
</style>