<?php

namespace App\Models;

use Flight;
use PDO;
use Exception;
use App\Helpers\MoneyHelper;

class Invoice extends \App\Core\Model
{
    protected $table = 'invoices';
    
    /**
     * Fields that contain monetary values
     */
    protected $monetary = [
        'subtotal',
        'vat_amount',
        'cns_base_amount',
        'secretariat_vat_amount',
        'total',
        'original_amount',
        'staff_limit_amount',
        'paid_amount'
    ];
    
    // Invoice types
    const TYPE_RENTAL = 'rental';
    const TYPE_HOURLY = 'hourly';
    const TYPE_RETROCESSION_30 = 'retrocession_30';
    const TYPE_RETROCESSION_25 = 'retrocession_25';
    const TYPE_SERVICE = 'service';
    
    // Invoice statuses
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_PAID = 'paid';
    const STATUS_PARTIAL = 'partial';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_CANCELLED = 'cancelled';
    
    protected $fillable = [
        'invoice_number',
        'document_type_id',
        'billable_type',
        'billable_id',
        'client_id',
        'user_id',
        'issue_date',
        'due_date',
        'payment_term_id',
        'payment_terms',
        'status',
        'subtotal',
        'vat_amount',
        'cns_base_amount',
        'secretariat_vat_amount',
        'total',
        'paid_amount',
        'notes',
        'internal_notes',
        'reference_invoice_id',
        'subject',
        'period',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'subtotal' => 'decimal:2',
        'vat_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'paid_amount' => 'decimal:2'
    ];
    
    /**
     * Get the latest SENT invoice number for a given document type (all invoice types share sequence)
     */
    private function getLatestSentInvoiceNumber($documentTypeId, $year = null)
    {
        $db = Flight::db();
        
        if (!$year) {
            $year = date('Y');
        }
        
        // Build query to find latest SENT invoice across ALL invoice types
        // This ensures all types (LOC, RET, etc.) share the same sequence
        $query = "
            SELECT invoice_number 
            FROM invoices 
            WHERE document_type_id = :doc_type 
            AND status = :status
            AND invoice_number LIKE :year_pattern
            ORDER BY 
                CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED) DESC,
                invoice_number DESC
            LIMIT 1
        ";
        
        $params = [
            ':doc_type' => $documentTypeId,
            ':status' => self::STATUS_SENT,
            ':year_pattern' => "%-$year-%"
        ];
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchColumn();
    }
    
    /**
     * Extract sequence number from invoice number
     */
    private function extractSequenceNumber($invoiceNumber)
    {
        // Match last 4 digits in the invoice number
        if (preg_match('/(\d{4})$/', $invoiceNumber, $matches)) {
            return intval($matches[1]);
        }
        return 0;
    }
    
    /**
     * Generate document number based on document type
     */
    public function generateDocumentNumber($documentTypeId, $invoiceTypeId = null)
    {
        $db = Flight::db();
        
        // Get document type configuration
        $stmt = $db->prepare("
            SELECT id, code, prefix, counter_type, current_number, current_year, current_month, number_format 
            FROM document_types 
            WHERE id = ?
        ");
        $stmt->execute([$documentTypeId]);
        $docType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$docType) {
            throw new Exception("Document type not found");
        }
        
        // Get invoice type prefix if invoice type is provided
        $invoiceTypePrefix = '';
        if ($invoiceTypeId && $docType['code'] === 'invoice') {
            $stmt = $db->prepare("SELECT id, code, prefix FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$invoiceTypeId]);
            $typeData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Debug logging
            error_log("generateDocumentNumber - Invoice type ID: $invoiceTypeId");
            error_log("generateDocumentNumber - Type data: " . json_encode($typeData));
            
            $invoiceTypePrefix = $typeData['prefix'] ?? 'GEN';
            error_log("generateDocumentNumber - Invoice type prefix: $invoiceTypePrefix");
        }
        
        // Determine sequence parameters based on counter type
        $year = date('Y');
        $month = date('m');
        $sequenceYear = null;
        $sequenceMonth = null;
        
        switch ($docType['counter_type']) {
            case 'yearly':
                $sequenceYear = $year;
                break;
            case 'monthly':
                $sequenceYear = $year;
                $sequenceMonth = $month;
                break;
            // 'global' doesn't use year/month
        }
        
        // NEW LOGIC: Get next number based on latest SENT invoice
        // All invoice types share the same sequence
        $latestInvoiceNumber = $this->getLatestSentInvoiceNumber($documentTypeId, $year);
        
        if ($latestInvoiceNumber) {
            // Extract the sequence number from the latest invoice
            $lastSequence = $this->extractSequenceNumber($latestInvoiceNumber);
            $nextNumber = $lastSequence + 1;
            
            error_log("generateDocumentNumber - Latest SENT invoice: $latestInvoiceNumber");
            error_log("generateDocumentNumber - Extracted sequence: $lastSequence");
            error_log("generateDocumentNumber - Next number: $nextNumber");
        } else {
            // No SENT invoices found for this year, start from 1
            $nextNumber = 1;
            error_log("generateDocumentNumber - No SENT invoices found for year $year, starting from 1");
        }
        
        // If custom number format is provided, use it
        if (!empty($docType['number_format'])) {
            $replacements = [
                'PREFIX' => $docType['prefix'],
                'TYPE' => $invoiceTypePrefix,
                'YYYY' => $year,
                'YY' => substr($year, -2),
                'MM' => $month,
                'M' => intval($month),
                'NNNNN' => str_pad($nextNumber, 5, '0', STR_PAD_LEFT),
                'NNNN' => str_pad($nextNumber, 4, '0', STR_PAD_LEFT),
                'NNN' => str_pad($nextNumber, 3, '0', STR_PAD_LEFT),
                'NN' => str_pad($nextNumber, 2, '0', STR_PAD_LEFT),
                'N' => $nextNumber
            ];
            
            return $this->formatInvoiceNumber($docType['number_format'], $replacements);
        }
        
        // Default format for invoices: FAC-{TYPE}-{YYYY}-{NNNN}
        error_log("generateDocumentNumber - Document code: {$docType['code']}, Invoice type prefix: '$invoiceTypePrefix'");
        
        if ($docType['code'] === 'invoice' && !empty($invoiceTypePrefix)) {
            $numberParts = [$docType['prefix'], $invoiceTypePrefix];
            
            if ($docType['counter_type'] === 'yearly' || $docType['counter_type'] === 'monthly') {
                $numberParts[] = $year;
            }
            
            if ($docType['counter_type'] === 'monthly') {
                $numberParts[] = $month;
            }
            
            $numberParts[] = str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            
            $result = implode('-', $numberParts);
            error_log("generateDocumentNumber - Generated number with type: $result");
            return $result;
        }
        
        // Default format for other document types (backward compatibility)
        $numberParts = [$docType['prefix']];
        
        if ($docType['counter_type'] === 'yearly' || $docType['counter_type'] === 'monthly') {
            $numberParts[] = $year;
        }
        
        if ($docType['counter_type'] === 'monthly') {
            $numberParts[] = $month;
        }
        
        $numberParts[] = str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
        
        $result = implode('-', $numberParts);
        error_log("generateDocumentNumber - Generated number without type: $result");
        return $result;
    }
    
    /**
     * Suggest document number without incrementing the sequence
     * Used for displaying suggested numbers in forms
     */
    public function suggestDocumentNumber($documentTypeId, $invoiceTypeId = null)
    {
        $db = Flight::db();
        
        // Get document type configuration
        $stmt = $db->prepare("
            SELECT id, code, prefix, counter_type, current_number, current_year, current_month, number_format 
            FROM document_types 
            WHERE id = ?
        ");
        $stmt->execute([$documentTypeId]);
        $docType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$docType) {
            throw new Exception("Document type not found");
        }
        
        // Get invoice type prefix if invoice type is provided
        $invoiceTypePrefix = '';
        if ($invoiceTypeId && $docType['code'] === 'invoice') {
            $stmt = $db->prepare("SELECT id, code, prefix FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$invoiceTypeId]);
            $typeData = $stmt->fetch(PDO::FETCH_ASSOC);
            $invoiceTypePrefix = $typeData['prefix'] ?? 'GEN';
        }
        
        // Determine sequence parameters based on counter type
        $year = date('Y');
        $month = date('m');
        $sequenceYear = null;
        $sequenceMonth = null;
        
        switch ($docType['counter_type']) {
            case 'yearly':
                $sequenceYear = $year;
                break;
            case 'monthly':
                $sequenceYear = $year;
                $sequenceMonth = $month;
                break;
            // 'global' doesn't use year/month
        }
        
        // Check if we should reuse deleted numbers
        $reuseNumbers = Flight::get('config.reuse_deleted_invoice_numbers') === 'true';
        $nextNumber = null;
        
        if ($reuseNumbers) {
            // Check for available deleted numbers
            $stmt = $db->prepare("
                SELECT sequence_number
                FROM deleted_invoice_numbers
                WHERE document_type_id = ?
                  AND (invoice_type_id = ? OR (invoice_type_id IS NULL AND ? IS NULL))
                  AND (year = ? OR (year IS NULL AND ? IS NULL))
                  AND (month = ? OR (month IS NULL AND ? IS NULL))
                  AND reused_at IS NULL
                ORDER BY sequence_number ASC
                LIMIT 1
            ");
            $stmt->execute([
                $documentTypeId, 
                $invoiceTypeId, $invoiceTypeId,
                $sequenceYear, $sequenceYear,
                $sequenceMonth, $sequenceMonth
            ]);
            $deletedNumber = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($deletedNumber) {
                $nextNumber = $deletedNumber['sequence_number'];
            }
        }
        
        // If no deleted number available, get next from sequence
        if ($nextNumber === null) {
            // Get current sequence without updating
            $stmt = $db->prepare("
                SELECT last_number 
                FROM document_sequences 
                WHERE document_type_id = ? 
                  AND (year = ? OR year IS NULL)
                  AND (month = ? OR month IS NULL)
            ");
            $stmt->execute([$documentTypeId, $sequenceYear, $sequenceMonth]);
            $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($sequence) {
                $nextNumber = $sequence['last_number'] + 1;
            } else {
                $nextNumber = 1;
            }
        }
        
        // If custom number format is provided, use it
        if (!empty($docType['number_format'])) {
            $replacements = [
                'PREFIX' => $docType['prefix'],
                'TYPE' => $invoiceTypePrefix,
                'YYYY' => $year,
                'YY' => substr($year, -2),
                'MM' => $month,
                'M' => intval($month),
                'NNNNN' => str_pad($nextNumber, 5, '0', STR_PAD_LEFT),
                'NNNN' => str_pad($nextNumber, 4, '0', STR_PAD_LEFT),
                'NNN' => str_pad($nextNumber, 3, '0', STR_PAD_LEFT),
                'NN' => str_pad($nextNumber, 2, '0', STR_PAD_LEFT),
                'N' => $nextNumber
            ];
            
            return $this->formatInvoiceNumber($docType['number_format'], $replacements);
        }
        
        // Default format for invoices: FAC-{TYPE}-{YYYY}-{NNNN}
        if ($docType['code'] === 'invoice' && !empty($invoiceTypePrefix)) {
            $numberParts = [$docType['prefix'], $invoiceTypePrefix];
            
            if ($docType['counter_type'] === 'yearly' || $docType['counter_type'] === 'monthly') {
                $numberParts[] = $year;
            }
            
            if ($docType['counter_type'] === 'monthly') {
                $numberParts[] = $month;
            }
            
            $numberParts[] = str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            
            return implode('-', $numberParts);
        }
        
        // Default format for other document types
        $numberParts = [$docType['prefix']];
        
        if ($docType['counter_type'] === 'yearly' || $docType['counter_type'] === 'monthly') {
            $numberParts[] = $year;
        }
        
        if ($docType['counter_type'] === 'monthly') {
            $numberParts[] = $month;
        }
        
        $numberParts[] = str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
        
        return implode('-', $numberParts);
    }
    
    /**
     * Format invoice number based on pattern
     */
    private function formatInvoiceNumber($pattern, $values)
    {
        $formatted = $pattern;
        
        // Replace placeholders
        foreach ($values as $key => $value) {
            // Handle padded numbers like {NUMBER:5}
            if (preg_match('/\{' . $key . ':(\d+)\}/', $pattern, $matches)) {
                $padLength = $matches[1];
                $paddedValue = str_pad($value, $padLength, '0', STR_PAD_LEFT);
                $formatted = str_replace($matches[0], $paddedValue, $formatted);
            } else {
                // Simple replacement
                $formatted = str_replace('{' . $key . '}', $value, $formatted);
            }
        }
        
        return $formatted;
    }
    
    /**
     * Create invoice with all Phase 3 features
     */
    public function createInvoice($data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get document type if not provided
            if (empty($data['document_type_id'])) {
                // Default to invoice type
                $stmt = $db->prepare("SELECT id FROM document_types WHERE code = 'invoice' LIMIT 1");
                $stmt->execute();
                $defaultType = $stmt->fetch(PDO::FETCH_ASSOC);
                $data['document_type_id'] = $defaultType['id'];
            }
            
            // Generate document number if not provided
            if (empty($data['invoice_number'])) {
                $data['invoice_number'] = $this->generateDocumentNumber($data['document_type_id'], $data['type_id'] ?? null);
            }
            
            // Set draft period if not specified
            if ($data['status'] === self::STATUS_DRAFT && empty($data['draft_until'])) {
                $data['draft_until'] = date('Y-m-d H:i:s', strtotime('+48 hours'));
            }
            
            // Calculate due date if not provided
            if (empty($data['due_date']) || $data['due_date'] === '0000-00-00') {
                // For retrocession invoices, default to 30 days
                $isRetrocession = isset($data['invoice_type']) && 
                                  in_array($data['invoice_type'], [self::TYPE_RETROCESSION_30, self::TYPE_RETROCESSION_25]);
                
                if ($isRetrocession || $data['status'] === self::STATUS_DRAFT) {
                    // Use 30 days as default for retrocession and drafts
                    $data['due_date'] = date('Y-m-d', strtotime($data['issue_date'] . ' +30 days'));
                } else {
                    // Use payment term if specified
                    if (!empty($data['payment_term_id'])) {
                        $stmt = $db->prepare("SELECT days FROM config_payment_terms WHERE id = ?");
                        $stmt->execute([$data['payment_term_id']]);
                        $term = $stmt->fetch(PDO::FETCH_ASSOC);
                        $days = $term ? $term['days'] : 30;
                    } else {
                        $days = 30; // Default to 30 days
                    }
                    $data['due_date'] = date('Y-m-d', strtotime($data['issue_date'] . ' +' . $days . ' days'));
                }
            }
            
            // Calculate totals if not provided
            if (empty($data['total'])) {
                $data['total'] = MoneyHelper::round($data['subtotal'] + $data['vat_amount']);
            }
            
            // Round all monetary values
            $data['subtotal'] = MoneyHelper::round($data['subtotal'] ?? 0);
            $data['vat_amount'] = MoneyHelper::round($data['vat_amount'] ?? 0);
            $data['cns_base_amount'] = MoneyHelper::round($data['cns_base_amount'] ?? 0);
            $data['secretariat_vat_amount'] = MoneyHelper::round($data['secretariat_vat_amount'] ?? 0);
            $data['total'] = MoneyHelper::round($data['total']);
            
            // Apply staff limit if applicable
            $staffLimitApplied = $this->applyStaffLimit($data);
            
            // Insert invoice
            $stmt = $db->prepare("
                INSERT INTO invoices (
                    invoice_number, document_type_id, type_id, invoice_type, template_id, profile_id,
                    client_id, user_id, status, issue_date, due_date, 
                    subtotal, vat_amount, cns_base_amount, secretariat_vat_amount, total,
                    original_amount, staff_limit_applied, staff_limit_amount,
                    secretariat_vat_note_shown, cns_reference, draft_until,
                    currency, notes, internal_notes, payment_terms,
                    reference_document_id, reference_document_number, credit_reason, created_by,
                    is_archived, subject, period
                ) VALUES (
                    :invoice_number, :document_type_id, :type_id, :invoice_type, :template_id, :profile_id,
                    :client_id, :user_id, :status, :issue_date, :due_date,
                    :subtotal, :vat_amount, :cns_base_amount, :secretariat_vat_amount, :total,
                    :original_amount, :staff_limit_applied, :staff_limit_amount,
                    :secretariat_vat_note_shown, :cns_reference, :draft_until,
                    :currency, :notes, :internal_notes, :payment_terms,
                    :reference_document_id, :reference_document_number, :credit_reason, :created_by,
                    :is_archived, :subject, :period
                )
            ");
            
            $stmt->execute([
                ':invoice_number' => $data['invoice_number'],
                ':document_type_id' => $data['document_type_id'],
                ':type_id' => $data['type_id'] ?? 1,
                ':invoice_type' => $data['invoice_type'] ?? null,
                ':template_id' => $data['template_id'] ?? null,
                ':profile_id' => $data['profile_id'] ?? null,
                ':client_id' => $data['client_id'] ?? null,
                ':user_id' => $data['user_id'] ?? null,
                ':status' => $data['status'] ?? self::STATUS_DRAFT,
                ':issue_date' => $data['issue_date'],
                ':due_date' => $data['due_date'], // Now always has a value from calculation above
                ':subtotal' => $data['subtotal'] ?? 0,
                ':vat_amount' => $data['vat_amount'] ?? 0,
                ':cns_base_amount' => $data['cns_base_amount'] ?? 0,
                ':secretariat_vat_amount' => $data['secretariat_vat_amount'] ?? 0,
                ':total' => $data['total'],
                ':original_amount' => $data['original_amount'] ?? null,
                ':staff_limit_applied' => $data['staff_limit_applied'] ?? false,
                ':staff_limit_amount' => $data['staff_limit_amount'] ?? null,
                ':secretariat_vat_note_shown' => $data['secretariat_vat_note_shown'] ?? false,
                ':cns_reference' => $data['cns_reference'] ?? null,
                ':draft_until' => $data['draft_until'] ?? null,
                ':currency' => $data['currency'] ?? 'EUR',
                ':notes' => $data['notes'] ?? null,
                ':internal_notes' => $data['internal_notes'] ?? null,
                ':payment_terms' => $data['payment_terms'] ?? 'Dès réception',
                ':reference_document_id' => $data['reference_document_id'] ?? null,
                ':reference_document_number' => $data['reference_document_number'] ?? null,
                ':credit_reason' => $data['credit_reason'] ?? null,
                ':created_by' => $data['created_by'] ?? $_SESSION['user_id'] ?? 1,
                ':is_archived' => 0,  // Always set to 0 (not archived) for new invoices
                ':subject' => $data['subject'] ?? null,
                ':period' => $data['period'] ?? null
            ]);
            
            $invoiceId = $db->lastInsertId();
            
            // Add invoice lines if provided
            if (!empty($data['lines'])) {
                $this->addInvoiceLines($invoiceId, $data['lines']);
            }
            
            // Create retrocession record if this is a retrocession invoice
            if (isset($data['invoice_type']) && in_array($data['invoice_type'], [self::TYPE_RETROCESSION_30, self::TYPE_RETROCESSION_25])) {
                $this->createRetrocessionRecord($invoiceId, $data);
            }
            
            $db->commit();
            
            return $this->getById($invoiceId);
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    
    /**
     * Create retrocession record
     */
    private function createRetrocessionRecord($invoiceId, $data)
    {
        $db = Flight::db();
        
        // Calculate VAT amount using MoneyHelper
        $secretariatTvac = MoneyHelper::round($data['secretariat_tvac'] ?? 0);
        $vatAmount = MoneyHelper::calculateTax($secretariatTvac, 17, true);
        $secretariatHtva = MoneyHelper::round($secretariatTvac - $vatAmount);
        
        $stmt = $db->prepare("
            INSERT INTO invoice_retrocessions (
                invoice_id, total_amount, cns_amount, patient_amount,
                cns_percentage, patient_percentage, secretariat_percentage,
                secretariat_tvac, secretariat_htva, vat_amount,
                has_overrides, override_notes
            ) VALUES (
                :invoice_id, :total_amount, :cns_amount, :patient_amount,
                :cns_percentage, :patient_percentage, :secretariat_percentage,
                :secretariat_tvac, :secretariat_htva, :vat_amount,
                :has_overrides, :override_notes
            )
        ");
        
        $stmt->execute([
            ':invoice_id' => $invoiceId,
            ':total_amount' => $data['retro_total_amount'] ?? 0,
            ':cns_amount' => $data['retro_cns_amount'] ?? 0,
            ':patient_amount' => $data['retro_patient_amount'] ?? 0,
            ':cns_percentage' => $data['retro_cns_percentage'] ?? 20,
            ':patient_percentage' => $data['retro_patient_percentage'] ?? 20,
            ':secretariat_percentage' => $data['retro_secretariat_percentage'] ?? 10,
            ':secretariat_tvac' => $secretariatTvac,
            ':secretariat_htva' => $secretariatHtva,
            ':vat_amount' => $vatAmount,
            ':has_overrides' => $data['retro_has_overrides'] ?? false,
            ':override_notes' => $data['retro_override_notes'] ?? null
        ]);
    }
    
    /**
     * Get invoice by ID with caching
     */
    public function getById($id)
    {
        $cache = Flight::cache();
        $cacheKey = "invoice_{$id}";
        
        // Try to get from cache first
        $invoice = $cache->get($cacheKey);
        if ($invoice !== null) {
            return $invoice;
        }
        
        // Fetch from database
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$id]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Cache for 1 hour
        if ($invoice) {
            $cache->set($cacheKey, $invoice, 3600);
        }
        
        return $invoice;
    }
    
    /**
     * Apply staff invoice limit if applicable
     */
    private function applyStaffLimit(&$data)
    {
        $db = Flight::db();
        
        // Check if staff limit is enabled
        $stmt = $db->query("SELECT value FROM config WHERE `key` = 'staff_invoice_limit_enabled'");
        $limitEnabled = $stmt->fetchColumn();
        
        if ($limitEnabled != '1') {
            return false;
        }
        
        // Check if client is a staff member
        $stmt = $db->prepare("SELECT is_staff_member, staff_limit_override FROM clients WHERE id = ?");
        $stmt->execute([$data['client_id']]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$client || !$client['is_staff_member']) {
            return false;
        }
        
        // Get the limit configuration
        $config = [];
        $stmt = $db->query("SELECT `key`, value FROM config WHERE `key` LIKE 'staff_invoice_limit_%'");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $config[$row['key']] = $row['value'];
        }
        
        // Check validity dates
        $today = date('Y-m-d');
        if (!empty($config['staff_invoice_limit_valid_from']) && $today < $config['staff_invoice_limit_valid_from']) {
            return false;
        }
        if (!empty($config['staff_invoice_limit_valid_to']) && $today > $config['staff_invoice_limit_valid_to']) {
            return false;
        }
        
        // Get the limit amount - check in order: individual override, group limit, global limit
        $limitAmount = null;
        
        // 1. Check individual override
        if (!empty($client['staff_limit_override'])) {
            $limitAmount = $client['staff_limit_override'];
        } else {
            // 2. Check group-based limits
            $stmt = $db->prepare("
                SELECT sil.limit_amount 
                FROM staff_invoice_limits sil
                JOIN clients c ON c.id = ?
                JOIN user_groups_members ugm ON ugm.user_id = c.created_by
                WHERE sil.group_id = ugm.group_id
                  AND sil.is_active = 1
                  AND sil.valid_from <= CURDATE()
                  AND (sil.valid_to IS NULL OR sil.valid_to >= CURDATE())
                ORDER BY sil.limit_amount DESC
                LIMIT 1
            ");
            $stmt->execute([$data['client_id']]);
            $groupLimit = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($groupLimit) {
                $limitAmount = $groupLimit['limit_amount'];
            } else {
                // 3. Use global limit
                $limitAmount = $config['staff_invoice_limit_amount'] ?? 5000;
            }
        }
        
        // Check if invoice exceeds limit
        if ($data['total'] > $limitAmount) {
            // Store original amount
            $data['original_amount'] = $data['total'];
            $data['staff_limit_applied'] = true;
            $data['staff_limit_amount'] = $limitAmount;
            
            // Apply the limit
            $ratio = $limitAmount / $data['total'];
            $data['subtotal'] = MoneyHelper::round($data['subtotal'] * $ratio);
            $data['vat_amount'] = MoneyHelper::round($data['vat_amount'] * $ratio);
            $data['total'] = $limitAmount;
            
            // Add note about limit
            $limitMessage = $config['staff_invoice_limit_message'] ?? 'Invoice amount has been limited to the maximum allowed for staff members';
            $data['internal_notes'] = trim(($data['internal_notes'] ?? '') . "\n\n" . $limitMessage);
            
            // Adjust invoice lines if present
            if (!empty($data['lines'])) {
                foreach ($data['lines'] as &$line) {
                    $line['unit_price'] = MoneyHelper::round($line['unit_price'] * $ratio);
                    $line['line_total'] = $line['quantity'] * $line['unit_price'];
                }
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if invoice can be edited
     */
    public function canEdit($invoiceId = null)
    {
        if ($invoiceId) {
            $invoice = $this->getById($invoiceId);
        } else {
            $invoice = $this;
        }
        
        // Cannot edit if locked
        if (!empty($invoice['locked_at'])) {
            return false;
        }
        
        // Cannot edit if not draft
        if ($invoice['status'] !== self::STATUS_DRAFT) {
            return false;
        }
        
        // Draft period check removed - drafts should remain editable until sent
        // The draft_until field can still be used for informational purposes
        // but won't prevent editing
        
        return true;
    }
    
    /**
     * Lock invoice
     */
    public function lockInvoice($invoiceId, $userId = null)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE invoices 
            SET locked_at = NOW(), 
                locked_by = :locked_by,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        return $stmt->execute([
            ':id' => $invoiceId,
            ':locked_by' => $userId ?? $_SESSION['user_id'] ?? 1
        ]);
    }
    
    /**
     * Update invoice status
     */
    public function updateStatus($invoiceId, $status)
    {
        $db = Flight::db();
        
        // Validate status transition
        if (!$this->isValidStatusTransition($invoiceId, $status)) {
            throw new Exception("Invalid status transition");
        }
        
        $updates = ['status' => $status];
        
        // Get current invoice to check if it's transitioning from draft to sent
        $currentInvoice = $this->getById($invoiceId);
        
        // Set additional fields based on status
        switch ($status) {
            case self::STATUS_SENT:
                $updates['sent_at'] = date('Y-m-d H:i:s');
                
                // If transitioning from draft to sent, regenerate invoice number
                if ($currentInvoice['status'] === self::STATUS_DRAFT) {
                    // Get the latest sequence number and generate new invoice number
                    $newInvoiceNumber = $this->generateDocumentNumber(
                        $currentInvoice['document_type_id'], 
                        $currentInvoice['type_id']
                    );
                    $updates['invoice_number'] = $newInvoiceNumber;
                    error_log("updateStatus - Regenerated invoice number for draft->sent: $newInvoiceNumber");
                }
                break;
            case self::STATUS_PAID:
                $updates['paid_at'] = date('Y-m-d H:i:s');
                break;
        }
        
        $setClause = [];
        $params = ['id' => $invoiceId];
        
        foreach ($updates as $field => $value) {
            $setClause[] = "$field = :$field";
            $params[$field] = $value;
        }
        
        $sql = "UPDATE invoices SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = :id";
        $stmt = $db->prepare($sql);
        
        return $stmt->execute($params);
    }
    
    /**
     * Validate status transition
     */
    private function isValidStatusTransition($invoiceId, $newStatus)
    {
        $invoice = $this->getById($invoiceId);
        $currentStatus = $invoice['status'];
        
        // Define valid transitions
        $validTransitions = [
            self::STATUS_DRAFT => [self::STATUS_SENT, self::STATUS_CANCELLED],
            self::STATUS_SENT => [self::STATUS_PAID, self::STATUS_PARTIAL, self::STATUS_OVERDUE, self::STATUS_CANCELLED],
            self::STATUS_PARTIAL => [self::STATUS_PAID, self::STATUS_OVERDUE],
            self::STATUS_OVERDUE => [self::STATUS_PAID, self::STATUS_PARTIAL],
            self::STATUS_PAID => [], // No transitions from paid
            self::STATUS_CANCELLED => [] // No transitions from cancelled
        ];
        
        return in_array($newStatus, $validTransitions[$currentStatus] ?? []);
    }
    
    /**
     * Calculate totals from lines
     */
    public function calculateTotals($invoiceId)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                SUM(line_total) as subtotal,
                SUM(vat_amount) as total_vat
            FROM invoice_lines
            WHERE invoice_id = ?
        ");
        $stmt->execute([$invoiceId]);
        $totals = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $subtotal = MoneyHelper::round($totals['subtotal'] ?? 0);
        $vatAmount = MoneyHelper::round($totals['total_vat'] ?? 0);
        $total = MoneyHelper::round($subtotal + $vatAmount); // Fix: Total should be subtotal + VAT
        
        // Update invoice totals
        $stmt = $db->prepare("
            UPDATE invoices 
            SET subtotal = :subtotal,
                vat_amount = :vat_amount,
                total = :total,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':subtotal' => $subtotal,
            ':vat_amount' => $vatAmount,
            ':total' => $total,
            ':id' => $invoiceId
        ]);
        
        return [
            'subtotal' => $subtotal,
            'vat_amount' => $vatAmount,
            'total' => $total
        ];
    }
    
    /**
     * Get invoice with all relations (with caching)
     */
    public function getInvoiceWithDetails($invoiceId)
    {
        $cache = Flight::cache();
        $cacheKey = "invoice_details_{$invoiceId}";
        
        // Try to get from cache first
        $cachedData = $cache->get($cacheKey);
        if ($cachedData !== null) {
            return $cachedData;
        }
        
        $invoice = $this->getById($invoiceId);
        
        if (!$invoice) {
            return null;
        }
        
        // Get invoice lines (from invoice_lines table)
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT * FROM invoice_lines
            WHERE invoice_id = ? 
            ORDER BY sort_order, id
        ");
        $stmt->execute([$invoiceId]);
        $invoice['lines'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get retrocession details if applicable
        if (in_array($invoice['invoice_type'], [self::TYPE_RETROCESSION_30, self::TYPE_RETROCESSION_25])) {
            $stmt = $db->prepare("SELECT * FROM invoice_retrocessions WHERE invoice_id = ?");
            $stmt->execute([$invoiceId]);
            $invoice['retrocession'] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Get client details
        if (!empty($invoice['client_id'])) {
            $client = new Client();
            $invoice['client'] = $client->getById($invoice['client_id']);
        }
        
        // Get user details if applicable
        if (!empty($invoice['user_id'])) {
            $stmt = $db->prepare("
                SELECT u.*, CONCAT(u.first_name, ' ', u.last_name) as full_name,
                       COALESCE(u.billing_email, u.email) as invoice_email
                FROM users u
                WHERE u.id = ?
            ");
            $stmt->execute([$invoice['user_id']]);
            $invoice['user'] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Get document type details
        if (!empty($invoice['document_type_id'])) {
            $stmt = $db->prepare("SELECT * FROM document_types WHERE id = ?");
            $stmt->execute([$invoice['document_type_id']]);
            $invoice['document_type'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Decode JSON fields
            if ($invoice['document_type']) {
                $invoice['document_type']['name'] = json_decode($invoice['document_type']['name'], true);
                $invoice['document_type']['description'] = json_decode($invoice['document_type']['description'], true);
            }
        }
        
        // Get invoice type details (legacy)
        if (!empty($invoice['type_id'])) {
            $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$invoice['type_id']]);
            $invoice['type_details'] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Cache the complete data for 30 minutes
        $cache->set($cacheKey, $invoice, 1800);
        
        return $invoice;
    }
    
    /**
     * Create credit note from invoice
     */
    public function createCreditNote($originalInvoiceId, $data)
    {
        $originalInvoice = $this->getInvoiceWithDetails($originalInvoiceId);
        
        if (!$originalInvoice) {
            throw new Exception("Original invoice not found");
        }
        
        // Get credit note document type
        $db = Flight::db();
        $stmt = $db->prepare("SELECT id FROM document_types WHERE code = 'credit_note'");
        $stmt->execute();
        $cnType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$cnType) {
            throw new Exception("Credit note document type not found");
        }
        
        // Prepare credit note data
        $creditNoteData = array_merge($data, [
            'document_type_id' => $cnType['id'],
            'type_id' => $originalInvoice['type_id'],
            'invoice_type' => 'credit_note',
            'client_id' => $originalInvoice['client_id'],
            'reference_document_id' => $originalInvoiceId,
            'reference_document_number' => $originalInvoice['invoice_number'],
            'subtotal' => -abs($data['amount'] ?? $originalInvoice['subtotal']),
            'vat_amount' => -abs($data['vat_amount'] ?? $originalInvoice['vat_amount']),
            'total' => -abs($data['total'] ?? $originalInvoice['total']),
            'status' => self::STATUS_SENT,
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d')
        ]);
        
        // Copy invoice lines as negative amounts if not provided
        if (empty($data['lines']) && !empty($originalInvoice['lines'])) {
            $creditNoteData['lines'] = [];
            foreach ($originalInvoice['lines'] as $line) {
                $creditNoteData['lines'][] = [
                    'description' => $line['description'],
                    'quantity' => $line['quantity'],
                    'unit_price' => -abs($line['unit_price']),
                    'vat_rate' => $line['vat_rate'],
                    'line_type' => $line['line_type']
                ];
            }
        }
        
        return $this->createInvoice($creditNoteData);
    }
    
    /**
     * Add invoice lines
     */
    public function addInvoiceLines($invoiceId, $lines)
    {
        $db = Flight::db();
        
        foreach ($lines as $index => $line) {
            // Skip if no description
            if (empty($line['description'])) {
                continue;
            }
            
            // Calculate VAT and totals
            $quantity = $line['quantity'] ?? 1;
            $unitPrice = $line['unit_price'] ?? 0;
            $vatRate = $line['vat_rate'] ?? 0;
            
            $subtotal = MoneyHelper::round($quantity * $unitPrice);
            $vatAmount = MoneyHelper::round($subtotal * ($vatRate / 100));
            $lineTotal = MoneyHelper::round($subtotal + $vatAmount);
            
            // Determine line type based on description or default to 'service'
            $lineType = $line['line_type'] ?? 'service';
            
            $stmt = $db->prepare("
                INSERT INTO invoice_lines (
                    invoice_id, line_type, description, quantity, unit_price,
                    vat_rate, line_total, sort_order, item_id
                ) VALUES (
                    :invoice_id, :line_type, :description, :quantity, :unit_price,
                    :vat_rate, :line_total, :sort_order, :item_id
                )
            ");
            
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':line_type' => $lineType,
                ':description' => $line['description'],
                ':quantity' => $quantity,
                ':unit_price' => $unitPrice,
                ':vat_rate' => $vatRate,
                ':line_total' => $lineTotal,
                ':sort_order' => isset($line['sort_order']) ? $line['sort_order'] : $index,
                ':item_id' => $line['item_id'] ?? null
            ]);
        }
    }
    
    /**
     * Clear invoice cache
     */
    public function clearInvoiceCache($invoiceId)
    {
        $cache = Flight::cache();
        $cache->forget("invoice_{$invoiceId}");
        $cache->forget("invoice_details_{$invoiceId}");
    }
    
    /**
     * Update invoice with cache invalidation
     */
    public function updateInvoice($invoiceId, $data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Define allowed fields for update
            $allowedFields = [
                'status', 'issue_date', 'due_date', 'notes', 'internal_notes',
                'payment_terms', 'footer_text', 'subtotal', 'vat_amount', 'total'
            ];
            
            // Filter data to only include allowed fields
            $updateData = array_intersect_key($data, array_flip($allowedFields));
            
            if (!empty($updateData)) {
                $setClause = [];
                $params = ['id' => $invoiceId];
                
                foreach ($updateData as $field => $value) {
                    $setClause[] = "{$field} = :{$field}";
                    $params[$field] = $value;
                }
                
                $sql = "UPDATE invoices SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = :id";
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
            }
            
            // Update invoice lines if provided
            if (!empty($data['lines'])) {
                // Delete existing lines
                $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                $stmt->execute([$invoiceId]);
                
                // Add new lines
                $this->addInvoiceLines($invoiceId, $data['lines']);
            }
            
            // Recalculate totals
            $this->calculateTotals($invoiceId);
            
            $db->commit();
            
            // Clear cache after successful update
            $this->clearInvoiceCache($invoiceId);
            
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Archive an invoice
     */
    public function archiveInvoice($invoiceId, $userId = null, $reason = null)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get current invoice
            $invoice = $this->getById($invoiceId);
            if (!$invoice) {
                throw new Exception("Invoice not found");
            }
            
            // Check if invoice can be archived (only paid or cancelled)
            if (!in_array($invoice['status'], [self::STATUS_PAID, self::STATUS_CANCELLED])) {
                throw new Exception("Only paid or cancelled invoices can be archived");
            }
            
            // Archive the invoice
            $stmt = $db->prepare("
                UPDATE invoices 
                SET is_archived = TRUE, 
                    archived_at = NOW(), 
                    archived_by = :archived_by,
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $userId = $userId ?? $_SESSION['user_id'] ?? 1;
            $stmt->execute([
                ':id' => $invoiceId,
                ':archived_by' => $userId
            ]);
            
            // Log the archive action
            $stmt = $db->prepare("
                INSERT INTO invoice_archive_logs (invoice_id, action, user_id, reason)
                VALUES (:invoice_id, 'archived', :user_id, :reason)
            ");
            
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':user_id' => $userId,
                ':reason' => $reason
            ]);
            
            $db->commit();
            
            // Clear cache
            $this->clearInvoiceCache($invoiceId);
            
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Restore an archived invoice
     */
    public function restoreInvoice($invoiceId, $userId = null, $reason = null)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Restore the invoice
            $stmt = $db->prepare("
                UPDATE invoices 
                SET is_archived = FALSE, 
                    archived_at = NULL, 
                    archived_by = NULL,
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $stmt->execute([':id' => $invoiceId]);
            
            // Log the restore action
            $stmt = $db->prepare("
                INSERT INTO invoice_archive_logs (invoice_id, action, user_id, reason)
                VALUES (:invoice_id, 'restored', :user_id, :reason)
            ");
            
            $userId = $userId ?? $_SESSION['user_id'] ?? 1;
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':user_id' => $userId,
                ':reason' => $reason
            ]);
            
            $db->commit();
            
            // Clear cache
            $this->clearInvoiceCache($invoiceId);
            
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Delete an invoice
     */
    public function delete($id = null)
    {
        if ($id === null && isset($this->attributes[$this->primaryKey])) {
            $id = $this->attributes[$this->primaryKey];
        }
        
        if (!$id) {
            return false;
        }
        
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Check if invoice number reuse is enabled
            $reuseNumbers = Flight::get('config.reuse_deleted_invoice_numbers') === 'true';
            
            // Get invoice details before deletion (for tracking deleted numbers)
            $invoice = null;
            if ($reuseNumbers) {
                $stmt = $db->prepare("
                    SELECT i.*, dt.counter_type
                    FROM invoices i
                    LEFT JOIN document_types dt ON i.document_type_id = dt.id
                    WHERE i.id = ?
                ");
                $stmt->execute([$id]);
                $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
            }
            
            // Delete related records first
            // Delete invoice lines
            $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
            $stmt->execute([$id]);
            
            // Delete payment allocations
            $stmt = $db->prepare("DELETE FROM payment_allocations WHERE invoice_id = ?");
            $stmt->execute([$id]);
            
            // Delete retrocession data if exists
            $stmt = $db->prepare("DELETE FROM invoice_retrocessions WHERE invoice_id = ?");
            $stmt->execute([$id]);
            
            // Delete the invoice
            $stmt = $db->prepare("DELETE FROM invoices WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            // Track deleted invoice number if enabled and invoice was found
            if ($reuseNumbers && $invoice && $result) {
                $this->trackDeletedInvoiceNumber($invoice);
            }
            
            $db->commit();
            
            // Clear cache
            $this->clearInvoiceCache($id);
            
            return $result;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Track deleted invoice number for potential reuse
     */
    private function trackDeletedInvoiceNumber($invoice)
    {
        $db = Flight::db();
        
        // Extract sequence number from invoice number
        // Handle various formats: FAC-2025-0001, FAC-LOY-2025-0001, etc.
        $invoiceNumber = $invoice['invoice_number'];
        $parts = explode('-', $invoiceNumber);
        $sequenceNumber = intval(end($parts));
        
        // Determine year and month based on counter type
        $year = null;
        $month = null;
        
        if ($invoice['counter_type'] === 'yearly') {
            // Extract year from invoice number (e.g., FAC-2025-0001)
            foreach ($parts as $part) {
                if (preg_match('/^20\d{2}$/', $part)) {
                    $year = intval($part);
                    break;
                }
            }
        } elseif ($invoice['counter_type'] === 'monthly') {
            // For monthly counters, use issue date
            $issueDate = new DateTime($invoice['issue_date']);
            $year = intval($issueDate->format('Y'));
            $month = intval($issueDate->format('n'));
        }
        
        // Insert into deleted numbers pool
        $stmt = $db->prepare("
            INSERT INTO deleted_invoice_numbers (
                document_type_id,
                invoice_type_id,
                invoice_number,
                year,
                month,
                sequence_number,
                deleted_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $invoice['document_type_id'],
            $invoice['type_id'] ?? null,
            $invoiceNumber,
            $year,
            $month,
            $sequenceNumber,
            $_SESSION['user_id'] ?? null
        ]);
    }
    
    /**
     * Get archive statistics by document type
     */
    public function getArchiveStatistics()
    {
        $db = Flight::db();
        
        $stmt = $db->query("
            SELECT 
                dt.id as document_type_id,
                dt.code as document_type_code,
                dt.name as document_type_name,
                dt.color,
                dt.icon,
                COUNT(i.id) as count,
                SUM(i.total) as total_amount
            FROM document_types dt
            LEFT JOIN invoices i ON dt.id = i.document_type_id AND i.is_archived = TRUE
            WHERE dt.is_active = TRUE
            GROUP BY dt.id
            ORDER BY dt.sort_order
        ");
        
        $stats = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Process JSON names
        $lang = $_SESSION['lang'] ?? 'fr';
        foreach ($stats as &$stat) {
            if (!empty($stat['document_type_name'])) {
                $nameData = json_decode($stat['document_type_name'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($nameData)) {
                    $stat['document_type_display_name'] = $nameData[$lang] ?? $nameData['fr'] ?? $nameData['en'] ?? $stat['document_type_code'];
                } else {
                    $stat['document_type_display_name'] = $stat['document_type_name'];
                }
            } else {
                $stat['document_type_display_name'] = $stat['document_type_code'];
            }
        }
        
        return $stats;
    }
    
    /**
     * Recalculate invoice totals based on items
     */
    public function recalculateTotals()
    {
        $db = Flight::db();
        
        // Get all items for this invoice
        $stmt = $db->prepare("
            SELECT SUM(quantity * unit_price) as subtotal,
                   SUM(vat_amount) as vat_total,
                   SUM(total_amount) as total
            FROM invoice_items
            WHERE invoice_id = ?
        ");
        $stmt->execute([$this->id]);
        $totals = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Update invoice totals
        $this->subtotal = $totals['subtotal'] ?? 0;
        $this->vat_amount = $totals['vat_total'] ?? 0;
        $this->total = $totals['total'] ?? 0;
        
        // Save the updated totals
        $stmt = $db->prepare("
            UPDATE invoices 
            SET subtotal = ?, vat_amount = ?, total = ?
            WHERE id = ?
        ");
        $stmt->execute([
            $this->subtotal,
            $this->vat_amount,
            $this->total,
            $this->id
        ]);
        
        return $this;
    }
    
    /**
     * Update paid amount based on payments
     */
    public function updatePaidAmount()
    {
        $db = Flight::db();
        
        // Get total paid amount
        $stmt = $db->prepare("
            SELECT SUM(amount) as total_paid
            FROM invoice_payments
            WHERE invoice_id = ?
        ");
        $stmt->execute([$this->id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->paid_amount = $result['total_paid'] ?? 0;
        
        // Update status based on paid amount
        if ($this->paid_amount >= $this->total) {
            $this->status = self::STATUS_PAID;
        } elseif ($this->paid_amount > 0) {
            $this->status = self::STATUS_PARTIAL;
        }
        
        // Save the updated paid amount and status
        $stmt = $db->prepare("
            UPDATE invoices 
            SET paid_amount = ?, status = ?
            WHERE id = ?
        ");
        $stmt->execute([
            $this->paid_amount,
            $this->status,
            $this->id
        ]);
        
        return $this;
    }
    
    /**
     * Check if invoice is overdue
     */
    public function isOverdue()
    {
        if ($this->status === self::STATUS_PAID || $this->status === self::STATUS_CANCELLED) {
            return false;
        }
        
        if (!$this->due_date) {
            return false;
        }
        
        return strtotime($this->due_date) < strtotime('today');
    }
    
    /**
     * Calculate due date based on payment terms
     */
    public function calculateDueDate()
    {
        if (!$this->issue_date) {
            return null;
        }
        
        if ($this->payment_term_id) {
            $paymentTerm = ConfigPaymentTerm::find($this->payment_term_id);
            if ($paymentTerm) {
                return $paymentTerm->calculateDueDate($this->issue_date);
            }
        }
        
        // Default to 30 days if no payment term
        $date = new \DateTime($this->issue_date);
        $date->add(new \DateInterval('P30D'));
        return $date->format('Y-m-d');
    }
}