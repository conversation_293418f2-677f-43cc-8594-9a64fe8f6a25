<?php
/**
 * Check email sending status for invoice FAC-DIV-2025-0190
 */

require_once __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();
require_once __DIR__ . '/app/config/bootstrap.php';

use PDO;

try {
    $db = Flight::db();
    
    echo "=== Checking Email Status for Invoice FAC-DIV-2025-0190 ===\n\n";
    
    // 1. First, find the invoice
    echo "1. Looking for invoice FAC-DIV-2025-0190...\n";
    $stmt = $db->prepare("
        SELECT 
            i.id, 
            i.invoice_number, 
            i.status, 
            i.issue_date,
            i.sent_at,
            i.created_at,
            i.updated_at,
            i.client_id,
            i.user_id,
            i.invoice_type,
            i.total_ttc,
            c.name as client_name,
            c.email as client_email,
            u.first_name,
            u.last_name,
            u.email as user_email,
            u.invoice_email as user_invoice_email
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN users u ON i.user_id = u.id
        WHERE i.invoice_number = :invoice_number
    ");
    $stmt->execute([':invoice_number' => 'FAC-DIV-2025-0190']);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "❌ Invoice FAC-DIV-2025-0190 not found in database!\n";
        
        // Check if it might have a different number format
        echo "\nChecking for similar invoice numbers...\n";
        $stmt = $db->query("
            SELECT id, invoice_number, status, created_at 
            FROM invoices 
            WHERE invoice_number LIKE '%190%' OR invoice_number LIKE '%DIV%'
            ORDER BY id DESC 
            LIMIT 10
        ");
        $similar = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($similar) {
            echo "Found similar invoices:\n";
            foreach ($similar as $inv) {
                echo "  - {$inv['invoice_number']} (ID: {$inv['id']}, Status: {$inv['status']}, Created: {$inv['created_at']})\n";
            }
        }
        exit;
    }
    
    // Display invoice details
    echo "✓ Invoice found!\n";
    echo "  - ID: {$invoice['id']}\n";
    echo "  - Number: {$invoice['invoice_number']}\n";
    echo "  - Type: {$invoice['invoice_type']}\n";
    echo "  - Status: {$invoice['status']}\n";
    echo "  - Total: " . number_format($invoice['total_ttc'], 2) . " €\n";
    echo "  - Issue Date: {$invoice['issue_date']}\n";
    echo "  - Sent At: " . ($invoice['sent_at'] ?: 'Not sent') . "\n";
    echo "  - Created: {$invoice['created_at']}\n";
    echo "  - Updated: {$invoice['updated_at']}\n";
    
    // Display recipient info
    echo "\nRecipient Information:\n";
    if ($invoice['client_id']) {
        echo "  - Client: {$invoice['client_name']}\n";
        echo "  - Client Email: " . ($invoice['client_email'] ?: 'No email') . "\n";
    } elseif ($invoice['user_id']) {
        echo "  - User: {$invoice['first_name']} {$invoice['last_name']}\n";
        echo "  - User Email: " . ($invoice['user_email'] ?: 'No email') . "\n";
        echo "  - Invoice Email: " . ($invoice['user_invoice_email'] ?: 'Not set') . "\n";
    } else {
        echo "  - No recipient found (no client or user linked)\n";
    }
    
    // 2. Check if email_logs table exists
    echo "\n2. Checking email_logs table...\n";
    $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "❌ email_logs table does not exist! Run migration 083_create_email_logs_table.sql\n";
    } else {
        echo "✓ email_logs table exists\n";
        
        // 3. Check for email logs for this invoice
        echo "\n3. Checking email logs for this invoice...\n";
        $stmt = $db->prepare("
            SELECT 
                id,
                template_id,
                recipient_type,
                recipient_email,
                subject,
                status,
                sent_at,
                error_message,
                created_at,
                attachments_sent,
                body_preview
            FROM email_logs 
            WHERE invoice_id = :invoice_id
            ORDER BY created_at DESC
        ");
        $stmt->execute([':invoice_id' => $invoice['id']]);
        $emailLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($emailLogs)) {
            echo "❌ No email logs found for this invoice.\n";
            echo "   This means either:\n";
            echo "   - The invoice has never been emailed\n";
            echo "   - Email logging was not implemented when it was sent\n";
            echo "   - The email failed before logging could occur\n";
        } else {
            echo "✓ Found " . count($emailLogs) . " email log(s):\n\n";
            
            foreach ($emailLogs as $index => $log) {
                echo "Email Log #" . ($index + 1) . ":\n";
                echo "  - Log ID: {$log['id']}\n";
                echo "  - To: {$log['recipient_email']}\n";
                echo "  - Subject: {$log['subject']}\n";
                echo "  - Status: {$log['status']}\n";
                echo "  - Sent At: " . ($log['sent_at'] ?: 'Not sent') . "\n";
                echo "  - Created: {$log['created_at']}\n";
                
                if ($log['error_message']) {
                    echo "  - Error: {$log['error_message']}\n";
                }
                
                if ($log['attachments_sent']) {
                    $attachments = json_decode($log['attachments_sent'], true);
                    if ($attachments) {
                        echo "  - Attachments: ";
                        foreach ($attachments as $att) {
                            echo $att['name'] . " ";
                        }
                        echo "\n";
                    }
                }
                
                if ($log['body_preview']) {
                    echo "  - Body Preview: " . substr($log['body_preview'], 0, 50) . "...\n";
                }
                
                echo "\n";
            }
        }
    }
    
    // 4. Check system email configuration
    echo "\n4. Checking email configuration...\n";
    $mailDriver = $_ENV['MAIL_DRIVER'] ?? 'Not set';
    echo "  - Mail Driver: $mailDriver\n";
    
    if ($mailDriver === 'smtp') {
        echo "  - SMTP Host: " . ($_ENV['MAIL_HOST'] ?? 'Not set') . "\n";
        echo "  - SMTP Port: " . ($_ENV['MAIL_PORT'] ?? 'Not set') . "\n";
        echo "  - SMTP User: " . ($_ENV['MAIL_USERNAME'] ?? 'Not set') . "\n";
        echo "  - SMTP Encryption: " . ($_ENV['MAIL_ENCRYPTION'] ?? 'Not set') . "\n";
    }
    
    echo "  - From Address: " . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'Not set') . "\n";
    echo "  - From Name: " . ($_ENV['MAIL_FROM_NAME'] ?? 'Not set') . "\n";
    
    // 5. Check recent error logs
    echo "\n5. Checking for recent errors in logs...\n";
    $logFile = __DIR__ . '/storage/logs/app.log';
    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        $lines = explode("\n", $logs);
        $invoiceErrors = [];
        
        foreach ($lines as $line) {
            if (stripos($line, 'FAC-DIV-2025-0190') !== false || 
                (stripos($line, 'invoice') !== false && stripos($line, '190') !== false)) {
                $invoiceErrors[] = $line;
            }
        }
        
        if (!empty($invoiceErrors)) {
            echo "Found " . count($invoiceErrors) . " log entries mentioning this invoice:\n";
            foreach (array_slice($invoiceErrors, -5) as $error) {
                echo "  " . substr($error, 0, 150) . "...\n";
            }
        } else {
            echo "No log entries found for this invoice.\n";
        }
    } else {
        echo "Log file not found at: $logFile\n";
    }
    
    // 6. Summary and recommendations
    echo "\n=== SUMMARY ===\n";
    if (!empty($emailLogs)) {
        $lastLog = $emailLogs[0];
        if ($lastLog['status'] === 'sent') {
            echo "✓ Invoice WAS SENT successfully on {$lastLog['sent_at']} to {$lastLog['recipient_email']}\n";
        } else {
            echo "❌ Last email attempt FAILED on {$lastLog['created_at']}\n";
            echo "   Error: {$lastLog['error_message']}\n";
        }
    } else {
        echo "❌ No email logs found - invoice appears to have NEVER BEEN SENT\n";
        
        if (!$invoice['client_email'] && !$invoice['user_email'] && !$invoice['user_invoice_email']) {
            echo "   ⚠️  No recipient email address available!\n";
        }
    }
    
    echo "\nRecommendations:\n";
    if (empty($emailLogs)) {
        echo "1. Try sending the invoice email from the invoice detail page\n";
        echo "2. Check if the recipient has a valid email address\n";
        echo "3. Verify email configuration is correct\n";
    } elseif ($lastLog['status'] === 'failed') {
        echo "1. Fix the error: {$lastLog['error_message']}\n";
        echo "2. Retry sending the email\n";
        echo "3. Check SMTP configuration if it's a connection error\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}