<?php
// Direct email test bypassing Flight framework
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load autoloader first
require_once __DIR__ . '/vendor/autoload.php';

// Use statements must be at the top
use PHPMailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html><html><head><title>Direct Email Test</title></head><body>";
    echo "<h2>Direct Email Test with PDF</h2>";
    
    // Get invoice
    $stmt = $db->prepare("
        SELECT i.*, 
               u.first_name, u.last_name, u.email,
               it.name as invoice_type_name,
               it.code as invoice_type_code
        FROM invoices i
        LEFT JOIN users u ON i.user_id = u.id
        LEFT JOIN invoice_types it ON i.invoice_type_id = it.id
        WHERE i.user_id = 1 
        AND i.document_type_id = 1 
        AND i.invoice_type_id = 4
        ORDER BY i.id DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>No invoice found!</p>";
        exit;
    }
    
    echo "<h3>Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>User: {$invoice['first_name']} {$invoice['last_name']}</li>";
    echo "<li>Email: {$invoice['email']}</li>";
    echo "</ul>";
    
    // PHPMailer is already loaded at the top of the file
    
    // Create PDF content (simple test PDF)
    echo "<h3>Creating Test PDF...</h3>";
    
    // Use TCPDF to create a simple invoice PDF
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('Fit360');
    $pdf->SetAuthor('Fit360');
    $pdf->SetTitle('Invoice ' . $invoice['invoice_number']);
    
    // Remove default header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // Add a page
    $pdf->AddPage();
    
    // Set font
    $pdf->SetFont('helvetica', '', 12);
    
    // Add content
    $pdf->Cell(0, 10, 'FACTURE / INVOICE', 0, 1, 'C');
    $pdf->Ln(10);
    
    $pdf->SetFont('helvetica', 'B', 14);
    $pdf->Cell(0, 10, $invoice['invoice_number'], 0, 1, 'C');
    $pdf->Ln(10);
    
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 10, 'Date: ' . date('d/m/Y', strtotime($invoice['issue_date'])), 0, 1);
    $pdf->Cell(0, 10, 'Client: ' . $invoice['first_name'] . ' ' . $invoice['last_name'], 0, 1);
    $pdf->Ln(10);
    
    // Get items
    $stmt = $db->prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
    $stmt->execute([$invoice['id']]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($items) {
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(100, 7, 'Description', 1, 0);
        $pdf->Cell(30, 7, 'Quantity', 1, 0, 'C');
        $pdf->Cell(30, 7, 'Price', 1, 0, 'R');
        $pdf->Cell(30, 7, 'Total', 1, 1, 'R');
        
        $pdf->SetFont('helvetica', '', 10);
        $total = 0;
        foreach ($items as $item) {
            $lineTotal = $item['quantity'] * $item['unit_price'];
            $pdf->Cell(100, 7, $item['description'], 1, 0);
            $pdf->Cell(30, 7, $item['quantity'], 1, 0, 'C');
            $pdf->Cell(30, 7, '€' . number_format($item['unit_price'], 2), 1, 0, 'R');
            $pdf->Cell(30, 7, '€' . number_format($lineTotal, 2), 1, 1, 'R');
            $total += $lineTotal;
        }
        
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(160, 7, 'Total:', 1, 0, 'R');
        $pdf->Cell(30, 7, '€' . number_format($total, 2), 1, 1, 'R');
    }
    
    // Output PDF to string
    $pdfContent = $pdf->Output('', 'S');
    echo "<p>✓ PDF created (Size: " . number_format(strlen($pdfContent)) . " bytes)</p>";
    
    // Send email
    echo "<h3>Sending Email...</h3>";
    
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = 'localhost';
        $mail->Port = 1025;
        $mail->SMTPAuth = false;
        $mail->SMTPSecure = false;
        $mail->SMTPAutoTLS = false;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'Fit360 AdminDesk');
        $mail->addAddress($invoice['email'], $invoice['first_name'] . ' ' . $invoice['last_name']);
        
        // Attach PDF
        $mail->addStringAttachment($pdfContent, $invoice['invoice_number'] . '.pdf', 'base64', 'application/pdf');
        
        // Content
        $mail->isHTML(true);
        $mail->CharSet = 'UTF-8';
        $mail->Subject = 'Facture ' . $invoice['invoice_number'] . ' - ' . ($invoice['subject'] ?? 'Fit360');
        
        $mail->Body = '<div style="font-family: Arial, sans-serif;">
<p>Bonjour ' . $invoice['first_name'] . ',</p>
<p>Veuillez trouver ci-joint votre facture <strong>' . $invoice['invoice_number'] . '</strong>.</p>
<p>Objet: <strong>' . ($invoice['subject'] ?? 'LOYER + CHARGES') . '</strong><br>
Période: <strong>' . ($invoice['period'] ?? date('F Y')) . '</strong></p>
<p>La facture détaillée est disponible en pièce jointe.</p>
<p>Cordialement,<br>
<strong>Fit360 AdminDesk</strong></p>
</div>';
        
        $mail->AltBody = strip_tags(str_replace('<br>', "\n", $mail->Body));
        
        // Send
        $mail->send();
        
        echo "<p style='color: green;'>✓ Email sent successfully!</p>";
        echo "<p>Subject: " . htmlspecialchars($mail->Subject) . "</p>";
        echo "<h3>🎉 Success! Check Mailhog</h3>";
        echo "<p>Open <a href='http://localhost:8025' target='_blank'>Mailhog (http://localhost:8025)</a> to see:</p>";
        echo "<ul>";
        echo "<li>Email to: {$invoice['email']}</li>";
        echo "<li>Subject: {$mail->Subject}</li>";
        echo "<li>PDF attachment: {$invoice['invoice_number']}.pdf</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Email Error: {$mail->ErrorInfo}</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='/fit/public/invoices'>Back to Invoices</a></p>";
    echo "</body></html>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}