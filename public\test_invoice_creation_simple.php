<?php
/**
 * Simple Invoice Creation Test
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables BEFORE bootstrap
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

// Session is already started in bootstrap.php
if (!isset($_SESSION['user_id'])) {
    die("Please login first.");
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<h1>Invoice Creation Test</h1>";

// Show last error if any
if (isset($_SESSION['error'])) {
    echo '<div style="background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0;">';
    echo 'Last Error: ' . htmlspecialchars($_SESSION['error']);
    echo '</div>';
}

// Show last success if any
if (isset($_SESSION['success'])) {
    echo '<div style="background: #d4edda; color: #155724; padding: 10px; margin: 10px 0;">';
    echo 'Success: ' . htmlspecialchars($_SESSION['success']);
    echo '</div>';
}
?>

<form method="POST" action="<?php echo Flight::get('flight.base_url'); ?>/invoices" style="border: 1px solid #ccc; padding: 20px;">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
    <input type="hidden" name="document_type_id" value="1">
    <input type="hidden" name="invoice_type_id" value="1">
    <input type="hidden" name="template_id" value="1">
    <input type="hidden" name="payment_term_id" value="1">
    
    <h3>Test Invoice Form</h3>
    
    <p>
        <label>Invoice Number:</label><br>
        <input type="text" name="invoice_number" value="TEST-<?php echo time(); ?>" required>
    </p>
    
    <p>
        <label>Bill To:</label><br>
        <select name="billable_id" required>
            <option value="user_1">User 1 (Admin)</option>
        </select>
    </p>
    
    <p>
        <label>Issue Date:</label><br>
        <input type="date" name="issue_date" value="<?php echo date('Y-m-d'); ?>" required>
    </p>
    
    <p>
        <label>Due Date:</label><br>
        <input type="date" name="due_date" value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
    </p>
    
    <p>
        <label>Subject:</label><br>
        <input type="text" name="subject" value="Test Invoice" required>
    </p>
    
    <p>
        <label>Period:</label><br>
        <input type="text" name="period" value="<?php echo date('F Y'); ?>">
    </p>
    
    <h4>Invoice Item:</h4>
    <p>
        Description: <input type="text" name="items[0][description]" value="Test Service" required><br>
        Quantity: <input type="number" name="items[0][quantity]" value="1" required><br>
        Unit Price: <input type="number" name="items[0][unit_price]" value="100" step="0.01" required><br>
        <input type="hidden" name="items[0][vat_rate_id]" value="1">
    </p>
    
    <p>
        <label>
            <input type="checkbox" name="send_email" value="false" checked>
            Do NOT send email (just save)
        </label>
    </p>
    
    <p>
        <button type="submit" name="action" value="save">Save as Draft</button>
        <button type="submit" name="action" value="save_and_send">Save and Send</button>
    </p>
</form>

<hr>

<h3>Check Recent Invoices:</h3>
<pre>
<?php
try {
    $db = Flight::db();
    $stmt = $db->query("SELECT id, invoice_number, status, total, created_at 
                        FROM invoices 
                        ORDER BY id DESC 
                        LIMIT 5");
    
    echo "Recent Invoices:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo sprintf("ID: %d | %s | %s | €%.2f | %s\n", 
            $row['id'], 
            $row['invoice_number'], 
            $row['status'], 
            $row['total'], 
            $row['created_at']
        );
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
</pre>