<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fixing Retrocession Invoice Issues</h2>\n";
    
    // 1. Check config_invoice_types table
    echo "<h3>1. Checking Invoice Type Configuration</h3>\n";
    $stmt = $pdo->query("SELECT * FROM config_invoice_types WHERE prefix = 'RET'");
    $oldType = $stmt->fetch();
    
    if (!$oldType) {
        echo "⚠️ 'RET' type missing in config_invoice_types table. Creating it...\n";
        
        // Get the ID from invoice_types table
        $stmt = $pdo->query("SELECT id FROM invoice_types WHERE code = 'RET'");
        $newType = $stmt->fetch();
        
        if ($newType) {
            // Insert into config_invoice_types with same ID
            $stmt = $pdo->prepare("
                INSERT INTO config_invoice_types (id, prefix, name, is_active, created_at, updated_at)
                VALUES (:id, 'RET', 'Rétrocession', 1, NOW(), NOW())
            ");
            $stmt->execute(['id' => $newType['id']]);
            echo "✓ Created 'RET' type in config_invoice_types with ID {$newType['id']}\n";
        }
    } else {
        echo "✓ 'RET' type already exists in config_invoice_types (ID: {$oldType['id']})\n";
    }
    
    // 2. Check monthly amounts for test users
    echo "\n<h3>2. Checking Monthly Amounts for July 2025</h3>\n";
    $testUsers = [
        1 => 'Frank Huet',
        10 => 'Ismail Lakouar', 
        12 => 'Lyse Henry'
    ];
    
    foreach ($testUsers as $userId => $userName) {
        $stmt = $pdo->prepare("
            SELECT * FROM user_monthly_retrocession_amounts
            WHERE user_id = :user_id AND month = 7 AND year = 2025
        ");
        $stmt->execute(['user_id' => $userId]);
        $amounts = $stmt->fetch();
        
        if (!$amounts || ($amounts['cns_amount'] == 0 && $amounts['patient_amount'] == 0)) {
            echo "⚠️ User #{$userId} ({$userName}) has no amounts for July 2025\n";
            
            // Get amounts from another month as reference
            $stmt = $pdo->prepare("
                SELECT AVG(cns_amount) as avg_cns, AVG(patient_amount) as avg_patient
                FROM user_monthly_retrocession_amounts
                WHERE user_id = :user_id AND cns_amount > 0
            ");
            $stmt->execute(['user_id' => $userId]);
            $avgAmounts = $stmt->fetch();
            
            if ($avgAmounts && $avgAmounts['avg_cns'] > 0) {
                echo "   Average amounts: CNS {$avgAmounts['avg_cns']}€, Patient {$avgAmounts['avg_patient']}€\n";
                echo "   <a href='?fix_amounts=$userId&month=7&year=2025&cns={$avgAmounts['avg_cns']}&patient={$avgAmounts['avg_patient']}'>Click to set July 2025 amounts to average</a>\n";
            }
        } else {
            echo "✓ User #{$userId} ({$userName}) - CNS: {$amounts['cns_amount']}€, Patient: {$amounts['patient_amount']}€\n";
        }
    }
    
    // Handle fix request
    if (isset($_GET['fix_amounts'])) {
        $userId = $_GET['fix_amounts'];
        $month = $_GET['month'];
        $year = $_GET['year'];
        $cns = $_GET['cns'];
        $patient = $_GET['patient'];
        
        echo "\n<h3>Fixing Amounts</h3>\n";
        
        // Check if record exists
        $stmt = $pdo->prepare("
            SELECT id FROM user_monthly_retrocession_amounts
            WHERE user_id = :user_id AND month = :month AND year = :year
        ");
        $stmt->execute(['user_id' => $userId, 'month' => $month, 'year' => $year]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Update existing
            $stmt = $pdo->prepare("
                UPDATE user_monthly_retrocession_amounts
                SET cns_amount = :cns, patient_amount = :patient, updated_at = NOW()
                WHERE id = :id
            ");
            $stmt->execute([
                'cns' => $cns,
                'patient' => $patient,
                'id' => $existing['id']
            ]);
        } else {
            // Insert new
            $stmt = $pdo->prepare("
                INSERT INTO user_monthly_retrocession_amounts 
                (user_id, month, year, cns_amount, patient_amount, is_active, created_by, created_at)
                VALUES (:user_id, :month, :year, :cns, :patient, 1, 1, NOW())
            ");
            $stmt->execute([
                'user_id' => $userId,
                'month' => $month,
                'year' => $year,
                'cns' => $cns,
                'patient' => $patient
            ]);
        }
        
        echo "✓ Updated amounts for User #$userId - Month $month/$year - CNS: {$cns}€, Patient: {$patient}€\n";
        echo "<a href='test_retrocession_generation.php?user_id=$userId&month=$month&year=$year'>Test invoice generation now</a>\n";
    }
    
    // 3. Show UnifiedInvoiceGenerator client lookup logic
    echo "\n<h3>3. Client Lookup Logic</h3>\n";
    echo "The UnifiedInvoiceGenerator needs to find the client_id for each user.\n";
    echo "Current mapping:\n";
    
    $stmt = $pdo->query("
        SELECT u.id, u.first_name, u.last_name, u.email,
               c.id as client_id, c.name as client_name, c.is_practitioner
        FROM users u
        LEFT JOIN clients c ON c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name
        WHERE u.id IN (1, 10, 12, 18)
    ");
    
    while ($row = $stmt->fetch()) {
        echo "User #{$row['id']} → Client #{$row['client_id']} (matched by " . 
             ($row['email'] == $row['email'] ? "email" : "name") . ")\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}