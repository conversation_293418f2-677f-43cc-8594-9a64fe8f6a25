<?php
// Comprehensive fix for Invoice 263
echo "<h1>Invoice 263 Comprehensive Fix</h1>";

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if form was submitted
    if ($_POST) {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add_missing_component') {
            $pdo->beginTransaction();
            
            try {
                // Get current invoice total
                $stmt = $pdo->prepare("SELECT total FROM invoices WHERE id = 263");
                $stmt->execute();
                $currentTotal = $stmt->fetchColumn();
                
                $missingAmount = 930.00 - $currentTotal;
                
                // Add the missing component as a new invoice line
                $stmt = $pdo->prepare("
                    INSERT INTO invoice_lines (
                        invoice_id, 
                        line_type, 
                        description, 
                        quantity, 
                        unit_price, 
                        vat_rate, 
                        line_total, 
                        sort_order
                    ) VALUES (
                        263, 
                        'service', 
                        'Complément facturation', 
                        1, 
                        :amount, 
                        0, 
                        :amount, 
                        999
                    )
                ");
                $stmt->execute(['amount' => $missingAmount]);
                
                // Update invoice total
                $stmt = $pdo->prepare("UPDATE invoices SET total = 930.00, updated_at = NOW() WHERE id = 263");
                $stmt->execute();
                
                $pdo->commit();
                echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
                echo "✅ Successfully added missing component of " . number_format($missingAmount, 2) . "€ to invoice 263";
                echo "</div>";
                
            } catch (Exception $e) {
                $pdo->rollBack();
                echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
                echo "❌ Error: " . $e->getMessage();
                echo "</div>";
            }
        }
        
        if ($action === 'recalculate_courses') {
            $pdo->beginTransaction();
            
            try {
                // Clear existing lines
                $stmt = $pdo->prepare("DELETE FROM invoice_lines WHERE invoice_id = 263");
                $stmt->execute();
                
                // Add Individuel course line
                $stmt = $pdo->prepare("
                    INSERT INTO invoice_lines (
                        invoice_id, 
                        line_type, 
                        description, 
                        quantity, 
                        unit_price, 
                        vat_rate, 
                        line_total, 
                        sort_order
                    ) VALUES (
                        263, 
                        'service', 
                        'Cours Individuel', 
                        15, 
                        :unit_price, 
                        17, 
                        585.00, 
                        1
                    )
                ");
                $individuellUnitPrice = 585.00 / 15;
                $stmt->execute(['unit_price' => $individuellUnitPrice]);
                
                // Add Collectif course line
                $stmt = $pdo->prepare("
                    INSERT INTO invoice_lines (
                        invoice_id, 
                        line_type, 
                        description, 
                        quantity, 
                        unit_price, 
                        vat_rate, 
                        line_total, 
                        sort_order
                    ) VALUES (
                        263, 
                        'service', 
                        'Cours Collectif', 
                        30, 
                        :unit_price, 
                        17, 
                        210.00, 
                        2
                    )
                ");
                $collectifUnitPrice = 210.00 / 30;
                $stmt->execute(['unit_price' => $collectifUnitPrice]);
                
                // Add missing component
                $missingAmount = 930.00 - 795.00;
                $stmt = $pdo->prepare("
                    INSERT INTO invoice_lines (
                        invoice_id, 
                        line_type, 
                        description, 
                        quantity, 
                        unit_price, 
                        vat_rate, 
                        line_total, 
                        sort_order
                    ) VALUES (
                        263, 
                        'patient_part', 
                        'Part Patient', 
                        1, 
                        :amount, 
                        0, 
                        :amount, 
                        3
                    )
                ");
                $stmt->execute(['amount' => $missingAmount]);
                
                // Update invoice total and subtotal
                $stmt = $pdo->prepare("
                    UPDATE invoices SET 
                        subtotal = 795.00, 
                        total = 930.00, 
                        updated_at = NOW() 
                    WHERE id = 263
                ");
                $stmt->execute();
                
                $pdo->commit();
                echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
                echo "✅ Successfully recalculated invoice 263 with proper course structure";
                echo "</div>";
                
            } catch (Exception $e) {
                $pdo->rollBack();
                echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
                echo "❌ Error: " . $e->getMessage();
                echo "</div>";
            }
        }
    }
    
    // Display current state
    echo "<h2>Current Invoice State</h2>";
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 263");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
        echo "<tr><td>Current Total</td><td>{$invoice['total']}€</td></tr>";
        echo "<tr><td>Target Total</td><td>930.00€</td></tr>";
        echo "<tr><td>Difference</td><td>" . number_format(930.00 - $invoice['total'], 2) . "€</td></tr>";
        echo "</table>";
        
        echo "<h2>Current Invoice Lines</h2>";
        $stmt = $pdo->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 263 ORDER BY sort_order, id");
        $stmt->execute();
        $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($lines) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Type</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
            $totalCalculated = 0;
            foreach ($lines as $line) {
                echo "<tr>";
                echo "<td>{$line['line_type']}</td>";
                echo "<td>{$line['description']}</td>";
                echo "<td>{$line['quantity']}</td>";
                echo "<td>{$line['unit_price']}€</td>";
                echo "<td>{$line['vat_rate']}%</td>";
                echo "<td>{$line['line_total']}€</td>";
                echo "</tr>";
                $totalCalculated += $line['line_total'];
            }
            echo "<tr style='font-weight: bold; background-color: #f0f0f0;'>";
            echo "<td colspan='5'>Total from Lines</td>";
            echo "<td>{$totalCalculated}€</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p>No invoice lines found.</p>";
        }
        
        // Show fix options
        if ($invoice['total'] != 930.00) {
            echo "<h2>Fix Options</h2>";
            
            echo "<form method='post' style='margin: 10px 0;'>";
            echo "<input type='hidden' name='action' value='add_missing_component'>";
            echo "<button type='submit' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>";
            echo "Add Missing Component (+" . number_format(930.00 - $invoice['total'], 2) . "€)";
            echo "</button>";
            echo "</form>";
            
            echo "<form method='post' style='margin: 10px 0;'>";
            echo "<input type='hidden' name='action' value='recalculate_courses'>";
            echo "<button type='submit' style='padding: 10px 20px; background: #28a745; color: white; border: none; cursor: pointer;'>";
            echo "Recalculate with Proper Course Structure";
            echo "</button>";
            echo "</form>";
        } else {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
            echo "✅ Invoice 263 is already at the correct total of 930.00€";
            echo "</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>