<?php
/**
 * Check Mailhog Status and Test Email
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

echo "<h1>Mailhog Status Check</h1>";

// Check SMTP port
echo "<h2>Port Check:</h2>";
echo "<ul>";

// Check port 1025 (SMTP)
$smtp = @fsockopen('localhost', 1025, $errno, $errstr, 2);
if ($smtp) {
    echo "<li style='color: green;'>✓ SMTP Port 1025: <strong>ACTIVE</strong> - Mailhog SMTP is running!</li>";
    fclose($smtp);
} else {
    echo "<li style='color: red;'>✗ SMTP Port 1025: Not accessible</li>";
}

// Check port 8025 (Web UI)
$ch = curl_init('http://localhost:8025/api/v2/messages');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 2);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode > 0) {
    echo "<li style='color: green;'>✓ Web UI Port 8025: <strong>ACTIVE</strong> - Mailhog Web UI is running!</li>";
} else {
    echo "<li style='color: red;'>✗ Web UI Port 8025: Not accessible</li>";
}
echo "</ul>";

// Show Mailhog link
echo "<div style='background: #e3f2fd; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>📧 View Captured Emails:</h3>";
echo "<p><a href='http://localhost:8025' target='_blank' style='font-size: 18px;'>Open Mailhog Web Interface</a></p>";
echo "<p>All emails sent by the application will appear here!</p>";
echo "</div>";

// Quick test email
if (isset($_POST['send_test'])) {
    echo "<h2>Sending Test Email...</h2>";
    echo "<pre>";
    
    try {
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'localhost';
        $mail->Port       = 1025;
        $mail->SMTPAuth   = false;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'Fit360 Test');
        $mail->addAddress($_POST['test_email'] ?? '<EMAIL>');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email - ' . date('Y-m-d H:i:s');
        $mail->Body    = '<h1>Test Email from Fit360</h1><p>If you see this in Mailhog, email sending is working!</p>';
        
        $mail->send();
        echo "✓ Test email sent successfully!\n";
        echo "Check Mailhog at: http://localhost:8025\n";
        
    } catch (Exception $e) {
        echo "✗ Error: " . $e->getMessage() . "\n";
    }
    
    echo "</pre>";
}

// Test existing invoice
if (isset($_POST['test_invoice'])) {
    echo "<h2>Testing Invoice Email...</h2>";
    echo "<pre>";
    
    try {
        $invoiceId = intval($_POST['invoice_id']);
        $emailService = new \App\Services\EmailService();
        $result = $emailService->sendInvoiceEmail($invoiceId);
        
        if ($result['success']) {
            echo "✓ Invoice email sent successfully!\n";
            echo "Check Mailhog at: http://localhost:8025\n";
        } else {
            echo "✗ Failed: " . $result['message'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Error: " . $e->getMessage() . "\n";
    }
    
    echo "</pre>";
}

// Check recent invoices
$db = Flight::db();
$invoices = $db->query("SELECT id, invoice_number, status FROM invoices ORDER BY id DESC LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Test Options:</h2>

<div style="display: flex; gap: 20px;">
    <!-- Simple test email -->
    <div style="border: 1px solid #ccc; padding: 15px; flex: 1;">
        <h3>1. Send Simple Test Email</h3>
        <form method="POST">
            <p>
                <input type="email" name="test_email" placeholder="<EMAIL>" value="<EMAIL>">
            </p>
            <button type="submit" name="send_test" value="1" style="background: #4CAF50; color: white; padding: 10px 20px; border: none;">
                Send Test Email
            </button>
        </form>
    </div>

    <!-- Test with existing invoice -->
    <div style="border: 1px solid #ccc; padding: 15px; flex: 1;">
        <h3>2. Send Invoice Email</h3>
        <form method="POST">
            <p>
                <select name="invoice_id" required>
                    <option value="">Select an invoice...</option>
                    <?php foreach ($invoices as $inv): ?>
                    <option value="<?php echo $inv['id']; ?>">
                        <?php echo $inv['invoice_number']; ?> (<?php echo $inv['status']; ?>)
                    </option>
                    <?php endforeach; ?>
                </select>
            </p>
            <button type="submit" name="test_invoice" value="1" style="background: #2196F3; color: white; padding: 10px 20px; border: none;">
                Send Invoice Email
            </button>
        </form>
    </div>
</div>

<div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
    <h3>⚠️ Important:</h3>
    <ul>
        <li>Mailhog captures ALL emails - they won't go to real email addresses</li>
        <li>This is perfect for development and testing</li>
        <li>To send real emails, configure Gmail SMTP instead</li>
    </ul>
</div>