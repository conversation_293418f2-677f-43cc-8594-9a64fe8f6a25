<?php
/**
 * Simple test for PDF attachment emails
 * Run this in a browser: http://localhost/fit/test_pdf_attachment_simple.php
 */

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
            continue;
        }
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Configure PHP to use Mailhog
ini_set('SMTP', $_ENV['MAIL_HOST'] ?? 'localhost');
ini_set('smtp_port', $_ENV['MAIL_PORT'] ?? '1025');
ini_set('sendmail_from', $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>');

// Create a sample PDF content (just text for testing)
$pdfContent = "%PDF-1.4\n";
$pdfContent .= "1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n";
$pdfContent .= "2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n";
$pdfContent .= "3 0 obj<</Type/Page/Parent 2 0 R/Resources<</Font<</F1<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>>>>>/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj\n";
$pdfContent .= "4 0 obj<</Length 44>>stream\n";
$pdfContent .= "BT /F1 12 Tf 100 700 Td (Test Invoice) Tj ET\n";
$pdfContent .= "endstream\nendobj\n";
$pdfContent .= "xref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000056 00000 n\n0000000111 00000 n\n0000000260 00000 n\n";
$pdfContent .= "trailer<</Size 5/Root 1 0 R>>\n";
$pdfContent .= "startxref\n349\n%%EOF";

// Email parameters
$to = '<EMAIL>';
$subject = 'Test Invoice with PDF Attachment';
$fromEmail = $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>';
$fromName = 'Fit360 AdminDesk';

// Create boundaries
$mainBoundary = md5('main' . time());
$altBoundary = md5('alt' . time());

// Build headers
$headers = "From: \"$fromName\" <$fromEmail>\r\n";
$headers .= "Reply-To: $fromEmail\r\n";
$headers .= "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: multipart/mixed; boundary=\"$mainBoundary\"\r\n";

// Build message body
$body = "This is a multi-part message in MIME format.\r\n\r\n";
$body .= "--$mainBoundary\r\n";
$body .= "Content-Type: multipart/alternative; boundary=\"$altBoundary\"\r\n\r\n";

// Text part
$body .= "--$altBoundary\r\n";
$body .= "Content-Type: text/plain; charset=UTF-8\r\n";
$body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
$body .= "Dear Client,\n\nPlease find attached your invoice.\n\nAmount: €1,170.00\nDue Date: " . date('d/m/Y', strtotime('+30 days')) . "\n\nBest regards,\nFit360 Team\r\n\r\n";

// HTML part
$body .= "--$altBoundary\r\n";
$body .= "Content-Type: text/html; charset=UTF-8\r\n";
$body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
$body .= "<html><body><h2>Invoice from Fit360</h2><p>Dear Client,</p><p>Please find attached your invoice.</p><p><strong>Amount:</strong> €1,170.00<br><strong>Due Date:</strong> " . date('d/m/Y', strtotime('+30 days')) . "</p><p>Best regards,<br>Fit360 Team</p></body></html>\r\n\r\n";
$body .= "--$altBoundary--\r\n\r\n";

// PDF attachment
$body .= "--$mainBoundary\r\n";
$body .= "Content-Type: application/pdf;\r\n";
$body .= " name=\"TEST-INVOICE-001.pdf\"\r\n";
$body .= "Content-Transfer-Encoding: base64\r\n";
$body .= "Content-Disposition: attachment;\r\n";
$body .= " filename=\"TEST-INVOICE-001.pdf\"\r\n\r\n";
$body .= chunk_split(base64_encode($pdfContent), 76, "\r\n");
$body .= "\r\n";
$body .= "--$mainBoundary--\r\n";

// Display HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>PDF Attachment Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>PDF Attachment Email Test</h1>
    
    <div class="info">
        <h3>Email Configuration:</h3>
        <ul>
            <li><strong>SMTP Host:</strong> <?php echo $_ENV['MAIL_HOST'] ?? 'localhost'; ?></li>
            <li><strong>SMTP Port:</strong> <?php echo $_ENV['MAIL_PORT'] ?? '1025'; ?></li>
            <li><strong>From:</strong> <?php echo $fromEmail; ?></li>
            <li><strong>To:</strong> <?php echo $to; ?></li>
            <li><strong>Subject:</strong> <?php echo $subject; ?></li>
        </ul>
    </div>
    
    <?php
    // Send the email
    $result = mail($to, $subject, $body, $headers);
    
    if ($result) {
        echo '<h3 class="success">✓ Email sent successfully!</h3>';
        echo '<p>Check Mailhog at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a> to see the email with PDF attachment.</p>';
    } else {
        echo '<h3 class="error">✗ Failed to send email</h3>';
        echo '<p>Make sure Mailhog is running and PHP is configured correctly.</p>';
    }
    ?>
    
    <div class="info">
        <h3>What to look for in Mailhog:</h3>
        <ul>
            <li>The email should appear in the inbox</li>
            <li>It should have both text and HTML versions</li>
            <li>There should be a PDF attachment named "TEST-INVOICE-001.pdf"</li>
            <li>You should be able to download the PDF attachment</li>
        </ul>
    </div>
    
    <h3>Email Structure (Debug):</h3>
    <details>
        <summary>Click to view email headers and structure</summary>
        <pre><?php echo htmlspecialchars($headers); ?></pre>
        <h4>Body Preview (first 500 chars):</h4>
        <pre><?php echo htmlspecialchars(substr($body, 0, 500)) . '...'; ?></pre>
    </details>
</body>
</html>