<?php
/**
 * Test retrocession generation JSON response
 */

// Start output buffering to catch any warnings/errors
ob_start();

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

// Simulate session
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['language'] = 'fr';

// Test parameters
$userId = 1;
$month = 7;
$year = 2025;

echo "=== Testing Retrocession Generation ===\n\n";

// Check if user exists and has client
$db = Flight::db();
$stmt = $db->prepare("
    SELECT u.*, c.id as client_id, c.is_practitioner 
    FROM users u 
    LEFT JOIN clients c ON c.id = u.client_id 
    WHERE u.id = ?
");
$stmt->execute([$userId]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

echo "User found: " . ($user ? "Yes" : "No") . "\n";
if ($user) {
    echo "User: {$user['first_name']} {$user['last_name']}\n";
    echo "Client ID: {$user['client_id']}\n";
    echo "Is Practitioner: " . ($user['is_practitioner'] ? 'Yes' : 'No') . "\n";
}

// Check monthly amounts
$stmt = $db->prepare("
    SELECT * FROM user_monthly_retrocession_amounts 
    WHERE user_id = ? AND month = ?
");
$stmt->execute([$userId, $month]);
$amounts = $stmt->fetch(PDO::FETCH_ASSOC);

echo "\nMonthly amounts found: " . ($amounts ? "Yes" : "No") . "\n";
if ($amounts) {
    echo "CNS: {$amounts['cns_amount']}€\n";
    echo "Patient: {$amounts['patient_amount']}€\n";
}

// Check for existing invoice
$stmt = $db->prepare("
    SELECT i.* FROM invoices i
    JOIN retrocession_data_entry rde ON rde.invoice_id = i.id
    WHERE rde.user_id = ? AND rde.month = ? AND rde.year = ?
    AND i.deleted_at IS NULL
");
$stmt->execute([$userId, $month, $year]);
$existingInvoice = $stmt->fetch(PDO::FETCH_ASSOC);

echo "\nExisting invoice: " . ($existingInvoice ? "Yes (#{$existingInvoice['invoice_number']})" : "No") . "\n";

// Now test the actual generation
echo "\n=== Testing UserController::generateRetrocession ===\n";

// Clear any previous output
ob_end_clean();
ob_start();

try {
    $controller = new App\Controllers\UserController();
    
    // Set up the request data as JSON input
    $jsonData = json_encode([
        'month' => $month,
        'year' => $year
    ]);
    
    // Simulate JSON input
    $tempInput = fopen('php://temp', 'w+');
    fwrite($tempInput, $jsonData);
    rewind($tempInput);
    stream_filter_append($tempInput, 'dechunk', STREAM_FILTER_READ);
    
    // Create request and response objects
    $request = new \App\Core\Request();
    $response = new \App\Core\Response();
    
    // Call the method with parameters
    $controller->generateRetrocession($request, $response, $userId);
    
    $output = ob_get_clean();
    
    echo "Raw output:\n";
    echo "---START---\n";
    echo $output;
    echo "\n---END---\n";
    
    // Try to parse as JSON
    $json = json_decode($output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "\nJSON parsed successfully:\n";
        print_r($json);
    } else {
        echo "\nJSON parse error: " . json_last_error_msg() . "\n";
        
        // Check for common issues
        if (strpos($output, '<') === 0) {
            echo "Output appears to be HTML - likely an error page\n";
        }
        if (strpos($output, 'Warning:') !== false || strpos($output, 'Notice:') !== false) {
            echo "PHP warnings/notices detected in output\n";
        }
    }
    
} catch (Exception $e) {
    echo "Exception caught: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

// Show any errors
$errors = ob_get_clean();
if ($errors) {
    echo "\n=== Buffered Errors ===\n";
    echo $errors;
}