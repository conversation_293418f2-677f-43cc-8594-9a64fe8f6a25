<?php
/**
 * Comprehensive Email Diagnostics and Testing Script
 * This script will:
 * 1. Check email infrastructure (Mailhog)
 * 2. Verify database structure
 * 3. Test simple email sending
 * 4. Test invoice email sending
 * 5. Show detailed debugging information
 */

require_once 'app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\EmailService;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

$db = Flight::db();

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Email Diagnostics</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; }";
echo "pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "table { border-collapse: collapse; width: 100%; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
echo "th { background-color: #f2f2f2; }";
echo "</style>";
echo "</head><body>";

echo "<h1>Email Diagnostics and Testing</h1>";

// 1. Check Email Infrastructure
echo "<h2>1. Email Infrastructure Check</h2>";

// Check Mailhog
echo "<h3>Mailhog Status:</h3>";
$mailhogSMTP = @fsockopen('localhost', 1025, $errno, $errstr, 1);
$mailhogWeb = @fsockopen('localhost', 8025, $errno, $errstr, 1);

if ($mailhogSMTP) {
    echo "<p class='success'>✓ Mailhog SMTP (port 1025) is running</p>";
    fclose($mailhogSMTP);
} else {
    echo "<p class='error'>✗ Mailhog SMTP (port 1025) is NOT running</p>";
    echo "<p class='info'>Start Mailhog with: <code>mailhog</code></p>";
}

if ($mailhogWeb) {
    echo "<p class='success'>✓ Mailhog Web UI (port 8025) is accessible</p>";
    echo "<p class='info'>View emails at: <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></p>";
    fclose($mailhogWeb);
} else {
    echo "<p class='warning'>⚠ Mailhog Web UI (port 8025) is NOT accessible</p>";
}

// Check email configuration
echo "<h3>Email Configuration (.env):</h3>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$emailConfig = [
    'MAIL_HOST' => $_ENV['MAIL_HOST'] ?? 'NOT SET',
    'MAIL_PORT' => $_ENV['MAIL_PORT'] ?? 'NOT SET',
    'MAIL_USERNAME' => $_ENV['MAIL_USERNAME'] ?? 'NOT SET',
    'MAIL_PASSWORD' => $_ENV['MAIL_PASSWORD'] ?? 'NOT SET',
    'MAIL_ENCRYPTION' => $_ENV['MAIL_ENCRYPTION'] ?? 'NOT SET',
    'MAIL_FROM_ADDRESS' => $_ENV['MAIL_FROM_ADDRESS'] ?? 'NOT SET',
    'MAIL_FROM_NAME' => $_ENV['MAIL_FROM_NAME'] ?? 'NOT SET'
];

foreach ($emailConfig as $key => $value) {
    echo "<tr>";
    echo "<td>$key</td>";
    echo "<td>" . ($key === 'MAIL_PASSWORD' && $value ? '***' : $value) . "</td>";
    
    if ($key === 'MAIL_HOST' && $value === 'localhost') {
        echo "<td class='success'>✓ Correct for Mailhog</td>";
    } elseif ($key === 'MAIL_PORT' && $value === '1025') {
        echo "<td class='success'>✓ Correct for Mailhog</td>";
    } elseif (in_array($key, ['MAIL_USERNAME', 'MAIL_PASSWORD', 'MAIL_ENCRYPTION']) && empty($value)) {
        echo "<td class='success'>✓ Correct (empty for Mailhog)</td>";
    } elseif ($value === 'NOT SET') {
        echo "<td class='error'>✗ Not configured</td>";
    } else {
        echo "<td class='info'>Configured</td>";
    }
    echo "</tr>";
}
echo "</table>";

// 2. Check Database Structure
echo "<h2>2. Database Structure Check</h2>";

// Check email_logs table
$stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
if ($stmt->fetch()) {
    echo "<p class='success'>✓ email_logs table exists</p>";
    
    // Check columns
    $stmt = $db->query("DESCRIBE email_logs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<details>";
    echo "<summary>email_logs table structure</summary>";
    echo "<table>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</details>";
} else {
    echo "<p class='error'>✗ email_logs table does NOT exist</p>";
}

// 3. Test Simple Email
echo "<h2>3. Simple Email Test</h2>";

if (isset($_GET['test_simple'])) {
    echo "<p class='info'>Sending test email...</p>";
    
    try {
        $mail = new PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $_ENV['MAIL_HOST'] ?? 'localhost';
        $mail->Port = $_ENV['MAIL_PORT'] ?? 1025;
        $mail->SMTPAuth = false;
        $mail->SMTPSecure = false;
        $mail->SMTPAutoTLS = false;
        
        // Enable debugging
        $mail->SMTPDebug = SMTP::DEBUG_SERVER;
        $mail->Debugoutput = function($str, $level) {
            echo "<pre class='info'>SMTP Debug: " . htmlspecialchars($str) . "</pre>";
        };
        
        // Recipients
        $mail->setFrom($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>', $_ENV['MAIL_FROM_NAME'] ?? 'Fit360');
        $mail->addAddress('<EMAIL>', 'Test User');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from Fit360';
        $mail->Body = '<h3>This is a test email</h3><p>If you can see this in Mailhog, email is working!</p>';
        $mail->AltBody = 'This is a test email. If you can see this in Mailhog, email is working!';
        
        // Send
        $mail->send();
        echo "<p class='success'>✓ Test email sent successfully!</p>";
        echo "<p class='info'>Check Mailhog at <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Email failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        if (isset($mail)) {
            echo "<p class='error'>PHPMailer Error: " . htmlspecialchars($mail->ErrorInfo) . "</p>";
        }
    }
} else {
    echo "<p><a href='?test_simple=1' class='button'>Send Test Email</a></p>";
}

// 4. Check Recent Email Logs
echo "<h2>4. Recent Email Logs</h2>";

$stmt = $db->query("
    SELECT el.*, i.invoice_number 
    FROM email_logs el
    LEFT JOIN invoices i ON el.invoice_id = i.id
    ORDER BY el.created_at DESC
    LIMIT 10
");
$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($logs)) {
    echo "<p class='warning'>No email logs found</p>";
} else {
    echo "<table>";
    echo "<tr><th>ID</th><th>Invoice</th><th>Recipient</th><th>Status</th><th>Sent At</th><th>Error</th></tr>";
    foreach ($logs as $log) {
        echo "<tr>";
        echo "<td>{$log['id']}</td>";
        echo "<td>{$log['invoice_number']}</td>";
        echo "<td>{$log['recipient_email']}</td>";
        echo "<td class='" . ($log['status'] === 'sent' ? 'success' : 'error') . "'>{$log['status']}</td>";
        echo "<td>{$log['sent_at']}</td>";
        echo "<td>" . htmlspecialchars($log['error_message'] ?? '-') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 5. Test Invoice Email
echo "<h2>5. Invoice Email Test</h2>";

// Get a recent invoice
$stmt = $db->query("
    SELECT i.*, u.email as user_email, u.invoice_email, c.email as client_email
    FROM invoices i
    LEFT JOIN users u ON i.user_id = u.id
    LEFT JOIN clients c ON i.client_id = c.id
    ORDER BY i.created_at DESC
    LIMIT 5
");
$invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($invoices)) {
    echo "<p class='warning'>No invoices found to test</p>";
} else {
    echo "<p>Select an invoice to test email:</p>";
    echo "<table>";
    echo "<tr><th>Invoice</th><th>Status</th><th>Recipient</th><th>Email</th><th>Action</th></tr>";
    
    foreach ($invoices as $inv) {
        $recipientType = $inv['user_id'] ? 'User' : 'Client';
        $email = $inv['user_id'] 
            ? ($inv['invoice_email'] ?: $inv['user_email'])
            : $inv['client_email'];
            
        echo "<tr>";
        echo "<td>{$inv['invoice_number']}</td>";
        echo "<td>{$inv['status']}</td>";
        echo "<td>$recipientType</td>";
        echo "<td>" . ($email ?: '<span class="error">NO EMAIL</span>') . "</td>";
        echo "<td>";
        if ($email) {
            echo "<a href='?test_invoice=" . $inv['id'] . "'>Test Email</a>";
        } else {
            echo "<span class='error'>Cannot send</span>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Process invoice email test
if (isset($_GET['test_invoice'])) {
    $invoiceId = (int)$_GET['test_invoice'];
    echo "<h3>Testing Invoice Email for ID: $invoiceId</h3>";
    
    try {
        $emailService = new EmailService();
        $result = $emailService->sendInvoiceEmail($invoiceId);
        
        echo "<pre>";
        echo "Result: " . json_encode($result, JSON_PRETTY_PRINT);
        echo "</pre>";
        
        if ($result['success']) {
            echo "<p class='success'>✓ Invoice email sent successfully!</p>";
        } else {
            echo "<p class='error'>✗ Invoice email failed: " . htmlspecialchars($result['message']) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
}

// 6. Check PHP Error Log
echo "<h2>6. Recent PHP Errors (Email-related)</h2>";

$errorLog = '/mnt/c/wamp64/logs/php_error.log';
if (file_exists($errorLog)) {
    $lines = file($errorLog);
    $emailErrors = [];
    
    foreach ($lines as $line) {
        if (stripos($line, 'email') !== false || stripos($line, 'mail') !== false || stripos($line, 'smtp') !== false) {
            $emailErrors[] = trim($line);
        }
    }
    
    if (!empty($emailErrors)) {
        $recent = array_slice($emailErrors, -10);
        echo "<pre>";
        foreach ($recent as $error) {
            echo htmlspecialchars($error) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p class='info'>No recent email-related errors found</p>";
    }
} else {
    echo "<p class='warning'>Cannot read PHP error log</p>";
}

echo "</body></html>";