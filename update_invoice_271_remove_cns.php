<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Updating Invoice 271 - Removing CNS from Description</h2>";
    
    // First check the invoice and its lines
    $checkSql = "SELECT i.*, u.first_name, u.last_name 
                 FROM invoices i
                 LEFT JOIN users u ON i.user_id = u.id
                 WHERE i.id = :id";
    
    $checkStmt = $db->prepare($checkSql);
    $checkStmt->execute(['id' => $invoiceId]);
    $invoice = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>User: {$invoice['first_name']} {$invoice['last_name']}</li>";
    echo "<li>Total: €" . number_format($invoice['total'], 2) . "</li>";
    echo "</ul>";
    
    // Get invoice items
    $itemsSql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id ORDER BY id";
    $itemsStmt = $db->prepare($itemsSql);
    $itemsStmt->execute(['invoice_id' => $invoiceId]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($items)) {
        echo "<p style='color: red;'>No invoice items found!</p>";
        exit;
    }
    
    echo "<h3>Current Invoice Lines:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Current Description</th><th>Action</th></tr>";
    
    $linesToUpdate = [];
    foreach ($items as $item) {
        echo "<tr>";
        echo "<td>{$item['id']}</td>";
        echo "<td>{$item['description']}</td>";
        
        if (strpos($item['description'], 'RÉTROCESSION CNS') !== false) {
            echo "<td style='color: orange;'>Will update to remove 'CNS'</td>";
            $linesToUpdate[] = $item['id'];
        } else {
            echo "<td style='color: green;'>No change needed</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    if (!empty($linesToUpdate)) {
        echo "<h3>Updating Lines...</h3>";
        
        // Update the lines
        $updateSql = "UPDATE invoice_items 
                     SET description = REPLACE(description, 'RÉTROCESSION CNS', 'RÉTROCESSION')
                     WHERE invoice_id = :invoice_id 
                     AND description LIKE '%RÉTROCESSION CNS%'";
        
        $updateStmt = $db->prepare($updateSql);
        $updateStmt->execute(['invoice_id' => $invoiceId]);
        $updatedCount = $updateStmt->rowCount();
        
        echo "<p style='color: green;'>✓ Successfully updated $updatedCount line(s)</p>";
        
        // Show updated lines
        echo "<h3>Updated Invoice Lines:</h3>";
        $itemsStmt->execute(['invoice_id' => $invoiceId]);
        $updatedItems = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Updated Description</th><th>Total</th></tr>";
        foreach ($updatedItems as $item) {
            $isUpdated = in_array($item['id'], $linesToUpdate);
            echo "<tr" . ($isUpdated ? " style='background-color: #e8ffe8;'" : "") . ">";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['description']}</td>";
            echo "<td>€" . number_format($item['total'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3 style='color: green;'>✓ Invoice 271 has been successfully updated!</h3>";
    } else {
        echo "<p style='color: green;'>No lines needed updating - invoice is already correct.</p>";
    }
    
    echo "<p><a href='/fit/public/invoices/$invoiceId' class='btn' style='background: blue; color: white; padding: 10px; text-decoration: none;'>View Updated Invoice</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>