<?php
/**
 * Find all invoices with number containing 190
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use PDO;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Find Invoice 190</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <h1>Search for Invoice 190</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // Search for invoices containing 190
        echo "<h2>Invoices containing '190' in number:</h2>";
        $stmt = $db->query("
            SELECT 
                i.id, 
                i.invoice_number, 
                i.invoice_type,
                i.status, 
                i.total_ttc,
                i.issue_date,
                i.sent_at,
                i.created_at,
                i.client_id,
                i.user_id,
                c.name as client_name,
                u.first_name,
                u.last_name
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN users u ON i.user_id = u.id
            WHERE i.invoice_number LIKE '%190%'
            ORDER BY i.id DESC
        ");
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($invoices) {
            echo "<p>Found " . count($invoices) . " invoice(s):</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Type</th><th>Status</th><th>Total</th><th>Recipient</th><th>Issue Date</th><th>Sent At</th><th>Created</th></tr>";
            foreach ($invoices as $inv) {
                $isTarget = $inv['invoice_number'] === 'FAC-DIV-2025-0190';
                $rowClass = $isTarget ? 'class="highlight"' : '';
                
                echo "<tr $rowClass>";
                echo "<td>{$inv['id']}</td>";
                echo "<td><strong>{$inv['invoice_number']}</strong></td>";
                echo "<td>{$inv['invoice_type']}</td>";
                echo "<td>{$inv['status']}</td>";
                echo "<td>" . number_format($inv['total_ttc'], 2) . " €</td>";
                
                // Recipient
                if ($inv['client_id']) {
                    echo "<td>Client: {$inv['client_name']}</td>";
                } elseif ($inv['user_id']) {
                    echo "<td>User: {$inv['first_name']} {$inv['last_name']}</td>";
                } else {
                    echo "<td>-</td>";
                }
                
                echo "<td>{$inv['issue_date']}</td>";
                echo "<td>" . ($inv['sent_at'] ?: '-') . "</td>";
                echo "<td>{$inv['created_at']}</td>";
                echo "</tr>";
                
                if ($isTarget) {
                    echo "<tr><td colspan='9' style='background-color: #f0f0f0; padding: 20px;'>";
                    echo "<h3>Found FAC-DIV-2025-0190!</h3>";
                    echo "<a href='check_invoice_190_email.php' class='button'>Check Email Status</a> | ";
                    echo "<a href='/fit/public/invoices/{$inv['id']}'>View Invoice</a>";
                    echo "</td></tr>";
                }
            }
            echo "</table>";
        } else {
            echo "<p>No invoices found containing '190'</p>";
        }
        
        // Search for specific invoice number
        echo "<h2>Direct search for FAC-DIV-2025-0190:</h2>";
        $stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = :number");
        $stmt->execute([':number' => 'FAC-DIV-2025-0190']);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "<p style='color: green;'>✓ Found directly!</p>";
            echo "<pre>" . print_r($invoice, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>✗ Not found with exact match</p>";
        }
        
        // Check latest DIV invoices
        echo "<h2>Latest 10 DIV (Rental) type invoices:</h2>";
        $stmt = $db->query("
            SELECT id, invoice_number, status, total_ttc, issue_date, created_at
            FROM invoices 
            WHERE invoice_type = 'rental' OR invoice_number LIKE '%DIV%'
            ORDER BY id DESC 
            LIMIT 10
        ");
        $divInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($divInvoices) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Status</th><th>Total</th><th>Issue Date</th><th>Created</th></tr>";
            foreach ($divInvoices as $inv) {
                echo "<tr>";
                echo "<td>{$inv['id']}</td>";
                echo "<td>{$inv['invoice_number']}</td>";
                echo "<td>{$inv['status']}</td>";
                echo "<td>" . number_format($inv['total_ttc'], 2) . " €</td>";
                echo "<td>{$inv['issue_date']}</td>";
                echo "<td>{$inv['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check number sequences
        echo "<h2>Invoice number sequences:</h2>";
        $stmt = $db->query("
            SELECT invoice_type, prefix, last_number, updated_at
            FROM invoice_number_sequences
            ORDER BY invoice_type
        ");
        $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($sequences) {
            echo "<table>";
            echo "<tr><th>Type</th><th>Prefix</th><th>Last Number</th><th>Updated</th></tr>";
            foreach ($sequences as $seq) {
                echo "<tr>";
                echo "<td>{$seq['invoice_type']}</td>";
                echo "<td>{$seq['prefix']}</td>";
                echo "<td>{$seq['last_number']}</td>";
                echo "<td>{$seq['updated_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    ?>
    
    <hr>
    <p><a href="/fit/public/">Back to Dashboard</a></p>
</body>
</html>