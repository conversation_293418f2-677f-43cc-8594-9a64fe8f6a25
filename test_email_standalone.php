<?php
/**
 * Standalone Email Diagnostics - No framework dependencies
 * This script will test email functionality without requiring the full application
 */

// Load environment variables manually
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        
        // Handle variable references like ${APP_NAME}
        if (preg_match('/\$\{(.+?)\}/', $value, $matches)) {
            $varName = $matches[1];
            if (isset($_ENV[$varName])) {
                $value = str_replace($matches[0], $_ENV[$varName], $value);
            }
        }
        
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Load composer autoloader
require_once __DIR__ . '/vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Database connection
function getDB() {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $user = $_ENV['DB_USERNAME'] ?? 'root';
    $pass = $_ENV['DB_PASSWORD'] ?? '';
    
    try {
        $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $db;
    } catch (PDOException $e) {
        die("Database connection failed: " . $e->getMessage());
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Diagnostics - Standalone</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2, h3 { color: #333; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; border: 1px solid #dee2e6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
        th { background-color: #e9ecef; font-weight: bold; }
        .button { 
            display: inline-block; 
            padding: 8px 16px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin: 5px;
        }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.success:hover { background: #218838; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; padding: 5px; }
        .status-box { 
            border: 1px solid #dee2e6; 
            border-radius: 4px; 
            padding: 15px; 
            margin: 10px 0;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Diagnostics - Standalone</h1>
        
        <?php
        $db = getDB();
        
        // 1. Check Email Infrastructure
        echo "<h2>1. Email Infrastructure Check</h2>";
        echo "<div class='status-box'>";
        
        // Check Mailhog
        echo "<h3>Mailhog Status:</h3>";
        $mailhogSMTP = @fsockopen('localhost', 1025, $errno, $errstr, 1);
        $mailhogWeb = @fsockopen('localhost', 8025, $errno, $errstr, 1);
        
        if ($mailhogSMTP) {
            echo "<p class='success'>✓ Mailhog SMTP (port 1025) is running</p>";
            fclose($mailhogSMTP);
        } else {
            echo "<p class='error'>✗ Mailhog SMTP (port 1025) is NOT running</p>";
            echo "<p class='info'>To start Mailhog:</p>";
            echo "<ol>";
            echo "<li>Download from: <a href='https://github.com/mailhog/MailHog/releases' target='_blank'>https://github.com/mailhog/MailHog/releases</a></li>";
            echo "<li>Download 'MailHog_windows_amd64.exe'</li>";
            echo "<li>Run the downloaded file</li>";
            echo "<li>Or use the provided <code>start_mailhog_windows.bat</code></li>";
            echo "</ol>";
        }
        
        if ($mailhogWeb) {
            echo "<p class='success'>✓ Mailhog Web UI (port 8025) is accessible</p>";
            echo "<p class='info'>View emails at: <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></p>";
            fclose($mailhogWeb);
        }
        echo "</div>";
        
        // Check email configuration
        echo "<div class='status-box'>";
        echo "<h3>Email Configuration (.env):</h3>";
        echo "<table>";
        echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
        
        $emailConfig = [
            'MAIL_HOST' => $_ENV['MAIL_HOST'] ?? 'NOT SET',
            'MAIL_PORT' => $_ENV['MAIL_PORT'] ?? 'NOT SET',
            'MAIL_USERNAME' => $_ENV['MAIL_USERNAME'] ?? 'NOT SET',
            'MAIL_PASSWORD' => $_ENV['MAIL_PASSWORD'] ?? 'NOT SET',
            'MAIL_ENCRYPTION' => $_ENV['MAIL_ENCRYPTION'] ?? 'NOT SET',
            'MAIL_FROM_ADDRESS' => $_ENV['MAIL_FROM_ADDRESS'] ?? 'NOT SET',
            'MAIL_FROM_NAME' => $_ENV['MAIL_FROM_NAME'] ?? 'NOT SET'
        ];
        
        foreach ($emailConfig as $key => $value) {
            echo "<tr>";
            echo "<td>$key</td>";
            echo "<td>" . ($key === 'MAIL_PASSWORD' && $value ? '***' : htmlspecialchars($value)) . "</td>";
            
            if ($key === 'MAIL_HOST' && $value === 'localhost') {
                echo "<td class='success'>✓ Correct for Mailhog</td>";
            } elseif ($key === 'MAIL_PORT' && $value === '1025') {
                echo "<td class='success'>✓ Correct for Mailhog</td>";
            } elseif (in_array($key, ['MAIL_USERNAME', 'MAIL_PASSWORD', 'MAIL_ENCRYPTION']) && empty($value)) {
                echo "<td class='success'>✓ Correct (empty for Mailhog)</td>";
            } elseif ($value === 'NOT SET') {
                echo "<td class='error'>✗ Not configured</td>";
            } else {
                echo "<td class='info'>Configured</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        // 2. Test Simple Email
        echo "<h2>2. Simple Email Test</h2>";
        echo "<div class='status-box'>";
        
        if (isset($_GET['test_simple'])) {
            echo "<p class='info'>Sending test email...</p>";
            
            try {
                $mail = new PHPMailer(true);
                
                // Server settings
                $mail->isSMTP();
                $mail->Host = $_ENV['MAIL_HOST'] ?? 'localhost';
                $mail->Port = $_ENV['MAIL_PORT'] ?? 1025;
                $mail->SMTPAuth = false;
                $mail->SMTPSecure = false;
                $mail->SMTPAutoTLS = false;
                
                // Enable debugging
                $mail->SMTPDebug = SMTP::DEBUG_SERVER;
                $mail->Debugoutput = function($str, $level) {
                    echo "<pre class='info'>SMTP Debug: " . htmlspecialchars($str) . "</pre>";
                };
                
                // Recipients
                $mail->setFrom($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>', $_ENV['MAIL_FROM_NAME'] ?? 'Fit360');
                $mail->addAddress('<EMAIL>', 'Test User');
                
                // Content
                $mail->isHTML(true);
                $mail->Subject = 'Test Email from Fit360 - ' . date('Y-m-d H:i:s');
                $mail->Body = '<h3>Test Email Successful!</h3>
                    <p>This email was sent at: ' . date('Y-m-d H:i:s') . '</p>
                    <p>If you can see this in Mailhog, your email system is working correctly!</p>';
                $mail->AltBody = 'Test email successful! Sent at: ' . date('Y-m-d H:i:s');
                
                // Send
                $mail->send();
                echo "<p class='success'>✓ Test email sent successfully!</p>";
                echo "<p class='info'>Check Mailhog at <a href='http://localhost:8025' target='_blank'>http://localhost:8025</a></p>";
                
            } catch (Exception $e) {
                echo "<p class='error'>✗ Email failed: " . htmlspecialchars($e->getMessage()) . "</p>";
                if (isset($mail)) {
                    echo "<p class='error'>PHPMailer Error: " . htmlspecialchars($mail->ErrorInfo) . "</p>";
                }
            }
        } else {
            echo "<p>Click the button below to send a test email:</p>";
            echo "<p><a href='?test_simple=1' class='button'>Send Test Email</a></p>";
        }
        echo "</div>";
        
        // 3. Check Email Logs
        echo "<h2>3. Recent Email Logs</h2>";
        echo "<div class='status-box'>";
        
        try {
            $stmt = $db->query("
                SELECT el.*, i.invoice_number 
                FROM email_logs el
                LEFT JOIN invoices i ON el.invoice_id = i.id
                ORDER BY el.created_at DESC
                LIMIT 10
            ");
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($logs)) {
                echo "<p class='warning'>No email logs found in the database</p>";
            } else {
                echo "<table>";
                echo "<tr><th>ID</th><th>Invoice</th><th>Recipient</th><th>Status</th><th>Sent At</th><th>Created</th><th>Error</th></tr>";
                foreach ($logs as $log) {
                    echo "<tr>";
                    echo "<td>{$log['id']}</td>";
                    echo "<td>" . htmlspecialchars($log['invoice_number'] ?? '-') . "</td>";
                    echo "<td>" . htmlspecialchars($log['recipient_email']) . "</td>";
                    echo "<td class='" . ($log['status'] === 'sent' ? 'success' : 'error') . "'>{$log['status']}</td>";
                    echo "<td>" . ($log['sent_at'] ?? '-') . "</td>";
                    echo "<td>{$log['created_at']}</td>";
                    echo "<td>" . htmlspecialchars($log['error_message'] ?? '-') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (PDOException $e) {
            echo "<p class='error'>Error reading email logs: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        echo "</div>";
        
        // 4. Test Invoice Email
        echo "<h2>4. Invoice Email Test</h2>";
        echo "<div class='status-box'>";
        echo "<p>To test invoice emails without framework conflicts, use the dedicated tool:</p>";
        echo "<p><a href='test_invoice_email_direct.php' class='button' target='_blank'>Open Direct Invoice Email Test</a></p>";
        echo "<p class='info'>This tool allows you to:</p>";
        echo "<ul>";
        echo "<li>Test email sending for specific invoices</li>";
        echo "<li>See detailed SMTP debugging information</li>";
        echo "<li>Verify email delivery without framework dependencies</li>";
        echo "</ul>";
        echo "</div>";
        
        // 5. Quick Actions
        echo "<h2>5. Quick Actions</h2>";
        echo "<div class='status-box'>";
        echo "<p>Useful links and actions:</p>";
        echo "<ul>";
        echo "<li><a href='http://localhost:8025' target='_blank'>Open Mailhog Web UI</a> - View all sent emails</li>";
        echo "<li><a href='start_mailhog_windows.bat'>Download start_mailhog_windows.bat</a> - Start Mailhog on Windows</li>";
        echo "<li><a href='?'>Refresh this page</a></li>";
        echo "</ul>";
        echo "</div>";
        ?>
    </div>
</body>
</html>