# 🏥 Fit360 AdminDesk - Healthcare Practice Management System

[![Version](https://img.shields.io/badge/version-2.3.6-blue.svg)](https://github.com/InfoALR/AdminDesk)
[![PHP](https://img.shields.io/badge/PHP-8.1%2B-777BB4.svg)](https://php.net)
[![Tests](https://img.shields.io/badge/tests-30%2F30%20passing-green.svg)](tests/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

**Professional healthcare practice management system designed specifically for Luxembourg healthcare providers.**

---

## 🎯 **Overview**

Fit360 AdminDesk is a comprehensive, production-ready healthcare practice management system that combines professional billing, client management, and inventory control in a modern, multilingual interface.

### **✨ Key Features**
- 🧾 **Professional Billing** - Complete invoice management with FIT 360 styling
- 💰 **Enhanced Retrocession Management** 
  - FAC-RET25 (25%) and FAC-RET30 (30%) support
  - Monthly amount configuration per practitioner
  - Automatic invoice generation from user profiles
  - Visual tracking of generated invoices
  - Invoice regeneration after deletion
  - Custom retrocession percentages per user
- 👥 **Client Management** - Full contact and billing information handling
- 📦 **Catalog System** - Multi-level product/service categorization
- 🏃 **Group Courses** - Instructor-specific service management
- 📊 **Inventory Control** - Real-time stock tracking and management
- 🌍 **Multilingual** - French, English, German support
- 🇱🇺 **Luxembourg Compliant** - VAT calculations (17% + 0% intracommunautaire)

---

## 🚀 **Quick Start**

### **Requirements**
- PHP 8.1+ (Tested with 8.3.6)
- MySQL 5.7+ or MariaDB 10.3+
- Composer
- Web server (Apache/Nginx)

### **Installation**
```bash
# Clone repository
git clone https://github.com/InfoALR/AdminDesk.git
cd AdminDesk

# Install dependencies
composer install

# Configure environment
cp .env.example .env
# Edit .env with your database settings

# Setup database
php database/migrate.php

# Start development server
php -S localhost:8000 -t public
```

Visit `http://localhost:8000` to access the system.

---

## 📋 **System Status**

### **Development Phases**
- ✅ **Phase 1:** Foundation & Configuration (100%)
- ✅ **Phase 2:** Translation & UI Enhancement (100%)
- ✅ **Phase 3:** Practitioner Billing System (100%)
- ✅ **Phase 4:** Catalog & Stock Management (100%) 🎉

### **Recent Updates (January 30, 2025)**
- 🔧 **Invoice Type System** - Fixed duplicate invoice types and MySQL constraint violations
- 📝 **Course Management** - Fixed course saving issues for coaches with CSRF validation
- 🧾 **LOC Invoice Format** - Changed subject to "LOCATION SALLE" with clean course descriptions
- 🌍 **International VAT** - Fixed positioning and display of TVA Intercommunautaire
- 🤖 **Development Tools** - Added Claude Code subagents and documentation automation
- 🧪 **Testing Infrastructure** - Created comprehensive test scripts for invoice generation
- 🔢 **Standardization** - Unified invoice numbering patterns: FAC-{PREFIX}-{YEAR}-{NUMBER:4}

### **Test Coverage**
- **Catalog Item Tests:** 10/10 ✅
- **Category Management Tests:** 12/12 ✅
- **Stock Management Tests:** 8/8 ✅
- **Total:** 30/30 passing ✅

---

## 🏗️ **Architecture**

### **Technology Stack**
- **Backend:** PHP 8.3+ with Flight Framework
- **Database:** MySQL/MariaDB with optimized schema
- **Frontend:** Bootstrap 5.3 with responsive design
- **PDF Generation:** TCPDF for professional invoices
- **Authentication:** JWT-based with secure sessions

### **Key Components**
- **Custom ORM** - Enhanced with modern query methods
- **Multilingual Engine** - Dynamic translation system with complete coverage
- **Invoice System** - Professional PDF generation with FIT 360 styling
- **Catalog Management** - Hierarchical categories with stock tracking
- **Permission System** - Groups-based access control (no role confusion)
- **User Management** - Complete profile and permission management

---

## 📦 **Features**

### **💼 Invoice Management**
- Create, edit, and manage draft invoices
- Professional PDF generation with FIT 360 styling
- Send invoices and track payment status
- Generate credit notes and adjustments
- Multiple invoice types (FAC-LOY, FAC-RET30, FAC-RET25, FAC-USR)
- Retrocession calculations (20% CNS, 20% Patients, 5-10% Secretariat)
- Shared sequence counter across all invoice types

### **👥 Client Management**
- Individual and company client profiles
- Complete contact information with addresses
- Billing preferences and payment terms
- Client relationship tracking

### **🛍️ Catalog & Inventory**
- Multi-level category hierarchies with JSON multilingual support
- Real-time stock tracking and control
- Stock movement history with audit trail
- Low stock alerts and notifications
- Automatic code generation for items
- Group courses catalog ("Cours collectifs")
- Service-based items with hourly rates
- VAT support (17% standard + 0% intracommunautaire)

### **🌍 Multilingual Support**
- French (primary), English, German
- Dynamic translation system
- Localized number and date formatting
- Currency support (EUR)

---

## 🆕 **What's New in v2.3.6**

### **Invoice Generation Fixes**
- **Fixed Critical Bugs**:
  - Resolved duplicate invoice type entries causing MySQL errors
  - Fixed 4-character unique index limitation on invoice type codes
  - Corrected UnifiedInvoiceGenerator SQL query (was using 'prefix' instead of 'code')
  - Standardized all invoice type codes: LOY, LOC, RET2 (25%), RET3 (30%)

### **Course Management Improvements**
- **CSRF Protection**: Added validation to all course-related methods
- **UI Fixes**: Third course now properly appears after saving
- **Error Handling**: Cleaned up console errors and improved form submission

### **Bulk Invoice Enhancements**
- **LOC Invoice Format**:
  - Subject: "LOCATION SALLE"
  - Description: Only course names (no decimals)
  - Quantity: Shows integers for sessions
- **International VAT**: Properly positioned below payment conditions

### **Development Infrastructure**
- **Claude Code Integration**:
  - Created 7 specialized subagents for different development aspects
  - Added /update-docs command for automatic documentation updates
  - Integrated Task Master AI workflow
- **Diagnostic Tools**:
  - test-user-invoice-generation.php
  - check-invoice-type-mapping.php
  - fix-all-invoice-patterns.php
  - delete-test-invoices.php

## 🆕 **What's New in v2.3.5**

### **Bulk Loyer Invoice Generation**
- Fixed invoice line items not displaying (invoice_lines vs invoice_items)
- Corrected invoice number prefixes
- Fixed secretary amount calculations
- Enhanced error handling

## 🆕 **What's New in v2.3.4**

### **Retrocession System Enhancements**
- **Monthly Amount Configuration**: Configure CNS and patient amounts for each month in user profiles
- **Automatic Invoice Generation**: One-click generation from user profile with visual indicators
- **Invoice Regeneration**: Delete and regenerate invoices while preserving retrocession data
- **Custom Percentages**: User-specific retrocession percentages and labels
- **Permission Controls**: Manager/Admin only delete functionality
- **Enhanced UI**: Visual tracking of generated invoices per month

### **Technical Improvements**
- **Foreign Key Management**: Proper handling of related data on invoice deletion
- **Error Handling**: Clean JSON responses with detailed error messages
- **Database Optimization**: Improved retrocession data entry management

## 🆕 **What's New in v2.3.3**

### **Email System Improvements**
- **PDF Email Attachments**: Invoices are now properly attached to emails as PDFs
- **Period in Subject**: Email subjects include the invoice period (e.g., "Facture (JUIN 2025)")
- **Consistent PDF Generation**: Emails use the same PDF generation as the download function
- **CSRF Protection**: Enhanced security for form submissions
- **Send Button Fix**: Fixed "Method Not Allowed" error when sending invoices from list view

### **Code Quality**
- Removed 200+ temporary test and debug files
- Improved error handling and debugging
- Enhanced JavaScript form submissions
- Better session management

## 📚 **Previous Updates (v2.3.2)**

### **Retrocession Invoice Support**
- **FAC-RET25**: 20% CNS + 20% Patients + 5% Secretariat
- **FAC-RET30**: 20% CNS + 20% Patients + 10% Secretariat
- Automatic calculation of retrocession amounts
- Pre-configured invoice lines
- Shared sequence numbering across all invoice types

### **Enhanced Product Catalog**
- Fixed category creation with JSON multilingual names
- Added "Cours collectifs" category with instructor services:
  - Yoga & AeroYoga (Malaurie Zéler)
  - Individual & Group Classes (Nicolas Moineau)
  - Pilates (Isabelle Lamy)
  - Spinning (Pedro)
  - Group Classes (Justine)

### **Technical Improvements**
- Fixed POST data handling in Flight framework
- Enhanced form submission for retrocession invoices
- Improved JavaScript for dynamic column layouts
- Better error handling and validation

---

## 💰 **Retrocession Management Setup**

### **1. Configure Monthly Amounts**
1. Navigate to Users → Edit User (practitioner)
2. Scroll to "Retrocession - Monthly Amounts" section
3. Enter CNS and Patient amounts for each month
4. Save the user profile

### **2. Generate Retrocession Invoice**
1. From the user profile, click "Generate Invoice" for the desired month
2. The system automatically creates an invoice for the previous month
3. Visual indicators show which months have invoices generated

### **3. Manage Retrocession Invoices**
- **View**: Check generated invoices in the main invoice list
- **Delete**: Managers/Admins can delete invoices (data is preserved)
- **Regenerate**: After deletion, regenerate from user profile
- **Custom Settings**: Configure user-specific percentages and labels

### **4. Retrocession Calculation**
- **Standard (30%)**: 20% CNS + 20% Patients + 10% Secretary
- **Reduced (25%)**: 20% CNS + 20% Patients + 5% Secretary
- **Custom**: Set per-user percentages in retrocession settings

---

## 🧪 **Testing**

### **Run Tests**
```bash
# Run all tests
php tests/run-all-tests.php

# Run specific phase
php public/test-runner.php?phase=4

# Run individual test categories
php public/test-runner.php?test=CatalogItemTest
```

### **Test Categories**
- **Unit Tests** - Model and service testing
- **Integration Tests** - Database and API testing
- **Feature Tests** - End-to-end functionality
- **Performance Tests** - Load and stress testing

---

## 📚 **Documentation**

### **Core Documentation**
- [`CURRENT_STATUS.md`](tasks/CURRENT_STATUS.md) - Real-time system status
- [`PROJECT_SUMMARY.md`](tasks/PROJECT_SUMMARY.md) - Complete feature overview
- [`CLAUDE.md`](CLAUDE.md) - AI assistant instructions
- [`CLEANUP_SUMMARY.md`](CLEANUP_SUMMARY.md) - Recent cleanup activities

### **Development Guides**
- [`tasks/`](tasks/) - Phase-by-phase implementation guides
- [`database/`](database/) - Database schema and migrations
- [`tests/`](tests/) - Test suite documentation

---

## 🔧 **Configuration**

### **Environment Variables**
```env
# Application
APP_NAME="Fit360 AdminDesk"
APP_ENV=production
APP_TIMEZONE=Europe/Luxembourg

# Database
DB_HOST=127.0.0.1
DB_DATABASE=fitapp
DB_USERNAME=your_user
DB_PASSWORD=your_password

# Security
JWT_SECRET=your_secret_key
CSRF_PROTECTION=true
```

### **Production Settings**
- Enable CSRF protection
- Set `APP_DEBUG=false`
- Use HTTPS URLs
- Configure proper SMTP settings
- Set secure file permissions

---

## 🤝 **Contributing**

### **Development Workflow**
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### **Code Standards**
- PSR-4 autoloading
- Comprehensive testing
- Clear documentation
- Security best practices

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🆘 **Support**

### **Getting Help**
- 📖 Check the [documentation](tasks/)
- 🐛 Report issues on [GitHub Issues](https://github.com/InfoALR/AdminDesk/issues)
- 💬 Join discussions in [GitHub Discussions](https://github.com/InfoALR/AdminDesk/discussions)

### **Professional Support**
For professional support, customization, or deployment assistance, please contact the development team.

---

## 🎉 **Acknowledgments**

- Built with ❤️ for Luxembourg healthcare providers
- Powered by modern PHP and web technologies
- Designed for professional healthcare practice management

---

**🚀 Ready for production deployment in Luxembourg healthcare practices!** 🇱🇺
