<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Auto-fill Fix Applied ✅</h2>";
    
    echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Fix Summary:</h3>";
    echo "<p>The LOY auto-fill functionality has been updated to trigger when the page loads with LOY pre-selected.</p>";
    echo "</div>";
    
    echo "<h3>What was fixed:</h3>";
    echo "<ul>";
    echo "<li>Added code to detect when LOY invoice type is pre-selected on page load</li>";
    echo "<li>Added automatic trigger of the change event to execute auto-fill logic</li>";
    echo "<li>Added 300ms delay to ensure all page elements are fully loaded</li>";
    echo "</ul>";
    
    echo "<h3>Expected Behavior:</h3>";
    echo "<ol>";
    echo "<li>When you go to <a href='/fit/public/invoices/create?type=loyer' target='_blank'>/invoices/create?type=loyer</a></li>";
    echo "<li>The 'Objet' field should auto-fill with <strong>LOYER + CHARGES</strong></li>";
    echo "<li>The 'Période' field should auto-fill with the previous month (e.g., <strong>JUIN 2025</strong>)</li>";
    echo "<li>Only Medical and Managers group users should be shown in the user dropdown</li>";
    echo "</ol>";
    
    // Calculate expected period
    $currentMonth = date('n');
    $currentYear = date('Y');
    $previousMonth = $currentMonth - 1;
    $previousYear = $currentYear;
    
    if ($previousMonth < 1) {
        $previousMonth = 12;
        $previousYear--;
    }
    
    $frenchMonths = [
        1 => 'JANVIER', 2 => 'FÉVRIER', 3 => 'MARS', 4 => 'AVRIL',
        5 => 'MAI', 6 => 'JUIN', 7 => 'JUILLET', 8 => 'AOÛT',
        9 => 'SEPTEMBRE', 10 => 'OCTOBRE', 11 => 'NOVEMBRE', 12 => 'DÉCEMBRE'
    ];
    
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Current Date Information:</h3>";
    echo "<ul>";
    echo "<li>Today's date: " . date('d/m/Y') . "</li>";
    echo "<li>Expected period: <strong>{$frenchMonths[$previousMonth]} {$previousYear}</strong></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Clear your browser cache (Ctrl+F5 or Cmd+Shift+R)</li>";
    echo "<li>Go to the invoice creation page with type=loyer parameter</li>";
    echo "<li>Check if the Subject and Period fields are automatically filled</li>";
    echo "<li>If not working, check the browser console for any error messages</li>";
    echo "</ol>";
    
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Direct Test Links:</h3>";
    echo "<p><a href='/fit/public/invoices/create?type=loyer' target='_blank' style='font-size: 18px; font-weight: bold;'>🔗 Create LOY Invoice</a></p>";
    echo "<p><a href='/fit/public/invoices/create' target='_blank'>🔗 Create Invoice (no type)</a></p>";
    echo "</div>";
    
    // Check if LOY type exists in database
    $stmt = $db->prepare("SELECT * FROM invoice_types WHERE prefix = 'LOY'");
    $stmt->execute();
    $loyType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($loyType) {
        echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
        echo "<h3>LOY Invoice Type Status:</h3>";
        echo "<p style='color: green;'>✅ LOY invoice type exists in database</p>";
        echo "<ul>";
        echo "<li>ID: {$loyType['id']}</li>";
        echo "<li>Name: {$loyType['name']}</li>";
        echo "<li>Prefix: {$loyType['prefix']}</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ LOY invoice type not found in database</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<script>
console.log('Test page loaded - LOY auto-fill fix has been applied to create-modern.twig');
</script>