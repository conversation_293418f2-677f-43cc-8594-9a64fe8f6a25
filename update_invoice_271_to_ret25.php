<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Updating Invoice ID: $invoiceId to RET25 type</h2>";
    
    // First, get the RET25 invoice type ID
    $typeSql = "SELECT id FROM config_invoice_types WHERE code = 'RET25' OR code = 'RT25'";
    $typeStmt = $db->prepare($typeSql);
    $typeStmt->execute();
    $invoiceType = $typeStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoiceType) {
        echo "<p style='color: red;'>RET25 invoice type not found in config_invoice_types!</p>";
        
        // Show available types
        $allTypesSql = "SELECT * FROM config_invoice_types ORDER BY code";
        $allTypesStmt = $db->prepare($allTypesSql);
        $allTypesStmt->execute();
        $types = $allTypesStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Available Invoice Types:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th></tr>";
        foreach ($types as $type) {
            echo "<tr><td>{$type['id']}</td><td>{$type['code']}</td><td>{$type['name']}</td></tr>";
        }
        echo "</table>";
        exit;
    }
    
    $ret25TypeId = $invoiceType['id'];
    echo "<p>Found RET25 invoice type with ID: $ret25TypeId</p>";
    
    // Check current invoice details
    $checkSql = "SELECT i.*, it.code as type_code, it.name as type_name 
                 FROM invoices i
                 LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
                 WHERE i.id = :id";
    $checkStmt = $db->prepare($checkSql);
    $checkStmt->execute(['id' => $invoiceId]);
    $invoice = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Current Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>Type: {$invoice['type_name']} ({$invoice['type_code']})</li>";
    echo "<li>Period: {$invoice['period']}</li>";
    echo "<li>Status: {$invoice['status']}</li>";
    echo "</ul>";
    
    // Update the invoice
    $updateSql = "UPDATE invoices 
                  SET invoice_number = :new_number,
                      invoice_type_id = :type_id
                  WHERE id = :id";
    
    $updateStmt = $db->prepare($updateSql);
    $result = $updateStmt->execute([
        'new_number' => 'FAC-RET25-2025-0198',
        'type_id' => $ret25TypeId,
        'id' => $invoiceId
    ]);
    
    if ($result) {
        echo "<h3 style='color: green;'>Invoice updated successfully!</h3>";
        
        // Show updated details
        $checkStmt->execute(['id' => $invoiceId]);
        $updated = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>Updated Invoice Details:</h3>";
        echo "<ul>";
        echo "<li>Number: {$updated['invoice_number']}</li>";
        echo "<li>Type: {$updated['type_name']} ({$updated['type_code']})</li>";
        echo "<li>Period: {$updated['period']}</li>";
        echo "</ul>";
        
        // Check if this is for Rémi Heine and update CNS lines
        $userSql = "SELECT u.first_name, u.last_name 
                    FROM invoices i
                    JOIN users u ON i.user_id = u.id
                    WHERE i.id = :invoice_id";
        $userStmt = $db->prepare($userSql);
        $userStmt->execute(['invoice_id' => $invoiceId]);
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && ($user['first_name'] === 'Rémi' && $user['last_name'] === 'Heine')) {
            echo "<h3>Invoice is for Rémi Heine - Updating retrocession lines...</h3>";
            
            // Update lines that contain "RÉTROCESSION CNS"
            $updateLinesSql = "UPDATE invoice_items 
                              SET description = REPLACE(description, 'RÉTROCESSION CNS', 'RÉTROCESSION')
                              WHERE invoice_id = :invoice_id 
                              AND description LIKE '%RÉTROCESSION CNS%'";
            
            $updateLinesStmt = $db->prepare($updateLinesSql);
            $updateResult = $updateLinesStmt->execute(['invoice_id' => $invoiceId]);
            
            if ($updateResult) {
                $affectedLines = $updateLinesStmt->rowCount();
                echo "<p style='color: green;'>Updated $affectedLines invoice line(s) to remove 'CNS' from description</p>";
            }
        }
        
        echo "<p><a href='/fit/public/invoices/$invoiceId'>View Updated Invoice</a></p>";
    } else {
        echo "<p style='color: red;'>Failed to update invoice!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>