<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Invoice Type Table Mismatch</h2>";
    
    // Check what the foreign key expects
    echo "<h3>Checking Foreign Key Constraint:</h3>";
    $stmt = $db->query("
        SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            information_schema.KEY_COLUMN_USAGE
        WHERE 
            TABLE_SCHEMA = 'fitapp' 
            AND TABLE_NAME = 'invoices' 
            AND COLUMN_NAME = 'type_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $constraint = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>The invoices.type_id column references: <strong>{$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}</strong></p>";
    
    // Show what's in each table
    echo "<h3>invoice_types table (UnifiedInvoiceGenerator uses this):</h3>";
    $stmt = $db->query("SELECT * FROM invoice_types WHERE code IN ('RET', 'RET2', 'RET3')");
    $invoiceTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Name</th></tr>";
    foreach ($invoiceTypes as $type) {
        echo "<tr><td>{$type['id']}</td><td>{$type['code']}</td><td>{$type['name']}</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>config_invoice_types table (Foreign key references this):</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code IN ('ret', 'ret2', 'ret3')");
    $configTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th></tr>";
    foreach ($configTypes as $type) {
        echo "<tr><td>{$type['id']}</td><td>{$type['code']}</td><td>{$type['prefix']}</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>Problem:</h3>";
    echo "<p>UnifiedInvoiceGenerator gets ID from invoice_types (e.g., ID 18 for RET2), but the foreign key expects an ID from config_invoice_types (e.g., ID 37 for ret2).</p>";
    
    echo "<h3>Solution:</h3>";
    echo "<p>We need to update UnifiedInvoiceGenerator to use config_invoice_types table instead of invoice_types.</p>";
    
    // Show the fix
    echo "<h3>Required Change in UnifiedInvoiceGenerator.php:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo "// Change this (around line 495-501):
\$stmt = \$this->db->prepare(\"
    SELECT * FROM invoice_types 
    WHERE code = :code AND is_active = 1
    LIMIT 1
\");

// To this:
\$stmt = \$this->db->prepare(\"
    SELECT * FROM config_invoice_types 
    WHERE code = :code AND is_active = 1
    LIMIT 1
\");";
    echo "</pre>";
    
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Action Required:</h3>";
    echo "<p>Edit <strong>/app/services/UnifiedInvoiceGenerator.php</strong> line ~496:</p>";
    echo "<p>Change <code>FROM invoice_types</code> to <code>FROM config_invoice_types</code></p>";
    echo "<p>Also ensure the codes match exactly (lowercase 'ret2' and 'ret3' in config_invoice_types).</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}