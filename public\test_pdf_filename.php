<?php
// Test PDF filename generation
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Load application bootstrap
require_once __DIR__ . '/../app/config/bootstrap.php';

$invoiceId = $_GET['id'] ?? 246;

// Load invoice data
$invoiceModel = new \App\Models\Invoice();
$invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);

if (!$invoice) {
    die("Invoice not found");
}

// Get additional data
$db = Flight::db();

// Get invoice type code if not already loaded
if (!isset($invoice['invoice_type_code']) && !empty($invoice['invoice_type_id'])) {
    $stmt = $db->prepare("SELECT code FROM config_invoice_types WHERE id = :id");
    $stmt->execute(['id' => $invoice['invoice_type_id']]);
    $typeData = $stmt->fetch(PDO::FETCH_ASSOC);
    $invoice['invoice_type_code'] = $typeData ? $typeData['code'] : null;
}

// Check if this is a rental invoice
$isRentalInvoice = ($invoice['invoice_type'] === 'rental' || 
                    (isset($invoice['invoice_type_code']) && $invoice['invoice_type_code'] === 'rental') || 
                    (isset($invoice['invoice_type_code']) && $invoice['invoice_type_code'] === 'loyer'));

// Generate filename
$invoiceYear = date('Y', strtotime($invoice['issue_date'] ?? $invoice['created_at'] ?? date('Y-m-d')));

// Get document type prefix
$docPrefix = 'FAC';
if (!empty($invoice['document_type']['code'])) {
    $docPrefix = strtoupper($invoice['document_type']['code']);
}

// Get invoice type prefix
$typePrefix = '';
if (!empty($invoice['type']['prefix'])) {
    $typePrefix = strtoupper($invoice['type']['prefix']);
} elseif (!empty($invoice['invoice_type_code'])) {
    $typePrefixMap = [
        'rental' => 'LOY',
        'loyer' => 'LOY',
        'retrocession_30' => 'RET30',
        'retrocession_25' => 'RET25',
        'retrocession' => 'RET',
        'service' => 'SRV',
        'hourly' => 'HOR'
    ];
    $typePrefix = $typePrefixMap[$invoice['invoice_type_code']] ?? '';
} elseif ($isRentalInvoice) {
    $typePrefix = 'LOY';
}

// Build filename
if ($typePrefix) {
    $filename = sprintf('%s-%s-%s-%s.pdf', $docPrefix, $typePrefix, $invoiceYear, $invoice['invoice_number']);
} else {
    $filename = sprintf('%s-%s-%s.pdf', $docPrefix, $invoiceYear, $invoice['invoice_number']);
}

// Debug output
echo "<h2>PDF Filename Debug</h2>";
echo "<pre>";
echo "Invoice ID: " . $invoiceId . "\n";
echo "Invoice Number: " . $invoice['invoice_number'] . "\n";
echo "Issue Date: " . ($invoice['issue_date'] ?? 'N/A') . "\n";
echo "Year: " . $invoiceYear . "\n";
echo "Document Type Code: " . ($invoice['document_type']['code'] ?? 'N/A') . "\n";
echo "Invoice Type Code: " . ($invoice['invoice_type_code'] ?? 'N/A') . "\n";
echo "Type Prefix: " . ($invoice['type']['prefix'] ?? 'N/A') . "\n";
echo "Is Rental: " . ($isRentalInvoice ? 'Yes' : 'No') . "\n";
echo "\n";
echo "Generated Values:\n";
echo "Doc Prefix: " . $docPrefix . "\n";
echo "Type Prefix: " . $typePrefix . "\n";
echo "Filename: " . $filename . "\n";
echo "</pre>";

// Test with TCPDF
echo "<h3>Testing with TCPDF:</h3>";
echo "<pre>";

class TestPDF extends TCPDF {
    public function Header() {}
    public function Footer() {}
}

$pdf = new TestPDF();
$pdf->SetTitle($filename);
echo "SetTitle: " . $filename . "\n";
echo "PDF Title: " . $pdf->title . "\n";
echo "</pre>";

echo "<p><a href='/fit/public/invoice-pdf.php?id=" . $invoiceId . "&action=download'>Test Download PDF</a></p>";
echo "<p><a href='/fit/public/invoices/" . $invoiceId . "'>Back to Invoice</a></p>";