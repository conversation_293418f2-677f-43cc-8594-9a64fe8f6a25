<?php
// Database investigation for Invoice 263
echo "<h1>Invoice 263 Database Investigation</h1>";

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. Get Invoice 263 basic info
    echo "<h2>1. Invoice 263 Basic Information</h2>";
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 263");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($invoice as $key => $value) {
            echo "<tr><td>$key</td><td>$value</td></tr>";
        }
        echo "</table>";
        
        $clientId = $invoice['client_id'];
        
        // 2. Get client information
        echo "<h2>2. Client Information</h2>";
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = :client_id");
        $stmt->execute(['client_id' => $clientId]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($client) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            echo "<tr><td>ID</td><td>{$client['id']}</td></tr>";
            echo "<tr><td>Name</td><td>{$client['first_name']} {$client['last_name']}</td></tr>";
            echo "<tr><td>Email</td><td>{$client['email']}</td></tr>";
            echo "<tr><td>Exclude Patient Retrocession</td><td>" . ($client['exclude_patient_retrocession'] ?? 'NULL') . "</td></tr>";
            echo "</table>";
        }
        
        // 3. Get user courses
        echo "<h2>3. User Courses Configuration</h2>";
        $stmt = $pdo->prepare("SELECT * FROM user_courses WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $clientId]);
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($courses) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Course Name</th><th>Hourly Rate</th><th>VAT Rate</th><th>Is Active</th></tr>";
            foreach ($courses as $course) {
                echo "<tr>";
                echo "<td>{$course['id']}</td>";
                echo "<td>{$course['course_name']}</td>";
                echo "<td>{$course['hourly_rate']}€</td>";
                echo "<td>{$course['vat_rate']}%</td>";
                echo "<td>" . ($course['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No courses found for this user.</p>";
        }
        
        // 4. Get invoice lines
        echo "<h2>4. Invoice Lines</h2>";
        $stmt = $pdo->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 263 ORDER BY sort_order, id");
        $stmt->execute();
        $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($lines) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Line Type</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
            $totalCalculated = 0;
            foreach ($lines as $line) {
                echo "<tr>";
                echo "<td>{$line['id']}</td>";
                echo "<td>{$line['line_type']}</td>";
                echo "<td>{$line['description']}</td>";
                echo "<td>{$line['quantity']}</td>";
                echo "<td>{$line['unit_price']}€</td>";
                echo "<td>{$line['vat_rate']}%</td>";
                echo "<td>{$line['line_total']}€</td>";
                echo "</tr>";
                $totalCalculated += $line['line_total'];
            }
            echo "<tr style='font-weight: bold; background-color: #f0f0f0;'>";
            echo "<td colspan='6'>Total from Lines</td>";
            echo "<td>{$totalCalculated}€</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p>No invoice lines found!</p>";
        }
        
        // 5. Check for invoice_items (old table)
        echo "<h2>5. Invoice Items (Legacy Table)</h2>";
        $stmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = 263");
        $stmt->execute();
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($items) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Total</th></tr>";
            $totalCalculated = 0;
            foreach ($items as $item) {
                echo "<tr>";
                echo "<td>{$item['id']}</td>";
                echo "<td>{$item['description']}</td>";
                echo "<td>{$item['quantity']}</td>";
                echo "<td>{$item['unit_price']}€</td>";
                echo "<td>{$item['vat_rate']}%</td>";
                echo "<td>{$item['total']}€</td>";
                echo "</tr>";
                $totalCalculated += $item['total'];
            }
            echo "<tr style='font-weight: bold; background-color: #f0f0f0;'>";
            echo "<td colspan='5'>Total from Items</td>";
            echo "<td>{$totalCalculated}€</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p>No invoice items found in legacy table.</p>";
        }
        
        // 6. Analysis
        echo "<h2>6. Analysis</h2>";
        echo "<p><strong>Invoice Total:</strong> {$invoice['total']}€</p>";
        echo "<p><strong>Target Total:</strong> 930.00€</p>";
        echo "<p><strong>Difference:</strong> " . number_format(930.00 - $invoice['total'], 2) . "€</p>";
        
        // Based on the user's data: Nicolas Moineau has:
        // - Individuel: 15 sessions, 17% VAT, 585€
        // - Collectif: 30 sessions, 17% VAT, 210€
        echo "<h2>7. Expected Calculation (From User Data)</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Course Type</th><th>Sessions</th><th>VAT Rate</th><th>Amount</th></tr>";
        echo "<tr><td>Individuel</td><td>15</td><td>17%</td><td>585€</td></tr>";
        echo "<tr><td>Collectif</td><td>30</td><td>17%</td><td>210€</td></tr>";
        echo "<tr><td><strong>Total</strong></td><td>45</td><td>17%</td><td><strong>795€</strong></td></tr>";
        echo "</table>";
        
        echo "<p><strong>Expected vs Actual:</strong></p>";
        echo "<ul>";
        echo "<li>Expected based on courses: 795€</li>";
        echo "<li>Current invoice total: {$invoice['total']}€</li>";
        echo "<li>Target total: 930.00€</li>";
        echo "<li>Missing amount: " . number_format(930.00 - 795.00, 2) . "€</li>";
        echo "</ul>";
        
    } else {
        echo "<p>Invoice 263 not found in database.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}
?>