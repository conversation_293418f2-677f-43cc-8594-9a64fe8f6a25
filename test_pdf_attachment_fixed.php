<?php
/**
 * Fixed test for PDF attachment emails
 * Run this in a browser: http://localhost/fit/test_pdf_attachment_fixed.php
 */

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
            continue;
        }
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Configure PHP to use Mailhog
ini_set('SMTP', $_ENV['MAIL_HOST'] ?? 'localhost');
ini_set('smtp_port', $_ENV['MAIL_PORT'] ?? '1025');
ini_set('sendmail_from', $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>');

// Create a real PDF using TCPDF
require_once __DIR__ . '/vendor/autoload.php';

// Create new PDF document
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Set document information
$pdf->SetCreator('Fit360 AdminDesk');
$pdf->SetAuthor('Fit360');
$pdf->SetTitle('Test Invoice');
$pdf->SetSubject('Test Invoice with Attachment');

// Remove default header/footer
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);

// Add a page
$pdf->AddPage();

// Set font
$pdf->SetFont('helvetica', '', 12);

// Add content
$pdf->Cell(0, 10, 'TEST INVOICE', 0, 1, 'C');
$pdf->Ln(10);
$pdf->Cell(0, 10, 'Invoice Number: TEST-001', 0, 1);
$pdf->Cell(0, 10, 'Date: ' . date('d/m/Y'), 0, 1);
$pdf->Cell(0, 10, 'Amount: €1,170.00', 0, 1);
$pdf->Ln(10);
$pdf->MultiCell(0, 10, 'This is a test invoice to verify PDF attachment functionality.', 0, 'L');

// Get PDF content
$pdfContent = $pdf->Output('', 'S');

// Email parameters
$to = '<EMAIL>';
$subject = 'Test Invoice with PDF Attachment';
$fromEmail = $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>';
$fromName = 'Fit360 AdminDesk';

// Generate unique boundary
$boundary = md5(uniqid(time()));

// Headers
$headers = "From: $fromName <$fromEmail>\r\n";
$headers .= "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: multipart/mixed; boundary=\"$boundary\"\r\n";

// Message body
$message = "--$boundary\r\n";
$message .= "Content-Type: text/plain; charset=\"UTF-8\"\r\n";
$message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
$message .= "Dear Client,\n\n";
$message .= "Please find attached your invoice TEST-001.\n\n";
$message .= "Amount: €1,170.00\n";
$message .= "Due Date: " . date('d/m/Y', strtotime('+30 days')) . "\n\n";
$message .= "Best regards,\nFit360 Team\r\n\r\n";

// Attachment
$message .= "--$boundary\r\n";
$message .= "Content-Type: application/pdf; name=\"TEST-INVOICE-001.pdf\"\r\n";
$message .= "Content-Transfer-Encoding: base64\r\n";
$message .= "Content-Disposition: attachment; filename=\"TEST-INVOICE-001.pdf\"\r\n\r\n";
$message .= chunk_split(base64_encode($pdfContent)) . "\r\n";
$message .= "--$boundary--";

// Display HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>PDF Attachment Email Test (Fixed)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeeba; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>PDF Attachment Email Test (Fixed Version)</h1>
    
    <div class="info">
        <h3>Email Configuration:</h3>
        <ul>
            <li><strong>SMTP Host:</strong> <?php echo $_ENV['MAIL_HOST'] ?? 'localhost'; ?></li>
            <li><strong>SMTP Port:</strong> <?php echo $_ENV['MAIL_PORT'] ?? '1025'; ?></li>
            <li><strong>From:</strong> <?php echo $fromEmail; ?></li>
            <li><strong>To:</strong> <?php echo $to; ?></li>
            <li><strong>Subject:</strong> <?php echo $subject; ?></li>
            <li><strong>PDF Size:</strong> <?php echo strlen($pdfContent); ?> bytes</li>
        </ul>
    </div>
    
    <?php
    // Send the email
    $result = @mail($to, $subject, $message, $headers);
    
    if ($result) {
        echo '<h3 class="success">✓ Email sent successfully!</h3>';
        echo '<p>Check Mailhog at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a> to see the email with PDF attachment.</p>';
        
        // Also save a copy of the PDF for testing
        file_put_contents('test-invoice.pdf', $pdfContent);
        echo '<p>A copy of the PDF has been saved as <a href="test-invoice.pdf" target="_blank">test-invoice.pdf</a> for verification.</p>';
    } else {
        echo '<h3 class="error">✗ Failed to send email</h3>';
        echo '<p>Error: ' . error_get_last()['message'] . '</p>';
        
        // Check if Mailhog is accessible
        $mailhogTest = @file_get_contents('http://localhost:8025/api/v2/messages', false, stream_context_create([
            'http' => ['timeout' => 2]
        ]));
        
        if ($mailhogTest === false) {
            echo '<div class="warning">';
            echo '<strong>Mailhog might not be running!</strong><br>';
            echo 'Please make sure Mailhog is started:<br>';
            echo '1. Navigate to C:\wamp64\mailhog\<br>';
            echo '2. Run start_mailhog.bat';
            echo '</div>';
        }
    }
    ?>
    
    <div class="info">
        <h3>What to look for in Mailhog:</h3>
        <ul>
            <li>The email should appear in the inbox</li>
            <li>Look for the 📎 (paperclip) icon indicating an attachment</li>
            <li>Click on the email to view details</li>
            <li>You should see "1 attachment" or similar</li>
            <li>The attachment should be named "TEST-INVOICE-001.pdf"</li>
            <li>You should be able to download and open the PDF</li>
        </ul>
    </div>
    
    <h3>Alternative Test Method:</h3>
    <p>If the attachment still doesn't appear, try this command in Windows command prompt:</p>
    <pre>cd C:\wamp64\www\fit
C:\wamp64\bin\php\php8.2.28\php.exe -r "ini_set('SMTP','localhost');ini_set('smtp_port','1025');mail('<EMAIL>','Test','Test message','From: <EMAIL>');"</pre>
    
    <details>
        <summary>Debug Information</summary>
        <h4>PHP Configuration:</h4>
        <pre>
SMTP: <?php echo ini_get('SMTP'); ?>

smtp_port: <?php echo ini_get('smtp_port'); ?>

sendmail_from: <?php echo ini_get('sendmail_from'); ?>

PHP Version: <?php echo phpversion(); ?>
</pre>
        <h4>Email Headers:</h4>
        <pre><?php echo htmlspecialchars($headers); ?></pre>
    </details>
</body>
</html>