<?php
/**
 * Debug PDF Generation Issue
 * This script helps identify why PDFs are only 3KB
 */

// Load environment and bootstrap
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\EmailService;

$invoiceId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug PDF Generation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; border: 1px solid #ddd; border-radius: 4px; }
        .debug-section { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 4px;
        }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Debug PDF Generation</h1>
    
    <?php
    if ($invoiceId) {
        echo "<h2>Testing PDF Generation for Invoice ID: $invoiceId</h2>";
        
        // 1. Test Direct PDF Generation from invoice-pdf.php
        echo "<div class='debug-section'>";
        echo "<h3>1. Direct PDF Generation (invoice-pdf.php)</h3>";
        
        try {
            // Test if we can access the invoice
            $invoiceModel = new Invoice();
            $invoiceData = $invoiceModel->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                echo "<p class='error'>Invoice not found!</p>";
                exit;
            }
            
            echo "<p class='success'>✓ Invoice found: {$invoiceData['invoice_number']}</p>";
            
            // Try to generate PDF directly
            $_GET['id'] = $invoiceId;
            $_GET['action'] = 'string';
            
            ob_start();
            $errorOutput = '';
            
            // Capture any errors
            set_error_handler(function($errno, $errstr, $errfile, $errline) use (&$errorOutput) {
                $errorOutput .= "Error [$errno]: $errstr in $errfile on line $errline\n";
                return true;
            });
            
            // Try to include the PDF script
            $scriptPath = __DIR__ . '/public/invoice-pdf.php';
            
            if (!file_exists($scriptPath)) {
                echo "<p class='error'>✗ invoice-pdf.php not found at: $scriptPath</p>";
            } else {
                echo "<p class='success'>✓ invoice-pdf.php found</p>";
                
                // Include it
                include $scriptPath;
            }
            
            restore_error_handler();
            
            $pdfContent = ob_get_clean();
            
            if ($errorOutput) {
                echo "<p class='error'>Errors during PDF generation:</p>";
                echo "<pre>" . htmlspecialchars($errorOutput) . "</pre>";
            }
            
            if ($pdfContent) {
                $pdfSize = strlen($pdfContent);
                echo "<p>PDF Size: <strong>" . number_format($pdfSize) . " bytes (" . number_format($pdfSize/1024, 2) . " KB)</strong></p>";
                
                // Check if it's a valid PDF
                if (substr($pdfContent, 0, 4) === '%PDF') {
                    echo "<p class='success'>✓ Valid PDF header detected</p>";
                    
                    // Save to temp file for inspection
                    $tempFile = tempnam(sys_get_temp_dir(), 'debug_pdf_');
                    file_put_contents($tempFile, $pdfContent);
                    echo "<p>PDF saved to: <code>$tempFile</code></p>";
                    
                    // Show first 500 chars
                    echo "<p>First 500 characters of PDF:</p>";
                    echo "<pre>" . htmlspecialchars(substr($pdfContent, 0, 500)) . "</pre>";
                    
                    // Look for common PDF issues
                    if ($pdfSize < 5000) { // Less than 5KB is suspicious
                        echo "<p class='warning'>⚠ PDF is very small, might be incomplete</p>";
                        
                        // Show last 500 chars to see if it ends properly
                        echo "<p>Last 500 characters of PDF:</p>";
                        echo "<pre>" . htmlspecialchars(substr($pdfContent, -500)) . "</pre>";
                    }
                } else {
                    echo "<p class='error'>✗ Not a valid PDF (doesn't start with %PDF)</p>";
                    echo "<p>Content starts with:</p>";
                    echo "<pre>" . htmlspecialchars(substr($pdfContent, 0, 200)) . "</pre>";
                }
            } else {
                echo "<p class='error'>✗ No PDF content generated</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        }
        echo "</div>";
        
        // 2. Test EmailService PDF Generation Method
        echo "<div class='debug-section'>";
        echo "<h3>2. EmailService PDF Generation Method</h3>";
        
        try {
            // Create a test instance of EmailService
            $emailService = new EmailService();
            
            // Use reflection to call the private method
            $reflection = new ReflectionClass($emailService);
            $method = $reflection->getMethod('generateInvoicePdfFromDownloadScript');
            $method->setAccessible(true);
            
            echo "<p>Calling generateInvoicePdfFromDownloadScript($invoiceId)...</p>";
            
            $pdfContent = $method->invoke($emailService, $invoiceId);
            
            if ($pdfContent) {
                $pdfSize = strlen($pdfContent);
                echo "<p>PDF Size: <strong>" . number_format($pdfSize) . " bytes (" . number_format($pdfSize/1024, 2) . " KB)</strong></p>";
                
                if (substr($pdfContent, 0, 4) === '%PDF') {
                    echo "<p class='success'>✓ Valid PDF generated</p>";
                } else {
                    echo "<p class='error'>✗ Invalid PDF content</p>";
                }
            } else {
                echo "<p class='error'>✗ No PDF content returned</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        }
        echo "</div>";
        
        // 3. Check PHP Error Log
        echo "<div class='debug-section'>";
        echo "<h3>3. Recent PHP Errors (PDF-related)</h3>";
        
        $errorLog = '/mnt/c/wamp64/logs/php_error.log';
        if (file_exists($errorLog)) {
            $lines = file($errorLog);
            $pdfErrors = [];
            
            foreach ($lines as $line) {
                if (stripos($line, 'pdf') !== false || stripos($line, 'tcpdf') !== false || stripos($line, 'invoice-pdf') !== false) {
                    $pdfErrors[] = trim($line);
                }
            }
            
            if (!empty($pdfErrors)) {
                $recent = array_slice($pdfErrors, -10);
                echo "<pre>";
                foreach ($recent as $error) {
                    echo htmlspecialchars($error) . "\n";
                }
                echo "</pre>";
            } else {
                echo "<p class='info'>No recent PDF-related errors found</p>";
            }
        } else {
            echo "<p class='warning'>Cannot read PHP error log</p>";
        }
        echo "</div>";
        
        // 4. Direct Download Link
        echo "<div class='debug-section'>";
        echo "<h3>4. Test Direct Download</h3>";
        echo "<p>Try downloading the PDF directly to see if it works:</p>";
        echo "<p><a href='" . Flight::get('flight.base_url') . "/invoices/$invoiceId/download' target='_blank'>Download Invoice PDF</a></p>";
        echo "</div>";
        
    } else {
        // Show invoice list
        echo "<h2>Select an Invoice to Debug</h2>";
        
        $db = Flight::db();
        $stmt = $db->query("
            SELECT i.id, i.invoice_number, i.status, i.total
            FROM invoices i
            ORDER BY i.created_at DESC
            LIMIT 20
        ");
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Status</th><th>Total</th><th>Action</th></tr>";
        
        foreach ($invoices as $inv) {
            echo "<tr>";
            echo "<td>{$inv['id']}</td>";
            echo "<td>{$inv['invoice_number']}</td>";
            echo "<td>{$inv['status']}</td>";
            echo "<td>" . number_format($inv['total'], 2) . "</td>";
            echo "<td><a href='?id={$inv['id']}'>Debug PDF</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    ?>
</body>
</html>