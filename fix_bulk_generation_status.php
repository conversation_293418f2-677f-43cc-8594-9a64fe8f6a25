<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Fix Bulk Generation Status Issue</h2>\n";
    
    // Check user_generated_invoices for July 2025 RET invoices
    echo "<h3>1. Checking user_generated_invoices table:</h3>\n";
    $stmt = $pdo->prepare("
        SELECT ugi.*, i.invoice_number, i.id as actual_invoice_id
        FROM user_generated_invoices ugi
        LEFT JOIN invoices i ON ugi.invoice_id = i.id
        WHERE ugi.period_month = :month 
        AND ugi.period_year = :year
        AND ugi.invoice_type = 'RET'
        ORDER BY ugi.user_id
    ");
    $stmt->execute(['month' => 7, 'year' => 2025]);
    
    $results = $stmt->fetchAll();
    if (count($results) > 0) {
        echo "<table border='1'>\n";
        echo "<tr><th>User ID</th><th>Invoice ID</th><th>Invoice Exists?</th><th>Invoice Number</th><th>Action</th></tr>\n";
        
        foreach ($results as $row) {
            echo "<tr>";
            echo "<td>{$row['user_id']}</td>";
            echo "<td>{$row['invoice_id']}</td>";
            echo "<td>" . ($row['actual_invoice_id'] ? 'Yes' : '<span style="color:red">No (Orphaned)</span>') . "</td>";
            echo "<td>" . ($row['invoice_number'] ?: '-') . "</td>";
            echo "<td>";
            if (!$row['actual_invoice_id']) {
                echo "<a href='?action=clean_orphan&id={$row['id']}'>Remove orphaned entry</a>";
            }
            echo "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>No entries found for July 2025 RET invoices.</p>\n";
    }
    
    // Check the query used by bulk generation view
    echo "\n<h3>2. Checking bulk generation query logic:</h3>\n";
    echo "<p>The bulk generation view is checking for existing invoices with this query:</p>\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            u.id as user_id,
            u.first_name,
            u.last_name,
            u.email,
            uma.cns_amount,
            uma.patient_amount,
            (uma.cns_amount + uma.patient_amount) as total_amount,
            ugi.invoice_id,
            i.invoice_number
        FROM users u
        INNER JOIN user_monthly_retrocession_amounts uma ON u.id = uma.user_id
        LEFT JOIN user_generated_invoices ugi ON (
            ugi.user_id = u.id 
            AND ugi.period_month = :month 
            AND ugi.period_year = :year 
            AND ugi.invoice_type = 'RET'
        )
        LEFT JOIN invoices i ON ugi.invoice_id = i.id
        WHERE uma.month = :data_month 
        AND uma.year = :data_year
        AND (uma.cns_amount > 0 OR uma.patient_amount > 0)
        ORDER BY u.first_name, u.last_name
    ");
    
    // For July 2025 invoices, we need June 2025 data
    $stmt->execute([
        'month' => 7,        // Invoice month
        'year' => 2025,      // Invoice year
        'data_month' => 6,   // Data month (previous)
        'data_year' => 2025  // Data year
    ]);
    
    echo "<h4>Users with June 2025 data (for July invoicing):</h4>\n";
    echo "<table border='1'>\n";
    echo "<tr><th>User</th><th>June CNS</th><th>June Patient</th><th>July Invoice Status</th></tr>\n";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>{$row['first_name']} {$row['last_name']}</td>";
        echo "<td>{$row['cns_amount']}€</td>";
        echo "<td>{$row['patient_amount']}€</td>";
        echo "<td>";
        if ($row['invoice_id']) {
            echo "Invoice ID: {$row['invoice_id']}";
            if ($row['invoice_number']) {
                echo " ({$row['invoice_number']})";
            } else {
                echo " <span style='color:red'>(No invoice found)</span>";
            }
        } else {
            echo "<span style='color:green'>Ready to generate</span>";
        }
        echo "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Handle cleanup actions
    if (isset($_GET['action']) && $_GET['action'] == 'clean_orphan' && isset($_GET['id'])) {
        $id = intval($_GET['id']);
        $pdo->exec("DELETE FROM user_generated_invoices WHERE id = $id");
        echo "\n<p style='color:green'>✓ Removed orphaned entry</p>\n";
        echo "<p><a href='fix_bulk_generation_status.php'>Refresh</a></p>\n";
    }
    
    // Clean all orphans option
    echo "\n<h3>3. Cleanup Options:</h3>\n";
    echo "<p><a href='?action=clean_all_orphans'>Remove ALL orphaned entries</a></p>\n";
    
    if (isset($_GET['action']) && $_GET['action'] == 'clean_all_orphans') {
        $stmt = $pdo->exec("
            DELETE ugi FROM user_generated_invoices ugi
            LEFT JOIN invoices i ON ugi.invoice_id = i.id
            WHERE i.id IS NULL
        ");
        echo "<p style='color:green'>✓ Cleaned all orphaned entries</p>\n";
        echo "<p><a href='fix_bulk_generation_status.php'>Refresh</a></p>\n";
    }
    
    echo "\n<hr>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Clean any orphaned entries above</li>\n";
    echo "<li>Make sure users have June 2025 data configured</li>\n";
    echo "<li><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation'>Return to Bulk Generation</a></li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}