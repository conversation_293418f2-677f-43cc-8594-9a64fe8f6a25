<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use Flight;

$db = Flight::db();

echo "Checking email templates...\n\n";

// Check if email_templates table exists
try {
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() > 0) {
        echo "✓ email_templates table exists\n\n";
        
        // Count templates
        $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total templates: " . $result['count'] . "\n\n";
        
        // List active templates
        $stmt = $db->query("SELECT id, name, code, email_type, invoice_type, is_active FROM email_templates WHERE is_active = 1");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($templates) > 0) {
            echo "Active templates:\n";
            foreach ($templates as $template) {
                echo "- ID: {$template['id']}, Name: {$template['name']}, Code: {$template['code']}, Type: {$template['email_type']}, Invoice Type: {$template['invoice_type']}\n";
            }
        } else {
            echo "✗ No active templates found!\n";
            echo "This is why emails are not being sent - no templates configured.\n";
        }
        
    } else {
        echo "✗ email_templates table does NOT exist!\n";
        echo "This is why emails are not being sent.\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nChecking email_logs table...\n";
try {
    $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
    if ($stmt->rowCount() > 0) {
        echo "✓ email_logs table exists\n";
    } else {
        echo "✗ email_logs table does NOT exist\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}