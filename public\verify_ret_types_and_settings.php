<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Verifying RET25/RET30 Types and Settings</h2>";
    
    // Check invoice_types table
    echo "<h3>Invoice Types Table:</h3>";
    $stmt = $db->query("SELECT * FROM invoice_types WHERE code IN ('RET', 'RET25', 'RET30') ORDER BY code");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($types)) {
        echo "<p style='color: red;'>No RET types found in invoice_types table!</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>";
        foreach ($types as $type) {
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>" . ($type['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check config_invoice_types table
    echo "<h3>Config Invoice Types Table:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE prefix IN ('RET', 'RET25', 'RET30') ORDER BY prefix");
    $configTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($configTypes)) {
        echo "<p style='color: red;'>No RET types found in config_invoice_types table!</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Color</th><th>Active</th></tr>";
        foreach ($configTypes as $type) {
            $name = json_decode($type['name'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $type['name']) : $type['name'];
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>{$type['prefix']}</td>";
            echo "<td>{$displayName}</td>";
            echo "<td style='background-color: {$type['color']}; color: white;'>{$type['color']}</td>";
            echo "<td>" . ($type['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check config table for ret_invoice_type
    echo "<h3>Config Setting:</h3>";
    $stmt = $db->prepare("SELECT * FROM config WHERE `key` = 'ret_invoice_type'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        echo "<p>ret_invoice_type = <strong>{$config['value']}</strong></p>";
    } else {
        echo "<p style='color: orange;'>No ret_invoice_type config found</p>";
    }
    
    // Check Frank's secretary settings
    echo "<h3>Frank Huet's Secretary Settings:</h3>";
    $stmt = $db->prepare("
        SELECT u.id, u.first_name, u.last_name, urs.secretary_value, urs.secretary_type 
        FROM users u
        LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
        WHERE u.email = '<EMAIL>'
        ORDER BY urs.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $frank = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($frank) {
        echo "<p>User: {$frank['first_name']} {$frank['last_name']} (ID: {$frank['id']})</p>";
        if ($frank['secretary_value'] !== null) {
            echo "<p>Secretary Fee: <strong>{$frank['secretary_value']}" . 
                 ($frank['secretary_type'] == 'percentage' ? '%' : '€') . "</strong></p>";
            echo "<p>Expected Invoice Type: <strong>";
            if ($frank['secretary_value'] == 5) {
                echo "RET25";
            } elseif ($frank['secretary_value'] == 10) {
                echo "RET30";
            } else {
                echo "RET (default)";
            }
            echo "</strong></p>";
        } else {
            echo "<p style='color: orange;'>No custom secretary settings (will use default 10% → RET30)</p>";
        }
    }
    
    // Create missing types if needed
    $missingTypes = [];
    
    // Check invoice_types
    $stmt = $db->prepare("SELECT code FROM invoice_types WHERE code = 'RET25'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $missingTypes[] = ['table' => 'invoice_types', 'code' => 'RET25', 'name' => 'Rétrocession 5%'];
    }
    
    $stmt = $db->prepare("SELECT code FROM invoice_types WHERE code = 'RET30'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $missingTypes[] = ['table' => 'invoice_types', 'code' => 'RET30', 'name' => 'Rétrocession 10%'];
    }
    
    if (!empty($missingTypes)) {
        echo "<h3 style='color: orange;'>Creating Missing Types:</h3>";
        foreach ($missingTypes as $missing) {
            if ($missing['table'] == 'invoice_types') {
                $stmt = $db->prepare("
                    INSERT INTO invoice_types (code, name, is_active) 
                    VALUES (:code, :name, 1)
                ");
                $stmt->execute(['code' => $missing['code'], 'name' => $missing['name']]);
                echo "<p style='color: green;'>✓ Created {$missing['code']} in invoice_types</p>";
            }
        }
    }
    
    echo "<hr>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>The system will now automatically select RET25 for 5% secretary fee</li>";
    echo "<li>And RET30 for 10% secretary fee</li>";
    echo "<li>Invoice numbers will be: FAC-RET25-2025-XXXX or FAC-RET30-2025-XXXX</li>";
    echo "</ul>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession'>Test Bulk Generation</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}