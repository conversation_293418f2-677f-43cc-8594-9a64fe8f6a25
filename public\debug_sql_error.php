<?php
/**
 * Debug SQL Error for Invoice Email
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug SQL Error</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .sql { background: #f5f5f5; padding: 10px; font-family: monospace; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug SQL Error in EmailService</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // Test the exact SQL from selectInvoiceTemplate
        echo "<h2>1. Test Template Selection SQL</h2>";
        
        $invoiceType = 'rental';
        
        $sql = "
            SELECT * FROM email_templates
            WHERE email_type = 'new_invoice'
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY invoice_type DESC, priority DESC
            LIMIT 1
        ";
        
        echo '<div class="sql">' . htmlspecialchars($sql) . '</div>';
        echo '<p>Parameter: :invoice_type = ' . htmlspecialchars($invoiceType) . '</p>';
        
        try {
            $stmt = $db->prepare($sql);
            $stmt->execute([':invoice_type' => $invoiceType]);
            $template = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($template) {
                echo '<div class="success">✅ Query executed successfully!</div>';
                echo '<pre>' . print_r($template, true) . '</pre>';
            } else {
                echo '<div class="error">❌ No template found</div>';
            }
        } catch (PDOException $e) {
            echo '<div class="error">❌ SQL Error: ' . $e->getMessage() . '</div>';
        }
        
        // Check the actual error location
        echo "<h2>2. Check EmailService Error Location</h2>";
        
        // Read the EmailService file to find where HY093 might occur
        $emailServicePath = __DIR__ . '/../app/services/EmailService.php';
        $content = file_get_contents($emailServicePath);
        
        // Find all prepare statements
        preg_match_all('/->prepare\(([^)]+)\)/s', $content, $matches);
        
        echo "<p>Found " . count($matches[0]) . " prepare statements in EmailService.php</p>";
        
        // Check logEmail method specifically
        echo "<h2>3. Test logEmail SQL</h2>";
        
        $testData = [
            ':invoice_id' => 279,
            ':template_id' => null,
            ':recipient_type' => 'primary',
            ':recipient_email' => '<EMAIL>',
            ':subject' => 'Test',
            ':body_preview' => 'Test body',
            ':attachments_sent' => null,
            ':status' => 'failed',
            ':sent_at' => null,
            ':error_message' => 'Test error'
        ];
        
        $logSql = "
            INSERT INTO email_logs (
                invoice_id, template_id, recipient_type, recipient_email,
                subject, body_preview, attachments_sent, status, 
                sent_at, error_message, created_at
            ) VALUES (
                :invoice_id, :template_id, :recipient_type, :recipient_email,
                :subject, :body_preview, :attachments_sent, :status,
                :sent_at, :error_message, NOW()
            )
        ";
        
        echo '<div class="sql">' . htmlspecialchars($logSql) . '</div>';
        echo '<p>Parameters:</p>';
        echo '<pre>' . print_r($testData, true) . '</pre>';
        
        try {
            $stmt = $db->prepare($logSql);
            $stmt->execute($testData);
            echo '<div class="success">✅ logEmail SQL works correctly</div>';
            
            // Clean up
            $db->exec("DELETE FROM email_logs WHERE subject = 'Test' AND invoice_id = 279");
        } catch (PDOException $e) {
            echo '<div class="error">❌ logEmail SQL Error: ' . $e->getMessage() . '</div>';
        }
        
        // Check other potential SQL issues
        echo "<h2>4. Check getInvoiceWithDetails</h2>";
        
        $invoiceModel = new \App\Models\Invoice();
        
        try {
            $invoiceData = $invoiceModel->getInvoiceWithDetails(279);
            echo '<div class="success">✅ getInvoiceWithDetails works</div>';
            
            // Check specific fields that might cause issues
            echo "<p>Key fields:</p>";
            echo "<ul>";
            echo "<li>invoice_type: " . ($invoiceData['invoice_type'] ?? 'NULL') . "</li>";
            echo "<li>invoice_type_code: " . ($invoiceData['invoice_type_code'] ?? 'NULL') . "</li>";
            echo "<li>Has user data: " . (isset($invoiceData['user']) ? 'YES' : 'NO') . "</li>";
            echo "<li>Has client data: " . (isset($invoiceData['client']) ? 'YES' : 'NO') . "</li>";
            echo "</ul>";
            
        } catch (Exception $e) {
            echo '<div class="error">❌ getInvoiceWithDetails Error: ' . $e->getMessage() . '</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/test_email_simple.php">Simple Email Test</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>