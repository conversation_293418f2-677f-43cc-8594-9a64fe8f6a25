<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice VAT Fix Test ✅</h2>";
    
    echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Fixes Applied:</h3>";
    echo "<ol>";
    echo "<li>✅ Improved VAT rate lookup to properly find 0% VAT</li>";
    echo "<li>✅ Added debugging for VAT rate selection</li>";
    echo "<li>✅ Fixed VAT rate ID type conversion</li>";
    echo "<li>✅ Secretary lines only appear when values > 0</li>";
    echo "</ol>";
    echo "</div>";
    
    // Check VAT rates in database
    echo "<h3>Available VAT Rates:</h3>";
    $stmt = $db->prepare("SELECT id, name, rate FROM vat_rates ORDER BY rate ASC");
    $stmt->execute();
    $vatRates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>Name</th><th>Rate</th><th>Note</th></tr>";
    foreach ($vatRates as $vat) {
        $note = '';
        if ($vat['rate'] == 0) $note = '✅ For rent & charges';
        if ($vat['rate'] == 17) $note = '✅ For secretary services';
        
        echo "<tr>";
        echo "<td>{$vat['id']}</td>";
        echo "<td>{$vat['name']}</td>";
        echo "<td>{$vat['rate']}%</td>";
        echo "<td>{$note}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check Pierre Louis financial obligations (simple case)
    echo "<h3>Pierre Louis (Simple LOY Invoice):</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_financial_obligations 
        WHERE user_id = 3 AND end_date IS NULL
        LIMIT 1
    ");
    $stmt->execute();
    $pierre = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($pierre) {
        echo "<div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px;'>";
        echo "<strong>Expected Output:</strong><br>";
        if ($pierre['rent_amount'] > 0) {
            echo "✅ Loyer mensuel: €" . number_format($pierre['rent_amount'], 2) . " (0% VAT)<br>";
        }
        if ($pierre['charges_amount'] > 0) {
            echo "✅ Charges location: €" . number_format($pierre['charges_amount'], 2) . " (0% VAT)<br>";
        }
        echo "<strong>Total: €" . number_format($pierre['rent_amount'] + $pierre['charges_amount'], 2) . "</strong><br>";
        echo "<em>No secretary lines should appear</em>";
        echo "</div>";
    }
    
    // Check Bernard Heens financial obligations (complex case)
    echo "<h3>Bernard Heens (Complex LOY Invoice with Secretary):</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_financial_obligations 
        WHERE user_id = 6 AND end_date IS NULL
        LIMIT 1
    ");
    $stmt->execute();
    $bernard = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($bernard) {
        echo "<div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px;'>";
        echo "<strong>Expected Output:</strong><br>";
        if ($bernard['rent_amount'] > 0) {
            echo "✅ Loyer mensuel: €" . number_format($bernard['rent_amount'], 2) . " (0% VAT)<br>";
        }
        if ($bernard['charges_amount'] > 0) {
            echo "✅ Charges location: €" . number_format($bernard['charges_amount'], 2) . " (0% VAT)<br>";
        }
        if ($bernard['secretary_tvac_17'] > 0) {
            echo "✅ Secrétariat TVAC 17%: €" . number_format($bernard['secretary_tvac_17'], 2) . " (17% VAT)<br>";
        }
        if ($bernard['secretary_htva'] > 0) {
            echo "✅ Secrétariat HTVA: €" . number_format($bernard['secretary_htva'], 2) . " (0% VAT)<br>";
        }
        if ($bernard['tva_17'] > 0) {
            echo "✅ TVA 17%: €" . number_format($bernard['tva_17'], 2) . " (17% VAT)<br>";
        }
        echo "</div>";
    }
    
    echo "<h3>JavaScript Debug Instructions:</h3>";
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px;'>";
    echo "<p>When creating a LOY invoice, check the browser console for:</p>";
    echo "<ol>";
    echo "<li>\"Available VAT rates:\" - Shows all VAT rates loaded</li>";
    echo "<li>\"Found 0% VAT rate with ID:\" - Confirms 0% VAT was found</li>";
    echo "<li>\"Adding rent line: X with VAT ID: Y\" - Shows VAT ID being used</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>Testing Steps:</h3>";
    echo "<ol>";
    echo "<li>Clear browser cache (Ctrl+F5)</li>";
    echo "<li>Go to: <a href='/fit/public/invoices/create?type=loyer' target='_blank'>/invoices/create?type=loyer</a></li>";
    echo "<li>Select <strong>Pierre Louis</strong> - Should show only rent + charges with 0% VAT</li>";
    echo "<li>Select <strong>Bernard Heens</strong> - Should show all lines with correct VAT rates</li>";
    echo "<li>Check browser console for VAT debugging messages</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<script>
console.log('LOY VAT fix test page loaded');
</script>