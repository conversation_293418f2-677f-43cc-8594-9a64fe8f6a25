{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.invoice') }} {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.invoice') }} {{ invoice.invoice_number }}</h1>
            <div class="mt-2">
                {% if invoice.status == 'draft' %}
                    <span class="badge bg-secondary">{{ __('invoices.status.draft') }}</span>
                {% elseif invoice.status == 'sent' %}
                    <span class="badge bg-info">{{ __('invoices.status.sent') }}</span>
                {% elseif invoice.status == 'paid' %}
                    <span class="badge bg-success">{{ __('invoices.status.paid') }}</span>
                {% elseif invoice.status == 'partial' %}
                    <span class="badge bg-warning">{{ __('invoices.status.partial') }}</span>
                {% elseif invoice.status == 'overdue' %}
                    <span class="badge bg-danger">{{ __('invoices.status.overdue') }}</span>
                {% elseif invoice.status == 'cancelled' %}
                    <span class="badge bg-dark">{{ __('invoices.status.cancelled') }}</span>
                {% endif %}
            </div>
        </div>
        <div class="d-flex gap-2">
            {% if invoice.status == 'draft' %}
                <a href="{{ base_url }}/invoices/{{ invoice.id }}/edit" class="btn btn-primary">
                    <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                </a>
                <button type="button" class="btn btn-success" onclick="markAsSent()">
                    <i class="bi bi-send me-2"></i>{{ __('invoices.mark_as_sent')|default('Marquer comme envoyée') }}
                </button>
            {% endif %}
            <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" 
                        data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical me-2"></i>{{ __('common.actions') }}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank">
                            <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/download">
                            <i class="bi bi-download me-2"></i>{{ __('invoices.download_pdf') }}
                        </a>
                    </li>
                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="sendInvoice()">
                            <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_invoice') }}
                        </a>
                    </li>
                    {% endif %}
                    {% if invoice.status == 'sent' or invoice.status == 'partial' or invoice.status == 'overdue' %}
                    <li>
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#paymentModal">
                            <i class="bi bi-cash me-2"></i>{{ __('invoices.record_payment') }}
                        </a>
                    </li>
                    {% endif %}
                    {% if invoice.status in ['sent', 'paid', 'partial'] %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/credit-note">
                            <i class="bi bi-file-earmark-minus me-2"></i>{{ __('invoices.create_credit_note') }}
                        </a>
                    </li>
                    {% endif %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="event.preventDefault(); createTemplateFromInvoice({{ invoice.id }}); return false;">
                            <i class="bi bi-file-earmark-text me-2"></i>{{ __('config.create_template_from_invoice')|default('Créer un modèle à partir de cette facture') }}
                        </a>
                    </li>
                    {% if invoice.status == 'draft' %}
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/create?duplicate_id={{ invoice.id }}">
                            <i class="bi bi-copy me-2"></i>{{ __('common.duplicate')|default('Dupliquer') }}
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="confirmCancel()">
                            <i class="bi bi-x-circle me-2"></i>{{ __('invoices.cancel_invoice') }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
            <a href="{{ base_url }}/invoices" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Invoice Preview -->
            <div class="card shadow-sm mb-4">
                <div class="card-body p-5">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            {% if company_logo %}
                                <img src="{{ base_url }}/{{ company_logo }}" alt="{{ company_name }}" style="max-height: 80px;">
                            {% else %}
                                <h2 class="text-primary mb-0">{{ company_name }}</h2>
                            {% endif %}
                            <div class="mt-3">
                                {% if company_address %}
                                    <p class="mb-1">{{ company_address }}</p>
                                {% endif %}
                                {% if company_city or company_postal_code %}
                                    <p class="mb-1">
                                        {% if company_city and company_postal_code %}
                                            {{ company_city }}, {{ company_postal_code }}
                                        {% else %}
                                            {{ company_city }}{{ company_postal_code }}
                                        {% endif %}
                                    </p>
                                {% endif %}
                                {% if company_country %}
                                    <p class="mb-1">{{ company_country }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-sm-6 text-sm-end">
                            <h3 class="text-uppercase text-muted">{{ __('invoices.invoice') }}</h3>
                            <h4 class="mb-3">{{ invoice.invoice_number }}</h4>
                            
                            <dl class="row mb-0">
                                <dt class="col-6 text-muted">{{ __('invoices.issue_date') }}:</dt>
                                <dd class="col-6">{{ invoice.issue_date|date('d/m/Y') }}</dd>
                                
                                {% if invoice.due_date and invoice.due_date != '0000-00-00' and invoice.due_date|date('Y') > 0 %}
                                <dt class="col-6 text-muted">{{ __('invoices.due_date') }}:</dt>
                                <dd class="col-6">
                                    {{ invoice.due_date|date('d/m/Y') }}
                                    {% if invoice.is_overdue %}
                                        <i class="bi bi-exclamation-circle text-danger ms-1"></i>
                                    {% endif %}
                                </dd>
                                {% endif %}
                                
                                {% if invoice.period %}
                                <dt class="col-6 text-muted">{{ __('invoices.period')|default('Période') }}:</dt>
                                <dd class="col-6">{{ invoice.period }}</dd>
                                {% endif %}
                                
                                {% if invoice.subject %}
                                <dt class="col-6 text-muted">{{ __('invoices.subject')|default('Objet') }}:</dt>
                                <dd class="col-6">{{ invoice.subject }}</dd>
                                {% endif %}
                            </dl>
                        </div>
                    </div>

                    <!-- Bill To -->
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <h6 class="text-muted mb-2">{{ __('invoices.from') }}:</h6>
                            <p class="mb-0">
                                <strong>{{ company_name }}</strong><br>
                                {% if company_vat_number %}
                                    {{ __('config.vat_number') }}: {{ company_vat_number }}<br>
                                {% endif %}
                                {% if company_phone %}
                                    {{ __('common.phone') }}: {{ company_phone }}<br>
                                {% endif %}
                                {% if company_email %}
                                    {{ __('common.email') }}: {{ company_email }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-sm-6">
                            <h6 class="text-muted mb-2">{{ __('invoices.bill_to') }}:</h6>
                            <div class="mb-0">
                                {% if invoice.patient_id and invoice.patient %}
                                    {# Patient invoice #}
                                    <strong>{{ invoice.patient.first_name }} {{ invoice.patient.last_name }}</strong><br>
                                    {% if invoice.patient.address or invoice.patient.address_line1 %}
                                        {{ invoice.patient.address|default(invoice.patient.address_line1) }}<br>
                                    {% endif %}
                                    {% if invoice.patient.address_line2 %}
                                        {{ invoice.patient.address_line2 }}<br>
                                    {% endif %}
                                    {% if invoice.patient.postal_code or invoice.patient.city %}
                                        {{ invoice.patient.postal_code }} {{ invoice.patient.city }}<br>
                                    {% endif %}
                                    {% if invoice.patient.country %}
                                        {{ invoice.patient.country }}<br>
                                    {% endif %}
                                {% elseif invoice.client %}
                                    {# Client invoice #}
                                    <strong>
                                        {% if invoice.client.company_name %}
                                            {{ invoice.client.company_name }}<br>
                                        {% endif %}
                                        {{ invoice.client.first_name }} {{ invoice.client.last_name }}
                                    </strong><br>
                                    {% if invoice.client.address or invoice.client.address_line1 %}
                                        {{ invoice.client.address|default(invoice.client.address_line1) }}<br>
                                        {% if invoice.client.address_line2 %}
                                            {{ invoice.client.address_line2 }}<br>
                                        {% endif %}
                                        {{ invoice.client.postal_code }} {{ invoice.client.city }}<br>
                                        {% if invoice.client.country %}{{ invoice.client.country }}{% endif %}
                                    {% endif %}
                                {% elseif invoice.user %}
                                    {# User invoice (internal) #}
                                    <strong>
                                        {% if invoice.user.company_name %}
                                            {{ invoice.user.company_name }}<br>
                                        {% endif %}
                                        {{ invoice.user.first_name }} {{ invoice.user.last_name }}
                                    </strong><br>
                                    {% if invoice.user.address or invoice.user.billing_address %}
                                        {{ invoice.user.address|default(invoice.user.billing_address) }}<br>
                                        {% if invoice.user.billing_postal_code and invoice.user.billing_city %}
                                            {{ invoice.user.billing_postal_code }} {{ invoice.user.billing_city }}<br>
                                        {% elseif invoice.user.postal_code and invoice.user.city %}
                                            {{ invoice.user.postal_code }} {{ invoice.user.city }}<br>
                                        {% endif %}
                                        {% if invoice.user.billing_country %}
                                            {{ invoice.user.billing_country }}
                                        {% elseif invoice.user.country %}
                                            {{ invoice.user.country }}
                                        {% endif %}
                                    {% endif %}
                                {% else %}
                                    {# Fallback - try direct invoice fields #}
                                    {% if invoice.first_name or invoice.last_name %}
                                        <strong>{{ invoice.first_name }} {{ invoice.last_name }}</strong><br>
                                    {% endif %}
                                    {% if invoice.address %}
                                        {{ invoice.address }}<br>
                                    {% endif %}
                                    {% if invoice.postal_code or invoice.city %}
                                        {{ invoice.postal_code }} {{ invoice.city }}<br>
                                    {% endif %}
                                    {% if invoice.country %}
                                        {{ invoice.country }}
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">{{ __('invoices.items') }}</h6>
                        {% if invoice.status == 'draft' %}
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openEditItemsModal()">
                                <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                            </button>
                        {% endif %}
                    </div>
                    
                    {# Check if this is a retrocession invoice - check multiple ways #}
                    {% set invoiceTypeCode = invoice.invoice_type|default('') %}
                    {% set invoiceNumber = invoice.invoice_number|default('') %}
                    {% set isRetrocession = (invoiceTypeCode in ['retrocession_30', 'retrocession_25']) or 
                                           (invoiceNumber matches '/RET30|RET25|FAC-RET/') %}
                    
                    <div class="table-responsive mb-4">
                        <table class="table">
                            <thead>
                                <tr>
                                    {% if isRetrocession %}
                                        {# Retrocession columns: OBJET | Montant Base | TOTAL | HTVA | % TVA #}
                                        <th width="45%">{{ __('invoices.object')|default('OBJET') }}</th>
                                        <th width="15%" class="text-center">{{ __('invoices.base_amount')|default('Montant Base') }}</th>
                                        <th width="15%" class="text-end">{{ __('invoices.total')|default('TOTAL') }}</th>
                                        <th width="15%" class="text-end">{{ __('invoices.amount_excl_vat')|default('HTVA') }}</th>
                                        <th width="10%" class="text-center">{{ __('invoices.vat_rate')|default('% TVA') }}</th>
                                    {% else %}
                                        {# Standard invoice columns #}
                                        <th>{{ __('invoices.description') }}</th>
                                        <th class="text-center">{{ __('invoices.quantity') }}</th>
                                        <th class="text-end">{{ __('invoices.unit_price') }}</th>
                                        <th class="text-center">{{ __('invoices.vat_rate') }}</th>
                                        <th class="text-end">{{ __('invoices.total') }}</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.lines %}
                                {# Special styling for secretary line #}
                                {% set isSecretaryLine = item.description starts with 'FRAIS SECRÉTARIAT' %}
                                {% set rowBgColor = isSecretaryLine ? '#e8e8e8' : (loop.index0 % 2 == 1 ? '#f9f9f9' : '') %}
                                <tr {% if rowBgColor %}style="background-color: {{ rowBgColor }};"{% endif %}>
                                    {% if isRetrocession %}
                                        {# Retrocession line display - OBJET first, then base amount #}
                                        <td>{{ item.description }}</td>
                                        {# Base amount calculation #}
                                        {% if item.description starts with 'RÉTROCESSION CNS' %}
                                            {# CNS base = unit_price / 0.20 #}
                                            <td class="text-center">€{{ (item.unit_price / 0.20)|number_format(2, ',', '.') }}</td>
                                        {% elseif item.description starts with 'RÉTROCESSION PATIENTS' %}
                                            {# Patients base = unit_price / 0.20 #}
                                            <td class="text-center">€{{ (item.unit_price / 0.20)|number_format(2, ',', '.') }}</td>
                                        {% elseif item.description starts with 'FRAIS SECRÉTARIAT' %}
                                            {# Secretary shows the total of CNS + Patients base amounts #}
                                            {% set cns_base = 0 %}
                                            {% set patient_base = 0 %}
                                            {% for other_item in invoice.lines %}
                                                {% if other_item.description starts with 'RÉTROCESSION CNS' %}
                                                    {% set cns_base = other_item.unit_price / 0.20 %}
                                                {% elseif other_item.description starts with 'RÉTROCESSION PATIENTS' %}
                                                    {% set patient_base = other_item.unit_price / 0.20 %}
                                                {% endif %}
                                            {% endfor %}
                                            <td class="text-center" style="font-weight: 500;">€{{ (cns_base + patient_base)|number_format(2, ',', '.') }}</td>
                                        {% else %}
                                            <td class="text-center">-</td>
                                        {% endif %}
                                        <td class="text-end">{{ (item.quantity * item.unit_price * (1 + item.vat_rate / 100))|number_format(2, ',', '.') }} €</td>
                                        <td class="text-end">{{ (item.quantity * item.unit_price)|number_format(2, ',', '.') }} €</td>
                                        <td class="text-center">{{ item.vat_rate|number_format(0) }} %</td>
                                    {% else %}
                                        {# Standard line display #}
                                        <td>{{ item.description }}</td>
                                        <td class="text-center">{{ item.quantity }}</td>
                                        <td class="text-end">{{ item.unit_price|number_format(2, ',', '.') }} €</td>
                                        <td class="text-center">{{ item.vat_rate }} %</td>
                                        <td class="text-end">{{ (item.quantity * item.unit_price * (1 + item.vat_rate / 100))|number_format(2, ',', '.') }} €</td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                    <td class="text-end">{{ invoice.subtotal|number_format(2, ',', '.') }} €</td>
                                </tr>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                    <td class="text-end">{{ invoice.vat_amount|number_format(2, ',', '.') }} €</td>
                                </tr>
                                {% if invoice.cns_base_amount > 0 %}
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.cns_base_amount') | default('CNS/Patient Amount') }}:</strong></td>
                                    <td class="text-end">{{ invoice.cns_base_amount|number_format(2, ',', '.') }} €</td>
                                </tr>
                                {% endif %}
                                {% if invoice.secretariat_vat_amount > 0 %}
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.secretary_fee') | default('Secretary Fee') }}:</strong></td>
                                    <td class="text-end">{{ invoice.secretariat_vat_amount|number_format(2, ',', '.') }} €</td>
                                </tr>
                                {% endif %}
                                {% if invoice.discount_amount > 0 %}
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.discount') }}:</strong></td>
                                    <td class="text-end text-success">-{{ invoice.discount_amount|number_format(2, ',', '.') }} €</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td colspan="4" class="text-end"><h5 class="mb-0">{{ __('invoices.total') }}:</h5></td>
                                    <td class="text-end"><h5 class="mb-0 text-primary">{{ invoice.total|number_format(2, ',', '.') }} €</h5></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Notes -->
                    {% if invoice.notes %}
                    <div class="mb-4">
                        <h6 class="text-muted">{{ __('invoices.notes') }}:</h6>
                        <p>{{ invoice.notes|nl2br }}</p>
                    </div>
                    {% endif %}

                    <!-- Payment Terms -->
                    {% if invoice.payment_terms %}
                    <div class="mb-4">
                        <h6 class="text-muted">{{ __('invoices.payment_terms') }}:</h6>
                        <p>{{ invoice.payment_terms }}</p>
                    </div>
                    {% endif %}

                    <!-- Footer removed - no thank you message -->
                </div>
            </div>

            <!-- Internal Notes (if any) -->
            {% if invoice.internal_notes %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="bi bi-sticky-fill me-2"></i>{{ __('invoices.internal_notes') }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ invoice.internal_notes|nl2br }}</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Payment Status -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-cash-stack me-2"></i>{{ __('invoices.payment_status') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-6">{{ __('common.status') }}:</dt>
                        <dd class="col-6 text-end">
                            {% if invoice.status == 'draft' %}
                                <span class="badge bg-secondary">{{ __('invoices.status.draft') }}</span>
                            {% elseif invoice.status == 'sent' %}
                                <span class="badge bg-info">{{ __('invoices.status.sent') }}</span>
                            {% elseif invoice.status == 'paid' %}
                                <span class="badge bg-success">{{ __('invoices.status.paid') }}</span>
                            {% elseif invoice.status == 'partial' %}
                                <span class="badge bg-warning">{{ __('invoices.status.partial') }}</span>
                            {% elseif invoice.status == 'overdue' %}
                                <span class="badge bg-danger">{{ __('invoices.status.overdue') }}</span>
                            {% elseif invoice.status == 'cancelled' %}
                                <span class="badge bg-dark">{{ __('invoices.status.cancelled') }}</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-6">{{ __('invoices.total') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.total|number_format(2, ',', '.') }} €</dd>
                        
                        <dt class="col-6">{{ __('invoices.paid_amount') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.paid_amount|default(0)|number_format(2, ',', '.') }} €</dd>
                        
                        <dt class="col-6">{{ __('invoices.balance_due') }}:</dt>
                        <dd class="col-6 text-end">
                            {% set balance = invoice.total - invoice.paid_amount|default(0) %}
                            <strong class="{{ balance > 0 ? 'text-danger' : 'text-success' }}">
                                {{ balance|number_format(2, ',', '.') }} €
                            </strong>
                        </dd>
                    </dl>
                    
                    {% if payments|length > 0 %}
                    <hr>
                    <h6 class="mb-3">{{ __('invoices.payment_history') }}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ __('common.date') }}</th>
                                    <th>{{ __('invoices.amount') }}</th>
                                    <th>{{ __('invoices.payment_method') }}</th>
                                    <th>{{ __('invoices.reference') }}</th>
                                    <th>{{ __('common.status') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_date|date('d/m/Y') }}</td>
                                    <td class="text-nowrap">
                                        <strong class="text-success">{{ payment.amount|number_format(2, ',', '.') }} €</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ payment.payment_method_name|default(payment.payment_method_code|default('N/A')) }}
                                        </span>
                                    </td>
                                    <td>{{ payment.reference|default('-') }}</td>
                                    <td>
                                        {% if loop.last and invoice.total > invoice.paid_amount %}
                                            <span class="badge bg-warning">{{ __('invoices.status.partial') }}</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ __('invoices.payment_complete') }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if payment.notes %}
                                <tr>
                                    <td colspan="5" class="text-muted small ps-4">
                                        <i class="bi bi-chat-text me-1"></i> {{ payment.notes }}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-secondary">
                                    <td><strong>{{ __('common.total') }}</strong></td>
                                    <td colspan="4">
                                        <strong>{{ invoice.paid_amount|default(0)|number_format(2, ',', '.') }} €</strong>
                                        {% if invoice.paid_amount < invoice.total %}
                                            <span class="text-muted">/ {{ invoice.total|number_format(2, ',', '.') }} €</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>{{ __('common.quick_actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank" class="btn btn-outline-primary">
                            <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                        </a>
                        <a href="{{ base_url }}/invoices/{{ invoice.id }}/download" class="btn btn-outline-primary">
                            <i class="bi bi-download me-2"></i>{{ __('invoices.download_pdf') }}
                        </a>
                        {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                        <button type="button" class="btn btn-info" onclick="sendInvoice()">
                            <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_invoice') }}
                        </button>
                        {% endif %}
                        <button type="button" class="btn btn-outline-secondary" onclick="duplicateInvoice()">
                            <i class="bi bi-files me-2"></i>{{ __('invoices.duplicate_invoice') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.system_info') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0 small">
                        <dt class="col-6">{{ __('common.created_at') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.created_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-6">{{ __('common.updated_at') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.updated_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-6">{{ __('common.created_by') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.created_by_name|default('System') }}</dd>
                        
                        {% if invoice.sent_at %}
                        <dt class="col-6">{{ __('invoices.sent_at') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.sent_at|date('d/m/Y H:i') }}</dd>
                        {% endif %}
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{{ base_url }}/invoices/{{ invoice.id }}/payment" id="paymentForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('invoices.record_payment') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">{{ __('invoices.amount') }} *</label>
                        <div class="input-group">
                            <span class="input-group-text">{{ currency }}</span>
                            {% set balance = invoice.total - invoice.paid_amount|default(0) %}
                            <input type="number" class="form-control" id="payment_amount" name="amount" 
                                   value="{{ balance }}" min="0.01" max="{{ balance }}" 
                                   step="0.01" required>
                        </div>
                        <small class="text-muted">{{ __('invoices.balance_due') }}: {{ balance|number_format(2, ',', '.') }} €</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">{{ __('invoices.payment_date') }} *</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" 
                               value="{{ 'now'|date('Y-m-d') }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">{{ __('invoices.payment_method') }} *</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">{{ __('common.select') }}</option>
                            {% for method in paymentMethods %}
                                <option value="{{ method.code }}">{{ method.display_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_reference" class="form-label">{{ __('invoices.payment_reference') }}</label>
                        <input type="text" class="form-control" id="payment_reference" name="reference">
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">{{ __('common.notes') }}</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('invoices.record_payment') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Payment form with debug AJAX submission
document.addEventListener('DOMContentLoaded', function() {
    // Check if we should open the payment modal
    if (window.location.hash === '#payment') {
        const paymentModal = document.getElementById('paymentModal');
        if (paymentModal) {
            const modal = new bootstrap.Modal(paymentModal);
            modal.show();
            // Remove the hash to prevent reopening on refresh
            history.replaceState(null, null, window.location.pathname);
        }
    }
    
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        console.log('Payment form initialized');
        
        paymentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submission intercepted for debugging');
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show form data in console
            console.log('=== FORM DATA ===');
            for (let [key, value] of formData.entries()) {
                console.log(key + ':', value);
            }
            console.log('=================');
            
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
            
            // Submit with AJAX to get debug info
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                // First get the text to see what's actually returned
                return response.text().then(text => {
                    console.log('Raw response:', text);
                    
                    // Try to parse as JSON
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Failed to parse JSON:', e);
                        console.error('Response was:', text);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 200));
                    }
                });
            })
            .then(data => {
                console.log('=== SERVER RESPONSE ===');
                console.log(data);
                console.log('====================');
                
                if (data.debug) {
                    console.log('=== DEBUG INFO ===');
                    console.log('Invoice ID:', data.debug.invoice_id);
                    console.log('Current Total:', data.debug.invoice_current_total);
                    console.log('Current Paid:', data.debug.invoice_current_paid);
                    console.log('Payment Amount:', data.debug.payment_data?.amount);
                    console.log('Payment ID:', data.debug.payment_id);
                    console.log('Updated Totals:', data.debug.updated_totals);
                    console.log('Status Change:', data.debug.old_status, '->', data.debug.new_status);
                    console.log('Full Debug:', data.debug);
                    console.log('==================');
                }
                
                if (data.success) {
                    console.log('Payment recorded successfully, redirecting...');
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        window.location.reload();
                    }
                } else {
                    console.error('Payment failed:', data.error);
                    alert('Error: ' + data.error);
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('=== FETCH ERROR ===');
                console.error(error);
                console.error('===================');
                alert('Error submitting payment: ' + error.message);
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
});

function sendInvoice() {
    console.log('sendInvoice() called');
    
    if (confirm('{{ __("invoices.send_confirm") }}')) {
        console.log('User confirmed send');
        
        try {
            // Create a form and submit it as POST
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/{{ invoice.id }}/send';
            
            console.log('Form action:', form.action);
            
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = '{{ csrf_token }}';
            form.appendChild(csrfInput);
            
            console.log('CSRF token:', csrfInput.value || 'EMPTY!');
            
            // Ensure email is sent (this is the envelope button click)
            const sendEmailInput = document.createElement('input');
            sendEmailInput.type = 'hidden';
            sendEmailInput.name = 'send_email';
            sendEmailInput.value = 'true';
            form.appendChild(sendEmailInput);
            
            document.body.appendChild(form);
            console.log('Submitting form...');
            form.submit();
        } catch (error) {
            console.error('Error in sendInvoice:', error);
            alert('Error sending invoice: ' + error.message);
        }
    } else {
        console.log('User cancelled send');
    }
}

function duplicateInvoice() {
    window.location.href = '{{ base_url }}/invoices/create?duplicate={{ invoice.id }}';
}

function confirmCancel() {
    if (confirm('{{ __("invoices.cancel_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/invoices/{{ invoice.id }}/cancel';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function markAsSent() {
    Swal.fire({
        title: '{{ __("invoices.mark_as_sent_confirm_title")|default("Marquer comme envoyée ?") }}',
        text: '{{ __("invoices.mark_as_sent_confirm_text")|default("Cette facture sera marquée comme envoyée et ne pourra plus être modifiée.") }}',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("invoices.yes_mark_sent")|default("Oui, marquer comme envoyée") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/{{ invoice.id }}/send';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>

<!-- Edit Items Modal -->
<div class="modal fade" id="editItemsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post" action="{{ base_url }}/invoices/{{ invoice.id }}/items" id="editItemsForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('invoices.edit_items') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div id="itemsContainer">
                        <!-- Items will be loaded here -->
                    </div>
                    
                    <button type="button" class="btn btn-sm btn-success mt-3" onclick="addNewItem()">
                        <i class="bi bi-plus-circle me-1"></i>{{ __('invoices.add_item') }}
                    </button>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save_changes') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let itemIndex = 0;

function openEditItemsModal() {
    // Load existing items
    const modal = new bootstrap.Modal(document.getElementById('editItemsModal'));
    itemIndex = 0;
    
    // Clear container
    document.getElementById('itemsContainer').innerHTML = '';
    
    // Load existing items
    {% for item in invoice.lines %}
    addItemRow({
        description: {{ item.description|json_encode|raw }},
        quantity: {{ item.quantity|default(1) }},
        unit_price: {{ item.unit_price|default(0) }},
        vat_rate: {{ item.vat_rate|default(0) }}
    });
    {% endfor %}
    
    // If no items, add one empty row
    if (itemIndex === 0) {
        addNewItem();
    }
    
    modal.show();
}

function addNewItem() {
    addItemRow({
        description: '',
        quantity: 1,
        unit_price: 0,
        vat_rate: 0
    });
}

function addItemRow(item) {
    const container = document.getElementById('itemsContainer');
    const row = document.createElement('div');
    row.className = 'invoice-item-row mb-3 p-3 border rounded';
    row.id = 'item_' + itemIndex;
    
    row.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">{{ __('invoices.description') }}</label>
                <input type="text" class="form-control" name="items[${itemIndex}][description]" 
                       value="${escapeHtml(item.description)}" required>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ __('invoices.quantity') }}</label>
                <input type="number" class="form-control" name="items[${itemIndex}][quantity]" 
                       value="${item.quantity}" min="0.01" step="0.01" required onchange="calculateItemTotal(${itemIndex})">
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ __('invoices.unit_price') }}</label>
                <input type="number" class="form-control" name="items[${itemIndex}][unit_price]" 
                       value="${item.unit_price}" min="0" step="0.01" required onchange="calculateItemTotal(${itemIndex})">
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ __('invoices.vat_rate') }}</label>
                <select class="form-select" name="items[${itemIndex}][vat_rate]" onchange="calculateItemTotal(${itemIndex})">
                    <option value="0" ${item.vat_rate == 0 ? 'selected' : ''}>0%</option>
                    <option value="3" ${item.vat_rate == 3 ? 'selected' : ''}>3%</option>
                    <option value="17" ${item.vat_rate == 17 ? 'selected' : ''}>17%</option>
                </select>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-10">
                <div class="text-muted small">
                    {{ __('invoices.total') }}: <span id="item_total_${itemIndex}">0.00</span>€
                </div>
            </div>
            <div class="col-md-2 text-end">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeItem(${itemIndex})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(row);
    calculateItemTotal(itemIndex);
    itemIndex++;
}

function removeItem(index) {
    const row = document.getElementById('item_' + index);
    if (row) {
        row.remove();
    }
}

function calculateItemTotal(index) {
    const quantity = parseFloat(document.querySelector(`input[name="items[${index}][quantity]"]`).value) || 0;
    const unitPrice = parseFloat(document.querySelector(`input[name="items[${index}][unit_price]"]`).value) || 0;
    const vatRate = parseFloat(document.querySelector(`select[name="items[${index}][vat_rate]"]`).value) || 0;
    
    const subtotal = quantity * unitPrice;
    const vat = subtotal * (vatRate / 100);
    const total = subtotal + vat;
    
    document.getElementById('item_total_' + index).textContent = total.toFixed(2);
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Handle form submission
document.getElementById('editItemsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}';
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message || '{{ __("common.saved_successfully") }}');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            toastr.error(data.message || '{{ __("common.error_occurred") }}');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        toastr.error('{{ __("common.error_occurred") }}');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
</script>

<script>
// Create template from invoice - MUST be in global scope
function createTemplateFromInvoice(invoiceId) {
    console.log('Creating template from invoice:', invoiceId);
    
    if (!confirm('{{ __("config.confirm_create_template_from_invoice")|default("Create a template from this invoice?") }}')) {
        return;
    }
    
    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    console.log('CSRF Token:', csrfToken ? 'Found' : 'Not found');
    
    // Show loading toast
    toastr.info('{{ __("config.creating_template")|default("Creating template...") }}');
    
    const url = '{{ base_url }}/config/invoice-templates/create-from-invoice';
    console.log('Sending request to:', url);
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            invoice_id: invoiceId,
            csrf_token: csrfToken
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
            return response.json();
        } else {
            // If not JSON, read as text to see what we got
            return response.text().then(text => {
                console.error('Non-JSON response:', text);
                throw new Error('Server returned non-JSON response');
            });
        }
    })
    .then(data => {
        console.log('Response data:', data);
        
        if (data.success) {
            toastr.success(data.message || '{{ __("config.template_created_successfully")|default("Template created successfully") }}');
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = '{{ base_url }}' + data.redirect;
                }, 1500);
            }
        } else {
            toastr.error(data.message || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error creating template:', error);
        toastr.error('{{ __("common.error_occurred")|default("An error occurred while creating the template") }}');
    });
}
</script>
{% endblock %}