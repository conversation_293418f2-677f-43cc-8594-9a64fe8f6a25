<?php
// Debug PDF download headers
$invoiceId = $_GET['id'] ?? 246;

// Simulate filename generation
$invoiceNumber = 'FAC-LOY-2025-0186';
$filename = 'FAC-LOY-2025-0186.pdf';

// Clear any output buffers
while (ob_get_level()) {
    ob_end_clean();
}

// Create a minimal PDF for testing
require_once __DIR__ . '/../vendor/autoload.php';

$pdf = new TCPDF();
$pdf->AddPage();
$pdf->SetFont('helvetica', '', 12);
$pdf->Cell(0, 10, 'Test Invoice ' . $invoiceNumber, 0, 1);

// Get PDF content
$pdfContent = $pdf->Output('', 'S');

// Send headers
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . strlen($pdfContent));
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Output PDF
echo $pdfContent;
exit;