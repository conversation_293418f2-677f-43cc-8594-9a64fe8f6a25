<?php

return array (
  'account_details' => 'Account Details',
  'account_disabled' => 'Account disabled',
  'account_settings' => 'Account Settings',
  'action_cannot_undone' => 'This action cannot be undone.',
  'activate_account' => 'Activate Account',
  'add_group' => 'Add Group',
  'add_member' => 'Add Member',
  'add_user' => 'Add User',
  'already_member' => 'This user is already a member of the group',
  'assign_to_groups' => 'Assign to Groups',
  'available_group_icons' => 'Available Group Icons',
  'avatar' => 'Profile Picture',
  'avatar_removed' => 'Profile picture removed successfully',
  'avatar_uploaded' => 'Profile picture uploaded successfully',
  'back_to_users' => 'Back to Users',
  'change_password' => 'Change Password',
  'choose_file' => 'Choose file',
  'color' => 'Color',
  'confirm_password' => 'Confirm Password',
  'create_group' => 'Create Group',
  'create_user' => 'Create User',
  'create_users' => 'Create users',
  'current_password' => 'Current Password',
  'custom_group' => 'Custom',
  'deactivate_account' => 'Deactivate Account',
  'delete_group' => 'Delete Group',
  'delete_group_confirm' => 'Delete Group?',
  'delete_group_msg' => 'Are you sure you want to delete the group',
  'delete_user' => 'Delete User',
  'delete_user_confirm' => 'Delete User?',
  'delete_user_msg' => 'Are you sure you want to delete',
  'delete_users' => 'Delete users',
  'deleted' => 'Deleted!',
  'edit_group' => 'Edit Group',
  'edit_profile' => 'Edit Profile',
  'edit_user' => 'Edit User',
  'edit_users' => 'Edit users',
  'email_exists' => 'This email address is already in use',
  'email_hint' => 'Can also be used for login',
  'error_deleting_group' => 'An error occurred while deleting the group',
  'error_deleting_user' => 'An error occurred while deleting the user',
  'error_saving_group' => 'An error occurred while saving the group',
  'error_saving_user' => 'An error occurred while saving the user',
  'error_updating_user' => 'An error occurred while updating the user',
  'first_name' => 'First Name',
  'full_name' => 'Full Name',
  'group' => 'Group',
  'group_created' => 'Group created successfully',
  'group_deleted' => 'Group deleted successfully',
  'group_description' => 'Group Description',
  'group_description_placeholder' => 'Group description will appear here',
  'group_has_members' => 'This group has members and cannot be deleted',
  'group_information' => 'Group Information',
  'group_members' => 'Group Members',
  'group_members_removed' => 'All members will be removed from this group.',
  'group_name' => 'Group Name',
  'group_not_found' => 'Group not found',
  'group_permissions' => 'Group Permissions',
  'group_updated' => 'Group updated successfully',
  'groups' => 'Groups',
  'icon' => 'Icon',
  'invalid_credentials' => 'Invalid credentials',
  'language' => 'Language',
  'last_login' => 'Last Login',
  'last_name' => 'Last Name',
  'login_count' => 'Login Count',
  'manage_groups' => 'Manage groups',
  'manage_members' => 'Manage Members',
  'manage_permissions' => 'Manage permissions',
  'member_added' => 'Member added successfully',
  'member_count' => 'Member Count',
  'member_removed' => 'Member removed successfully',
  'member_since' => 'Member Since',
  'members' => 'members',
  'my_profile' => 'My Profile',
  'new_group' => 'New Group',
  'new_password' => 'New Password',
  'no_groups_assigned' => 'This user is not a member of any group.',
  'no_permissions_assigned' => 'This user has no permissions assigned through groups.',
  'notification_settings' => 'Notification Settings',
  'password' => 'Password',
  'password_changed' => 'Password changed successfully',
  'password_changed_at' => 'Password Changed',
  'password_hint' => 'Leave blank to keep current password',
  'password_min_hint' => 'Minimum 6 characters',
  'password_mismatch' => 'Passwords do not match',
  'password_too_short' => 'Password is too short',
  'permission' => 'Permission',
  'permissions' => 'Permissions',
  'personal_information' => 'Personal Information',
  'preview' => 'Preview',
  'profile_picture' => 'Profile Picture',
  'profile_updated' => 'Profile updated successfully',
  'remove_avatar' => 'Remove Photo',
  'remove_current_picture' => 'Remove current picture',
  'remove_member' => 'Remove Member',
  'reset_password' => 'Reset Password',
  'role' => 'Role',
  'save_group' => 'Save Group',
  'save_user' => 'Save User',
  'security_settings' => 'Security Settings',
  'select_groups_hint' => 'Select one or more groups',
  'send_welcome_email' => 'Send Welcome Email',
  'success' => 'Success',
  'system_group' => 'System',
  'system_group_notice' => 'This is a system group. Some features may be restricted.',
  'timezone' => 'Timezone',
  'toggle_all' => 'Toggle All',
  'toggle_user_status' => 'Toggle User Status?',
  'toggle_user_status_msg' => 'This will activate/deactivate the user account.',
  'two_factor_auth' => 'Two-Factor Authentication',
  'updated' => 'Updated!',
  'upload_avatar' => 'Upload Photo',
  'user' => 'User',
  'user_created' => 'User created successfully',
  'user_deleted' => 'User deleted successfully',
  'user_details' => 'User Details',
  'user_group' => 'User Group',
  'user_groups' => 'User Groups',
  'user_groups_management' => 'User Groups Management',
  'user_information' => 'User Information',
  'user_list' => 'User List',
  'user_management' => 'User Management',
  'user_not_found' => 'User not found',
  'user_profile' => 'User Profile',
  'user_updated' => 'User updated successfully',
  'username' => 'Username',
  'username_exists' => 'This username already exists',
  'username_hint' => 'Used for login (letters, numbers and underscore only)',
  'users' => 'Users',
  'view_users' => 'View users',
  'yes_delete' => 'Yes, delete it!',
  'yes_toggle' => 'Yes, toggle it!',
  'group_management' => 'Group Management',
  'view_members' => 'View Members',
  'user' => 'User',
  
  // Financial Obligations
  'financial_obligations' => 'Financial Obligations',
  'current_financial_obligations' => 'Current Financial Obligations',
  'financial_obligations_history' => 'Financial Obligations History',
  'edit_financial_obligations' => 'Edit Financial Obligations',
  'financial_obligations_updated' => 'Financial obligations updated successfully',
  'rent_amount' => 'Rent Amount',
  'loyer' => 'Rent',
  'charges_amount' => 'Charges Amount',
  'total_tvac' => 'Total incl. VAT',
  'secretary_tvac_17' => 'Secretary incl. VAT 17%',
  'secretary_htva' => 'Secretary excl. VAT',
  'tva_17' => 'VAT 17%',
  'effective_date' => 'Effective Date',
  'end_date' => 'End Date',
  'additional_charges' => 'Additional Charges',
  'no_financial_obligations' => 'No financial obligations defined',
  'add_financial_obligations' => 'Add Financial Obligations',
  'no_history_found' => 'No history found',
  'effective_date_hint' => 'Date from which these amounts apply',
  'unauthorized_financial_edit' => 'Only administrators and managers can edit financial obligations',
  'contact_admin_for_changes' => 'Please contact an administrator or manager to modify this information',
  
  // Retrocession Settings
  'retrocession_settings' => 'Retrocession Settings',
  'add_retrocession_settings' => 'Add Retrocession Settings',
  'current_active_settings' => 'Current Active Settings',
  'no_specific_retrocession_settings' => 'No specific retrocession settings. System defaults will be used.',
  'view_history' => 'View History',
  'retrocession_history' => 'Retrocession History',
  'no_retrocession_history' => 'No retrocession history available',
  'valid_from' => 'Valid From',
  'valid_to' => 'Valid To',
  'leave_empty_for_indefinite' => 'Leave empty for indefinite period',
  'total_percentage_exceeds_100' => 'Total percentage exceeds 100%',
  'continue_anyway' => 'Continue anyway',
  'retrocession_date_overlap' => 'Validity dates overlap with existing configuration',
  'exclude_patient_retrocession' => 'Exclude patient retrocession from invoices',
  'exclude_patient_retrocession_help' => 'When checked, the "Patient Retrocession" line will be automatically removed from retrocession invoices generated for this practitioner.',
  
  // Group Members
  'group_members' => 'Members of :name',
  'members' => 'members',
  'current_members' => 'Current Members',
  'add_member' => 'Add Member',
  'select_user' => 'Select User',
  'add_to_group' => 'Add to Group',
  'all_users_already_members' => 'All users are already members of this group',
  'group_information' => 'Group Information',
  'is_system' => 'System Group',
  'no_members_in_group' => 'No members in this group',
  'joined' => 'Joined',
  'member' => 'Member',
  'confirm_remove_member' => 'Are you sure you want to remove this member',
  'cannot_remove_last_member' => 'Cannot remove the last member',
  
  // Field validation
  'field_required' => ':field is required',
  'username_format' => 'Username can only contain letters, numbers and underscores',
  'username_length' => 'Username must be between 3 and 50 characters',
  'invalid_email' => 'Invalid email address',
  'password_length' => 'Password must be at least 6 characters',
  'invalid_file_type' => 'Invalid file type. Only images are allowed',
  'file_size_exceeded' => 'File size exceeds the maximum allowed (5MB)',
  'upload_failed' => 'Failed to upload file',
  'cannot_delete_self' => 'You cannot delete your own account',
  'cannot_deactivate_self' => 'You cannot deactivate your own account',
  'status_updated' => 'User status updated successfully',
  
  // Additional translations for profile page
  'my_groups' => 'My Groups',
  'role_' => 'User',
  'role_practitioner' => 'Practitioner',
  'role_receptionist' => 'Receptionist',
  'preferences' => 'Preferences',
  'preferred_language' => 'Preferred Language',
  'email_notifications' => 'Email Notifications',
  'save_preferences' => 'Save Preferences',
  'two_factor_description' => 'Add an extra layer of security to your account',
  'enable_2fa' => 'Enable 2FA',
  'disable_2fa' => 'Disable 2FA',
  'disable_2fa_confirm' => 'Are you sure you want to disable two-factor authentication?',
  'confirm_new_password' => 'Confirm New Password',
  'update_password' => 'Update Password',
  'password_requirements' => 'At least 8 characters, including letters and numbers',
  
  // Admin password reset
  'admin_password_reset_description' => 'As an administrator, you can reset this user\'s password',
  'reset_password_for' => 'Reset Password for',
  'reset_password_warning' => 'This will immediately change the user\'s password. Make sure to communicate the new password securely.',
  'send_password_email' => 'Send new password to user via email',
  'password_reset_success' => 'Password has been reset successfully',
  'unauthorized_password_reset' => 'You are not authorized to reset user passwords',
  'use_change_password_instead' => 'Please use the change password feature for your own account',
  'new_password_required' => 'New password is required',
  'confirm_password_required' => 'Password confirmation is required',
  'reset_password_confirm' => 'Are you sure you want to reset this user\'s password?',
  'preferences_updated' => 'Preferences updated successfully',
);
