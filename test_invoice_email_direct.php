<?php
/**
 * Direct Invoice Email Test - Bypasses framework dependencies
 */

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        
        // Handle variable references like ${APP_NAME}
        if (preg_match('/\$\{(.+?)\}/', $value, $matches)) {
            $varName = $matches[1];
            if (isset($_ENV[$varName])) {
                $value = str_replace($matches[0], $_ENV[$varName], $value);
            }
        }
        
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Load composer autoloader
require_once __DIR__ . '/vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

// Database connection
function getDB() {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $user = $_ENV['DB_USERNAME'] ?? 'root';
    $pass = $_ENV['DB_PASSWORD'] ?? '';
    
    try {
        $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $db;
    } catch (PDOException $e) {
        die("Database connection failed: " . $e->getMessage());
    }
}

$db = getDB();
$invoiceId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Direct Invoice Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; border: 1px solid #ddd; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Direct Invoice Email Test</h1>
    
    <?php
    if ($invoiceId) {
        // Get invoice details with all necessary joins
        $stmt = $db->prepare("
            SELECT i.*,
                   u.email as user_email, u.first_name, u.last_name,
                   c.email as client_email, c.name as client_name,
                   it.code as invoice_type_code, it.name as invoice_type_name,
                   dt.code as document_type_code
            FROM invoices i
            LEFT JOIN users u ON i.user_id = u.id
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN config_invoice_types it ON i.type_id = it.id
            LEFT JOIN document_types dt ON i.document_type_id = dt.id
            WHERE i.id = :id
        ");
        $stmt->execute([':id' => $invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invoice) {
            echo "<p class='error'>Invoice not found!</p>";
            echo "<p><a href='?'>Back to list</a></p>";
            exit;
        }
        
        echo "<h2>Testing email for invoice: {$invoice['invoice_number']}</h2>";
        
        // Determine recipient
        $recipientEmail = null;
        $recipientName = null;
        
        if ($invoice['user_id']) {
            $recipientEmail = $invoice['user_email'];
            $recipientName = $invoice['first_name'] . ' ' . $invoice['last_name'];
        } elseif ($invoice['client_id']) {
            $recipientEmail = $invoice['client_email'];
            $recipientName = $invoice['client_name'];
        }
        
        echo "<table>";
        echo "<tr><th>Invoice Number</th><td>{$invoice['invoice_number']}</td></tr>";
        echo "<tr><th>Type</th><td>{$invoice['invoice_type_code']}</td></tr>";
        echo "<tr><th>Recipient</th><td>" . htmlspecialchars($recipientName ?: 'Unknown') . "</td></tr>";
        echo "<tr><th>Email</th><td>" . ($recipientEmail ? htmlspecialchars($recipientEmail) : '<span class="error">NO EMAIL</span>') . "</td></tr>";
        echo "<tr><th>Total</th><td>" . number_format($invoice['total'], 2) . " EUR</td></tr>";
        echo "</table>";
        
        if (!$recipientEmail) {
            echo "<p class='error'>Cannot send email - recipient has no email address!</p>";
            echo "<p><a href='?'>Back to list</a></p>";
            exit;
        }
        
        if (isset($_GET['send'])) {
            echo "<h3>Sending Email...</h3>";
            
            try {
                // Create email
                $mail = new PHPMailer(true);
                
                // Server settings
                $mail->isSMTP();
                $mail->Host = $_ENV['MAIL_HOST'] ?? 'localhost';
                $mail->Port = $_ENV['MAIL_PORT'] ?? 1025;
                $mail->SMTPAuth = false;
                $mail->SMTPSecure = false;
                $mail->SMTPAutoTLS = false;
                
                // Enable debugging
                $mail->SMTPDebug = SMTP::DEBUG_SERVER;
                $mail->Debugoutput = function($str, $level) {
                    echo "<pre class='info'>SMTP: " . htmlspecialchars($str) . "</pre>";
                };
                
                // Recipients
                $mail->setFrom($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>', $_ENV['MAIL_FROM_NAME'] ?? 'Fit360');
                $mail->addAddress($recipientEmail, $recipientName);
                
                // Email content
                $mail->isHTML(true);
                $mail->CharSet = 'UTF-8';
                $mail->Subject = "Facture {$invoice['invoice_number']}";
                
                // Build email body
                $bodyHtml = "<h3>Facture {$invoice['invoice_number']}</h3>";
                $bodyHtml .= "<p>Bonjour " . htmlspecialchars($recipientName) . ",</p>";
                $bodyHtml .= "<p>Veuillez trouver ci-joint votre facture.</p>";
                $bodyHtml .= "<ul>";
                $bodyHtml .= "<li>Numéro: {$invoice['invoice_number']}</li>";
                $bodyHtml .= "<li>Date: " . date('d/m/Y', strtotime($invoice['issue_date'])) . "</li>";
                $bodyHtml .= "<li>Montant: " . number_format($invoice['total'], 2) . " EUR</li>";
                $bodyHtml .= "</ul>";
                $bodyHtml .= "<p>Cordialement,<br>Fit360</p>";
                
                $mail->Body = $bodyHtml;
                $mail->AltBody = strip_tags(str_replace(['<br>', '</p>'], ["\n", "\n\n"], $bodyHtml));
                
                // Note: Not attaching PDF in this test to keep it simple
                
                // Send
                $mail->send();
                
                echo "<p class='success'>✓ Email sent successfully!</p>";
                
                // Log to database
                try {
                    $stmt = $db->prepare("
                        INSERT INTO email_logs (
                            invoice_id, recipient_email, subject, 
                            status, sent_at, created_at
                        ) VALUES (
                            :invoice_id, :recipient_email, :subject,
                            'sent', NOW(), NOW()
                        )
                    ");
                    $stmt->execute([
                        ':invoice_id' => $invoiceId,
                        ':recipient_email' => $recipientEmail,
                        ':subject' => $mail->Subject
                    ]);
                    echo "<p class='success'>✓ Email logged to database</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>Failed to log email: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                
                echo "<p><a href='http://localhost:8025' target='_blank'>View in Mailhog</a></p>";
                
            } catch (Exception $e) {
                echo "<p class='error'>✗ Email failed: " . htmlspecialchars($e->getMessage()) . "</p>";
                if (isset($mail)) {
                    echo "<p class='error'>PHPMailer Error: " . htmlspecialchars($mail->ErrorInfo) . "</p>";
                }
                
                // Log failure
                try {
                    $stmt = $db->prepare("
                        INSERT INTO email_logs (
                            invoice_id, recipient_email, subject,
                            status, error_message, created_at
                        ) VALUES (
                            :invoice_id, :recipient_email, :subject,
                            'failed', :error, NOW()
                        )
                    ");
                    $stmt->execute([
                        ':invoice_id' => $invoiceId,
                        ':recipient_email' => $recipientEmail,
                        ':subject' => "Facture {$invoice['invoice_number']}",
                        ':error' => $e->getMessage()
                    ]);
                } catch (Exception $e2) {
                    // Ignore logging errors
                }
            }
            
            echo "<p><a href='?'>Back to list</a></p>";
            
        } else {
            echo "<p><a href='?id=$invoiceId&send=1' class='button' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;display:inline-block;'>Send Email Now</a></p>";
            echo "<p><a href='?'>Back to list</a></p>";
        }
        
    } else {
        // Show invoice list
        echo "<h2>Select an Invoice to Test</h2>";
        
        $stmt = $db->query("
            SELECT i.*, 
                   u.email as user_email, CONCAT(u.first_name, ' ', u.last_name) as user_name,
                   c.email as client_email, c.name as client_name
            FROM invoices i
            LEFT JOIN users u ON i.user_id = u.id
            LEFT JOIN clients c ON i.client_id = c.id
            ORDER BY i.created_at DESC
            LIMIT 20
        ");
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Recipient</th><th>Email</th><th>Status</th><th>Action</th></tr>";
        
        foreach ($invoices as $inv) {
            $recipientName = $inv['user_id'] ? $inv['user_name'] : $inv['client_name'];
            $email = $inv['user_id'] ? $inv['user_email'] : $inv['client_email'];
            
            echo "<tr>";
            echo "<td>{$inv['id']}</td>";
            echo "<td>{$inv['invoice_number']}</td>";
            echo "<td>" . htmlspecialchars($recipientName ?: 'Unknown') . "</td>";
            echo "<td>" . ($email ? htmlspecialchars($email) : '<span style="color:red">NO EMAIL</span>') . "</td>";
            echo "<td>{$inv['status']}</td>";
            echo "<td>";
            if ($email) {
                echo "<a href='?id={$inv['id']}'>Test Email</a>";
            } else {
                echo "<span style='color:red'>No email</span>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    ?>
</body>
</html>