<?php
// Fix invoice rounding issues - specifically for invoice 263 and similar cases

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Include MoneyHelper
require_once __DIR__ . '/app/helpers/MoneyHelper.php';
use App\Helpers\MoneyHelper;

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Fix Invoice Rounding Issues</h1>";

// Function to recalculate and fix an invoice
function fixInvoice($db, $invoiceId, $desiredTotal = null) {
    // Get invoice lines
    $stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = ? ORDER BY sort_order, id");
    $stmt->execute([$invoiceId]);
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($lines)) {
        return ['error' => 'No invoice lines found'];
    }
    
    // Calculate using TTC-first approach
    $totals = MoneyHelper::calculateTTCTotals($lines, $desiredTotal);
    
    // Update invoice totals
    $stmt = $db->prepare("
        UPDATE invoices 
        SET subtotal = :subtotal,
            vat_amount = :vat_amount,
            total = :total,
            updated_at = NOW()
        WHERE id = :id
    ");
    
    $result = $stmt->execute([
        ':subtotal' => $totals['subtotal'],
        ':vat_amount' => $totals['vat_amount'],
        ':total' => $totals['total'],
        ':id' => $invoiceId
    ]);
    
    return [
        'success' => $result,
        'old_total' => null, // We'll get this from the calling function
        'new_totals' => $totals
    ];
}

// Handle POST request to fix specific invoice
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $invoiceId = $_POST['invoice_id'] ?? null;
    $desiredTotal = $_POST['desired_total'] ?? null;
    
    if ($invoiceId) {
        // Get current totals
        $stmt = $db->prepare("SELECT subtotal, vat_amount, total FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $currentTotals = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $result = fixInvoice($db, $invoiceId, $desiredTotal ? (float)$desiredTotal : null);
        
        if (isset($result['error'])) {
            echo "<div style='color: red;'>Error: {$result['error']}</div>";
        } else {
            echo "<div style='color: green;'>✅ Invoice $invoiceId updated successfully!</div>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Field</th><th>Old Value</th><th>New Value</th></tr>";
            echo "<tr><td>Subtotal</td><td>{$currentTotals['subtotal']}</td><td>{$result['new_totals']['subtotal']}</td></tr>";
            echo "<tr><td>VAT Amount</td><td>{$currentTotals['vat_amount']}</td><td>{$result['new_totals']['vat_amount']}</td></tr>";
            echo "<tr><td>Total</td><td><strong>{$currentTotals['total']}</strong></td><td><strong>{$result['new_totals']['total']}</strong></td></tr>";
            echo "</table>";
        }
    }
}

// Find invoices with rounding issues
echo "<h2>Invoices with Rounding Issues</h2>";
$stmt = $db->prepare("
    SELECT i.id, i.invoice_number, i.subtotal, i.vat_amount, i.total, 
           (i.subtotal + i.vat_amount) as calculated_total,
           ROUND((i.subtotal + i.vat_amount) - i.total, 2) as difference
    FROM invoices i
    WHERE ABS((i.subtotal + i.vat_amount) - i.total) > 0.001
    AND i.status = 'sent'
    ORDER BY ABS((i.subtotal + i.vat_amount) - i.total) DESC
    LIMIT 20
");
$stmt->execute();
$issues = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($issues) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice ID</th><th>Invoice Number</th><th>Stored Total</th><th>Calculated Total</th><th>Difference</th><th>Actions</th></tr>";
    
    foreach ($issues as $issue) {
        $bgColor = abs($issue['difference']) > 0.05 ? 'background-color: #ffcccc;' : '';
        echo "<tr style='$bgColor'>";
        echo "<td>{$issue['id']}</td>";
        echo "<td><a href='http://localhost/fit/public/invoices/{$issue['id']}' target='_blank'>{$issue['invoice_number']}</a></td>";
        echo "<td>" . number_format($issue['total'], 2) . "</td>";
        echo "<td>" . number_format($issue['calculated_total'], 2) . "</td>";
        echo "<td>" . number_format($issue['difference'], 2) . "</td>";
        echo "<td>";
        echo "<form method='post' style='display: inline;'>";
        echo "<input type='hidden' name='invoice_id' value='{$issue['id']}'>";
        echo "<button type='submit' onclick='return confirm(\"Recalculate totals for invoice {$issue['invoice_number']}?\");'>Fix</button>";
        echo "</form>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>✅ No invoices found with rounding issues!</p>";
}

// Specific fix for Invoice 263 (if it exists)
echo "<h2>Fix Invoice 263 Specifically</h2>";
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = 263");
$stmt->execute();
$invoice263 = $stmt->fetch(PDO::FETCH_ASSOC);

if ($invoice263) {
    echo "<p>Current total: <strong>{$invoice263['total']}</strong></p>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='invoice_id' value='263'>";
    echo "<label>Desired total: <input type='number' name='desired_total' value='930.00' step='0.01'></label> ";
    echo "<button type='submit'>Fix Invoice 263 to Exact Total</button>";
    echo "</form>";
    
    // Show detailed breakdown
    echo "<h3>Invoice 263 Detailed Analysis</h3>";
    $stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 263 ORDER BY sort_order, id");
    $stmt->execute();
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Subtotal</th><th>VAT</th><th>Total</th></tr>";
    
    $totalSubtotal = 0;
    $totalVat = 0;
    
    foreach ($lines as $line) {
        $lineCalc = MoneyHelper::calculateLineTotal($line['quantity'], $line['unit_price'], $line['vat_rate']);
        $totalSubtotal += $lineCalc['subtotal'];
        $totalVat += $lineCalc['vat_amount'];
        
        echo "<tr>";
        echo "<td>{$line['description']}</td>";
        echo "<td>{$line['quantity']}</td>";
        echo "<td>" . number_format($line['unit_price'], 2) . "</td>";
        echo "<td>{$line['vat_rate']}%</td>";
        echo "<td>" . number_format($lineCalc['subtotal'], 2) . "</td>";
        echo "<td>" . number_format($lineCalc['vat_amount'], 2) . "</td>";
        echo "<td>" . number_format($lineCalc['total'], 2) . "</td>";
        echo "</tr>";
    }
    
    echo "<tr style='font-weight: bold;'>";
    echo "<td colspan='4'>TOTALS</td>";
    echo "<td>" . number_format($totalSubtotal, 2) . "</td>";
    echo "<td>" . number_format($totalVat, 2) . "</td>";
    echo "<td>" . number_format($totalSubtotal + $totalVat, 2) . "</td>";
    echo "</tr>";
    echo "</table>";
    
    // Show TTC calculation
    $ttcTotals = MoneyHelper::calculateTTCTotals($lines, 930.00);
    echo "<h4>TTC-First Calculation (Target: 930.00)</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Current</th><th>TTC-First</th><th>Difference</th></tr>";
    echo "<tr><td>Subtotal</td><td>{$invoice263['subtotal']}</td><td>{$ttcTotals['subtotal']}</td><td>" . number_format($ttcTotals['subtotal'] - $invoice263['subtotal'], 2) . "</td></tr>";
    echo "<tr><td>VAT Amount</td><td>{$invoice263['vat_amount']}</td><td>{$ttcTotals['vat_amount']}</td><td>" . number_format($ttcTotals['vat_amount'] - $invoice263['vat_amount'], 2) . "</td></tr>";
    echo "<tr><td>Total</td><td><strong>{$invoice263['total']}</strong></td><td><strong>{$ttcTotals['total']}</strong></td><td>" . number_format($ttcTotals['total'] - $invoice263['total'], 2) . "</td></tr>";
    echo "</table>";
    
} else {
    echo "<p>Invoice 263 not found in database.</p>";
}

echo "<hr>";
echo "<h3>Summary of Changes Made</h3>";
echo "<ul>";
echo "<li>✅ Fixed <code>calculateTotals()</code> method in Invoice.php (line 888)</li>";
echo "<li>✅ Added TTC-first calculation methods to MoneyHelper class</li>";
echo "<li>✅ Added validation methods to detect rounding issues</li>";
echo "<li>✅ Created this fix script to correct existing issues</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Run this script to fix invoice 263 with the desired total</li>";
echo "<li>Test other invoices with rounding issues</li>";
echo "<li>Monitor future invoices to ensure the fix works</li>";
echo "</ol>";
?>