<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Check current labels
$stmt = $db->prepare("
    SELECT user_id, cns_label, patient_label, secretary_label, created_at 
    FROM user_retrocession_settings 
    WHERE user_id = 1 
    ORDER BY created_at DESC 
    LIMIT 1
");
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);

echo "Current retrocession labels for user 1:\n";
if ($result) {
    echo "CNS Label: " . $result['cns_label'] . "\n";
    echo "Patient Label: " . $result['patient_label'] . "\n";
    echo "Secretary Label: " . $result['secretary_label'] . "\n";
    echo "Created: " . $result['created_at'] . "\n";
} else {
    echo "No retrocession settings found for user 1\n";
}

// Check last generated invoice
echo "\nLast generated retrocession invoice lines:\n";
$stmt = $db->prepare("
    SELECT il.description 
    FROM invoice_lines il
    JOIN invoices i ON i.id = il.invoice_id
    WHERE i.invoice_type_id = 14
    ORDER BY i.id DESC
    LIMIT 3
");
$stmt->execute();
$lines = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($lines as $line) {
    echo "- " . $line['description'] . "\n";
}