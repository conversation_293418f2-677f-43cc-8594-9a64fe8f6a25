<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing Admin Deletion Functionality</h2>";
    
    // Check if User model has isAdmin method
    echo "<h3>1. Checking User Model for isAdmin method:</h3>";
    $userModelPath = __DIR__ . '/app/models/User.php';
    if (file_exists($userModelPath)) {
        $content = file_get_contents($userModelPath);
        if (strpos($content, 'public static function isAdmin') !== false) {
            echo "<span style='color: green;'>✅ User::isAdmin() method exists</span><br>";
        } else {
            echo "<span style='color: red;'>❌ User::isAdmin() method NOT found</span><br>";
            echo "<p>Need to add isAdmin method to User model</p>";
        }
    }
    
    // Check admin users
    echo "<h3>2. Admin Users in Database:</h3>";
    $sql = "SELECT u.id, u.username, u.email, ug.group_id, g.name as group_name
            FROM users u
            LEFT JOIN user_groups ug ON u.id = ug.user_id
            LEFT JOIN groups g ON ug.group_id = g.id
            WHERE g.name = 'Admin' OR g.id = 1
            ORDER BY u.id";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($admins)) {
        echo "<span style='color: orange;'>⚠️ No admin users found</span><br>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Group</th></tr>";
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td>{$admin['id']}</td>";
            echo "<td>{$admin['username']}</td>";
            echo "<td>{$admin['email']}</td>";
            echo "<td>{$admin['group_name']} (ID: {$admin['group_id']})</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check sent invoices
    echo "<h3>3. Sent Invoices (that normally can't be deleted):</h3>";
    $sql = "SELECT id, invoice_number, status, created_at, total
            FROM invoices 
            WHERE status != 'draft'
            ORDER BY id DESC
            LIMIT 5";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($invoices)) {
        echo "<span style='color: orange;'>⚠️ No sent invoices found</span><br>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Number</th><th>Status</th><th>Total</th><th>Created</th></tr>";
        foreach ($invoices as $invoice) {
            echo "<tr>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td>{$invoice['invoice_number']}</td>";
            echo "<td><span style='background-color: #17a2b8; color: white; padding: 2px 5px; border-radius: 3px;'>{$invoice['status']}</span></td>";
            echo "<td>€" . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>4. Controller Changes Status:</h3>";
    $controllerPath = __DIR__ . '/app/controllers/InvoiceController.php';
    if (file_exists($controllerPath)) {
        $content = file_get_contents($controllerPath);
        
        // Check for admin check in confirmDelete
        if (strpos($content, '$isAdmin = User::isAdmin($currentUserId)') !== false) {
            echo "<span style='color: green;'>✅ confirmDelete() has admin check</span><br>";
        } else {
            echo "<span style='color: red;'>❌ confirmDelete() missing admin check</span><br>";
        }
        
        // Check for admin check in delete
        if (strpos($content, 'User::isAdmin') !== false && strpos($content, 'Admin deleted sent invoice') !== false) {
            echo "<span style='color: green;'>✅ delete() has admin logging</span><br>";
        } else {
            echo "<span style='color: red;'>❌ delete() missing admin logging</span><br>";
        }
        
        // Check for bulkDelete admin support
        if (strpos($content, 'bulkDelete') !== false && strpos($content, 'if (!$isAdmin && $status !== Invoice::STATUS_DRAFT)') !== false) {
            echo "<span style='color: green;'>✅ bulkDelete() supports admin deletion</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠️ bulkDelete() may need admin support</span><br>";
        }
    }
    
    echo "<h3>5. View Templates Status:</h3>";
    $viewsDir = __DIR__ . '/app/views/invoices/';
    $viewFiles = ['edit-modern.twig', 'index-modern.twig', 'index-modern-clean.twig', 'index-modern-enhanced.twig'];
    
    foreach ($viewFiles as $viewFile) {
        $viewPath = $viewsDir . $viewFile;
        if (file_exists($viewPath)) {
            $content = file_get_contents($viewPath);
            if (strpos($content, 'invoice.status == \'draft\' or isAdmin') !== false) {
                echo "<span style='color: green;'>✅ $viewFile supports admin deletion</span><br>";
            } else {
                echo "<span style='color: orange;'>⚠️ $viewFile may need update</span><br>";
            }
        }
    }
    
    echo "<h3>6. Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Log in as an admin user (e.g., <EMAIL>)</li>";
    echo "<li>Navigate to <a href='/fit/public/invoices'>Invoices List</a></li>";
    echo "<li>Find a sent invoice and check if delete option appears in dropdown</li>";
    echo "<li>Try deleting a sent invoice - should see admin warning</li>";
    echo "<li>Confirm deletion works and is logged</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>