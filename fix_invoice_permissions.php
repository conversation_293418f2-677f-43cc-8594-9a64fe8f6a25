<?php
/**
 * <PERSON><PERSON>t to fix invoice.create permission for users
 * Run this from command line: php fix_invoice_permissions.php
 * Or access via browser: http://localhost/fit/fix_invoice_permissions.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
try {
    $dsn = sprintf(
        "mysql:host=%s;dbname=%s;charset=utf8mb4",
        $_ENV['DB_HOST'] ?? 'localhost',
        $_ENV['DB_DATABASE'] ?? 'healthcenter_billing'
    );
    
    $db = new PDO($dsn, $_ENV['DB_USERNAME'] ?? 'root', $_ENV['DB_PASSWORD'] ?? '');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n\n";
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage() . "\n");
}

// Function to display users and their groups
function displayUsers($db) {
    echo "=== CURRENT USERS AND THEIR GROUPS ===\n";
    $stmt = $db->query("
        SELECT 
            u.id,
            CONCAT(u.first_name, ' ', u.last_name) as name,
            u.email,
            GROUP_CONCAT(ug.name SEPARATOR ', ') as `groups`,
            GROUP_CONCAT(ug.id SEPARATOR ', ') as group_ids
        FROM users u
        LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
        LEFT JOIN user_groups ug ON ugm.group_id = ug.id
        WHERE u.is_active = 1
        GROUP BY u.id
        ORDER BY u.id
    ");
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        echo sprintf(
            "ID: %d | %s (%s) | Groups: %s\n",
            $user['id'],
            $user['name'],
            $user['email'],
            $user['groups'] ?: 'No groups'
        );
    }
    
    return $users;
}

// Function to check which groups have invoice.create permission
function checkInvoicePermission($db) {
    echo "\n=== GROUPS WITH invoice.create PERMISSION ===\n";
    $stmt = $db->query("
        SELECT ug.id, ug.name
        FROM user_groups ug
        JOIN group_permissions gp ON ug.id = gp.group_id
        JOIN permissions p ON gp.permission_id = p.id
        WHERE p.code = 'invoice.create'
        ORDER BY ug.id
    ");
    
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($groups)) {
        echo "No groups have invoice.create permission!\n";
    } else {
        foreach ($groups as $group) {
            echo sprintf("Group ID %d: %s\n", $group['id'], $group['name']);
        }
    }
    
    return $groups;
}

// Function to grant permission to a user
function grantPermissionToUser($db, $userId, $method = 'admin') {
    try {
        // Check if user exists
        $stmt = $db->prepare("SELECT CONCAT(first_name, ' ', last_name) as name FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo "Error: User ID $userId not found!\n";
            return false;
        }
        
        echo "\nGranting invoice.create permission to: {$user['name']} (ID: $userId)\n";
        
        if ($method === 'admin') {
            // Add to Administrators group (ID: 1)
            $stmt = $db->prepare("
                INSERT IGNORE INTO user_group_members (user_id, group_id) 
                VALUES (?, 1)
            ");
            $stmt->execute([$userId]);
            echo "✓ Added to Administrators group\n";
            
        } elseif ($method === 'receptionist') {
            // Add to Receptionists group (ID: 5)
            $stmt = $db->prepare("
                INSERT IGNORE INTO user_group_members (user_id, group_id) 
                VALUES (?, 5)
            ");
            $stmt->execute([$userId]);
            echo "✓ Added to Receptionists group\n";
            
        } elseif ($method === 'current_group') {
            // Grant permission to user's current group
            $stmt = $db->prepare("
                SELECT ugm.group_id, ug.name 
                FROM user_group_members ugm
                JOIN user_groups ug ON ugm.group_id = ug.id
                WHERE ugm.user_id = ?
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $group = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$group) {
                echo "User has no group. Adding to Receptionists group instead.\n";
                return grantPermissionToUser($db, $userId, 'receptionist');
            }
            
            // Get permission ID
            $stmt = $db->prepare("SELECT id FROM permissions WHERE code = 'invoice.create'");
            $stmt->execute();
            $permission = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$permission) {
                echo "Error: invoice.create permission not found in database!\n";
                return false;
            }
            
            // Grant permission to group
            $stmt = $db->prepare("
                INSERT IGNORE INTO group_permissions (group_id, permission_id) 
                VALUES (?, ?)
            ");
            $stmt->execute([$group['group_id'], $permission['id']]);
            echo "✓ Granted invoice.create to group: {$group['name']}\n";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        return false;
    }
}

// Main execution
echo "=== INVOICE PERMISSION FIX SCRIPT ===\n\n";

// Display current users
$users = displayUsers($db);

// Check which groups have the permission
$groupsWithPermission = checkInvoicePermission($db);

// Interactive or automatic mode
if (php_sapi_name() === 'cli' && !empty($argv[1])) {
    // Command line with user ID argument
    $userId = (int)$argv[1];
    $method = $argv[2] ?? 'admin';
    grantPermissionToUser($db, $userId, $method);
    
} elseif (php_sapi_name() === 'cli') {
    // Interactive CLI mode
    echo "\nEnter User ID to grant invoice.create permission (or 'q' to quit): ";
    $handle = fopen("php://stdin", "r");
    $line = trim(fgets($handle));
    
    if ($line !== 'q' && is_numeric($line)) {
        echo "\nChoose method:\n";
        echo "1. Add to Administrators group (full access)\n";
        echo "2. Add to Receptionists group (invoice permissions)\n";
        echo "3. Grant to user's current group\n";
        echo "Choice (1-3): ";
        
        $choice = trim(fgets($handle));
        $methods = [
            '1' => 'admin',
            '2' => 'receptionist',
            '3' => 'current_group'
        ];
        
        $method = $methods[$choice] ?? 'admin';
        grantPermissionToUser($db, (int)$line, $method);
    }
    
    fclose($handle);
    
} else {
    // Web browser mode
    header('Content-Type: text/html; charset=utf-8');
    echo "<pre style='font-family: monospace;'>";
    
    if (!empty($_GET['user_id'])) {
        $userId = (int)$_GET['user_id'];
        $method = $_GET['method'] ?? 'admin';
        grantPermissionToUser($db, $userId, $method);
        echo "\n<a href='?'>Back to list</a>";
    } else {
        echo "\n<h3>Quick Fix Options:</h3>";
        foreach ($users as $user) {
            if ($user['groups'] && strpos($user['groups'], 'Administrators') !== false) {
                echo sprintf("%s - Already has admin access\n", $user['name']);
            } else {
                echo sprintf(
                    "%s - <a href='?user_id=%d&method=admin'>Grant Admin</a> | <a href='?user_id=%d&method=receptionist'>Grant Receptionist</a> | <a href='?user_id=%d&method=current_group'>Grant to Current Group</a>\n",
                    $user['name'],
                    $user['id'],
                    $user['id'],
                    $user['id']
                );
            }
        }
    }
    
    echo "</pre>";
}

echo "\n=== DONE ===\n";