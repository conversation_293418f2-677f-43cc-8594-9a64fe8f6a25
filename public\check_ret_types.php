<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Retrocession Invoice Types</h2>";
    
    // Check config_invoice_types
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE prefix LIKE '%RET%' OR name LIKE '%retro%'");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current RET Types in config_invoice_types:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Color</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . htmlspecialchars($type['name']) . "</td>";
        echo "<td>" . htmlspecialchars($type['code']) . "</td>";
        echo "<td>" . htmlspecialchars($type['prefix']) . "</td>";
        echo "<td>" . htmlspecialchars($type['color']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check invoice_types table
    echo "<h3>Invoice Types table:</h3>";
    $stmt = $db->query("SELECT * FROM invoice_types WHERE code LIKE '%RET%' OR name LIKE '%retro%'");
    $invTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($invTypes) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach (array_keys($invTypes[0]) as $col) {
            echo "<th>" . htmlspecialchars($col) . "</th>";
        }
        echo "</tr>";
        foreach ($invTypes as $type) {
            echo "<tr>";
            foreach ($type as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check recent retrocession invoices
    echo "<h3>Recent Retrocession Invoices:</h3>";
    $stmt = $db->query("
        SELECT i.id, i.invoice_number, i.invoice_type_id, it.name as type_name
        FROM invoices i
        LEFT JOIN invoice_types it ON it.id = i.invoice_type_id
        WHERE i.invoice_number LIKE '%RET%'
        ORDER BY i.id DESC
        LIMIT 10
    ");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Invoice Number</th><th>Type ID</th><th>Type Name</th></tr>";
    foreach ($invoices as $inv) {
        echo "<tr>";
        echo "<td>" . $inv['id'] . "</td>";
        echo "<td>" . htmlspecialchars($inv['invoice_number']) . "</td>";
        echo "<td>" . $inv['invoice_type_id'] . "</td>";
        echo "<td>" . htmlspecialchars($inv['type_name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}