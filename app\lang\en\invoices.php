<?php

return array (
  'status' => 
  array (
    'draft' => 'Draft',
    'sent' => 'Sent',
    'paid' => 'Paid',
    'partial' => 'Partially Paid',
    'overdue' => 'Overdue',
    'cancelled' => 'Cancelled',
  ),
  'search_placeholder' => 'Number, client...',
  'send_selected' => 'Send selected invoices',
  'export_selected' => 'Export selected invoices',
  'delete_selected' => 'Delete selected invoices',
  'send_confirm' => 'Are you sure you want to send this invoice?',
  'bulk_delete_confirm' => 'Are you sure you want to delete selected invoices? Only draft invoices will be deleted.',
  'invoices' => 'Invoices',
  'invoice' => 'Invoice',
  'pending_invoices' => 'Pending Invoices',
  'create_invoice' => 'Create Invoice',
  'new_invoice_created' => 'New invoice created',
  'payment_received' => 'Payment received',
  'view_all_invoices' => 'View All Invoices',
  'billing_wizard' => 'Billing Wizard',
  'retrocession' => 'Retrocession',
  'vouchers' => 'Vouchers',
  'total_invoices' => 'Total Invoices',
  'total_revenue' => 'Total Revenue',
  'unpaid_invoices' => 'Unpaid Invoices',
  'outstanding_amount' => 'Outstanding Amount',
  'invoice_number' => 'Invoice Number',
  'enter_invoice_number' => 'Enter invoice number',
  'invoice_number_editable' => 'You can modify this number if needed',
  'document_type' => 'Document Type',
  'invoice_type' => 'Invoice Type',
  'issue_date' => 'Issue Date',
  'due_date' => 'Due Date',
  'amount' => 'Amount',
  'filter_all' => 'All invoices',
  'filter_draft' => 'Drafts',
  'filter_sent' => 'Sent',
  'filter_paid' => 'Paid',
  'filter_unpaid' => 'Unpaid',
  'filter_overdue' => 'Overdue',
  'no_invoices_found' => 'No invoices found',
  'download_pdf' => 'Download PDF',
  'send_invoice' => 'Send Invoice',
  'record_payment' => 'Record Payment',
  'create_credit_note' => 'Create Credit Note',
  'overdue' => 'Overdue',
  'delete_warning' => 'This action cannot be undone.',
  'mark_as_paid' => 'Mark as Paid',
  'mark_as_sent' => 'Mark as Sent',
  'mark_as_sent_confirm_title' => 'Mark as Sent?',
  'mark_as_sent_confirm_text' => 'This invoice will be marked as sent and cannot be edited anymore.',
  'yes_mark_sent' => 'Yes, mark as sent',
  'can_only_send_draft' => 'Only draft invoices can be marked as sent',
  'cannot_delete_non_draft' => 'Only draft invoices can be deleted',
  'deleted_successfully' => 'Invoice deleted successfully',
  'actions' => array(
    'send' => 'Send',
    'print' => 'Print',
    'download' => 'Download',
    'duplicate' => 'Duplicate',
    'cancel' => 'Cancel',
    'mark_as_paid' => 'Mark as Paid',
    'mark_as_sent' => 'Mark as Sent',
    'record_payment' => 'Record Payment',
  ),
  'bulk_mark_paid_confirm' => 'Are you sure you want to mark selected invoices as paid?',
  'manage_invoices_description' => 'Manage and track all your invoices',
  'create_first_invoice' => 'Create your first invoice',
  'archive' => 'Archive',
  'archive_invoice' => 'Archive Invoice',
  'archive_confirm' => 'Are you sure you want to archive this invoice?',
  'archived' => 'Archived',
  'archived_successfully' => 'Invoice archived successfully',
  'archive_failed' => 'Failed to archive invoice',
  'archive_selected' => 'Archive selected',
  'bulk_archived_success' => ':count invoice(s) archived successfully',
  'bulk_archive_failed' => 'Failed to archive invoices',
  'cannot_archive_invoice' => 'Cannot archive :number - :reason',
  'view_archive' => 'View Archive',
  'archive_description' => 'View and manage archived invoices by document type',
  'back_to_invoices' => 'Back to Invoices',
  'no_archived_invoices' => 'No archived invoices found',
  'archived_date' => 'Archived Date',
  'archived_from' => 'Archived From',
  'archived_to' => 'Archived To',
  'restore' => 'Restore',
  'restore_invoice' => 'Restore Invoice',
  'restore_confirm' => 'Are you sure you want to restore this invoice to the active list?',
  'restore_selected' => 'Restore selected',
  'restored_successfully' => 'Invoice restored successfully',
  'restore_failed' => 'Failed to restore invoice',
  'cannot_delete_invoice' => 'Cannot delete invoice :number',
  'bulk_deleted_success' => ':count invoice(s) deleted successfully',
  'bulk_delete_failed' => 'Failed to delete invoices',
  'no_invoices_selected' => 'No invoices selected',
  'back_to_list' => 'Back to list',
  'subtotal' => 'Subtotal',
  'vat_amount' => 'VAT Amount',
  'cns_base_amount' => 'CNS/Patient Amount',
  'secretary_fee' => 'Secretary Fee',
  'total' => 'Total',
  'description' => 'Description',
  'quantity' => 'Quantity',
  'unit_price' => 'Unit Price',
  'vat_rate' => 'VAT Rate',
  'invoice_details' => 'Invoice Details',
  'invoice_items' => 'Invoice Items',
  'add_item' => 'Add Item',
  'payment_terms' => 'Payment Terms',
  'notes' => 'Notes',
  'internal_notes' => 'Internal Notes',
  'additional_information' => 'Additional Information',
  'edit_invoice' => 'Edit Invoice',
  'invoice_category' => 'Invoice Category',
  'cant_find_client_add_new' => 'Can\'t find client? Click + to add a new one',
  'internal_invoice_hint' => 'Internal invoice for system user',
  'patient_invoice_hint' => 'Direct invoice to patient',
  'internal' => 'Internal',
  'payment_status' => 'Payment Status',
  'paid_amount' => 'Paid Amount',
  'duplicate_invoice' => 'Duplicate Invoice',
  'from' => 'From',
  'payment_cash' => 'Cash',
  'payment_card' => 'Credit/Debit Card',
  'payment_bank_transfer' => 'Bank Transfer',
  'payment_check' => 'Check',
  'payment_reference' => 'Payment Reference',
  'total' => 'Total',
  'payment_recorded' => 'Payment recorded successfully',
  'payment_failed' => 'Failed to record payment',
  'payment_complete' => 'Complete',
  'payment_history' => 'Payment History',
  'invoice_category' => 'Invoice Category',
  'additional_information' => 'Additional Information',
  'notes_placeholder' => 'Notes visible on the invoice (optional)',
  'internal_notes_placeholder' => 'Internal notes for your reference only',
  'internal_notes_hint' => 'These notes will not appear on the invoice',
  'leave_empty_immediate' => 'Leave empty for immediate payment',
  'default_payment_terms' => 'Payment due upon receipt',
  'voucher' => 'Voucher',
  'enter_voucher_code' => 'Enter voucher code',
  'cant_find_client_add_new' => 'Can\'t find the client? Click + to add a new one',
  'internal_invoice_hint' => 'Internal invoice for staff or company use',
  'patient_invoice_hint' => 'Direct invoice to patient',
  'internal' => 'Internal',
  'payment_terms_note_hint' => 'Additional payment instructions or notes (optional)',
  'template' => 'Template',
  'no_template' => 'No Template',
  'template_hint' => 'Select a template to pre-fill invoice lines',
  'type_rental' => 'Rental',
  'type_hourly' => 'Hourly',
  'type_retrocession_30' => 'Retrocession 30%',
  'type_retrocession_25' => 'Retrocession 25%',
  'type_service' => 'Service',
  'period' => 'Period',
  'subject' => 'Subject',
  'object' => 'OBJECT',
  'base_amount' => 'Base Amount',
  'amount_excl_vat' => 'EXCL VAT',
  'replace_existing_items' => 'This will replace existing items. Continue?',
  'select_user_for_loyalty' => 'Select a user from Medical or Managers group',
  'confirm_send_email' => 'Confirm Email Send',
  'email_will_be_sent_to' => 'The invoice will be sent by email to:',
  'recipient' => 'Recipient',
  'choose_action' => 'Choose an action:',
  'save_without_email' => 'Save without sending email',
  'save_and_send_email' => 'Save and send email',
  'invoice_saved_as_sent_no_email' => 'Invoice saved as sent (email not sent per your request)',
  'invoice_updated_as_sent_no_email' => 'Invoice updated and marked as sent (email not sent per your request)',
  'create_loy_invoice' => 'Create LOY Invoice',
  'recipient_required' => 'Please select a client or user',
);
