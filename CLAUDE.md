# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Fit360 AdminDesk is a comprehensive health center billing and management system built with PHP. It features multi-language support (FR/EN/DE), multiple UI themes, and extensive configuration options for healthcare billing workflows including retrocession management.

## Essential Commands

### Development Setup
```bash
# Install dependencies
composer install

# Copy environment configuration
cp .env.example .env
# Update DB credentials in .env

# Run database migrations
cd database
php migrate.php  # Can run via browser or CLI

# Start development server
composer start  # Runs on localhost:8000
# OR for WAMP: Access via http://localhost/fit/public/

# Run tests
composer test
# Run specific test
vendor/bin/phpunit tests/Phase3/InvoiceModelTest.php

# Clear cache
php clear-cache.php
```

### Database Management
```bash
# Import initial schema (if starting fresh)
mysql -u root healthcenter_billing < database/schema.sql

# Run all migrations in order
cd database
php migrate.php --run

# Manual migration example
mysql -u root healthcenter_billing < database/migrations/001_initial_schema.sql
```

## Architecture & Key Concepts

### Framework Stack
- **Flight PHP 3.x**: Micro-framework handling routing and MVC structure
- **Twig 3.0**: Template engine with multi-theme support
- **PDO**: Database abstraction with prepared statements
- **Composer**: PSR-4 autoloading for App namespace

### Critical Architecture Patterns

#### 1. Multi-Theme System
The application supports 4 themes (AdminLTE3, AdminLTE4, Tabler, Modern). Theme selection affects view resolution:
```php
// Controller determines template based on session/config
$template = $this->getTemplate(); // Returns: 'modern', 'tabler', etc.
$viewName = 'invoices/index-' . $template; // Resolves to specific theme view
```

#### 2. Base Model Pattern
All models extend a base Model class providing ORM-like features:
```php
// app/models/Base/Model.php provides:
- CRUD operations (create, find, findBy, update, delete)
- QueryBuilder for complex queries
- Relationship loading (belongsTo, hasMany)
- Collection class for result sets
```

#### 3. Translation System
Multi-language support with database-backed translations:
- Language files in `/app/lang/{locale}/*.php`
- Database translations in `translations` table
- Template usage: `{{ __('key.subkey') }}`
- Dynamic translation management at `/translations`

#### 4. Configuration Architecture
Layered configuration system:
1. Environment variables (`.env`)
2. Database config table (runtime settings)
3. File-based configs (`/app/config/*.php`)
4. Module-specific settings

#### 5. Module-Based Routing
Routes are organized by feature modules:
```php
// app/config/routes.php loads:
require 'modules/auth/routes.php';
require 'modules/clients/routes.php';
require 'modules/invoices/routes.php';
// Each module defines its own routes
```

### Key Business Logic Components

#### Invoice System
- **Models**: Invoice, InvoiceItem, InvoicePayment
- **Number Formats**: Configurable patterns (e.g., INV-{YYYY}-{NNNN})
- **Status Flow**: draft → sent → paid/partial/overdue → cancelled
- **Document Types**: Invoices, Credit Notes, Quotes (configurable)
- **Date Management**: 
  - Issue date: Editable on creation, defaults to today, can be modified for draft invoices
  - Due date: Auto-calculated based on payment terms (e.g., 30 days)
  - Sent invoices: Dates locked for audit compliance
  - JavaScript auto-calculation: `updateDueDate()` function in create/edit forms

#### Retrocession Management
Complex practitioner billing calculations:
- **Rate Profiles**: Define calculation rules per practitioner
- **Monthly Data Entry**: Track services provided
- **Automatic Calculations**: Based on percentage, fixed amounts, or tiered rates
- **Invoice Generation**: Convert retrocession data to invoices

#### User & Permission System
- **User Groups**: Role-based access control
- **Group Permissions**: Granular feature access
- **Multi-tenancy**: Group-specific configurations

### Database Schema Highlights
- **JSON Columns**: Used for multi-language content and flexible configurations
- **Soft Deletes**: Implemented on critical tables
- **Audit Fields**: created_at, updated_at, created_by, updated_by
- **Migration System**: Sequential SQL files in `/database/migrations/`

### Frontend Architecture
- **Table Helper v2**: Reusable JavaScript class for data tables with sorting, filtering, column reordering
- **AJAX Pattern**: XMLHttpRequest (not fetch) for browser extension compatibility
- **Bootstrap 5.3**: Primary CSS framework for Modern theme
- **Theme Assets**: Organized in `/public/assets/{theme}/`
- **Mobile Responsiveness**: Comprehensive mobile-first implementation with:
  - Touch-optimized form controls (44px minimum targets)
  - Responsive tables with card-based mobile views
  - Gesture support (swipe, pull-to-refresh)
  - Bottom navigation and floating action buttons
  - Mobile-specific CSS (`/public/css/mobile-responsive.css`)
  - JavaScript enhancements (`/public/js/mobile-enhancements.js`)

### Mobile Responsiveness Implementation
The application features a comprehensive mobile-first responsive design:

#### Key Mobile Features
- **Touch-Optimized Forms**: All form controls meet 44px minimum touch target size
- **Responsive Tables**: Tables transform into card-based layouts on mobile devices
- **Gesture Support**: 
  - Swipe right from edge to open sidebar
  - Swipe left to close sidebar
  - Pull down to refresh page content
- **Mobile Navigation**:
  - Hamburger menu for sidebar access
  - Fixed bottom navigation for key actions
  - Floating action button for primary actions
- **Mobile-Specific UI**:
  - Full-screen modals on mobile
  - Stacked button groups
  - Optimized dropdowns and menus

#### Mobile Classes & Attributes
```html
<!-- Mobile visibility classes -->
.mobile-only        <!-- Show only on mobile -->
.desktop-only       <!-- Hide on mobile -->
.hide-mobile        <!-- Hide specific elements -->
.mobile-full-width  <!-- Full width on mobile -->

<!-- Data attributes -->
data-mobile-fab="true"           <!-- Enable floating action button -->
data-mobile-fab-icon="<i>...</i>" <!-- FAB icon -->
data-mobile-fab-label="Label"     <!-- FAB accessibility label -->

<!-- Table mobile features -->
class="table mobile-cards"        <!-- Enable card view on mobile -->
data-label="Column Name"          <!-- Labels for card view -->
```

#### Mobile JavaScript API
```javascript
// Re-initialize after dynamic content
MobileEnhancements.init();

// Individual features
MobileEnhancements.initTableResponsive();
MobileEnhancements.initFormEnhancements();
MobileEnhancements.initSwipeGestures();
```

### Performance Considerations
- **Twig Caching**: Templates cached in `/storage/cache/twig/`
- **Database Indexes**: On foreign keys and frequently queried columns
- **Lazy Loading**: Relationships loaded on-demand
- **Query Optimization**: Using QueryBuilder for complex queries

### Security Implementation
- **CSRF Protection**: Token validation on state-changing requests
- **Input Sanitization**: Using filter_var and prepared statements
- **Session Security**: Secure session configuration
- **Authentication**: Session-based with JWT token support

### Testing Structure
- **Unit Tests**: Core model functionality
- **Integration Tests**: Feature workflows
- **Test Data**: Fixtures for consistent testing
- **Phase-based Tests**: Organized by development phase

### Critical Files to Understand
1. `/app/config/bootstrap.php` - Application initialization
2. `/app/Core/Controller.php` - Base controller with common methods
3. `/app/models/Base/Model.php` - ORM base class
4. `/app/views/base-modern.twig` - Primary theme layout
5. `/public/js/table-helper-v2.js` - Reusable table functionality
6. `/public/css/mobile-responsive.css` - Mobile-first responsive styles
7. `/public/js/mobile-enhancements.js` - Mobile interaction handlers
8. `/MOBILE_RESPONSIVENESS_IMPLEMENTATION.md` - Mobile implementation guide

## Recent Updates (July 2025)

### Retrocession System Enhancement (July 20, 2025)
- **Monthly Amount Configuration**: Added 12-month grid in user profiles for CNS/Patient amounts
- **Automatic Invoice Generation**: One-click generation from user profile with visual tracking
- **Invoice Regeneration**: Can delete and regenerate invoices while preserving retrocession data
- **Custom Settings**: User-specific retrocession percentages and labels via user_retrocession_settings
- **Permission Controls**: Manager/Admin only delete access with role-based visibility
- **Database Updates**:
  - Added `user_monthly_retrocession_amounts` table for monthly configuration
  - Updated foreign key handling to preserve data on invoice deletion
- **UI Enhancements**:
  - Visual indicators (green background, checkmarks) for generated invoices
  - Generate buttons disabled for invoiced months
  - Integrated retrocession settings management in user profiles
- **Error Handling**: Clean JSON responses with output buffer management
- **Key Files Modified**:
  - `/app/controllers/UserController.php` - Added generateRetrocession method
  - `/app/models/Invoice.php` - Updated delete method for cascade handling
  - `/app/views/users/form-modern.twig` - Added monthly amounts UI
  - `/app/services/RetrocessionCalculator.php` - Enhanced with user settings

## Recent Updates (July 2025)

### Mobile Responsiveness & Context7 Integration (July 20, 2025)
- **Mobile-First Design**: Implemented comprehensive mobile responsiveness across the application
- **Touch Optimization**: All interactive elements meet 44px minimum touch target size
- **Responsive Tables**: Tables transform into card-based layouts on mobile devices
- **Gesture Support**: Added swipe gestures for navigation and pull-to-refresh functionality
- **Mobile Navigation**: Implemented hamburger menu, bottom navigation, and floating action buttons
- **Context7 MCP Server**: Integrated for real-time documentation access during development
- **New Files Created**:
  - `/public/css/mobile-responsive.css` - Mobile-specific styles
  - `/public/js/mobile-enhancements.js` - Mobile interaction handlers
  - `/MOBILE_RESPONSIVENESS_IMPLEMENTATION.md` - Implementation guide
  - `/CONTEXT7_MCP_SETUP.md` - Context7 setup documentation
- **Version**: Updated to 2.3.4

### Email System Enhancement (July 19, 2025)
- **Email with PDF**: Fixed invoice email sending to properly attach PDFs
- **Email Templates**: Updated subjects to include period (e.g., "Facture (JUIN 2025)")
- **PDF Consistency**: Emails now use the same PDF generation as downloads (invoice-pdf.php)
- **CSRF Protection**: Added validation to send() method in InvoiceController
- **JavaScript Fixes**: Fixed sendInvoice() function in all invoice list views (POST instead of GET)
- **Error Handling**: Added debug logging to help troubleshoot email issues
- **Status Restriction**: Relaxed to allow resending of already-sent invoices
- **Version**: Updated to 2.3.3

### Invoice Date Management Enhancement (July 14, 2025)
- **Issue Date Control**: Invoice creation forms now properly support custom issue dates
- **Due Date Calculation**: Automatic calculation based on payment terms (1 month = 31 days)
- **Database Scripts**: Created helper scripts for date management
- **Example**: Updated invoice FAC-LOY-2025-0186 to issue date 14/07/2025, due date 14/08/2025

### Bulk Retrocession Generation (July 20, 2025)
- **Bulk Monthly Generation**: Added bulk generation for all practitioners with configured monthly amounts
- **Route**: `/retrocession/bulk-monthly` - Shows all practitioners with monthly configurations
- **Features**: 
  - Shows practitioners with CNS/Patient amounts for selected month
  - Visual indicators for already generated invoices
  - Checkbox selection for bulk operations
  - Progress tracking with modal
  - Detailed results showing success/failures
- **Controller Methods**: `bulkMonthlyView()` and `bulkGenerateMonthly()` in RetrocessionController
- **Navigation**: Added links in sidebar menu, users page, and retrocession index
- **Permissions**: Uses 'retrocession.bulk_generate' permission check
- **Version**: Updated to 2.3.4

### Session History (January 29, 2025)
- **Permission System**: Simplified from dual role/group system to groups-only
- **Translation Fixes**: Added missing French translations for user management
- **Last Login Tracking**: Fixed database field mismatch (last_login → last_login_at)
- **Project Cleanup**: Removed 200+ temporary files, prepared for GitHub deployment
- **Version**: Updated to 2.3.1

## Retrocession System Troubleshooting

### Common Issues and Solutions

#### "Une facture existe déjà pour cette période"
- **Issue**: Cannot regenerate invoice after deletion
- **Solution**: The system now properly handles deleted invoices. If you still see this error:
  1. Check if the invoice actually exists in the invoice list
  2. Clear browser cache and retry
  3. Check PHP error logs for database issues

#### "No retrocession data found for this period"
- **Issue**: Cannot generate invoice despite having monthly amounts configured
- **Causes & Solutions**:
  1. **No monthly amounts**: Ensure CNS and Patient amounts are saved for the month
  2. **Wrong practitioner**: Verify user has a linked client record with `is_practitioner = 1`
  3. **Database sync**: The retrocession_data_entry might have wrong status
  4. **Check logs**: Look for "UserController::generateRetrocession" entries

#### Foreign Key Constraint Errors
- **Issue**: Cannot delete invoice due to related records
- **Solution**: The system now handles these automatically by:
  - Setting invoice_id to NULL in retrocession_data_entry (preserves data)
  - Deleting email_logs entries
  - Deleting payment allocations
  - Use `.env` configuration for all database scripts

#### JSON Parse Errors
- **Issue**: "unexpected character at line 1 column 1"
- **Solution**: PHP error output is breaking JSON response
  - Check PHP error logs
  - Ensure no output before JSON headers
  - Look for missing namespace prefixes (e.g., `\PDO::FETCH_ASSOC`)

## Context7 MCP Server Integration

The project now includes Context7 Model Context Protocol (MCP) server integration for enhanced AI-assisted development.

### What is Context7?
Context7 is an MCP server that provides real-time, up-to-date documentation and code examples directly to AI coding assistants. It helps ensure accurate, version-specific code generation by fetching current documentation from official sources.

### Installation & Configuration
- **Package**: `@upstash/context7-mcp` (installed via Claude Code MCP)
- **Configuration**: Added to Claude Code via `claude mcp add context7 npx -- -y @upstash/context7-mcp@latest`
- **Node.js Requirement**: v18+ (project has v18.19.1)
- **Project Files**: 
  - `package.json` - Created for Node.js dependency management
  - `mcp-config.json` - Updated with correct project structure

### Usage
To use Context7 in your prompts, simply append `use context7` to get enhanced results with current documentation:

```
"How do I implement authentication in Flight PHP? use context7"
"Create a responsive data table with Bootstrap 5.3 use context7"
"Best practices for PHP 8.2 error handling use context7"
```

### Benefits
- **Real-time Documentation**: Always get the latest API documentation
- **Version-specific Examples**: Code examples match your exact library versions
- **Reduced Hallucination**: AI responses based on actual documentation, not training data
- **Framework Support**: Works with any framework or library with online documentation

## Important Instructions

When working with this codebase:
- Do what has been asked; nothing more, nothing less
- NEVER create files unless they're absolutely necessary for achieving your goal
- ALWAYS prefer editing an existing file to creating a new one
- NEVER proactively create documentation files (*.md) or README files unless explicitly requested
- When debugging, use existing error logs and debugging tools rather than creating new test files
- Clean up any temporary files created during debugging sessions
- Follow the existing code style and patterns in the project

## Development Workflow Protocol

**CRITICAL: For all future development tasks on this project, follow this step-by-step confirmation workflow:**

### Workflow Requirements

1. **Break Down Every Task**: Decompose each main task into clear, manageable subtasks
2. **Present Before Implementing**: Show the subtask plan and wait for confirmation
3. **Wait for Approval**: Do NOT proceed until the user confirms the current step is OK
4. **Adapt Based on Feedback**: If the user is not satisfied, modify the approach
5. **Sequential Execution**: Only move to the next subtask after explicit approval

### Example Workflow Structure

```
Main Task: [User's Request]
│
├── Subtask 1: Analysis and Planning
│   ├── Analyze current implementation
│   ├── Identify requirements
│   ├── Present detailed plan
│   └── ⏸️ WAIT FOR USER CONFIRMATION
│
├── Subtask 2: Implementation Step 1
│   ├── Show what will be changed
│   ├── Implement after approval
│   ├── Show results
│   └── ⏸️ WAIT FOR USER CONFIRMATION
│
├── Subtask 3: Implementation Step 2
│   ├── Build on previous step
│   ├── Test integration
│   └── ⏸️ WAIT FOR USER CONFIRMATION
│
└── Subtask 4: Testing and Documentation
    ├── Run tests
    ├── Update documentation
    └── ⏸️ WAIT FOR FINAL CONFIRMATION
```

### Communication Format

For each subtask, present:
1. **What**: Clear description of what will be done
2. **Why**: Reasoning behind the approach
3. **How**: Specific files and changes involved
4. **Result**: Expected outcome

### Important Notes

- **No Assumptions**: Never assume approval to proceed
- **Explicit Confirmation**: Wait for clear "OK", "proceed", or similar confirmation
- **Feedback Integration**: Be ready to revise based on user input
- **Progress Tracking**: Use the TodoWrite tool to track subtasks
- **Transparency**: Show actual results after each implementation

This workflow ensures complete control and visibility for the user throughout the development process.

## Important Technical Notes

### Invoice System Architecture
- **Dual Table System**: 
  - `invoice_items` - Model-based table (not used for display)
  - `invoice_lines` - Display table used by templates (MUST use this for line items)
- **Invoice Type System**:
  - `invoice_types` table (new) - Uses 'code' field
  - `config_invoice_types` table (old) - Uses 'prefix' field for invoice numbers
  - Must set both `invoice_type_id` and `type_id` for proper invoice number generation
- **Generated Columns**: `vat_amount` in invoice_lines is auto-calculated, never insert directly
- **VAT Calculation**: For TVAC amounts, provide line_total only; VAT is calculated as: line_total - (line_total / (1 + vat_rate/100))

### Bulk Loyer Invoice Generation
- **Financial Obligations**: Use EITHER secretary_tvac_17 OR (secretary_htva + tva_17), never both
- **TVA Field**: `tva_17` is a VAT amount, not a service needing VAT added
- **Invoice Creation**: Use `createInvoice()` method, not `create()`
- **Line Items**: Create in `invoice_lines` table with proper `line_type` enum values

## Memories

- **Database Scripts**: Always use .env file for database connections in scripts - never hardcode credentials. Use PDO with .env configuration for all database operations.
- **Development Workflow**: For every task, generate subtasks and do it step by step, wait for user confirmation before proceeding.
- **Invoice Line Items**: Always create items in `invoice_lines` table, NOT `invoice_items` table for them to display correctly.