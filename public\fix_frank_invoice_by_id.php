<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing <PERSON>'s Invoice #336 (Using ID)</h2>";
    
    // Get <PERSON>'s client ID
    $stmt = $db->prepare("
        SELECT c.id as client_id, c.name, u.first_name, u.last_name, urs.secretary_value
        FROM users u
        JOIN clients c ON c.user_id = u.id
        LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
        WHERE u.id = 1
        ORDER BY urs.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $frank = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$frank) {
        echo "<p style='color: red;'>Error: <PERSON>'s client record not found!</p>";
        exit;
    }
    
    echo "<p>Found <PERSON>: {$frank['first_name']} {$frank['last_name']}</p>";
    echo "<p>Client: {$frank['name']} (ID: {$frank['client_id']})</p>";
    echo "<p>Secretary Fee: {$frank['secretary_value']}%</p>";
    
    // Since Frank has 5%, we use ID 37 (which has FAC-RET25 prefix)
    $typeId = 37; // This is the ret2 entry with FAC-RET25 prefix
    
    // Get the type details
    $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = ?");
    $stmt->execute([$typeId]);
    $type = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Will use type ID {$typeId}: Code='{$type['code']}', Prefix='{$type['prefix']}'</p>";
    
    // Update the invoice
    $newInvoiceNumber = 'FAC-RET25-2025-0200';
    
    $stmt = $db->prepare("
        UPDATE invoices 
        SET type_id = :type_id,
            client_id = :client_id,
            invoice_number = :invoice_number
        WHERE id = 336
    ");
    
    $stmt->execute([
        ':type_id' => $typeId,
        ':client_id' => $frank['client_id'],
        ':invoice_number' => $newInvoiceNumber
    ]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Successfully updated invoice #336</p>";
        
        // Verify the update
        $stmt = $db->prepare("
            SELECT i.*, cit.code, cit.prefix, c.name as client_name
            FROM invoices i
            LEFT JOIN config_invoice_types cit ON cit.id = i.type_id
            LEFT JOIN clients c ON c.id = i.client_id
            WHERE i.id = 336
        ");
        $stmt->execute();
        $updated = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>Updated Invoice Details:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><td><strong>Invoice Number:</strong></td><td>{$updated['invoice_number']}</td></tr>";
        echo "<tr><td><strong>Type ID:</strong></td><td>{$updated['type_id']}</td></tr>";
        echo "<tr><td><strong>Type Code:</strong></td><td>{$updated['code']}</td></tr>";
        echo "<tr><td><strong>Prefix:</strong></td><td>{$updated['prefix']}</td></tr>";
        echo "<tr><td><strong>Client:</strong></td><td>{$updated['client_name']}</td></tr>";
        echo "<tr><td><strong>Status:</strong></td><td>{$updated['status']}</td></tr>";
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>Frank's invoice has been updated to use the FAC-RET25 prefix.</p>";
        echo "<p>Even though the type code is 'ret2', it has the correct prefix 'FAC-RET25'.</p>";
        echo "<p><a href='/fit/public/invoices/336' style='color: #155724; font-weight: bold;'>→ View the updated invoice</a></p>";
        echo "<p><a href='/fit/public/invoices' style='color: #155724; font-weight: bold;'>→ Return to invoice list</a></p>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>No changes made - invoice may already be updated.</p>";
        
        // Check current state
        $stmt = $db->prepare("SELECT invoice_number, type_id FROM invoices WHERE id = 336");
        $stmt->execute();
        $current = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Current invoice number: {$current['invoice_number']}</p>";
        echo "<p>Current type_id: {$current['type_id']}</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}