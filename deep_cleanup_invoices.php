<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Deep Invoice Cleanup</h2>\n";
    
    // 1. Find orphaned entries in user_generated_invoices
    echo "<h3>1. Checking for orphaned entries in user_generated_invoices:</h3>\n";
    $stmt = $pdo->query("
        SELECT ugi.*, i.id as invoice_exists
        FROM user_generated_invoices ugi
        LEFT JOIN invoices i ON ugi.invoice_id = i.id
        WHERE i.id IS NULL
    ");
    
    $orphaned = $stmt->fetchAll();
    if (count($orphaned) > 0) {
        echo "Found " . count($orphaned) . " orphaned entries:\n";
        foreach ($orphaned as $row) {
            echo "- Invoice ID {$row['invoice_id']} (User: {$row['user_id']}, Period: {$row['period_month']}/{$row['period_year']}, Type: {$row['invoice_type']})\n";
        }
        echo "\n<a href='?clean_orphaned=1'>Clean all orphaned entries</a>\n";
    } else {
        echo "✓ No orphaned entries found\n";
    }
    
    // 2. Check Frank's July 2025 retrocession status specifically
    echo "\n<h3>2. Frank Huet's July 2025 Retrocession Status:</h3>\n";
    $stmt = $pdo->query("
        SELECT ugi.*, i.invoice_number, i.status, i.id as actual_invoice_id
        FROM user_generated_invoices ugi
        LEFT JOIN invoices i ON ugi.invoice_id = i.id
        WHERE ugi.user_id = 1 
        AND ugi.period_month = 7 
        AND ugi.period_year = 2025
        AND ugi.invoice_type = 'RET'
    ");
    
    $results = $stmt->fetchAll();
    if (count($results) > 0) {
        echo "<table border='1'>\n";
        echo "<tr><th>UGI ID</th><th>Invoice ID</th><th>Actual Invoice</th><th>Status</th><th>Action</th></tr>\n";
        foreach ($results as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['invoice_id']}</td>";
            echo "<td>" . ($row['actual_invoice_id'] ? "Yes" : "No (Orphaned)") . "</td>";
            echo "<td>{$row['status']}</td>";
            echo "<td><a href='?delete_ugi={$row['id']}'>Delete</a></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "✓ No July 2025 retrocession entries found for Frank\n";
    }
    
    // 3. Check all tables for invoice 319 references
    echo "\n<h3>3. Searching for any remaining references to invoice #319:</h3>\n";
    $tables = [
        'invoices' => 'id',
        'invoice_lines' => 'invoice_id',
        'user_generated_invoices' => 'invoice_id',
        'retrocession_data_entry' => 'invoice_id',
        'invoice_payments' => 'invoice_id',
        'email_logs' => 'invoice_id'
    ];
    
    foreach ($tables as $table => $column) {
        // Check if table exists
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            // Check if column exists
            $stmt = $pdo->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table WHERE $column = 319");
                $count = $stmt->fetchColumn();
                if ($count > 0) {
                    echo "⚠️ Found $count references in $table.$column\n";
                    echo "   <a href='?clean_table=$table&column=$column&value=319'>Clean these references</a>\n";
                } else {
                    echo "✓ No references in $table\n";
                }
            }
        }
    }
    
    // Handle cleanup actions
    if (isset($_GET['clean_orphaned'])) {
        echo "\n<h3>Cleaning orphaned entries...</h3>\n";
        $stmt = $pdo->exec("
            DELETE ugi FROM user_generated_invoices ugi
            LEFT JOIN invoices i ON ugi.invoice_id = i.id
            WHERE i.id IS NULL
        ");
        echo "✓ Cleaned orphaned entries\n";
        echo "<a href='test_retrocession_generation.php?user_id=1&month=7&year=2025'>Try generating invoice now</a>\n";
    }
    
    if (isset($_GET['delete_ugi'])) {
        $id = intval($_GET['delete_ugi']);
        $pdo->exec("DELETE FROM user_generated_invoices WHERE id = $id");
        echo "\n✓ Deleted user_generated_invoices entry #$id\n";
        echo "<meta http-equiv='refresh' content='1'>\n";
    }
    
    if (isset($_GET['clean_table'])) {
        $table = $_GET['clean_table'];
        $column = $_GET['column'];
        $value = intval($_GET['value']);
        
        // Whitelist tables and columns for safety
        $allowed = [
            'invoice_lines' => 'invoice_id',
            'user_generated_invoices' => 'invoice_id',
            'retrocession_data_entry' => 'invoice_id',
            'invoice_payments' => 'invoice_id',
            'email_logs' => 'invoice_id'
        ];
        
        if (isset($allowed[$table]) && $allowed[$table] == $column) {
            $pdo->exec("DELETE FROM $table WHERE $column = $value");
            echo "\n✓ Cleaned references from $table\n";
            echo "<meta http-equiv='refresh' content='1'>\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}