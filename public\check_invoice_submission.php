<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING INVOICE SUBMISSION ISSUES ===\n\n";

try {
    $db = Flight::db();
    
    // Check if any invoices were created recently
    echo "1. Recent invoices (last hour):\n";
    $stmt = $db->query("
        SELECT id, invoice_number, status, created_at, client_id, user_id,
               CASE 
                   WHEN client_id IS NOT NULL THEN 'client'
                   WHEN user_id IS NOT NULL THEN 'user'
                   ELSE 'unknown'
               END as billable_type
        FROM invoices 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY created_at DESC
    ");
    $recent = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($recent) > 0) {
        foreach ($recent as $inv) {
            echo sprintf("- ID: %d | Number: %s | Type: %s | Created: %s\n",
                $inv['id'], $inv['invoice_number'], $inv['billable_type'], $inv['created_at']
            );
        }
    } else {
        echo "No invoices created in the last hour.\n";
    }
    
    // Check the latest invoice number sequence
    echo "\n2. Latest document sequence:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 
        ORDER BY updated_at DESC 
        LIMIT 1
    ");
    $seq = $stmt->fetch(\PDO::FETCH_ASSOC);
    if ($seq) {
        echo "Last number: {$seq['last_number']} (Updated: {$seq['updated_at']})\n";
        echo "Next invoice would be: FAC-2025-" . str_pad($seq['last_number'] + 1, 4, '0', STR_PAD_LEFT) . "\n";
    }
    
    // Check if user_id 2 exists
    echo "\n3. Checking user_id 2:\n";
    $stmt = $db->prepare("SELECT id, username, first_name, last_name FROM users WHERE id = 2");
    $stmt->execute();
    $user = $stmt->fetch(\PDO::FETCH_ASSOC);
    if ($user) {
        echo "User found: {$user['username']} ({$user['first_name']} {$user['last_name']})\n";
    } else {
        echo "User ID 2 NOT FOUND!\n";
    }
    
    // Check payment term ID 1
    echo "\n4. Checking payment_term_id 1:\n";
    $stmt = $db->prepare("SELECT id, code, days FROM config_payment_terms WHERE id = 1");
    $stmt->execute();
    $term = $stmt->fetch(\PDO::FETCH_ASSOC);
    if ($term) {
        echo "Payment term found: {$term['code']} ({$term['days']} days)\n";
    } else {
        echo "Payment term ID 1 NOT FOUND!\n";
    }
    
    // Check VAT rate ID 26
    echo "\n5. Checking vat_rate_id 26:\n";
    $stmt = $db->prepare("SELECT id, code, rate FROM config_vat_rates WHERE id = 26");
    $stmt->execute();
    $vat = $stmt->fetch(\PDO::FETCH_ASSOC);
    if ($vat) {
        echo "VAT rate found: {$vat['code']} ({$vat['rate']}%)\n";
    } else {
        echo "VAT rate ID 26 NOT FOUND!\n";
    }
    
    // Check error logs
    echo "\n6. Checking recent PHP errors:\n";
    $errorLog = ini_get('error_log');
    if (file_exists($errorLog)) {
        $errors = file_get_contents($errorLog);
        $lines = explode("\n", $errors);
        $recentErrors = array_slice($lines, -10); // Last 10 lines
        
        $invoiceErrors = array_filter($recentErrors, function($line) {
            return stripos($line, 'invoice') !== false || stripos($line, 'error') !== false;
        });
        
        if (count($invoiceErrors) > 0) {
            echo "Recent invoice-related errors:\n";
            foreach ($invoiceErrors as $error) {
                echo "- " . trim($error) . "\n";
            }
        } else {
            echo "No recent invoice-related errors in PHP log.\n";
        }
    } else {
        echo "PHP error log not found at: $errorLog\n";
    }
    
    // Check if there's a problem with the items array format
    echo "\n7. Testing invoice creation with user_id:\n";
    $testData = [
        'document_type_id' => 1,
        'user_id' => 2,
        'status' => 'draft',
        'issue_date' => '2025-07-08',
        'due_date' => '2025-07-08',
        'payment_term_id' => 1,
        'lines' => [
            ['description' => 'Test item', 'quantity' => 1, 'unit_price' => 100, 'vat_rate' => 17]
        ],
        'subtotal' => 100,
        'vat_amount' => 17,
        'total' => 117
    ];
    
    echo "Would create invoice with this data (not actually creating).\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}