<?php
/**
 * Web-based Database Migration Runner
 * 
 * Access this file through your browser to run migrations
 * For security, this should be removed or protected in production
 */

session_start();

// Simple authentication - change this password!
$AUTH_PASSWORD = 'migrate123';

// Check authentication
$authenticated = isset($_SESSION['migrate_auth']) && $_SESSION['migrate_auth'] === true;

if (!$authenticated && isset($_POST['password'])) {
    if ($_POST['password'] === $AUTH_PASSWORD) {
        $_SESSION['migrate_auth'] = true;
        $authenticated = true;
    } else {
        $error = 'Invalid password';
    }
}

// Logout
if (isset($_GET['logout'])) {
    unset($_SESSION['migrate_auth']);
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .migration-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 500px;
            overflow-y: auto;
        }
        .status-pending { color: #ffc107; }
        .status-success { color: #198754; }
        .status-error { color: #dc3545; }
        .migration-row:hover { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="mb-4">Database Migration Tool</h1>
        
        <?php if (!$authenticated): ?>
            <!-- Login Form -->
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Authentication Required</h5>
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                            <?php endif; ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required autofocus>
                                </div>
                                <button type="submit" class="btn btn-primary">Login</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Migration Interface -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="?action=status" class="btn btn-info">Check Status</a>
                    <a href="?action=run" class="btn btn-success" onclick="return confirm('Run all pending migrations?')">Run Pending</a>
                    <a href="?action=run-all" class="btn btn-warning" onclick="return confirm('Run ALL migrations (including completed)?')">Run All</a>
                </div>
                <a href="?logout=1" class="btn btn-sm btn-secondary">Logout</a>
            </div>
            
            <?php
            if (isset($_GET['action'])) {
                echo '<div class="migration-output mb-4">';
                
                // Include the migration runner
                require_once dirname(__DIR__) . '/vendor/autoload.php';
                
                // Capture the output
                ob_start();
                
                // Set up arguments for the migration script
                $argv = ['migrate.php'];
                
                switch ($_GET['action']) {
                    case 'status':
                        $argv[] = '--status';
                        break;
                    case 'run':
                        // Default action
                        break;
                    case 'run-all':
                        $argv[] = '--run-all';
                        break;
                    case 'run-single':
                        if (isset($_GET['migration'])) {
                            $argv[] = '--run=' . $_GET['migration'];
                        }
                        break;
                }
                
                // Include and run the migration script
                $migrationScript = dirname(__DIR__) . '/migrate.php';
                if (file_exists($migrationScript)) {
                    // Override the script to not exit
                    eval(str_replace(['exit(0)', 'exit(1)', 'exit;'], 'return;', file_get_contents($migrationScript)));
                } else {
                    echo "Migration script not found!";
                }
                
                $output = ob_get_clean();
                
                // Convert ANSI colors to HTML
                $output = preg_replace('/\033\[31m(.*?)\033\[0m/', '<span class="status-error">$1</span>', $output);
                $output = preg_replace('/\033\[32m(.*?)\033\[0m/', '<span class="status-success">$1</span>', $output);
                $output = preg_replace('/\033\[33m(.*?)\033\[0m/', '<span class="status-pending">$1</span>', $output);
                $output = preg_replace('/\033\[0m/', '', $output);
                
                echo nl2br($output);
                echo '</div>';
            }
            ?>
            
            <!-- Migration List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Available Migrations</h5>
                </div>
                <div class="card-body">
                    <?php
                    // List all migrations
                    $migrationsPath = dirname(__DIR__) . '/database/migrations';
                    $migrations = glob($migrationsPath . '/*.sql');
                    
                    if (empty($migrations)) {
                        echo '<p class="text-muted">No migrations found.</p>';
                    } else {
                        echo '<div class="table-responsive">';
                        echo '<table class="table table-sm">';
                        echo '<thead><tr><th>Migration</th><th>Status</th><th>Action</th></tr></thead>';
                        echo '<tbody>';
                        
                        foreach ($migrations as $migration) {
                            $name = basename($migration);
                            echo '<tr class="migration-row">';
                            echo '<td>' . htmlspecialchars($name) . '</td>';
                            echo '<td><span class="badge bg-secondary">Unknown</span></td>';
                            echo '<td>';
                            echo '<a href="?action=run-single&migration=' . urlencode($name) . '" class="btn btn-sm btn-primary" onclick="return confirm(\'Run this migration?\')">Run</a>';
                            echo '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</tbody>';
                        echo '</table>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="mt-4">
                <div class="alert alert-warning">
                    <strong>Security Warning:</strong> This tool should be removed or properly secured in production environments.
                </div>
                <div class="alert alert-info">
                    <strong>CLI Alternative:</strong> You can also run migrations from the command line:
                    <pre class="mb-0 mt-2">php migrate.php --status
php migrate.php                    # Run pending
php migrate.php --run-all          # Run all</pre>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>