name: Generate Monthly Retrocession Invoices
description: Automates the generation of monthly invoices for practitioners with configured retrocession amounts
version: 1.0.0
author: Claude<PERSON><PERSON>

# Input parameters that can be provided when running the workflow
inputs:
  - name: month
    type: string
    description: Month to generate invoices for (format: MM)
    required: true
    default: "{{ current_month }}"
  
  - name: year
    type: string  
    description: Year to generate invoices for (format: YYYY)
    required: true
    default: "{{ current_year }}"
  
  - name: practitioner_ids
    type: array
    description: Specific practitioner IDs to process (optional, processes all if empty)
    required: false
    default: []

# Environment variables and configuration
env:
  database_url: "${DATABASE_URL}"
  app_base_url: "${APP_BASE_URL:-http://localhost/fit/public}"

# Main workflow steps
steps:
  - name: Check Database Connection
    type: shell
    command: |
      php -r "
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      try {
          \$pdo = new PDO(
              'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
              \$_ENV['DB_USER'],
              \$_ENV['DB_PASS']
          );
          echo 'Database connection successful';
      } catch (Exception \$e) {
          echo 'Database connection failed: ' . \$e->getMessage();
          exit(1);
      }
      "
    on_error: stop

  - name: Fetch Practitioners with Monthly Amounts
    type: shell
    command: |
      php -r "
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      \$month = '${inputs.month}';
      \$year = '${inputs.year}';
      
      \$pdo = new PDO(
          'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
          \$_ENV['DB_USER'],
          \$_ENV['DB_PASS']
      );
      
      // Get practitioners with monthly amounts configured
      \$sql = 'SELECT 
          u.id,
          u.first_name,
          u.last_name,
          c.id as client_id,
          uma.cns_amount,
          uma.patient_amount
      FROM users u
      JOIN clients c ON c.id = u.client_id
      JOIN user_monthly_retrocession_amounts uma ON uma.user_id = u.id
      WHERE c.is_practitioner = 1
      AND uma.month = :month
      AND (uma.cns_amount > 0 OR uma.patient_amount > 0)
      AND NOT EXISTS (
          SELECT 1 FROM invoices i
          JOIN retrocession_data_entry rde ON rde.invoice_id = i.id
          WHERE rde.user_id = u.id
          AND rde.month = :month
          AND rde.year = :year
          AND i.deleted_at IS NULL
      )';
      
      \$stmt = \$pdo->prepare(\$sql);
      \$stmt->execute(['month' => \$month, 'year' => \$year]);
      \$practitioners = \$stmt->fetchAll(PDO::FETCH_ASSOC);
      
      echo json_encode(['practitioners' => \$practitioners]);
      "
    output: practitioners_data

  - name: Generate Invoice for Each Practitioner
    type: foreach
    items: "{{ practitioners_data.practitioners }}"
    as: practitioner
    steps:
      - name: Create Invoice
        type: shell
        command: |
          php -r "
          require_once 'vendor/autoload.php';
          require_once 'app/Core/bootstrap.php';
          
          \$practitioner = json_decode('{{ json_encode(practitioner) }}', true);
          \$month = '${inputs.month}';
          \$year = '${inputs.year}';
          
          try {
              // Initialize app context
              \$userController = new App\Controllers\UserController();
              
              // Generate retrocession invoice
              \$_POST = [
                  'user_id' => \$practitioner['id'],
                  'month' => \$month,
                  'year' => \$year
              ];
              
              // Mock session for authentication
              \$_SESSION['user_id'] = 1; // Admin user
              
              ob_start();
              \$result = \$userController->generateRetrocession();
              \$output = ob_get_clean();
              
              \$response = json_decode(\$output, true);
              
              if (\$response && \$response['success']) {
                  echo json_encode([
                      'success' => true,
                      'practitioner' => \$practitioner['first_name'] . ' ' . \$practitioner['last_name'],
                      'invoice_id' => \$response['invoice_id'] ?? null,
                      'message' => 'Invoice generated successfully'
                  ]);
              } else {
                  echo json_encode([
                      'success' => false,
                      'practitioner' => \$practitioner['first_name'] . ' ' . \$practitioner['last_name'],
                      'error' => \$response['error'] ?? 'Unknown error'
                  ]);
              }
          } catch (Exception \$e) {
              echo json_encode([
                  'success' => false,
                  'practitioner' => \$practitioner['first_name'] . ' ' . \$practitioner['last_name'],
                  'error' => \$e->getMessage()
              ]);
          }
          "
        output: invoice_result
        continue_on_error: true

  - name: Generate Summary Report
    type: shell
    command: |
      php -r "
      \$results = json_decode('{{ json_encode(results) }}', true);
      
      \$successful = 0;
      \$failed = 0;
      \$summary = [];
      
      foreach (\$results as \$result) {
          if (\$result['success']) {
              \$successful++;
              \$summary[] = '✓ ' . \$result['practitioner'] . ' (Invoice #' . (\$result['invoice_id'] ?? 'N/A') . ')';
          } else {
              \$failed++;
              \$summary[] = '✗ ' . \$result['practitioner'] . ' - ' . \$result['error'];
          }
      }
      
      echo \"\\n=== Monthly Retrocession Invoice Generation Summary ===\\n\";
      echo \"Month: ${inputs.month}/${inputs.year}\\n\";
      echo \"Total Processed: \" . count(\$results) . \"\\n\";
      echo \"Successful: \$successful\\n\";
      echo \"Failed: \$failed\\n\\n\";
      echo \"Details:\\n\";
      echo implode(\"\\n\", \$summary);
      "

  - name: Send Notification Email (Optional)
    type: shell
    command: |
      php -r "
      // This step could send an email summary to administrators
      // You can implement email sending logic here if needed
      echo \"\\nWorkflow completed. Check the summary above for details.\\n\";
      "
    continue_on_error: true

# Error handling
on_error:
  - name: Log Error
    type: shell
    command: |
      echo "Error in workflow: {{ error_message }}" >> storage/logs/claude-flow-errors.log
      echo "Timestamp: $(date)" >> storage/logs/claude-flow-errors.log

# Success handling  
on_success:
  - name: Log Success
    type: shell
    command: |
      echo "Successfully completed monthly retrocession invoice generation" >> storage/logs/claude-flow-success.log
      echo "Timestamp: $(date)" >> storage/logs/claude-flow-success.log