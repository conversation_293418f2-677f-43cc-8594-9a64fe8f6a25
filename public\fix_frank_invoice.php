<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing <PERSON>'s Invoice #336</h2>";
    
    // First, get <PERSON>'s client_id
    $stmt = $db->prepare("
        SELECT c.id as client_id, c.name, u.first_name, u.last_name
        FROM users u
        JOIN clients c ON c.user_id = u.id
        WHERE u.id = 1
        LIMIT 1
    ");
    $stmt->execute();
    $frankClient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$frankClient) {
        echo "<p style='color: red;'>Error: Could not find <PERSON>'s client record!</p>";
        exit;
    }
    
    echo "<p>Found <PERSON>'s client: {$frankClient['name']} (ID: {$frankClient['client_id']})</p>";
    
    // Get the correct type_id for ret25
    $stmt = $db->prepare("SELECT id, prefix FROM config_invoice_types WHERE code = 'ret25'");
    $stmt->execute();
    $ret25Type = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$ret25Type) {
        echo "<p style='color: red;'>Error: ret25 type not found in config_invoice_types!</p>";
        echo "<p>Please run the create_missing_ret_types.php script first.</p>";
        exit;
    }
    
    echo "<p>Found ret25 type: ID {$ret25Type['id']}, Prefix: {$ret25Type['prefix']}</p>";
    
    // Update the invoice
    echo "<h3>Updating Invoice:</h3>";
    
    $stmt = $db->prepare("
        UPDATE invoices 
        SET type_id = :type_id,
            client_id = :client_id
        WHERE id = 336
    ");
    
    $stmt->execute([
        ':type_id' => $ret25Type['id'],
        ':client_id' => $frankClient['client_id']
    ]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Successfully updated invoice #336</p>";
        echo "<ul>";
        echo "<li>Set type_id to {$ret25Type['id']} (ret25)</li>";
        echo "<li>Set client_id to {$frankClient['client_id']}</li>";
        echo "</ul>";
        
        // Now we need to regenerate the invoice number
        echo "<h3>Regenerating Invoice Number:</h3>";
        
        // Get the next number for FAC-RET25
        $stmt = $db->prepare("
            SELECT invoice_number 
            FROM invoices 
            WHERE invoice_number LIKE 'FAC-RET25-%' 
            ORDER BY id DESC 
            LIMIT 1
        ");
        $stmt->execute();
        $lastRet25 = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($lastRet25) {
            // Extract the number and increment
            preg_match('/FAC-RET25-(\d+)-(\d+)/', $lastRet25['invoice_number'], $matches);
            $nextNumber = intval($matches[2]) + 1;
        } else {
            $nextNumber = 1;
        }
        
        $newInvoiceNumber = sprintf('FAC-RET25-2025-%04d', $nextNumber);
        
        $stmt = $db->prepare("UPDATE invoices SET invoice_number = :number WHERE id = 336");
        $stmt->execute([':number' => $newInvoiceNumber]);
        
        echo "<p style='color: green;'>✓ Updated invoice number to: <strong>{$newInvoiceNumber}</strong></p>";
        
        // Verify the changes
        echo "<h3>Verification:</h3>";
        $stmt = $db->prepare("
            SELECT i.*, cit.code, cit.prefix, c.name as client_name
            FROM invoices i
            LEFT JOIN config_invoice_types cit ON cit.id = i.type_id
            LEFT JOIN clients c ON c.id = i.client_id
            WHERE i.id = 336
        ");
        $stmt->execute();
        $updated = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><td><strong>Invoice Number:</strong></td><td>{$updated['invoice_number']}</td></tr>";
        echo "<tr><td><strong>Type Code:</strong></td><td>{$updated['code']}</td></tr>";
        echo "<tr><td><strong>Prefix:</strong></td><td>{$updated['prefix']}</td></tr>";
        echo "<tr><td><strong>Client:</strong></td><td>{$updated['client_name']}</td></tr>";
        echo "<tr><td><strong>Status:</strong></td><td>{$updated['status']}</td></tr>";
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>Frank's invoice has been fixed and now uses the correct FAC-RET25 prefix.</p>";
        echo "<p><a href='/fit/public/invoices/336' style='color: #155724; font-weight: bold;'>→ View the updated invoice</a></p>";
        echo "<p><a href='/fit/public/invoices' style='color: #155724; font-weight: bold;'>→ Return to invoice list</a></p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>No changes made - invoice may already be updated.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}