<?php
// Prevent session issues
if (session_status() === PHP_SESSION_ACTIVE) {
    session_write_close();
}

// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html><html><head><title>Email Templates Setup</title></head><body>";
    echo "<h2>Email Templates Setup</h2>";
    
    // Check if table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() == 0) {
        echo "<p>Creating email_templates table...</p>";
        
        $sql = "
CREATE TABLE IF NOT EXISTS `email_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `code` varchar(50) NOT NULL,
    `email_type` varchar(50) NOT NULL,
    `invoice_type` varchar(50) DEFAULT NULL,
    `subject` varchar(255) NOT NULL,
    `body_html` text NOT NULL,
    `body_text` text NOT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `priority` int(11) DEFAULT 100,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_unique_code` (`code`),
    KEY `idx_email_templates_type` (`email_type`),
    KEY `idx_email_templates_invoice_type` (`invoice_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $db->exec($sql);
        echo "<p>✓ Created email_templates table</p>";
    }
    
    // Check if email_logs table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
    if ($stmt->rowCount() == 0) {
        echo "<p>Creating email_logs table...</p>";
        
        $sql = "
CREATE TABLE IF NOT EXISTS `email_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `invoice_id` int(11) DEFAULT NULL,
    `template_id` int(11) DEFAULT NULL,
    `recipient_email` varchar(255) NOT NULL,
    `subject` varchar(255) DEFAULT NULL,
    `status` enum('sent','failed') NOT NULL,
    `sent_at` timestamp NULL DEFAULT NULL,
    `error_message` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_email_logs_invoice` (`invoice_id`),
    KEY `idx_email_logs_template` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $db->exec($sql);
        echo "<p>✓ Created email_logs table</p>";
    }
    
    // Check existing templates
    $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        echo "<p>Adding default email templates...</p>";
        
        // Template data
        $templates = [
            [
                'name' => 'Nouvelle facture - Standard',
                'code' => 'new_invoice_standard',
                'email_type' => 'new_invoice',
                'invoice_type' => null,
                'subject' => 'Facture {INVOICE_NUMBER} - Fit360',
                'body_html' => '<div style="font-family: Arial, sans-serif;">
<p>Bonjour {CLIENT_NAME},</p>
<p>Veuillez trouver ci-joint votre facture <strong>{INVOICE_NUMBER}</strong>.</p>
<p>Montant total : <strong>{TOTAL_AMOUNT} €</strong><br>
Date d\'échéance : <strong>{DUE_DATE}</strong></p>
<p>Cordialement,<br>Fit360 AdminDesk</p>
</div>',
                'body_text' => 'Bonjour {CLIENT_NAME},

Veuillez trouver ci-joint votre facture {INVOICE_NUMBER}.

Montant total : {TOTAL_AMOUNT} €
Date d\'échéance : {DUE_DATE}

Cordialement,
Fit360 AdminDesk',
                'is_active' => 1,
                'priority' => 100
            ],
            [
                'name' => 'Nouvelle facture - Location (LOY/DIV)',
                'code' => 'new_invoice_rental',
                'email_type' => 'new_invoice',
                'invoice_type' => 'rental',
                'subject' => 'Facture {INVOICE_NUMBER} - {SUBJECT}',
                'body_html' => '<div style="font-family: Arial, sans-serif;">
<p>Bonjour {CLIENT_NAME},</p>
<p>Veuillez trouver ci-joint votre facture pour la période <strong>{PERIOD}</strong>.</p>
<p>Numéro : <strong>{INVOICE_NUMBER}</strong><br>
Objet : <strong>{SUBJECT}</strong><br>
Montant TTC : <strong>{TOTAL_AMOUNT} €</strong><br>
Date d\'échéance : <strong>{DUE_DATE}</strong></p>
<p>Cordialement,<br>Fit360 AdminDesk</p>
</div>',
                'body_text' => 'Bonjour {CLIENT_NAME},

Veuillez trouver ci-joint votre facture pour la période {PERIOD}.

Numéro : {INVOICE_NUMBER}
Objet : {SUBJECT}
Montant TTC : {TOTAL_AMOUNT} €
Date d\'échéance : {DUE_DATE}

Cordialement,
Fit360 AdminDesk',
                'is_active' => 1,
                'priority' => 110
            ]
        ];
        
        // Insert templates
        $stmt = $db->prepare("
            INSERT INTO email_templates (
                name, code, email_type, invoice_type,
                subject, body_html, body_text,
                is_active, priority
            ) VALUES (
                :name, :code, :email_type, :invoice_type,
                :subject, :body_html, :body_text,
                :is_active, :priority
            )
        ");
        
        foreach ($templates as $template) {
            try {
                $stmt->execute($template);
                echo "<p>✓ Added: {$template['name']}</p>";
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) {
                    echo "<p>⚠️ Already exists: {$template['name']}</p>";
                } else {
                    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
                }
            }
        }
    } else {
        echo "<p>Templates already exist ({$result['count']} found)</p>";
        
        // Show existing
        $stmt = $db->query("SELECT * FROM email_templates");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Active</th></tr>";
        foreach ($templates as $t) {
            echo "<tr>";
            echo "<td>{$t['id']}</td>";
            echo "<td>{$t['name']}</td>";
            echo "<td>{$t['email_type']}</td>";
            echo "<td>" . ($t['is_active'] ? '✓' : '✗') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Add company config
    echo "<h3>Company Configuration</h3>";
    $configs = [
        'company_name' => 'Fit360 AdminDesk',
        'company_email' => '<EMAIL>',
        'company_phone' => '+352 26 12 34 56'
    ];
    
    foreach ($configs as $key => $value) {
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        $existing = $stmt->fetch(PDO::FETCH_COLUMN);
        
        if (!$existing) {
            $stmt = $db->prepare("INSERT INTO config (`key`, `value`) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
            echo "<p>✓ Added: $key</p>";
        } else {
            echo "<p>$key = $existing</p>";
        }
    }
    
    echo "<hr><p><strong>Email system ready!</strong></p>";
    echo "<p><a href='/fit/public/'>Back to App</a></p>";
    echo "</body></html>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}