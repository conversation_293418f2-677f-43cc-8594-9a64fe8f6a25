<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Creating Missing RET25 and RET30 Types</h2>";
    
    // Types to create
    $typesToCreate = [
        [
            'code' => 'ret25',
            'prefix' => 'FAC-RET25',
            'name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%']),
            'color' => '#28a745',
            'is_active' => 1
        ],
        [
            'code' => 'ret30',
            'prefix' => 'FAC-RET30',
            'name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%']),
            'color' => '#007bff',
            'is_active' => 1
        ]
    ];
    
    $stmt = $db->prepare("
        INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
        VALUES (:code, :prefix, :name, :color, :is_active)
    ");
    
    foreach ($typesToCreate as $type) {
        try {
            $stmt->execute($type);
            $name = json_decode($type['name'], true);
            echo "<p style='color: green;'>✓ Successfully created {$type['code']} - {$type['prefix']} - {$name['fr']}</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Error creating {$type['code']}: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // Also ensure they exist in invoice_types table
    echo "<h3>Ensuring invoice_types Table:</h3>";
    
    $invoiceTypes = [
        ['code' => 'RET25', 'name' => 'Rétrocession 5%'],
        ['code' => 'RET30', 'name' => 'Rétrocession 10%']
    ];
    
    foreach ($invoiceTypes as $type) {
        // Check if exists
        $checkStmt = $db->prepare("SELECT id FROM invoice_types WHERE code = :code");
        $checkStmt->execute(['code' => $type['code']]);
        
        if (!$checkStmt->fetch()) {
            $insertStmt = $db->prepare("
                INSERT INTO invoice_types (code, name, is_active)
                VALUES (:code, :name, 1)
            ");
            try {
                $insertStmt->execute($type);
                echo "<p style='color: green;'>✓ Created {$type['code']} in invoice_types</p>";
            } catch (PDOException $e) {
                echo "<p style='color: orange;'>Note: {$type['code']} may already exist in invoice_types</p>";
            }
        } else {
            echo "<p>✓ {$type['code']} already exists in invoice_types</p>";
        }
    }
    
    // Show final configuration
    echo "<h3>Final Configuration:</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30') 
        ORDER BY 
            CASE code 
                WHEN 'ret' THEN 1 
                WHEN 'ret25' THEN 2 
                WHEN 'ret30' THEN 3 
            END
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Color</th><th>Active</th>";
    echo "</tr>";
    
    foreach ($types as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $rowStyle = '';
        if ($row['code'] == 'ret25') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['code'] == 'ret30') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td><span style='display: inline-block; width: 20px; height: 20px; background-color: {$row['color']}; border: 1px solid #ccc;'></span></td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (count($types) == 3) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
        echo "<p>All retrocession types are now configured correctly.</p>";
        echo "<p><a href='/fit/public/verify_ret_final.php' style='color: #155724; font-weight: bold;'>→ Verify the configuration</a></p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}