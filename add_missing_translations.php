<?php
require_once 'vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4',
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>Add Missing Translations for Bulk Generation</h2>\n";
    
    // Missing translation keys
    $translations = [
        // Page titles
        ['key' => 'invoices.bulk_generation', 'en' => 'Bulk Invoice Generation', 'fr' => 'Génération groupée de factures'],
        ['key' => 'invoices.generate_all_invoice_types', 'en' => 'Generate invoices for all types', 'fr' => 'Générer des factures pour tous les types'],
        
        // Tab labels
        ['key' => 'invoices.loyer', 'en' => 'Rent', 'fr' => 'Loyer'],
        ['key' => 'invoices.courses', 'en' => 'Courses', 'fr' => 'Cours'],
        
        // Table headers
        ['key' => 'invoices.practitioners_with_amounts', 'en' => 'Practitioners with amounts', 'fr' => 'Praticiens avec montants'],
        ['key' => 'invoices.practitioner', 'en' => 'Practitioner', 'fr' => 'Praticien'],
        ['key' => 'invoices.secretary', 'en' => 'Secretary', 'fr' => 'Secrétariat'],
        ['key' => 'invoices.coach', 'en' => 'Coach', 'fr' => 'Coach'],
        ['key' => 'invoices.coaches_with_courses', 'en' => 'Coaches with recorded courses', 'fr' => 'Coachs avec cours enregistrés'],
        ['key' => 'invoices.no_coaches_with_courses', 'en' => 'No coaches with courses for this period', 'fr' => 'Aucun coach avec des cours pour cette période'],
        
        // Month names
        ['key' => 'common.month.1', 'en' => 'January', 'fr' => 'Janvier'],
        ['key' => 'common.month.2', 'en' => 'February', 'fr' => 'Février'],
        ['key' => 'common.month.3', 'en' => 'March', 'fr' => 'Mars'],
        ['key' => 'common.month.4', 'en' => 'April', 'fr' => 'Avril'],
        ['key' => 'common.month.5', 'en' => 'May', 'fr' => 'Mai'],
        ['key' => 'common.month.6', 'en' => 'June', 'fr' => 'Juin'],
        ['key' => 'common.month.7', 'en' => 'July', 'fr' => 'Juillet'],
        ['key' => 'common.month.8', 'en' => 'August', 'fr' => 'Août'],
        ['key' => 'common.month.9', 'en' => 'September', 'fr' => 'Septembre'],
        ['key' => 'common.month.10', 'en' => 'October', 'fr' => 'Octobre'],
        ['key' => 'common.month.11', 'en' => 'November', 'fr' => 'Novembre'],
        ['key' => 'common.month.12', 'en' => 'December', 'fr' => 'Décembre'],
        
        // Actions
        ['key' => 'invoices.load_data', 'en' => 'Load Data', 'fr' => 'Charger les données'],
        ['key' => 'invoices.invoices_generated', 'en' => 'Invoices generated', 'fr' => 'Factures générées'],
        ['key' => 'invoices.total_sessions', 'en' => 'Total sessions', 'fr' => 'Sessions totales'],
    ];
    
    // Check and add translations
    $added = 0;
    $existing = 0;
    
    foreach ($translations as $trans) {
        // Check if translation exists
        $stmt = $pdo->prepare("SELECT id FROM translations WHERE `key` = :key");
        $stmt->execute(['key' => $trans['key']]);
        
        if ($stmt->rowCount() > 0) {
            $existing++;
            echo "✓ Translation already exists: {$trans['key']}\n";
        } else {
            // Add translation
            $stmt = $pdo->prepare("
                INSERT INTO translations (`key`, value, created_at, updated_at)
                VALUES (:key, :value, NOW(), NOW())
            ");
            
            $value = json_encode(['en' => $trans['en'], 'fr' => $trans['fr']]);
            $stmt->execute(['key' => $trans['key'], 'value' => $value]);
            
            $added++;
            echo "✅ Added translation: {$trans['key']} - FR: {$trans['fr']}\n";
        }
    }
    
    echo "\n<h3>Summary:</h3>\n";
    echo "- Added: $added translations\n";
    echo "- Already existed: $existing translations\n";
    
    if ($added > 0) {
        echo "\n<p style='color: green;'>✅ Translations added successfully!</p>\n";
        echo "<p>The page should now display correctly in French.</p>\n";
    }
    
    echo "\n<p><a href='{$_ENV['APP_BASE_URL']}/invoices/bulk-generation'>Return to Bulk Generation</a></p>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}