<?php
/**
 * Test Invoice Email with PDF Attachment
 * This script tests sending invoice emails with PDF attachments
 */

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        
        // Handle variable references like ${APP_NAME}
        if (preg_match('/\$\{(.+?)\}/', $value, $matches)) {
            $varName = $matches[1];
            if (isset($_ENV[$varName])) {
                $value = str_replace($matches[0], $_ENV[$varName], $value);
            }
        }
        
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Load composer autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Load the actual application bootstrap to get everything properly initialized
require_once __DIR__ . '/app/config/bootstrap.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use App\Models\Invoice;
use App\Services\PdfService;
use App\Services\EmailService;

$invoiceId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Invoice Email with PDF Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; border: 1px solid #ddd; border-radius: 4px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
        .button { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #28a745; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin: 5px;
        }
        .button:hover { background: #218838; }
        .debug-output { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Invoice Email with PDF Test</h1>
        
        <?php
        if ($invoiceId) {
            // Test sending email using the real EmailService
            echo "<h2>Testing email for invoice ID: $invoiceId</h2>";
            
            try {
                // First, get invoice details to show
                $invoiceModel = new Invoice();
                $invoiceData = $invoiceModel->getInvoiceWithDetails($invoiceId);
                
                if (!$invoiceData) {
                    echo "<p class='error'>Invoice not found!</p>";
                    echo "<p><a href='?'>Back to list</a></p>";
                    exit;
                }
                
                // Display invoice info
                echo "<table>";
                echo "<tr><th>Invoice Number</th><td>{$invoiceData['invoice_number']}</td></tr>";
                echo "<tr><th>Status</th><td>{$invoiceData['status']}</td></tr>";
                echo "<tr><th>Total</th><td>" . number_format($invoiceData['total'], 2) . " EUR</td></tr>";
                
                // Determine recipient
                $recipientEmail = null;
                $recipientName = null;
                
                if (!empty($invoiceData['client']['email'])) {
                    $recipientEmail = $invoiceData['client']['email'];
                    $recipientName = $invoiceData['client']['name'] ?? 'Client';
                } elseif (!empty($invoiceData['user']['email'])) {
                    $recipientEmail = $invoiceData['user']['email'];
                    $recipientName = trim(($invoiceData['user']['first_name'] ?? '') . ' ' . ($invoiceData['user']['last_name'] ?? ''));
                }
                
                echo "<tr><th>Recipient</th><td>" . htmlspecialchars($recipientName ?: 'Unknown') . "</td></tr>";
                echo "<tr><th>Email</th><td>" . ($recipientEmail ? htmlspecialchars($recipientEmail) : '<span class="error">NO EMAIL</span>') . "</td></tr>";
                echo "</table>";
                
                if (!$recipientEmail) {
                    echo "<p class='error'>Cannot send email - recipient has no email address!</p>";
                    echo "<p><a href='?'>Back to list</a></p>";
                    exit;
                }
                
                if (isset($_GET['send'])) {
                    echo "<h3>Sending Email with PDF...</h3>";
                    echo "<div class='debug-output'>";
                    
                    // Enable error capturing
                    $oldErrorHandler = set_error_handler(function($errno, $errstr, $errfile, $errline) {
                        echo "<p class='warning'>PHP Notice: " . htmlspecialchars($errstr) . "</p>";
                        return true;
                    });
                    
                    try {
                        // Use the real EmailService
                        $emailService = new EmailService();
                        
                        echo "<p class='info'>Using EmailService to send invoice email...</p>";
                        
                        // Send the email - this should include PDF attachment automatically
                        $result = $emailService->sendInvoiceEmail($invoiceId, $recipientEmail);
                        
                        echo "<h4>Result:</h4>";
                        echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                        
                        if ($result['success']) {
                            echo "<p class='success'>✓ Email sent successfully with PDF attachment!</p>";
                            echo "<p class='info'>The email should include the invoice PDF as an attachment.</p>";
                            echo "<p><a href='http://localhost:8025' target='_blank' class='button'>View in Mailhog</a></p>";
                        } else {
                            echo "<p class='error'>✗ Email failed: " . htmlspecialchars($result['message']) . "</p>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<p class='error'>✗ Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
                        echo "<details><summary>Stack trace</summary><pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre></details>";
                    }
                    
                    // Restore error handler
                    restore_error_handler();
                    
                    echo "</div>";
                    
                    // Check email logs
                    echo "<h3>Email Log Entry:</h3>";
                    $db = Flight::db();
                    $stmt = $db->prepare("
                        SELECT * FROM email_logs 
                        WHERE invoice_id = :invoice_id 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    ");
                    $stmt->execute([':invoice_id' => $invoiceId]);
                    $log = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($log) {
                        echo "<table>";
                        echo "<tr><th>Status</th><td class='" . ($log['status'] === 'sent' ? 'success' : 'error') . "'>{$log['status']}</td></tr>";
                        echo "<tr><th>Sent At</th><td>" . ($log['sent_at'] ?: '-') . "</td></tr>";
                        echo "<tr><th>Attachments</th><td>" . htmlspecialchars($log['attachments_sent'] ?: 'None') . "</td></tr>";
                        if ($log['error_message']) {
                            echo "<tr><th>Error</th><td class='error'>" . htmlspecialchars($log['error_message']) . "</td></tr>";
                        }
                        echo "</table>";
                    }
                    
                    echo "<p><a href='?'>Back to list</a></p>";
                    
                } else {
                    echo "<p><a href='?id=$invoiceId&send=1' class='button'>Send Email with PDF Now</a></p>";
                    echo "<p><a href='?'>Back to list</a></p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "<p><a href='?'>Back to list</a></p>";
            }
            
        } else {
            // Show invoice list
            echo "<h2>Select an Invoice to Test</h2>";
            echo "<p>This tool uses the actual EmailService which automatically includes PDF attachments.</p>";
            
            $db = Flight::db();
            $stmt = $db->query("
                SELECT i.*, 
                       u.email as user_email, CONCAT(u.first_name, ' ', u.last_name) as user_name,
                       c.email as client_email, c.name as client_name
                FROM invoices i
                LEFT JOIN users u ON i.user_id = u.id
                LEFT JOIN clients c ON i.client_id = c.id
                ORDER BY i.created_at DESC
                LIMIT 20
            ");
            $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Recipient</th><th>Email</th><th>Status</th><th>Action</th></tr>";
            
            foreach ($invoices as $inv) {
                $recipientName = $inv['user_id'] ? $inv['user_name'] : $inv['client_name'];
                $email = $inv['user_id'] ? $inv['user_email'] : $inv['client_email'];
                
                echo "<tr>";
                echo "<td>{$inv['id']}</td>";
                echo "<td>{$inv['invoice_number']}</td>";
                echo "<td>" . htmlspecialchars($recipientName ?: 'Unknown') . "</td>";
                echo "<td>" . ($email ? htmlspecialchars($email) : '<span style="color:red">NO EMAIL</span>') . "</td>";
                echo "<td>{$inv['status']}</td>";
                echo "<td>";
                if ($email) {
                    echo "<a href='?id={$inv['id']}' class='button' style='padding: 5px 10px; font-size: 14px;'>Test Email</a>";
                } else {
                    echo "<span style='color:red'>No email</span>";
                }
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<hr>";
            echo "<h3>How it works:</h3>";
            echo "<ol>";
            echo "<li>Select an invoice from the list above</li>";
            echo "<li>Click 'Send Email with PDF Now'</li>";
            echo "<li>The EmailService will generate and attach the PDF automatically</li>";
            echo "<li>Check Mailhog to see the email with PDF attachment</li>";
            echo "</ol>";
        }
        ?>
    </div>
</body>
</html>