<?php
/**
 * Check Email Templates Status and Configuration
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Templates Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .template-preview { background: white; border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Email Templates Status</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // Check if table exists
        echo "<h2>1. Email Templates Table</h2>";
        $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
        if (!$stmt->fetch()) {
            echo '<div class="error">❌ email_templates table DOES NOT EXIST!</div>';
            
            // Check for migration file
            $migrationFile = __DIR__ . '/../database/migrations/079_create_email_templates_table.sql';
            if (file_exists($migrationFile)) {
                echo '<div class="info">';
                echo '<p>Migration file found. To create the table, run:</p>';
                echo '<pre>' . htmlspecialchars(file_get_contents($migrationFile)) . '</pre>';
                echo '</div>';
                
                // Try to create it
                if (isset($_POST['create_table'])) {
                    try {
                        $sql = file_get_contents($migrationFile);
                        $db->exec($sql);
                        echo '<div class="success">✅ Table created successfully! Refresh the page.</div>';
                    } catch (Exception $e) {
                        echo '<div class="error">Failed to create table: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    }
                } else {
                    echo '<form method="POST">';
                    echo '<button type="submit" name="create_table" value="1">Create email_templates Table</button>';
                    echo '</form>';
                }
            } else {
                echo '<div class="error">Migration file not found!</div>';
            }
            
            exit;
        }
        
        echo '<div class="success">✅ email_templates table exists</div>';
        
        // Show table structure
        $stmt = $db->query("SHOW COLUMNS FROM email_templates");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Table Structure:</h3>";
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($col['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 2. Check existing templates
        echo "<h2>2. Existing Email Templates</h2>";
        $stmt = $db->query("SELECT * FROM email_templates ORDER BY email_type, invoice_type, priority DESC");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($templates) {
            echo "<p>Found " . count($templates) . " template(s):</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Email Type</th><th>Invoice Type</th><th>Active</th><th>Priority</th><th>Actions</th></tr>";
            foreach ($templates as $tpl) {
                echo "<tr>";
                echo "<td>{$tpl['id']}</td>";
                echo "<td>" . htmlspecialchars($tpl['name']) . "</td>";
                echo "<td>{$tpl['email_type']}</td>";
                echo "<td>" . ($tpl['invoice_type'] ?: 'All') . "</td>";
                echo "<td>" . ($tpl['is_active'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</td>";
                echo "<td>{$tpl['priority']}</td>";
                echo "<td><a href='#template-{$tpl['id']}'>View</a></td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show template details
            foreach ($templates as $tpl) {
                echo "<div class='template-preview' id='template-{$tpl['id']}'>";
                echo "<h3>Template: " . htmlspecialchars($tpl['name']) . "</h3>";
                echo "<p><strong>Subject:</strong> " . htmlspecialchars($tpl['subject']) . "</p>";
                echo "<p><strong>Variables:</strong> " . htmlspecialchars($tpl['available_variables'] ?: 'Not specified') . "</p>";
                echo "<h4>HTML Preview:</h4>";
                echo "<div style='border: 1px solid #ccc; padding: 10px;'>";
                echo $tpl['body_html'];
                echo "</div>";
                echo "<h4>Text Preview:</h4>";
                echo "<pre>" . htmlspecialchars($tpl['body_text']) . "</pre>";
                echo "</div>";
            }
        } else {
            echo '<div class="error">❌ No email templates found!</div>';
        }
        
        // 3. Check for required templates
        echo "<h2>3. Required Templates Check</h2>";
        $requiredTemplates = [
            'new_invoice' => ['rental', 'DIV', 'retrocession_30', 'retrocession_25', null],
            'invoice_reminder' => [null],
            'payment_received' => [null]
        ];
        
        foreach ($requiredTemplates as $emailType => $invoiceTypes) {
            echo "<h4>" . ucfirst(str_replace('_', ' ', $emailType)) . ":</h4>";
            echo "<ul>";
            foreach ($invoiceTypes as $invoiceType) {
                $stmt = $db->prepare("
                    SELECT COUNT(*) as count 
                    FROM email_templates 
                    WHERE email_type = :email_type 
                    AND (invoice_type = :invoice_type OR (:invoice_type IS NULL AND invoice_type IS NULL))
                    AND is_active = 1
                ");
                $stmt->execute([
                    ':email_type' => $emailType,
                    ':invoice_type' => $invoiceType
                ]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $typeLabel = $invoiceType ?: 'General';
                if ($result['count'] > 0) {
                    echo "<li class='success'>✅ $typeLabel: {$result['count']} active template(s)</li>";
                } else {
                    echo "<li class='error'>❌ $typeLabel: No active templates</li>";
                }
            }
            echo "</ul>";
        }
        
        // 4. Initialize default templates if needed
        echo "<h2>4. Initialize Default Templates</h2>";
        
        if (empty($templates) || isset($_POST['init_templates'])) {
            if (isset($_POST['init_templates'])) {
                // Create default templates
                $defaultTemplates = [
                    [
                        'name' => 'Default Invoice Email',
                        'code' => 'default_invoice',
                        'email_type' => 'new_invoice',
                        'invoice_type' => null,
                        'subject' => 'Invoice {INVOICE_NUMBER} - {SUBJECT}',
                        'body_html' => '<h2>Invoice {INVOICE_NUMBER}</h2>
<p>Dear {CLIENT_NAME},</p>
<p>Please find attached your invoice for {SUBJECT}.</p>
<p><strong>Amount:</strong> {TOTAL_AMOUNT} EUR<br>
<strong>Due Date:</strong> {DUE_DATE}</p>
<p>Thank you for your business.</p>
<p>Best regards,<br>{COMPANY_NAME}</p>',
                        'body_text' => 'Invoice {INVOICE_NUMBER}

Dear {CLIENT_NAME},

Please find attached your invoice for {SUBJECT}.

Amount: {TOTAL_AMOUNT} EUR
Due Date: {DUE_DATE}

Thank you for your business.

Best regards,
{COMPANY_NAME}',
                        'available_variables' => '{INVOICE_NUMBER}, {CLIENT_NAME}, {SUBJECT}, {TOTAL_AMOUNT}, {DUE_DATE}, {COMPANY_NAME}',
                        'priority' => 1,
                        'is_active' => 1
                    ],
                    [
                        'name' => 'Rental Invoice Email',
                        'code' => 'rental_invoice',
                        'email_type' => 'new_invoice',
                        'invoice_type' => 'rental',
                        'subject' => 'Facture {INVOICE_NUMBER} - {PERIOD}',
                        'body_html' => '<h2>Facture {INVOICE_NUMBER}</h2>
<p>Bonjour {CLIENT_NAME},</p>
<p>Veuillez trouver ci-joint votre facture pour {PERIOD}.</p>
<p><strong>Montant:</strong> {TOTAL_AMOUNT} EUR<br>
<strong>Date d\'échéance:</strong> {DUE_DATE}</p>
<p>Cordialement,<br>{COMPANY_NAME}</p>',
                        'body_text' => 'Facture {INVOICE_NUMBER}

Bonjour {CLIENT_NAME},

Veuillez trouver ci-joint votre facture pour {PERIOD}.

Montant: {TOTAL_AMOUNT} EUR
Date d\'échéance: {DUE_DATE}

Cordialement,
{COMPANY_NAME}',
                        'available_variables' => '{INVOICE_NUMBER}, {CLIENT_NAME}, {PERIOD}, {TOTAL_AMOUNT}, {DUE_DATE}, {COMPANY_NAME}',
                        'priority' => 2,
                        'is_active' => 1
                    ]
                ];
                
                $success = 0;
                foreach ($defaultTemplates as $template) {
                    try {
                        $stmt = $db->prepare("
                            INSERT INTO email_templates (
                                name, code, email_type, invoice_type, subject, 
                                body_html, body_text, available_variables, 
                                priority, is_active, created_at
                            ) VALUES (
                                :name, :code, :email_type, :invoice_type, :subject,
                                :body_html, :body_text, :available_variables,
                                :priority, :is_active, NOW()
                            )
                        ");
                        $stmt->execute($template);
                        $success++;
                    } catch (Exception $e) {
                        echo '<div class="error">Failed to create template "' . $template['name'] . '": ' . $e->getMessage() . '</div>';
                    }
                }
                
                if ($success > 0) {
                    echo '<div class="success">✅ Created ' . $success . ' default template(s). Refresh the page to see them.</div>';
                }
            } else {
                echo '<div class="info">';
                echo '<p>No templates found. Would you like to create default templates?</p>';
                echo '<form method="POST">';
                echo '<button type="submit" name="init_templates" value="1">Initialize Default Templates</button>';
                echo '</form>';
                echo '</div>';
            }
        } else {
            echo '<p>Templates are configured. To reinitialize, delete existing templates first.</p>';
        }
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<h3>Error:</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/debug_invoice_0190_email.php">Debug Invoice 0190</a> |
        <a href="/fit/public/test_invoice_279_email.php">Test Email Send</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>