<?php
/**
 * Fix EmailService logEmail method
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix EmailService</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .code { background: #f5f5f5; padding: 10px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Fix EmailService logEmail Method</h1>
    
    <?php
    $emailServicePath = __DIR__ . '/../app/services/EmailService.php';
    
    if (!file_exists($emailServicePath)) {
        echo "<p class='error'>EmailService.php not found!</p>";
        exit;
    }
    
    // Read the current file
    $content = file_get_contents($emailServicePath);
    
    // Find the sendInvoiceEmail method's catch block
    $oldCatch = "        } catch (Exception \$e) {
            return [
                'success' => false, 
                'message' => \$e->getMessage(),
                'subject' => \$params['subject'] ?? 'Invoice Email'
            ];
        }";
    
    $newCatch = "        } catch (Exception \$e) {
            // Log the error
            error_log('EmailService: sendInvoiceEmail error - ' . \$e->getMessage());
            
            // Still try to log the failed email attempt
            \$errorResult = [
                'success' => false, 
                'message' => \$e->getMessage(),
                'subject' => 'Invoice Email',
                'body_text' => '',
                'body_html' => '',
                'attachments' => []
            ];
            
            // Try to log the failed attempt
            \$this->logEmail(\$invoiceId, null, \$recipientEmail ?? 'unknown', \$errorResult);
            
            return \$errorResult;
        }";
    
    // Check if we need to fix it
    if (strpos($content, $oldCatch) !== false) {
        // Create backup
        $backupPath = $emailServicePath . '.backup.' . date('YmdHis');
        copy($emailServicePath, $backupPath);
        echo "<p class='success'>✓ Created backup at: " . basename($backupPath) . "</p>";
        
        // Apply fix
        $newContent = str_replace($oldCatch, $newCatch, $content);
        
        if (file_put_contents($emailServicePath, $newContent)) {
            echo "<p class='success'>✓ Fixed EmailService.php - catch block updated to handle errors properly</p>";
            echo "<div class='code'>New catch block:
" . htmlspecialchars($newCatch) . "</div>";
        } else {
            echo "<p class='error'>Failed to write changes!</p>";
        }
    } else {
        echo "<p>Checking current implementation...</p>";
        
        // Look for the current catch block
        if (preg_match('/} catch \(Exception \$e\) \{[\s\S]+?return \[[\s\S]+?\];\s+\}/m', $content, $matches)) {
            echo "<p>Current catch block:</p>";
            echo "<div class='code'>" . htmlspecialchars($matches[0]) . "</div>";
            
            // Check if it already has the fix
            if (strpos($matches[0], 'logEmail') !== false) {
                echo "<p class='success'>✓ EmailService already has error logging in catch block</p>";
            } else {
                echo "<p class='error'>❌ Catch block doesn't log failed attempts</p>";
            }
        }
    }
    
    // Now check the actual SQL error
    echo "<h2>Checking SQL Parameter Issue</h2>";
    
    // The issue might be in the database - check if all columns exist
    try {
        $db = Flight::db();
        $stmt = $db->query("SHOW COLUMNS FROM email_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p>email_logs columns: " . implode(', ', $columns) . "</p>";
        
        // Check for any missing columns that the INSERT expects
        $expectedColumns = ['invoice_id', 'template_id', 'recipient_type', 'recipient_email', 
                          'subject', 'body_preview', 'attachments_sent', 'status', 
                          'sent_at', 'error_message'];
        
        $missingColumns = array_diff($expectedColumns, $columns);
        if (!empty($missingColumns)) {
            echo "<p class='error'>Missing columns: " . implode(', ', $missingColumns) . "</p>";
        } else {
            echo "<p class='success'>✓ All expected columns exist</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>Database error: " . $e->getMessage() . "</p>";
    }
    
    // Test the email sending again
    echo "<h2>Test Email Send Again</h2>";
    echo "<p><a href='/fit/public/test_invoice_279_email.php'>Test Invoice 279 Email Again</a></p>";
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/check_email_logs_table.php">Check Email Logs</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>