<?php
/**
 * Check invoice table structure
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Invoice Table Structure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .info { background: #e8f4f8; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Invoice Table Structure</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // Get all columns from invoices table
        echo "<h2>Invoices Table Columns</h2>";
        $stmt = $db->query("SHOW COLUMNS FROM invoices");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($col['Field']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($col['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($col['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Look for total-related columns
        echo "<h2>Total/Amount Related Columns</h2>";
        echo "<div class='info'>";
        $totalColumns = array_filter($columns, function($col) {
            return stripos($col['Field'], 'total') !== false || 
                   stripos($col['Field'], 'amount') !== false ||
                   stripos($col['Field'], 'ttc') !== false ||
                   stripos($col['Field'], 'ht') !== false;
        });
        
        if ($totalColumns) {
            echo "<p>Found these total/amount columns:</p>";
            echo "<ul>";
            foreach ($totalColumns as $col) {
                echo "<li><strong>{$col['Field']}</strong> ({$col['Type']})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No columns with 'total', 'amount', 'ttc', or 'ht' in their names found.</p>";
        }
        echo "</div>";
        
        // Show sample invoice data
        echo "<h2>Sample Invoice Data (FAC-DIV-2025-0190)</h2>";
        $stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = :number LIMIT 1");
        $stmt->execute([':number' => 'FAC-DIV-2025-0190']);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "<div class='info'>";
            echo "<pre>";
            print_r($invoice);
            echo "</pre>";
            echo "</div>";
        } else {
            echo "<p>Invoice FAC-DIV-2025-0190 not found.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    ?>
    
    <hr>
    <p><a href="/fit/public/">Back to Dashboard</a></p>
</body>
</html>