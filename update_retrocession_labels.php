<?php
/**
 * <PERSON>ript to update retrocession labels from uppercase to proper case
 * 
 * Usage:
 *   php update_retrocession_labels.php           # Preview changes (dry-run)
 *   php update_retrocession_labels.php --execute # Apply changes
 *   php update_retrocession_labels.php --rollback # Restore original values
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Parse command line arguments
$options = getopt('', ['execute', 'rollback', 'help']);
$isDryRun = !isset($options['execute']) && !isset($options['rollback']);
$isRollback = isset($options['rollback']);
$showHelp = isset($options['help']);

// Colors for output
$GREEN = "\033[0;32m";
$RED = "\033[0;31m";
$YELLOW = "\033[0;33m";
$BLUE = "\033[0;34m";
$RESET = "\033[0m";

// Show help
if ($showHelp) {
    echo "{$BLUE}Retrocession Labels Update Script{$RESET}\n\n";
    echo "This script updates retrocession labels from uppercase to proper case.\n\n";
    echo "Usage:\n";
    echo "  php update_retrocession_labels.php           # Preview changes (dry-run)\n";
    echo "  php update_retrocession_labels.php --execute # Apply changes\n";
    echo "  php update_retrocession_labels.php --rollback # Restore original values\n";
    echo "  php update_retrocession_labels.php --help    # Show this help\n\n";
    echo "Label mappings:\n";
    echo "  RÉTROCESSION CNS      -> Rétrocession CNS\n";
    echo "  RÉTROCESSION PATIENTS -> Rétrocession PATIENTS\n";
    echo "  RÉTROCESSION          -> Rétrocession\n";
    exit(0);
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "{$GREEN}✓ Database connected successfully{$RESET}\n\n";
} catch (Exception $e) {
    die("{$RED}✗ Database connection failed: " . $e->getMessage() . "{$RESET}\n");
}

// Define label mappings
$labelMappings = [
    'cns_label' => [
        'RÉTROCESSION CNS' => 'Rétrocession CNS',
        'RÉTROCESSION' => 'Rétrocession'
    ],
    'patient_label' => [
        'RÉTROCESSION PATIENTS' => 'Rétrocession PATIENTS'
    ]
];

// Rollback function
if ($isRollback) {
    echo "{$YELLOW}=== Rollback Mode ==={$RESET}\n\n";
    
    // Check if backup table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_retrocession_settings_backup'");
    if ($stmt->rowCount() === 0) {
        die("{$RED}✗ No backup table found. Cannot rollback.{$RESET}\n");
    }
    
    // Get backup data
    $stmt = $pdo->query("
        SELECT b.id, b.cns_label as old_cns, b.patient_label as old_patient,
               s.cns_label as current_cns, s.patient_label as current_patient
        FROM user_retrocession_settings_backup b
        JOIN user_retrocession_settings s ON s.id = b.id
    ");
    
    $toRestore = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($toRestore)) {
        echo "{$YELLOW}No records to restore.{$RESET}\n";
        exit(0);
    }
    
    echo "Found " . count($toRestore) . " records to restore:\n\n";
    
    foreach ($toRestore as $record) {
        echo "ID {$record['id']}:\n";
        if ($record['current_cns'] !== $record['old_cns']) {
            echo "  CNS: {$record['current_cns']} -> {$record['old_cns']}\n";
        }
        if ($record['current_patient'] !== $record['old_patient']) {
            echo "  Patient: {$record['current_patient']} -> {$record['old_patient']}\n";
        }
    }
    
    echo "\n{$YELLOW}Restore these values? (yes/no):{$RESET} ";
    $confirm = trim(fgets(STDIN));
    
    if (strtolower($confirm) !== 'yes') {
        echo "{$RED}Rollback cancelled.{$RESET}\n";
        exit(0);
    }
    
    // Perform rollback
    $pdo->beginTransaction();
    try {
        $stmt = $pdo->prepare("
            UPDATE user_retrocession_settings s
            JOIN user_retrocession_settings_backup b ON s.id = b.id
            SET s.cns_label = b.cns_label,
                s.patient_label = b.patient_label
        ");
        $stmt->execute();
        
        // Drop backup table
        $pdo->exec("DROP TABLE user_retrocession_settings_backup");
        
        $pdo->commit();
        echo "{$GREEN}✓ Rollback completed successfully!{$RESET}\n";
    } catch (Exception $e) {
        $pdo->rollBack();
        die("{$RED}✗ Rollback failed: " . $e->getMessage() . "{$RESET}\n");
    }
    exit(0);
}

// Main update logic
echo "{$BLUE}=== Retrocession Labels Update Script ==={$RESET}\n\n";

if ($isDryRun) {
    echo "{$YELLOW}Running in DRY-RUN mode. No changes will be made.{$RESET}\n";
    echo "Use --execute to apply changes.\n\n";
} else {
    echo "{$RED}Running in EXECUTE mode. Changes will be applied!{$RESET}\n\n";
}

// Find records to update
$toUpdate = [];

foreach ($labelMappings as $column => $mappings) {
    foreach ($mappings as $oldValue => $newValue) {
        $stmt = $pdo->prepare("
            SELECT id, user_id, $column as label_value
            FROM user_retrocession_settings
            WHERE $column = :old_value
        ");
        $stmt->execute(['old_value' => $oldValue]);
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if (!isset($toUpdate[$row['id']])) {
                $toUpdate[$row['id']] = [
                    'id' => $row['id'],
                    'user_id' => $row['user_id'],
                    'changes' => []
                ];
            }
            $toUpdate[$row['id']]['changes'][$column] = [
                'old' => $oldValue,
                'new' => $newValue
            ];
        }
    }
}

if (empty($toUpdate)) {
    echo "{$GREEN}✓ No records need updating. All labels are already in proper case.{$RESET}\n";
    exit(0);
}

// Display changes to be made
echo "Found " . count($toUpdate) . " records to update:\n\n";

foreach ($toUpdate as $record) {
    echo "{$BLUE}Record ID {$record['id']} (User ID {$record['user_id']}):{$RESET}\n";
    foreach ($record['changes'] as $column => $change) {
        $label = str_replace('_label', '', $column);
        echo "  " . strtoupper($label) . ": {$change['old']} -> {$GREEN}{$change['new']}{$RESET}\n";
    }
    echo "\n";
}

// Show summary
$totalChanges = array_sum(array_map(function($r) { return count($r['changes']); }, $toUpdate));
echo "{$YELLOW}Summary: " . count($toUpdate) . " records, $totalChanges label changes{$RESET}\n\n";

// Execute if not dry run
if (!$isDryRun) {
    echo "{$YELLOW}Proceed with updates? (yes/no):{$RESET} ";
    $confirm = trim(fgets(STDIN));
    
    if (strtolower($confirm) !== 'yes') {
        echo "{$RED}Update cancelled.{$RESET}\n";
        exit(0);
    }
    
    // Create backup table
    echo "\nCreating backup...\n";
    
    $pdo->beginTransaction();
    try {
        // Drop existing backup if exists
        $pdo->exec("DROP TABLE IF EXISTS user_retrocession_settings_backup");
        
        // Create backup of records we're updating
        $ids = array_keys($toUpdate);
        $idList = implode(',', $ids);
        
        $pdo->exec("
            CREATE TABLE user_retrocession_settings_backup AS
            SELECT id, cns_label, patient_label
            FROM user_retrocession_settings
            WHERE id IN ($idList)
        ");
        
        // Perform updates
        foreach ($toUpdate as $record) {
            $updates = [];
            $params = ['id' => $record['id']];
            
            foreach ($record['changes'] as $column => $change) {
                $updates[] = "$column = :$column";
                $params[$column] = $change['new'];
            }
            
            $sql = "UPDATE user_retrocession_settings SET " . implode(', ', $updates) . " WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
        }
        
        $pdo->commit();
        
        echo "{$GREEN}✓ Successfully updated " . count($toUpdate) . " records!{$RESET}\n";
        echo "{$GREEN}✓ Backup created in 'user_retrocession_settings_backup' table{$RESET}\n";
        echo "\nTo rollback these changes, run: php update_retrocession_labels.php --rollback\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        die("{$RED}✗ Update failed: " . $e->getMessage() . "{$RESET}\n");
    }
} else {
    echo "To apply these changes, run: php update_retrocession_labels.php --execute\n";
}