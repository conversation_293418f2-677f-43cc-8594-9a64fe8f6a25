<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '\"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Bernard Heens - Correct LOY Invoice Calculation</h2>";
    
    // Check Bernard Heens financial obligations
    $stmt = $db->prepare("
        SELECT * FROM user_financial_obligations 
        WHERE user_id = 6 AND end_date IS NULL
        LIMIT 1
    ");
    $stmt->execute();
    $bernard = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($bernard) {
        echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px;'>";
        echo "<h3>Database Values:</h3>";
        echo "<ul>";
        echo "<li>Rent: €" . number_format($bernard['rent_amount'], 2) . "</li>";
        echo "<li>Secretary TVAC 17%: €" . number_format($bernard['secretary_tvac_17'], 2) . "</li>";
        echo "<li>Secretary HTVA: €" . number_format($bernard['secretary_htva'], 2) . "</li>";
        echo "<li>TVA 17%: €" . number_format($bernard['tva_17'], 2) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Calculate correct invoice structure
        $rent = floatval($bernard['rent_amount']); // 500
        $secretaryTvac = floatval($bernard['secretary_tvac_17']); // 1170
        $secretaryHtva = floatval($bernard['secretary_htva']); // 1000
        $tva17 = floatval($bernard['tva_17']); // 170
        
        // Total secretary amount (combining TVAC and HTVA)
        $totalSecretary = $secretaryTvac + $secretaryHtva; // 2170
        // Secretary HT amount (removing the TVA portion)
        $secretaryHT = $totalSecretary - $tva17; // 2170 - 170 = 2000
        
        echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>CORRECT Invoice Structure:</h3>";
        echo "<table border='1' cellpadding='10' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ddd;'>";
        echo "<th>Description</th><th>Quantité</th><th>Prix HT</th><th>TVA</th><th>Total TTC</th>";
        echo "</tr>";
        
        // Loyer line
        echo "<tr>";
        echo "<td>Loyer mensuel</td>";
        echo "<td>1</td>";
        echo "<td>€" . number_format($rent, 2) . "</td>";
        echo "<td>0%</td>";
        echo "<td>€" . number_format($rent, 2) . "</td>";
        echo "</tr>";
        
        // Secretary line  
        echo "<tr>";
        echo "<td>Frais secrétariat</td>";
        echo "<td>1</td>";
        echo "<td>€" . number_format(1000, 2) . "</td>";
        echo "<td>17%</td>";
        echo "<td>€" . number_format(1170, 2) . "</td>";
        echo "</tr>";
        
        echo "</table>";
        
        echo "<div style='margin-top: 20px;'>";
        echo "<p><strong>Sous-total HT:</strong> €" . number_format(1500, 2) . " (500 + 1000)</p>";
        echo "<p><strong>TVA 17%:</strong> €" . number_format(170, 2) . " (17% of 1000)</p>";
        echo "<hr>";
        echo "<p style='font-size: 1.2em; color: #007bff;'><strong>Total TTC:</strong> €" . number_format(1670, 2) . "</p>";
        echo "</div>";
        echo "</div>";
        
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>Understanding the Data:</h3>";
        echo "<ul>";
        echo "<li><strong>secretary_tvac_17 (€1,170)</strong> = Secretary amount WITH 17% VAT included</li>";
        echo "<li><strong>secretary_htva (€1,000)</strong> = Additional secretary amount WITHOUT VAT</li>";
        echo "<li><strong>tva_17 (€170)</strong> = The VAT amount</li>";
        echo "<li>But for the invoice, we should show: €1,000 HT + €170 TVA = €1,170 TTC</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>Test the Fix:</h3>";
        echo "<p><a href='/fit/public/invoices/create?type=loyer' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>Create LOY Invoice</a></p>";
        echo "<ol>";
        echo "<li>Select <strong>Bernard Heens</strong></li>";
        echo "<li>Should show 2 lines: Loyer (€500) and Frais secrétariat (€1,000)</li>";
        echo "<li>Total should be <strong>€1,670</strong></li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ No financial obligations found for Bernard Heens</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>