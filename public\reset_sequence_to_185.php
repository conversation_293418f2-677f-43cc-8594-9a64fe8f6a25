<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== RESETTING INVOICE SEQUENCE TO 185 ===\n\n";

try {
    $db = Flight::db();
    
    // Show current state
    echo "1. Current sequence state:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025 AND month IS NULL
    ");
    $seq = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($seq) {
        echo "Current last_number: {$seq['last_number']}\n";
        echo "Next invoice would be: FAC-2025-" . str_pad($seq['last_number'] + 1, 4, '0', STR_PAD_LEFT) . "\n";
    }
    
    // Reset to 185 
    echo "\n2. Resetting to 185 (so next will be 0186)...\n";
    $stmt = $db->prepare("
        UPDATE document_sequences 
        SET last_number = 185
        WHERE document_type_id = 1 
        AND year = 2025 
        AND month IS NULL
    ");
    $result = $stmt->execute();
    
    if ($result && $stmt->rowCount() > 0) {
        echo "✓ Success! Reset to 185\n";
    } else {
        echo "✗ No rows updated. Creating new sequence...\n";
        
        // If no sequence exists, create it
        $stmt = $db->prepare("
            INSERT INTO document_sequences (document_type_id, year, month, last_number, created_at, updated_at)
            VALUES (1, 2025, NULL, 185, NOW(), NOW())
            ON DUPLICATE KEY UPDATE last_number = 185, updated_at = NOW()
        ");
        $stmt->execute();
        echo "✓ Created/Updated sequence\n";
    }
    
    // Verify
    echo "\n3. Verification:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025 AND month IS NULL
    ");
    $seq = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($seq) {
        echo "Last number is now: {$seq['last_number']}\n";
        echo "Next invoice will be: FAC-2025-" . str_pad($seq['last_number'] + 1, 4, '0', STR_PAD_LEFT) . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}