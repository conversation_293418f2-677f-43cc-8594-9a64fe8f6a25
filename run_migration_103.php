<?php
require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database configuration from .env
$db_host = $_ENV['DB_HOST'] ?? 'localhost';
$db_name = $_ENV['DB_DATABASE'] ?? 'fitapp';
$db_user = $_ENV['DB_USERNAME'] ?? 'root';
$db_pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    // Connect to database
    $dsn = "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4";
    $pdo = new PDO($dsn, $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Read migration file
    $migrationFile = __DIR__ . '/database/migrations/103_create_user_generated_invoices.sql';
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    echo "Read migration file: 103_create_user_generated_invoices.sql\n";
    
    // Execute migration
    $pdo->exec($sql);
    
    echo "Migration executed successfully!\n";
    echo "Created table: user_generated_invoices\n";
    echo "Added config values for invoice types\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}