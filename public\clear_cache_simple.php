<?php
/**
 * Simple cache clearing script without dependencies
 * This script can be used if the main cache clearing script fails
 */

echo "<h2>Simple Cache Clearing</h2>";

// Define cache directories
$cacheDirectories = [
    __DIR__ . '/../storage/cache/twig',
    __DIR__ . '/../storage/cache/data',
    __DIR__ . '/../storage/cache'
];

$cleared = 0;
$errors = [];

foreach ($cacheDirectories as $dir) {
    if (!is_dir($dir)) {
        echo "<p>⚠️ Directory not found: " . basename($dir) . "</p>";
        continue;
    }
    
    try {
        // Clear files in directory
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                if (unlink($file)) {
                    $cleared++;
                } else {
                    $errors[] = "Failed to delete: " . basename($file);
                }
            }
        }
        echo "<p>✓ Cleared cache directory: " . basename($dir) . "</p>";
    } catch (Exception $e) {
        $errors[] = "Error in " . basename($dir) . ": " . $e->getMessage();
    }
}

echo "<p><strong>Total files cleared: $cleared</strong></p>";

if (!empty($errors)) {
    echo "<h3>Errors:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
}

// Browser cache instructions
echo "<h3>Browser Cache</h3>";
echo "<p>To ensure you see the latest changes, please also clear your browser cache:</p>";
echo "<ul>";
echo "<li><strong>Chrome/Edge:</strong> Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)</li>";
echo "<li><strong>Firefox:</strong> Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)</li>";
echo "<li><strong>Safari:</strong> Press Cmd+Option+E</li>";
echo "</ul>";
echo "<p>Or do a hard refresh: <strong>Ctrl+F5</strong> (or Cmd+Shift+R on Mac)</p>";

echo "<hr>";
echo "<p><a href='/fit/public/'>Back to Application</a></p>";