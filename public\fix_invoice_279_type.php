<?php
/**
 * Fix Invoice 279 Type Issue
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Invoice 279 Type</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; border-radius: 4px; }
        .btn-danger { background: #dc3545; color: white; border: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Fix Invoice 279 Type Issue</h1>
    
    <?php
    try {
        $db = Flight::db();
        $invoiceId = 279;
        
        // Get current invoice data
        echo "<h2>Current Invoice Data</h2>";
        $stmt = $db->prepare("
            SELECT i.*, it.code as type_code, it.name as type_name
            FROM invoices i
            LEFT JOIN invoice_types it ON i.type_id = it.id
            WHERE i.id = :id
        ");
        $stmt->execute([':id' => $invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invoice) {
            echo '<div class="error">Invoice not found!</div>';
            exit;
        }
        
        echo "<table>";
        echo "<tr><th>Field</th><th>Current Value</th></tr>";
        echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
        echo "<tr><td>type_id</td><td>{$invoice['type_id']}</td></tr>";
        echo "<tr><td>invoice_type (enum)</td><td>" . ($invoice['invoice_type'] ?: '<span class="error">EMPTY</span>') . "</td></tr>";
        echo "<tr><td>Type Code (from invoice_types)</td><td>{$invoice['type_code']}</td></tr>";
        echo "<tr><td>Type Name</td><td>{$invoice['type_name']}</td></tr>";
        echo "</table>";
        
        // Show the issue
        echo '<div class="warning">';
        echo '<h3>Issue Identified:</h3>';
        echo '<p>The invoice has type_id = 4 (DIV) but the invoice_type enum field is empty.</p>';
        echo '<p>This causes the EmailService SQL query to fail with "Invalid parameter number" error.</p>';
        echo '</div>';
        
        // Get invoice types
        echo "<h2>Available Invoice Types</h2>";
        $stmt = $db->query("SELECT * FROM invoice_types ORDER BY id");
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Suggested invoice_type value</th></tr>";
        foreach ($types as $type) {
            $suggested = '';
            if ($type['code'] === 'DIV' || $type['code'] === 'LOC') {
                $suggested = 'rental';
            } elseif ($type['code'] === 'RET') {
                $suggested = 'retrocession_30';
            }
            
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>" . ($suggested ?: '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Fix options
        echo "<h2>Fix Options</h2>";
        
        if (isset($_POST['action'])) {
            $action = $_POST['action'];
            
            if ($action === 'set_rental') {
                // Set invoice_type to 'rental' for DIV invoices
                $stmt = $db->prepare("UPDATE invoices SET invoice_type = 'rental' WHERE id = :id");
                $stmt->execute([':id' => $invoiceId]);
                echo '<div class="success">✅ Set invoice_type to "rental"</div>';
                
                // Clear the test email log
                $stmt = $db->prepare("DELETE FROM email_logs WHERE invoice_id = :id AND subject LIKE '%Test log%'");
                $stmt->execute([':id' => $invoiceId]);
                echo '<div class="success">✅ Cleared test email log</div>';
                
            } elseif ($action === 'fix_all_div') {
                // Fix all DIV invoices
                $stmt = $db->prepare("
                    UPDATE invoices 
                    SET invoice_type = 'rental' 
                    WHERE type_id = 4 
                    AND (invoice_type IS NULL OR invoice_type = '')
                ");
                $affected = $stmt->execute();
                echo '<div class="success">✅ Updated ' . $stmt->rowCount() . ' DIV invoices to use "rental" type</div>';
                
            } elseif ($action === 'test_email') {
                // Test email sending
                echo '<div class="info">Testing email send...</div>';
                echo '<script>window.location.href = "/fit/public/test_invoice_279_email.php";</script>';
            }
            
            // Refresh data
            echo '<p><a href="">Refresh page to see changes</a></p>';
        } else {
            ?>
            <div class="info">
                <h3>Recommended Fix:</h3>
                <p>Set the invoice_type field to "rental" for this DIV invoice. This will allow the email template system to find the appropriate template.</p>
                
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="set_rental">
                    <button type="submit" class="btn-primary">Fix This Invoice Only</button>
                </form>
                
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="fix_all_div">
                    <button type="submit" class="btn-danger">Fix All DIV Invoices</button>
                </form>
            </div>
            
            <div class="info" style="margin-top: 20px;">
                <h3>After fixing, test the email:</h3>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="test_email">
                    <button type="submit" class="btn-primary">Test Email Send</button>
                </form>
            </div>
            <?php
        }
        
        // Show invoice_type enum values
        echo "<h2>Database invoice_type Enum Values</h2>";
        $stmt = $db->query("SHOW COLUMNS FROM invoices LIKE 'invoice_type'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($column) {
            echo "<p>Allowed values: " . htmlspecialchars($column['Type']) . "</p>";
        }
        
    } catch (Exception $e) {
        echo '<div class="error">';
        echo '<h3>Error:</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/debug_invoice_0190_email.php">Debug Invoice Email</a> |
        <a href="/fit/public/test_invoice_279_email.php">Test Email Send</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>