<?php
/**
 * Cleanup script for retrocession data
 * Fixes invalid date ranges and ensures only one active setting per user
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
try {
    $dsn = sprintf(
        'mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4',
        $_ENV['DB_HOST'] ?? 'localhost',
        $_ENV['DB_PORT'] ?? '3306',
        $_ENV['DB_DATABASE'] ?? 'healthcenter_billing'
    );
    
    $db = new PDO($dsn, $_ENV['DB_USERNAME'] ?? 'root', $_ENV['DB_PASSWORD'] ?? '');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n\n";
    
    // 1. Fix invalid date ranges (where valid_to is before valid_from)
    echo "Fixing invalid date ranges...\n";
    $stmt = $db->prepare("
        UPDATE user_retrocession_settings 
        SET valid_to = NULL 
        WHERE valid_to IS NOT NULL AND valid_to < valid_from
    ");
    $stmt->execute();
    echo "Fixed " . $stmt->rowCount() . " records with invalid date ranges.\n\n";
    
    // 2. Get all users with retrocession settings
    echo "Processing users with retrocession settings...\n";
    $stmt = $db->query("
        SELECT DISTINCT user_id 
        FROM user_retrocession_settings 
        ORDER BY user_id
    ");
    $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($users as $userId) {
        echo "Processing user ID: $userId\n";
        
        // First, deactivate all settings for this user
        $stmt = $db->prepare("
            UPDATE user_retrocession_settings 
            SET is_active = 0 
            WHERE user_id = :user_id
        ");
        $stmt->execute(['user_id' => $userId]);
        
        // Find the most recent valid setting (by valid_from date)
        $stmt = $db->prepare("
            SELECT id, valid_from, valid_to 
            FROM user_retrocession_settings 
            WHERE user_id = :user_id 
            ORDER BY valid_from DESC, id DESC 
            LIMIT 1
        ");
        $stmt->execute(['user_id' => $userId]);
        $mostRecent = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($mostRecent) {
            // Activate only the most recent setting
            $stmt = $db->prepare("
                UPDATE user_retrocession_settings 
                SET is_active = 1 
                WHERE id = :id
            ");
            $stmt->execute(['id' => $mostRecent['id']]);
            echo "  - Activated setting ID: {$mostRecent['id']} (valid from: {$mostRecent['valid_from']})\n";
            
            // Update valid_to dates for all previous settings
            $stmt = $db->prepare("
                SELECT id, valid_from, valid_to 
                FROM user_retrocession_settings 
                WHERE user_id = :user_id 
                AND id != :current_id
                ORDER BY valid_from DESC
            ");
            $stmt->execute([
                'user_id' => $userId,
                'current_id' => $mostRecent['id']
            ]);
            
            $previousSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $lastValidFrom = $mostRecent['valid_from'];
            
            foreach ($previousSettings as $setting) {
                // Calculate the end date (one day before the next setting starts)
                $endDate = new DateTime($lastValidFrom);
                $endDate->modify('-1 day');
                $endDateStr = $endDate->format('Y-m-d');
                
                // Only update if the current valid_to is null or after the calculated end date
                if (is_null($setting['valid_to']) || $setting['valid_to'] > $endDateStr) {
                    $updateStmt = $db->prepare("
                        UPDATE user_retrocession_settings 
                        SET valid_to = :valid_to 
                        WHERE id = :id
                    ");
                    $updateStmt->execute([
                        'valid_to' => $endDateStr,
                        'id' => $setting['id']
                    ]);
                    echo "  - Updated setting ID: {$setting['id']} to end on: $endDateStr\n";
                }
                
                $lastValidFrom = $setting['valid_from'];
            }
        }
    }
    
    // 3. Show summary
    echo "\nSummary:\n";
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_settings,
            SUM(is_active = 1) as active_settings,
            COUNT(DISTINCT user_id) as total_users
        FROM user_retrocession_settings
    ");
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Total settings: {$summary['total_settings']}\n";
    echo "Active settings: {$summary['active_settings']}\n";
    echo "Total users: {$summary['total_users']}\n";
    
    // 4. Check for any remaining issues
    echo "\nChecking for remaining issues...\n";
    
    // Check for multiple active settings per user
    $stmt = $db->query("
        SELECT user_id, COUNT(*) as active_count 
        FROM user_retrocession_settings 
        WHERE is_active = 1 
        GROUP BY user_id 
        HAVING active_count > 1
    ");
    $multipleActive = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($multipleActive) > 0) {
        echo "WARNING: Found users with multiple active settings:\n";
        foreach ($multipleActive as $issue) {
            echo "  - User ID: {$issue['user_id']} has {$issue['active_count']} active settings\n";
        }
    } else {
        echo "✓ No users with multiple active settings found.\n";
    }
    
    // Check for overlapping date ranges
    $stmt = $db->query("
        SELECT a.user_id, a.id as id1, b.id as id2, 
               a.valid_from as from1, a.valid_to as to1,
               b.valid_from as from2, b.valid_to as to2
        FROM user_retrocession_settings a
        JOIN user_retrocession_settings b ON a.user_id = b.user_id AND a.id < b.id
        WHERE a.valid_from <= IFNULL(b.valid_to, '9999-12-31')
        AND b.valid_from <= IFNULL(a.valid_to, '9999-12-31')
    ");
    $overlaps = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($overlaps) > 0) {
        echo "WARNING: Found overlapping date ranges:\n";
        foreach ($overlaps as $overlap) {
            echo "  - User ID: {$overlap['user_id']}, Settings {$overlap['id1']} and {$overlap['id2']} overlap\n";
        }
    } else {
        echo "✓ No overlapping date ranges found.\n";
    }
    
    echo "\nCleanup completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}