<?php
// Test the new invoice numbering system based on latest SENT invoices

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Invoice Numbering System Test</h1>";
echo "<p>Testing the new system where all invoice types share the same sequence based on SENT invoices.</p>";

// 1. Get the latest SENT invoice for current year
echo "<h2>1. Latest SENT Invoice:</h2>";
$year = date('Y');
$stmt = $db->prepare("
    SELECT i.invoice_number, i.type_id, i.status, i.issue_date,
           COALESCE(cit.prefix, 'UNK') as type_prefix
    FROM invoices i
    LEFT JOIN config_invoice_types cit ON i.type_id = cit.id
    WHERE i.status = 'sent' 
    AND i.invoice_number LIKE :year_pattern
    ORDER BY 
        CAST(SUBSTRING(i.invoice_number, -4) AS UNSIGNED) DESC,
        i.invoice_number DESC
    LIMIT 1
");
$stmt->execute([':year_pattern' => "%-$year-%"]);
$latestInvoice = $stmt->fetch(PDO::FETCH_ASSOC);

if ($latestInvoice) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice Number</th><th>Type Prefix</th><th>Status</th><th>Issue Date</th></tr>";
    echo "<tr>";
    echo "<td>{$latestInvoice['invoice_number']}</td>";
    echo "<td>{$latestInvoice['type_prefix']}</td>";
    echo "<td>{$latestInvoice['status']}</td>";
    echo "<td>{$latestInvoice['issue_date']}</td>";
    echo "</tr>";
    echo "</table>";
    
    // Extract sequence number
    if (preg_match('/(\d{4})$/', $latestInvoice['invoice_number'], $matches)) {
        $currentSequence = intval($matches[1]);
        $nextSequence = $currentSequence + 1;
        echo "<p><strong>Current sequence:</strong> $currentSequence</p>";
        echo "<p><strong>Next sequence should be:</strong> $nextSequence (formatted as " . str_pad($nextSequence, 4, '0', STR_PAD_LEFT) . ")</p>";
    }
} else {
    echo "<p>No SENT invoices found for year $year. Next invoice will start at 0001.</p>";
}

// 2. Show draft invoices (they should not affect the sequence)
echo "<h2>2. Current DRAFT Invoices:</h2>";
$stmt = $db->prepare("
    SELECT invoice_number, type_id, status, created_at,
           (SELECT prefix FROM config_invoice_types WHERE id = invoices.type_id) as type_prefix
    FROM invoices 
    WHERE status = 'draft'
    AND invoice_number LIKE :year_pattern
    ORDER BY created_at DESC
    LIMIT 5
");
$stmt->execute([':year_pattern' => "%-$year-%"]);
$drafts = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($drafts) > 0) {
    echo "<p>These draft invoices do not affect the sequence numbering:</p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice Number</th><th>Type Prefix</th><th>Status</th><th>Created</th></tr>";
    foreach ($drafts as $draft) {
        echo "<tr>";
        echo "<td>{$draft['invoice_number']}</td>";
        echo "<td>{$draft['type_prefix']}</td>";
        echo "<td>{$draft['status']}</td>";
        echo "<td>{$draft['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p class='text-warning'>⚠️ When these drafts are converted to SENT, their numbers will be regenerated based on the latest SENT invoice.</p>";
} else {
    echo "<p>No draft invoices found.</p>";
}

// 3. Show different invoice types and their prefixes
echo "<h2>3. Invoice Types (all share same sequence):</h2>";
$stmt = $db->query("
    SELECT id, code, prefix, name 
    FROM config_invoice_types 
    WHERE is_active = 1
    ORDER BY name
");
$types = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Example Next Number</th></tr>";
foreach ($types as $type) {
    $nextSeq = isset($nextSequence) ? $nextSequence : 1;
    $exampleNumber = "FAC-{$type['prefix']}-$year-" . str_pad($nextSeq, 4, '0', STR_PAD_LEFT);
    echo "<tr>";
    echo "<td>{$type['id']}</td>";
    echo "<td>{$type['code']}</td>";
    echo "<td>{$type['prefix']}</td>";
    echo "<td>{$type['name']}</td>";
    echo "<td>$exampleNumber</td>";
    echo "</tr>";
}
echo "</table>";

// 4. Test the logic without creating invoices
echo "<h2>4. Test Scenarios:</h2>";
echo "<ul>";
echo "<li>✅ All invoice types (LOC, RET, etc.) share the same sequence counter</li>";
echo "<li>✅ Only SENT invoices are considered for the sequence</li>";
echo "<li>✅ Draft invoices get temporary numbers that are replaced when sent</li>";
echo "<li>✅ Sequence is year-based (resets each year)</li>";
echo "<li>✅ Format: FAC-{TYPE}-{YEAR}-{NNNN}</li>";
echo "</ul>";

// 5. Recent SENT invoices to show sequence progression
echo "<h2>5. Recent SENT Invoices (showing sequence progression):</h2>";
$stmt = $db->prepare("
    SELECT i.invoice_number, i.type_id, i.issue_date,
           COALESCE(cit.prefix, 'UNK') as type_prefix,
           CAST(SUBSTRING(i.invoice_number, -4) AS UNSIGNED) as sequence_num
    FROM invoices i
    LEFT JOIN config_invoice_types cit ON i.type_id = cit.id
    WHERE i.status = 'sent' 
    AND i.invoice_number LIKE :year_pattern
    ORDER BY sequence_num DESC
    LIMIT 10
");
$stmt->execute([':year_pattern' => "%-$year-%"]);
$recentSent = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($recentSent) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice Number</th><th>Type</th><th>Issue Date</th><th>Sequence #</th></tr>";
    foreach ($recentSent as $sent) {
        echo "<tr>";
        echo "<td>{$sent['invoice_number']}</td>";
        echo "<td>{$sent['type_prefix']}</td>";
        echo "<td>{$sent['issue_date']}</td>";
        echo "<td>{$sent['sequence_num']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No recent SENT invoices found.</p>";
}

echo "<hr>";
echo "<p><strong>Summary:</strong> The new invoice numbering system ensures all invoice types share a single sequence counter based only on SENT invoices. Draft invoices don't consume sequence numbers.</p>";
?>