<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\User;
use Flight;
use PDO;
use Exception;

class UnifiedInvoiceGenerator
{
    private $db;
    private $userId;
    private $invoiceType;
    private $month;
    private $year;
    private $generatedBy;
    
    public function __construct($userId, $invoiceType, $month, $year, $generatedBy)
    {
        $this->db = Flight::db();
        $this->userId = $userId;
        $this->invoiceType = strtoupper($invoiceType);
        $this->month = $month;
        $this->year = $year;
        $this->generatedBy = $generatedBy;
    }
    
    /**
     * Generate an invoice based on type
     * @return array ['success' => bool, 'invoice' => array|null, 'message' => string]
     */
    public function generate()
    {
        try {
            // Check if invoice already exists
            if ($this->invoiceExists()) {
                return [
                    'success' => false,
                    'message' => __('invoices.invoice_already_exists_for_period')
                ];
            }
            
            // Get generation data based on type
            $data = $this->getGenerationData();
            if (!$data || empty($data['items'])) {
                return [
                    'success' => false,
                    'message' => __('invoices.no_data_for_period')
                ];
            }
            
            // Get user info
            $user = $this->getUserInfo();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => __('users.user_not_found')
                ];
            }
            
            // Get invoice type configuration
            $invoiceTypeConfig = $this->getInvoiceTypeConfig();
            if (!$invoiceTypeConfig) {
                return [
                    'success' => false,
                    'message' => __('invoices.invalid_invoice_type')
                ];
            }
            
            // Begin transaction
            $this->db->beginTransaction();
            
            try {
                // Create invoice
                $invoice = $this->createInvoice($user, $invoiceTypeConfig, $data);
                
                // Add invoice lines
                $this->createInvoiceLines($invoice['id'], $data['items']);
                
                // Update totals
                $this->updateInvoiceTotals($invoice['id']);
                
                // Track generation
                $this->trackGeneration($invoice['id'], $data);
                
                // Update source records
                $this->updateSourceRecords($invoice['id']);
                
                $this->db->commit();
                
                // Reload invoice with number
                $invoice = Invoice::find($invoice['id']);
                
                return [
                    'success' => true,
                    'invoice' => $invoice,
                    'message' => __('invoices.invoice_generated_successfully')
                ];
                
            } catch (Exception $e) {
                $this->db->rollBack();
                throw $e;
            }
            
        } catch (Exception $e) {
            error_log("UnifiedInvoiceGenerator error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if invoice already exists for this period
     */
    private function invoiceExists()
    {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) 
            FROM user_generated_invoices 
            WHERE user_id = :user_id 
            AND invoice_type = :type 
            AND period_month = :month 
            AND period_year = :year
        ");
        
        $stmt->execute([
            'user_id' => $this->userId,
            'type' => $this->invoiceType,
            'month' => $this->month,
            'year' => $this->year
        ]);
        
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Get data for invoice generation based on type
     */
    private function getGenerationData()
    {
        // For RET and LOC invoices, we use the previous month's data
        // For LOY invoices, we use the current month
        $dataMonth = $this->month;
        $dataYear = $this->year;
        
        if (in_array($this->invoiceType, ['RET', 'LOC'])) {
            // Use previous month for RET and LOC
            $dataMonth--;
            if ($dataMonth < 1) {
                $dataMonth = 12;
                $dataYear--;
            }
        }
        
        switch ($this->invoiceType) {
            case 'RET':
                return $this->getRetrocessionData($dataMonth, $dataYear);
            case 'LOY':
                return $this->getLoyerData();
            case 'LOC':
                return $this->getLocationData($dataMonth, $dataYear);
            default:
                throw new Exception("Unknown invoice type: {$this->invoiceType}");
        }
    }
    
    /**
     * Get retrocession data
     */
    private function getRetrocessionData($month = null, $year = null)
    {
        // Use provided month/year or fall back to instance values
        $month = $month ?? $this->month;
        $year = $year ?? $this->year;
        
        // Get monthly amounts
        // Check if year column exists
        $yearCheck = $this->db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
        $hasYearColumn = $yearCheck->rowCount() > 0;
        
        if ($hasYearColumn) {
            $stmt = $this->db->prepare("
                SELECT cns_amount, patient_amount 
                FROM user_monthly_retrocession_amounts 
                WHERE user_id = :user_id AND month = :month AND year = :year
            ");
            $stmt->execute(['user_id' => $this->userId, 'month' => $month, 'year' => $year]);
        } else {
            $stmt = $this->db->prepare("
                SELECT cns_amount, patient_amount 
                FROM user_monthly_retrocession_amounts 
                WHERE user_id = :user_id AND month = :month
            ");
            $stmt->execute(['user_id' => $this->userId, 'month' => $month]);
        }
        $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$amounts || ($amounts['cns_amount'] == 0 && $amounts['patient_amount'] == 0)) {
            return null;
        }
        
        // Get retrocession settings
        $stmt = $this->db->prepare("
            SELECT * FROM user_retrocession_settings 
            WHERE user_id = :user_id 
            AND (valid_from IS NULL OR valid_from <= CURDATE())
            AND (valid_to IS NULL OR valid_to >= CURDATE())
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute(['user_id' => $this->userId]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $items = [];
        
        // CNS line
        if ($amounts['cns_amount'] > 0) {
            $cnsLabel = $settings['cns_label'] ?? 'Rétrocession CNS';
            $cnsValue = $settings['cns_value'] ?? 20;
            $cnsType = $settings['cns_type'] ?? 'percentage';
            
            // Clean description with only percentage
            $description = $cnsLabel;
            if ($cnsType === 'percentage') {
                $description .= ' ' . intval($cnsValue) . '%';
            }
            
            $items[] = [
                'description' => $description,
                'base_amount' => $amounts['cns_amount'],
                'calculation' => $cnsType === 'percentage' ? "{$cnsValue}%" : "{$cnsValue}€",
                'amount' => $cnsType === 'percentage' 
                    ? $amounts['cns_amount'] * ($cnsValue / 100)
                    : $cnsValue,
                'vat_rate' => 0
            ];
        }
        
        // Patient line
        if ($amounts['patient_amount'] > 0 && !($settings['exclude_patient_line'] ?? false)) {
            $patientLabel = $settings['patient_label'] ?? 'Rétrocession PATIENTS';
            $patientValue = $settings['patient_value'] ?? 20;
            $patientType = $settings['patient_type'] ?? 'percentage';
            
            // Clean description with only percentage
            $description = $patientLabel;
            if ($patientType === 'percentage') {
                $description .= ' ' . intval($patientValue) . '%';
            }
            
            $items[] = [
                'description' => $description,
                'base_amount' => $amounts['patient_amount'],
                'calculation' => $patientType === 'percentage' ? "{$patientValue}%" : "{$patientValue}€",
                'amount' => $patientType === 'percentage' 
                    ? $amounts['patient_amount'] * ($patientValue / 100)
                    : $patientValue,
                'vat_rate' => 0
            ];
        }
        
        // Secretary fees
        if (($settings['secretary_value'] ?? 0) > 0) {
            $secretaryLabel = $settings['secretary_label'] ?? 'Frais secrétariat et mise à disposition matériel';
            $secretaryValue = $settings['secretary_value'];
            $secretaryType = $settings['secretary_type'] ?? 'percentage';
            
            // Calculate secretary amount
            if ($secretaryType === 'percentage') {
                // Calculate subtotal of retrocession amounts
                $subtotal = 0;
                foreach ($items as $item) {
                    $subtotal += $item['amount'];
                }
                $secretaryAmount = $subtotal * ($secretaryValue / 100);
            } else {
                $secretaryAmount = $secretaryValue;
            }
            
            // Include percentage in description for percentage type
            $description = $secretaryLabel;
            if ($secretaryType === 'percentage') {
                $description .= ' ' . intval($secretaryValue) . '%';
            }
            
            $items[] = [
                'description' => $description,
                'base_amount' => $amounts['cns_amount'] + $amounts['patient_amount'], // Total base for secretary calculation
                'calculation' => $secretaryType === 'percentage' ? "{$secretaryValue}%" : "{$secretaryValue}€",
                'amount' => $secretaryAmount,
                'vat_rate' => 17
            ];
        }
        
        return [
            'items' => $items,
            'source_data' => [
                'amounts' => $amounts,
                'settings' => $settings
            ]
        ];
    }
    
    /**
     * Get loyer/rental data
     */
    private function getLoyerData()
    {
        // Get current financial obligations
        $stmt = $this->db->prepare("
            SELECT * FROM user_financial_obligations 
            WHERE user_id = :user_id 
            AND effective_date <= :date
            AND (end_date IS NULL OR end_date >= :date2)
            ORDER BY effective_date DESC LIMIT 1
        ");
        
        $periodDate = "{$this->year}-{$this->month}-01";
        $stmt->execute([
            'user_id' => $this->userId,
            'date' => $periodDate,
            'date2' => $periodDate
        ]);
        
        $obligations = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$obligations || $obligations['total_tvac'] == 0) {
            return null;
        }
        
        $items = [];
        
        // Rent
        if ($obligations['rent_amount'] > 0) {
            $items[] = [
                'description' => 'Loyer',
                'amount' => $obligations['rent_amount'],
                'vat_rate' => 0
            ];
        }
        
        // Charges
        if ($obligations['charges_amount'] > 0) {
            $items[] = [
                'description' => 'Charges',
                'amount' => $obligations['charges_amount'],
                'vat_rate' => 0
            ];
        }
        
        // Secretary TVAC
        if ($obligations['secretary_tvac_17'] > 0) {
            $items[] = [
                'description' => 'Secrétariat',
                'amount' => $obligations['secretary_tvac_17'],
                'vat_rate' => 17,
                'is_tvac' => true
            ];
        }
        
        // Secretary HTVA + TVA (if not using TVAC)
        if ($obligations['secretary_htva'] > 0 && $obligations['secretary_tvac_17'] == 0) {
            $totalWithVat = $obligations['secretary_htva'] + $obligations['tva_17'];
            $items[] = [
                'description' => 'Secrétariat',
                'amount' => $totalWithVat,
                'vat_rate' => 17,
                'is_tvac' => true
            ];
        }
        
        return [
            'items' => $items,
            'source_data' => ['obligations' => $obligations]
        ];
    }
    
    /**
     * Get location/course data
     */
    private function getLocationData($month = null, $year = null)
    {
        // Use provided month/year or fall back to instance values
        $month = $month ?? $this->month;
        $year = $year ?? $this->year;
        
        // Get course counts for the month
        $stmt = $this->db->prepare("
            SELECT cc.*, c.course_name, c.vat_rate
            FROM user_monthly_course_counts cc
            INNER JOIN user_courses c ON c.id = cc.course_id
            WHERE cc.user_id = :user_id 
            AND cc.month = :month 
            AND cc.year = :year
            AND cc.course_count > 0
            AND cc.is_active = 1
        ");
        
        $stmt->execute([
            'user_id' => $this->userId,
            'month' => $month,
            'year' => $year
        ]);
        
        $courseCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($courseCounts)) {
            return null;
        }
        
        $items = [];
        foreach ($courseCounts as $count) {
            $items[] = [
                'description' => $count['course_name'],
                'quantity' => $count['course_count'],
                'unit_price' => $count['hourly_rate'],
                'amount' => $count['course_count'] * $count['hourly_rate'],
                'vat_rate' => $count['vat_rate'],
                'is_tvac' => true
            ];
        }
        
        return [
            'items' => $items,
            'source_data' => ['course_counts' => $courseCounts]
        ];
    }
    
    /**
     * Get client info for user
     */
    private function getUserInfo()
    {
        $stmt = $this->db->prepare("
            SELECT u.*, 
                   CONCAT(u.first_name, ' ', u.last_name) as full_name,
                   u.address,
                   u.postal_code,
                   u.city,
                   u.country,
                   u.vat_intercommunautaire as vat_number,
                   u.email,
                   u.phone
            FROM users u
            WHERE u.id = :user_id
            LIMIT 1
        ");
        $stmt->execute(['user_id' => $this->userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get invoice type configuration
     */
    private function getInvoiceTypeConfig()
    {
        // Get the code from config
        $configKey = strtolower($this->invoiceType) . '_invoice_type';
        $stmt = $this->db->prepare("SELECT value FROM config WHERE `key` = :key");
        $stmt->execute(['key' => $configKey]);
        $code = $stmt->fetchColumn();
        
        if (!$code) {
            $code = $this->invoiceType; // Fallback to type name
        }
        
        // For retrocession invoices, determine if RET25 or RET30 based on secretary percentage
        if ($this->invoiceType === 'RET' && strtolower($code) === 'ret') {
            // Get user's retrocession settings
            $stmt = $this->db->prepare("
                SELECT secretary_value 
                FROM user_retrocession_settings 
                WHERE user_id = :user_id 
                AND (valid_from IS NULL OR valid_from <= CURDATE())
                AND (valid_to IS NULL OR valid_to >= CURDATE())
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute(['user_id' => $this->userId]);
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $secretaryValue = $settings['secretary_value'] ?? 10; // Default to 10%
            
            // Choose invoice type based on secretary percentage
            if ($secretaryValue == 5) {
                $code = 'ret2';
            } else {
                $code = 'ret3';
            }
        }
        
        // Get invoice type details
        $stmt = $this->db->prepare("
            SELECT * FROM config_invoice_types 
            WHERE code = :code AND is_active = 1
            LIMIT 1
        ");
        $stmt->execute(['code' => $code]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create the invoice
     */
    private function createInvoice($user, $invoiceType, $data)
    {
        $monthNames = ['', 'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL', 'MAI', 'JUIN', 
                      'JUILLET', 'AOÛT', 'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'];
        
        $subject = $this->getInvoiceSubject($monthNames[$this->month]);
        
        // Create invoice data using user information
        $invoiceData = [
            'user_id' => $user['id'],  // Link to user instead of client
            'invoice_type_id' => $invoiceType['id'],
            'type_id' => $invoiceType['id'], // For backward compatibility
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'status' => 'draft',
            'subject' => $subject,
            'notes' => $this->getInvoiceNotes($monthNames[$this->month]),
            'created_by' => $this->generatedBy,
            'subtotal' => 0,
            'vat_amount' => 0,
            'total' => 0,
            // Add billing information from user
            'billing_name' => $user['full_name'],
            'billing_address' => $user['address'],
            'billing_postal_code' => $user['postal_code'],
            'billing_city' => $user['city'],
            'billing_country' => $user['country'] ?: 'Luxembourg',
            'billing_vat_number' => $user['vat_number'],
            'billing_email' => $user['email'],
            'billing_phone' => $user['phone']
        ];
        
        $invoice = new Invoice();
        return $invoice->createInvoice($invoiceData);
    }
    
    /**
     * Get invoice subject based on type
     */
    private function getInvoiceSubject($monthName)
    {
        switch ($this->invoiceType) {
            case 'RET':
                return "Rétrocession - {$monthName} {$this->year}";
            case 'LOY':
                return "Loyer et charges";
            case 'LOC':
                return "LOCATION SALLE";
            default:
                return "{$monthName} {$this->year}";
        }
    }
    
    /**
     * Get invoice notes based on type
     */
    private function getInvoiceNotes($monthName)
    {
        switch ($this->invoiceType) {
            case 'RET':
                return "Facturation des rétrocessions pour {$monthName} {$this->year}";
            case 'LOY':
                // For LOY, use next month for the notes since rent is in advance
                $nextMonthTimestamp = mktime(0, 0, 0, $this->month + 1, 1, $this->year);
                $nextMonthName = ['', 'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL', 'MAI', 'JUIN', 
                                 'JUILLET', 'AOÛT', 'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'][date('n', $nextMonthTimestamp)];
                $nextYear = date('Y', $nextMonthTimestamp);
                return "Loyer et charges pour {$nextMonthName} {$nextYear}";
            case 'LOC':
                return "Facturation des cours pour {$monthName} {$this->year}";
            default:
                return "";
        }
    }
    
    /**
     * Create invoice lines
     */
    private function createInvoiceLines($invoiceId, $items)
    {
        // Check if we have meta_data column
        $checkStmt = $this->db->query("SHOW COLUMNS FROM invoice_lines LIKE 'meta_data'");
        $hasMetaData = $checkStmt->rowCount() > 0;
        
        if ($hasMetaData) {
            $stmt = $this->db->prepare("
                INSERT INTO invoice_lines 
                (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order, meta_data)
                VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order, :meta_data)
            ");
        } else {
            $stmt = $this->db->prepare("
                INSERT INTO invoice_lines 
                (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order)
                VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order)
            ");
        }
        
        $order = 1;
        foreach ($items as $item) {
            $quantity = $item['quantity'] ?? 1;
            $unitPrice = $item['unit_price'] ?? $item['amount'];
            $lineTotal = $item['amount'];
            
            // Handle TVAC prices
            if ($item['is_tvac'] ?? false) {
                // Price includes VAT - invoice_lines will auto-calculate VAT amount
                $lineTotal = $item['amount'];
            }
            
            $params = [
                'invoice_id' => $invoiceId,
                'line_type' => 'service',
                'description' => $item['description'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'vat_rate' => $item['vat_rate'] ?? 0,
                'line_total' => $lineTotal,
                'sort_order' => $order++
            ];
            
            // Add meta_data if column exists and we have base_amount
            if ($hasMetaData) {
                if (isset($item['base_amount'])) {
                    $params['meta_data'] = json_encode(['base_amount' => $item['base_amount']]);
                } else {
                    $params['meta_data'] = null;
                }
            }
            
            $stmt->execute($params);
        }
    }
    
    /**
     * Update invoice totals
     */
    private function updateInvoiceTotals($invoiceId)
    {
        $stmt = $this->db->prepare("
            UPDATE invoices 
            SET total = (SELECT SUM(line_total) FROM invoice_lines WHERE invoice_id = :invoice_id),
                subtotal = (SELECT SUM(line_total / (1 + vat_rate/100)) FROM invoice_lines WHERE invoice_id = :invoice_id2),
                vat_amount = (SELECT SUM(line_total - (line_total / (1 + vat_rate/100))) FROM invoice_lines WHERE invoice_id = :invoice_id3)
            WHERE id = :invoice_id4
        ");
        
        $stmt->execute([
            'invoice_id' => $invoiceId,
            'invoice_id2' => $invoiceId,
            'invoice_id3' => $invoiceId,
            'invoice_id4' => $invoiceId
        ]);
    }
    
    /**
     * Track invoice generation
     */
    private function trackGeneration($invoiceId, $data)
    {
        $stmt = $this->db->prepare("
            INSERT INTO user_generated_invoices 
            (user_id, invoice_id, invoice_type, period_month, period_year, generation_data, generated_by)
            VALUES (:user_id, :invoice_id, :invoice_type, :period_month, :period_year, :generation_data, :generated_by)
        ");
        
        $stmt->execute([
            'user_id' => $this->userId,
            'invoice_id' => $invoiceId,
            'invoice_type' => $this->invoiceType,
            'period_month' => $this->month,
            'period_year' => $this->year,
            'generation_data' => json_encode($data['source_data']),
            'generated_by' => $this->generatedBy
        ]);
    }
    
    /**
     * Update source records with invoice ID
     */
    private function updateSourceRecords($invoiceId)
    {
        switch ($this->invoiceType) {
            case 'RET':
                // Create retrocession_data_entry record
                $this->createRetrocessionDataEntry($invoiceId);
                break;
                
            case 'LOC':
                // Update course counts with invoice_id
                $stmt = $this->db->prepare("
                    UPDATE user_monthly_course_counts 
                    SET invoice_id = :invoice_id 
                    WHERE user_id = :user_id 
                    AND month = :month 
                    AND year = :year
                ");
                $stmt->execute([
                    'invoice_id' => $invoiceId,
                    'user_id' => $this->userId,
                    'month' => $this->month,
                    'year' => $this->year
                ]);
                break;
        }
    }
    
    /**
     * Create retrocession data entry for backward compatibility
     */
    private function createRetrocessionDataEntry($invoiceId)
    {
        // Get the data we used - pass the previous month/year used for generation
        $dataMonth = $this->month - 1;
        $dataYear = $this->year;
        if ($dataMonth < 1) {
            $dataMonth = 12;
            $dataYear--;
        }
        $data = $this->getRetrocessionData($dataMonth, $dataYear);
        if (!$data) return;
        
        $stmt = $this->db->prepare("
            INSERT INTO retrocession_data_entry
            (practitioner_id, period_month, period_year, cns_services_count, cns_amount,
             patient_services_count, patient_amount, secretary_services, status, 
             invoice_id, data_source, created_at, entered_by)
            SELECT 
                c.id, :month, :year, 1, :cns_amount,
                1, :patient_amount, :secretary_amount, 'invoiced',
                :invoice_id, 'auto_suggested', NOW(), :created_by
            FROM users u
            JOIN clients c ON c.email = u.email OR CONCAT(u.first_name, ' ', u.last_name) = c.name
            WHERE u.id = :user_id AND c.is_practitioner = 1
            LIMIT 1
            ON DUPLICATE KEY UPDATE
                cns_services_count = VALUES(cns_services_count),
                cns_amount = VALUES(cns_amount),
                patient_services_count = VALUES(patient_services_count),
                patient_amount = VALUES(patient_amount),
                secretary_services = VALUES(secretary_services),
                status = 'invoiced',
                invoice_id = VALUES(invoice_id),
                data_source = VALUES(data_source),
                updated_at = NOW(),
                entered_by = VALUES(entered_by)
        ");
        
        $stmt->execute([
            'month' => $dataMonth,
            'year' => $dataYear,
            'cns_amount' => $data['source_data']['amounts']['cns_amount'] ?? 0,
            'patient_amount' => $data['source_data']['amounts']['patient_amount'] ?? 0,
            'secretary_amount' => $data['source_data']['settings']['secretary_value'] ?? 0,
            'invoice_id' => $invoiceId,
            'created_by' => $this->generatedBy,
            'user_id' => $this->userId
        ]);
    }
    
    /**
     * Get all generated invoices for a user
     */
    public static function getUserGeneratedInvoices($userId, $year = null)
    {
        $db = Flight::db();
        $sql = "
            SELECT ugi.*, i.invoice_number, i.status, i.total
            FROM user_generated_invoices ugi
            INNER JOIN invoices i ON i.id = ugi.invoice_id
            WHERE ugi.user_id = :user_id
        ";
        
        $params = ['user_id' => $userId];
        
        if ($year) {
            $sql .= " AND ugi.period_year = :year";
            $params['year'] = $year;
        }
        
        $sql .= " ORDER BY ugi.period_year DESC, ugi.period_month DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if invoice can be regenerated
     */
    public static function canRegenerate($userId, $invoiceType, $month, $year)
    {
        $db = Flight::db();
        
        // Check if invoice exists and get its status
        $stmt = $db->prepare("
            SELECT i.status, i.id
            FROM user_generated_invoices ugi
            INNER JOIN invoices i ON i.id = ugi.invoice_id
            WHERE ugi.user_id = :user_id 
            AND ugi.invoice_type = :type 
            AND ugi.period_month = :month 
            AND ugi.period_year = :year
        ");
        
        $stmt->execute([
            'user_id' => $userId,
            'type' => $invoiceType,
            'month' => $month,
            'year' => $year
        ]);
        
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Can regenerate if no invoice or invoice is draft
        return !$invoice || $invoice['status'] === 'draft';
    }
}