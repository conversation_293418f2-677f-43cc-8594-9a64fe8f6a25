{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.bulk_generation') | default('Génération de factures en masse') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.bulk_generation') | default('Génération de factures en masse') }}</h1>
            <p class="text-muted mb-0">{{ __('invoices.generate_all_invoice_types') | default('Générer tous les types de factures pour une période') }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/invoices" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
        </div>
    </div>

    <!-- Period Selection -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ __('invoices.select_period') | default('Sélectionner la période') }}</h6>
        </div>
        <div class="card-body">
            <form id="periodForm" class="row g-3">
                <div class="col-md-3">
                    <label for="month" class="form-label">{{ __('invoices.month') | default('Mois') }}</label>
                    <select class="form-select" id="month" name="month">
                        {% for value, name in months %}
                            <option value="{{ value }}" {% if value == month %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="year" class="form-label">{{ __('invoices.year') | default('Année') }}</label>
                    <select class="form-select" id="year" name="year">
                        {% for y in years %}
                            <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">
                        <i class="bi bi-arrow-clockwise me-2"></i>{{ __('invoices.load_data') | default('Charger les données') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoice Type Tabs -->
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#retrocession" role="tab">
                        <i class="bi bi-arrow-left-right me-2"></i>{{ __('invoices.retrocession') | default('Rétrocession') }}
                        <span class="badge bg-primary ms-2">{{ stats.retrocession.total }}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#loyer" role="tab">
                        <i class="bi bi-house-door me-2"></i>{{ __('invoices.loyer') | default('Loyer') }}
                        <span class="badge bg-primary ms-2">{{ stats.loyer.total }}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#courses" role="tab">
                        <i class="bi bi-calendar-event me-2"></i>{{ __('invoices.courses') | default('Cours') }}
                        <span class="badge bg-primary ms-2">{{ stats.courses.total }}</span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <!-- Retrocession Tab -->
                <div class="tab-pane fade show active" id="retrocession" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="fw-bold">{{ __('invoices.practitioners_with_amounts') | default('Praticiens avec montants configurés') }}</h6>
                        {% if stats.retrocession.ready_to_generate > 0 %}
                            <button type="button" class="btn btn-success" onclick="generateBulk('RET')">
                                <i class="bi bi-play-circle me-2"></i>{{ __('invoices.generate_selected') | default('Générer sélectionnées') }} (<span id="retCount">0</span>)
                            </button>
                        {% endif %}
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input select-all" type="checkbox" data-type="RET">
                                        </div>
                                    </th>
                                    <th style="width: 30%;">{{ __('invoices.practitioner') | default('Praticien') }}</th>
                                    <th class="text-end" style="width: 15%;">{{ __('invoices.cns_amount') | default('Montant CNS') }}</th>
                                    <th class="text-end" style="width: 15%;">{{ __('invoices.patient_amount') | default('Montant Patients') }}</th>
                                    <th class="text-end" style="width: 15%;">{{ __('invoices.total') | default('Total') }}</th>
                                    <th class="text-center" style="width: 10%;">{{ __('common.status') | default('Statut') }}</th>
                                    <th class="text-center" style="width: 15%;">{{ __('invoices.invoice') | default('Facture') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in retrocession_users %}
                                <tr data-user-id="{{ user.user_id }}" class="{% if user.invoice_id %}table-secondary{% endif %}">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" 
                                                   type="checkbox" 
                                                   data-type="RET"
                                                   value="{{ user.user_id }}"
                                                   {% if user.invoice_id %}disabled{% endif %}>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ user.name }}</div>
                                            <small class="text-muted">{{ user.email }}</small>
                                        </div>
                                    </td>
                                    <td class="text-end">{{ user.cns_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-end">{{ user.patient_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-end fw-bold">{{ user.total_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-center">
                                        {% if user.invoice_id %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>{{ __('invoices.generated') | default('Générée') }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>{{ __('invoices.pending') | default('En attente') }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if user.invoice_number %}
                                            <a href="{{ base_url }}/invoices/{{ user.invoice_id }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye me-1"></i>{{ user.invoice_number }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center py-4 text-muted">
                                        {{ __('invoices.no_practitioners_with_amounts') | default('Aucun praticien avec des montants configurés pour cette période') }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Loyer Tab -->
                <div class="tab-pane fade" id="loyer" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="fw-bold">{{ __('invoices.users_with_obligations') | default('Utilisateurs avec obligations financières') }}</h6>
                        {% if stats.loyer.ready_to_generate > 0 %}
                            <button type="button" class="btn btn-success" onclick="generateBulk('LOY')">
                                <i class="bi bi-play-circle me-2"></i>{{ __('invoices.generate_selected') | default('Générer sélectionnées') }} (<span id="loyCount">0</span>)
                            </button>
                        {% endif %}
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input select-all" type="checkbox" data-type="LOY">
                                        </div>
                                    </th>
                                    <th style="width: 25%;">{{ __('invoices.user') | default('Utilisateur') }}</th>
                                    <th class="text-end" style="width: 12%;">{{ __('invoices.rent') | default('Loyer') }}</th>
                                    <th class="text-end" style="width: 12%;">{{ __('invoices.charges') | default('Charges') }}</th>
                                    <th class="text-end" style="width: 12%;">{{ __('invoices.secretary') | default('Secrétariat') }}</th>
                                    <th class="text-end" style="width: 12%;">{{ __('invoices.total') | default('Total') }}</th>
                                    <th class="text-center" style="width: 10%;">{{ __('common.status') | default('Statut') }}</th>
                                    <th class="text-center" style="width: 15%;">{{ __('invoices.invoice') | default('Facture') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in loyer_users %}
                                <tr data-user-id="{{ user.user_id }}" class="{% if user.invoice_id %}table-secondary{% endif %}">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" 
                                                   type="checkbox" 
                                                   data-type="LOY"
                                                   value="{{ user.user_id }}"
                                                   {% if user.invoice_id %}disabled{% endif %}>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ user.name }}</div>
                                            <small class="text-muted">{{ user.email }}</small>
                                        </div>
                                    </td>
                                    <td class="text-end">{{ user.rent_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-end">{{ user.charges_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-end">{{ user.secretary_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-end fw-bold">{{ user.total_amount|number_format(2, ',', ' ') }} €</td>
                                    <td class="text-center">
                                        {% if user.invoice_id %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>{{ __('invoices.generated') | default('Générée') }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>{{ __('invoices.pending') | default('En attente') }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if user.invoice_number %}
                                            <a href="{{ base_url }}/invoices/{{ user.invoice_id }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye me-1"></i>{{ user.invoice_number }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center py-4 text-muted">
                                        {{ __('invoices.no_users_with_obligations') | default('Aucun utilisateur avec des obligations financières pour cette période') }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Courses Tab -->
                <div class="tab-pane fade" id="courses" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="fw-bold">{{ __('invoices.coaches_with_courses') | default('Coachs avec cours enregistrés') }}</h6>
                        {% if stats.courses.ready_to_generate > 0 %}
                            <button type="button" class="btn btn-success" onclick="generateBulk('LOC')">
                                <i class="bi bi-play-circle me-2"></i>{{ __('invoices.generate_selected') | default('Générer sélectionnées') }} (<span id="locCount">0</span>)
                            </button>
                        {% endif %}
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input class="form-check-input select-all" type="checkbox" data-type="LOC">
                                        </div>
                                    </th>
                                    <th>{{ __('invoices.coach') | default('Coach') }}</th>
                                    <th>{{ __('invoices.courses') | default('Cours') }}</th>
                                    <th>{{ __('invoices.total_sessions') | default('Total séances') }}</th>
                                    <th>{{ __('invoices.total_amount') | default('Montant total') }}</th>
                                    <th>{{ __('common.status') }}</th>
                                    <th>{{ __('invoices.invoice') | default('Facture') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in course_users %}
                                <tr data-user-id="{{ user.user_id }}" class="{% if user.invoice_id %}table-secondary{% endif %}">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" 
                                                   type="checkbox" 
                                                   data-type="LOC"
                                                   value="{{ user.user_id }}"
                                                   {% if user.invoice_id %}disabled{% endif %}>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ user.name }}</div>
                                            <small class="text-muted">{{ user.email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            {% for course in user.courses %}
                                                <div>{{ course.name }}: {{ course.count }} séances</div>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>{{ user.total_sessions }}</td>
                                    <td class="fw-bold">€ {{ user.total_amount|number_format(2, ',', ' ') }}</td>
                                    <td>
                                        {% if user.invoice_id %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>{{ __('invoices.generated') | default('Générée') }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>{{ __('invoices.pending') | default('En attente') }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if user.invoice_number %}
                                            <a href="{{ base_url }}/invoices/{{ user.invoice_id }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye me-1"></i>{{ user.invoice_number }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center py-4 text-muted">
                                        {{ __('invoices.no_coaches_with_courses') | default('Aucun coach avec des cours enregistrés pour cette période') }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.generating_invoices') | default('Génération des factures') }}</h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <p class="text-center mb-2" id="progressText">{{ __('invoices.processing') | default('Traitement en cours...') }}</p>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" id="progressBar" style="width: 0%">
                        0%
                    </div>
                </div>
                <div class="mt-3" id="progressDetails"></div>
            </div>
        </div>
    </div>
</div>

<!-- Results Modal -->
<div class="modal fade" id="resultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.generation_results') | default('Résultats de la génération') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultsContent">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.close') }}</button>
                <button type="button" class="btn btn-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>{{ __('common.refresh') }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Period form submission
    $('#periodForm').on('submit', function(e) {
        e.preventDefault();
        const month = $('#month').val();
        const year = $('#year').val();
        window.location.href = '{{ base_url }}/invoices/bulk-generation?month=' + month + '&year=' + year;
    });
    
    // Select all checkbox for each type
    $('.select-all').on('change', function() {
        const type = $(this).data('type');
        $(`.user-checkbox[data-type="${type}"]:not(:disabled)`).prop('checked', $(this).prop('checked'));
        updateCounts();
    });
    
    // Individual checkbox change
    $('.user-checkbox').on('change', function() {
        updateCounts();
    });
    
    // Update counts for each type
    function updateCounts() {
        ['RET', 'LOY', 'LOC'].forEach(type => {
            const count = $(`.user-checkbox[data-type="${type}"]:checked`).length;
            $(`#${type.toLowerCase()}Count`).text(count);
        });
    }
    
    // Initialize counts
    updateCounts();
});

// Generate bulk invoices
function generateBulk(type) {
    console.log('=== Starting bulk generation ===');
    console.log('Type:', type);
    
    const selectedIds = [];
    $(`.user-checkbox[data-type="${type}"]:checked`).each(function() {
        selectedIds.push($(this).val());
    });
    
    console.log('Selected user IDs:', selectedIds);
    
    if (selectedIds.length === 0) {
        alert('{{ __("invoices.please_select_users") | default("Veuillez sélectionner au moins un utilisateur") }}');
        return;
    }
    
    // Show progress modal
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // Prepare form data
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token }}');
    formData.append('type', type);
    formData.append('month', '{{ month }}');
    formData.append('year', '{{ year }}');
    selectedIds.forEach(id => formData.append('user_ids[]', id));
    
    // Log form data
    console.log('Form data being sent:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }
    
    // Update progress
    let processed = 0;
    const total = selectedIds.length;
    updateProgress(0, total);
    
    // Build full URL
    const url = '{{ base_url }}/invoices/bulk-generation/generate';
    console.log('Request URL:', url);
    
    // Send request
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        console.log('Content-Type:', response.headers.get('content-type'));
        
        // Clone response to read it twice if needed
        const responseClone = response.clone();
        
        // First try to get text to see what we're dealing with
        return response.text().then(text => {
            console.log('Raw response:', text);
            console.log('Response length:', text.length);
            console.log('First 500 chars:', text.substring(0, 500));
            
            // Try to parse as JSON
            try {
                const data = JSON.parse(text);
                console.log('Successfully parsed JSON:', data);
                return data;
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                console.error('Full response that failed to parse:', text);
                
                // Check if it's HTML (error page)
                if (text.includes('<!DOCTYPE') || text.includes('<html')) {
                    console.error('Response is HTML, likely an error page');
                    // Try to extract error message from HTML
                    const match = text.match(/<title>(.*?)<\/title>/);
                    if (match) {
                        console.error('Page title:', match[1]);
                    }
                }
                
                throw new Error('Server returned invalid JSON. Check console for details.');
            }
        });
    })
    .then(data => {
        console.log('Processing response data:', data);
        
        // Hide progress modal
        bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
        
        if (data.success) {
            console.log('Success! Results:', data.results);
            showResults(data.results);
        } else {
            console.error('Server returned error:', data.message);
            alert(data.message || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack
        });
        
        bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
        alert('{{ __("common.error_occurred") }}: ' + error.message);
    });
}

// Update progress display
function updateProgress(current, total) {
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
    $('#progressBar').css('width', percentage + '%').text(percentage + '%');
    $('#progressText').text('{{ __("invoices.processing") | default("Traitement en cours") }}... ' + current + '/' + total);
}

// Show results
function showResults(results) {
    let html = '<div class="row g-3 mb-4">';
    
    // Success card
    html += '<div class="col-md-6">';
    html += '<div class="card border-start border-4 border-success">';
    html += '<div class="card-body">';
    html += '<h6 class="text-success">{{ __("invoices.invoices_generated") | default("Factures générées") }}</h6>';
    html += '<h3 class="mb-0">' + (results.success || 0) + '</h3>';
    html += '</div></div></div>';
    
    // Failed card
    html += '<div class="col-md-6">';
    html += '<div class="card border-start border-4 border-danger">';
    html += '<div class="card-body">';
    html += '<h6 class="text-danger">{{ __("invoices.generation_failed") | default("Échecs") }}</h6>';
    html += '<h3 class="mb-0">' + (results.failed || 0) + '</h3>';
    html += '</div></div></div>';
    
    html += '</div>';
    
    // Success list
    if (results.invoices && results.invoices.length > 0) {
        html += '<h6 class="fw-bold mb-3">{{ __("invoices.generated_invoices") | default("Factures générées avec succès") }}</h6>';
        html += '<div class="list-group mb-4">';
        results.invoices.forEach(function(invoice) {
            html += '<a href="{{ base_url }}/invoices/' + invoice.invoice_id + '" class="list-group-item list-group-item-action">';
            html += '<div class="d-flex justify-content-between align-items-center">';
            html += '<div>';
            html += '<strong>' + invoice.user_name + '</strong>';
            html += '<div class="text-muted small">' + invoice.invoice_number + ' - €' + invoice.amount + '</div>';
            html += '</div>';
            html += '<i class="bi bi-arrow-right"></i>';
            html += '</div></a>';
        });
        html += '</div>';
    }
    
    // Error list
    if (results.errors && results.errors.length > 0) {
        html += '<h6 class="fw-bold mb-3 text-danger">{{ __("invoices.errors") | default("Erreurs") }}</h6>';
        html += '<div class="list-group">';
        results.errors.forEach(function(error) {
            html += '<div class="list-group-item list-group-item-danger">';
            html += '<strong>' + error.user_name + '</strong>';
            html += '<div class="text-danger small">' + error.error + '</div>';
            html += '</div>';
        });
        html += '</div>';
    }
    
    $('#resultsContent').html(html);
    
    // Show results modal
    const resultsModal = new bootstrap.Modal(document.getElementById('resultsModal'));
    resultsModal.show();
}
</script>
{% endblock %}