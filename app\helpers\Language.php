<?php

namespace App\Helpers;

class Language
{
    private static $translations = [];
    private static $currentLanguage = 'fr';
    private static $fallbackLanguage = 'en';
    private static $loadedGroups = [];
    
    /**
     * Set the current language
     */
    public static function setLanguage($language)
    {
        self::$currentLanguage = $language;
        self::$loadedGroups = []; // Clear loaded groups when language changes
        self::$translations = [];
    }
    
    /**
     * Get the current language
     */
    public static function getCurrentLanguage()
    {
        return self::$currentLanguage;
    }
    
    /**
     * Set the fallback language
     */
    public static function setFallbackLanguage($language)
    {
        self::$fallbackLanguage = $language;
    }
    
    /**
     * Load translations from a specific group
     */
    public static function load($group, $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        $cacheKey = $language . '.' . $group;
        
        // Check if already loaded
        if (in_array($cacheKey, self::$loadedGroups)) {
            return true;
        }
        
        $file = __DIR__ . '/../lang/' . $language . '/' . $group . '.php';
        
        if (file_exists($file)) {
            $translations = include $file;
            
            if (is_array($translations)) {
                if (!isset(self::$translations[$language])) {
                    self::$translations[$language] = [];
                }
                
                self::$translations[$language][$group] = $translations;
                self::$loadedGroups[] = $cacheKey;
                return true;
            }
        }
        
        // Try to load fallback language
        if ($language !== self::$fallbackLanguage) {
            return self::load($group, self::$fallbackLanguage);
        }
        
        return false;
    }
    
    /**
     * Get a translation by key
     */
    public static function get($key, $params = [], $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        // Parse the key (e.g., "users.username" => group: users, item: username)
        $segments = explode('.', $key);
        
        if (count($segments) < 2) {
            return $key; // Invalid key format
        }
        
        $group = $segments[0];
        $item = implode('.', array_slice($segments, 1));
        
        // Load the group if not already loaded
        self::load($group, $language);
        
        // Get the translation
        // First try to get it directly if the key contains dots (like month.1)
        if (isset(self::$translations[$language][$group][$item])) {
            $translation = self::$translations[$language][$group][$item];
        } else {
            // Try using the array traversal method
            $translationKey = $language . '.' . $group . '.' . $item;
            $translation = self::getFromArray($translationKey, self::$translations);
        }
        
        // If not found, try fallback language
        if ($translation === null && $language !== self::$fallbackLanguage) {
            self::load($group, self::$fallbackLanguage);
            
            // Try direct access first
            if (isset(self::$translations[self::$fallbackLanguage][$group][$item])) {
                $translation = self::$translations[self::$fallbackLanguage][$group][$item];
            } else {
                $fallbackKey = self::$fallbackLanguage . '.' . $group . '.' . $item;
                $translation = self::getFromArray($fallbackKey, self::$translations);
            }
        }
        
        // If still not found, return the key
        if ($translation === null) {
            $translation = $key;
        }
        
        // Replace parameters
        if (!empty($params)) {
            foreach ($params as $param => $value) {
                $translation = str_replace(':' . $param, $value, $translation);
            }
        }
        
        return $translation;
    }
    
    /**
     * Get value from array using dot notation
     * 
     * @param string $key The dot-notation key
     * @param array $array The array to search
     * @return mixed|null The value or null if not found
     */
    private static function getFromArray(string $key, array $array)
    {
        if (strpos($key, '.') === false) {
            return isset($array[$key]) ? $array[$key] : null;
        }
        
        foreach (explode('.', $key) as $segment) {
            if (!is_array($array) || !array_key_exists($segment, $array)) {
                return null;
            }
            $array = $array[$segment];
        }
        
        return $array;
    }
    
    /**
     * Check if a translation exists
     */
    public static function has($key, $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        $translation = self::get($key, [], $language);
        return $translation !== $key;
    }
    
    /**
     * Get all translations for a group
     */
    public static function getGroup($group, $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        self::load($group, $language);
        
        return self::$translations[$language][$group] ?? [];
    }
    
    /**
     * Load all groups for the current language
     */
    public static function loadAll($language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        $langDir = __DIR__ . '/../lang/' . $language;
        
        if (is_dir($langDir)) {
            $files = glob($langDir . '/*.php');
            
            foreach ($files as $file) {
                $group = basename($file, '.php');
                self::load($group, $language);
            }
        }
    }
    
    /**
     * Get available languages
     */
    public static function getAvailableLanguages()
    {
        $languages = [];
        $langDir = __DIR__ . '/../lang';
        
        if (is_dir($langDir)) {
            $dirs = glob($langDir . '/*', GLOB_ONLYDIR);
            
            foreach ($dirs as $dir) {
                $languages[] = basename($dir);
            }
        }
        
        return $languages;
    }
}