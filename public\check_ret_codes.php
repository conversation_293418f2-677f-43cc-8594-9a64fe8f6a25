<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Retrocession Types</h2>";
    
    // Check what's using the 'retr' code
    echo "<h3>Records with code 'retr':</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code = 'retr'");
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "<pre>";
        print_r($result);
        echo "</pre>";
    } else {
        echo "<p>No records found with code 'retr'</p>";
    }
    
    // Check all retrocession-related types
    echo "<h3>All retrocession-related types:</h3>";
    $stmt = $db->query("SELECT id, code, prefix, name FROM config_invoice_types WHERE code LIKE '%retr%' OR prefix LIKE 'RET%' ORDER BY id");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . htmlspecialchars($type['code']) . "</td>";
        echo "<td>" . htmlspecialchars($type['prefix']) . "</td>";
        echo "<td>" . htmlspecialchars($type['name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}