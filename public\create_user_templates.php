<?php
// Create user templates script
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
try {
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $db = new PDO($dsn, $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

session_start();
$userId = $_SESSION['user_id'] ?? 1;

echo "<h2>Creating User Templates...</h2>";

// Create some user templates
$templates = [
    [
        'name' => 'My Custom Rental Template',
        'code' => 'USER_RENTAL_001',
        'invoice_type' => 'rental',
        'owner_type' => 'user',
        'owner_id' => $userId,
        'description' => 'Custom template for monthly rentals',
        'is_active' => 1,
        'created_by' => $userId
    ],
    [
        'name' => 'Hourly Service Template',
        'code' => 'USER_HOURLY_001',
        'invoice_type' => 'hourly',
        'owner_type' => 'user',
        'owner_id' => $userId,
        'description' => 'Custom template for hourly services',
        'is_active' => 1,
        'created_by' => $userId
    ],
    [
        'name' => 'Group Template Example',
        'code' => 'GROUP_TEMPLATE_001',
        'invoice_type' => 'service',
        'owner_type' => 'group',
        'owner_id' => 1, // Administrators group
        'description' => 'Template owned by Administrators group',
        'is_active' => 1,
        'created_by' => $userId
    ]
];

foreach ($templates as $template) {
    try {
        // Check if template already exists
        $checkStmt = $db->prepare("SELECT id FROM invoice_templates WHERE code = ?");
        $checkStmt->execute([$template['code']]);
        
        if ($checkStmt->rowCount() > 0) {
            echo "Template '{$template['name']}' already exists.<br>";
            continue;
        }
        
        // Insert template
        $sql = "INSERT INTO invoice_templates (name, code, invoice_type, owner_type, owner_id, description, is_active, created_by, created_at) 
                VALUES (:name, :code, :invoice_type, :owner_type, :owner_id, :description, :is_active, :created_by, NOW())";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($template);
        
        echo "✅ Created template: {$template['name']}<br>";
        
    } catch (PDOException $e) {
        echo "❌ Error creating template '{$template['name']}': " . $e->getMessage() . "<br>";
    }
}

echo "<br><a href='/fit/public/config/invoice-templates'>Go to Invoice Templates</a>";
echo " | <a href='/fit/public/template_diagnostic.php'>Check Diagnostic</a>";
?>