<?php
// Direct check of invoice 263 - simple query
echo "<h1>Direct Check of Invoice 263</h1>";

try {
    // Direct connection
    $db = new PDO("mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4", "root", "test1234");
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Simple query
    $stmt = $db->prepare("SELECT * FROM invoices WHERE id = 263");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<h2>✅ Invoice 263 Found!</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$invoice['id']}</td></tr>";
        echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
        echo "<tr><td>Total</td><td>€ " . number_format($invoice['total'], 2) . "</td></tr>";
        echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
        echo "<tr><td>Client ID</td><td>{$invoice['client_id']}</td></tr>";
        echo "</table>";
        
        // Check if it's already 930.00
        if (abs($invoice['total'] - 930.00) < 0.01) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>✅ Invoice Total is Already Correct!</h3>";
            echo "<p>The invoice shows €" . number_format($invoice['total'], 2) . " which matches the expected €930.00</p>";
            echo "<p><strong>No action needed - the invoice is already fixed!</strong></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>❌ Total Needs Adjustment</h3>";
            echo "<p>Current: €" . number_format($invoice['total'], 2) . "</p>";
            echo "<p>Expected: €930.00</p>";
            echo "<p>Difference: €" . number_format(930.00 - $invoice['total'], 2) . "</p>";
            echo "</div>";
        }
        
        // Get client name
        $stmt = $db->prepare("SELECT first_name, last_name FROM users WHERE id = ?");
        $stmt->execute([$invoice['client_id']]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($client) {
            echo "<p><strong>Client:</strong> {$client['first_name']} {$client['last_name']}</p>";
        }
        
    } else {
        echo "<h2>❌ Invoice 263 Not Found</h2>";
        echo "<p>The invoice doesn't exist in the database, but you can see it in the interface.</p>";
        echo "<p>This suggests either:</p>";
        echo "<ul>";
        echo "<li>Database connection issue</li>";
        echo "<li>Different database being used</li>";
        echo "<li>Caching issue in the interface</li>";
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    echo "<p>This explains why the invoice couldn't be found in previous scripts.</p>";
}

// Also check table structure
try {
    $stmt = $db->prepare("DESCRIBE invoices");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Database Table Structure</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Column</th><th>Type</th><th>Key</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p>Could not describe table: " . $e->getMessage() . "</p>";
}

// Check recent invoices
try {
    $stmt = $db->prepare("SELECT id, invoice_number, total, status FROM invoices ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Recent Invoices</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Invoice Number</th><th>Total</th><th>Status</th></tr>";
    
    foreach ($recent as $inv) {
        $highlight = ($inv['id'] == 263) ? 'background-color: #ffffcc;' : '';
        echo "<tr style='$highlight'>";
        echo "<td>{$inv['id']}</td>";
        echo "<td>{$inv['invoice_number']}</td>";
        echo "<td>€ " . number_format($inv['total'], 2) . "</td>";
        echo "<td>{$inv['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p>Could not fetch recent invoices: " . $e->getMessage() . "</p>";
}

?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
</style>