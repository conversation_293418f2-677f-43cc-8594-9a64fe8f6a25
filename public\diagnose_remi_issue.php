<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Diagnosing <PERSON><PERSON><PERSON>'s Invoice Generation Issue</h2>";
    
    // Find Rémi
    $stmt = $db->prepare("
        SELECT u.id as user_id, u.first_name, u.last_name, c.id as client_id
        FROM users u
        LEFT JOIN clients c ON c.user_id = u.id
        WHERE u.first_name = '<PERSON><PERSON><PERSON>' AND u.last_name = 'Heine'
    ");
    $stmt->execute();
    $remi = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><PERSON><PERSON><PERSON> Heine - User ID: {$remi['user_id']}, Client ID: {$remi['client_id']}</p>";
    
    // Current date context
    $currentMonth = 7; // July
    $currentYear = 2025;
    $dataMonth = 6; // June (previous month for retrocession)
    $dataYear = 2025;
    
    echo "<h3>Invoice Generation Context:</h3>";
    echo "<p>Generating in: July 2025</p>";
    echo "<p>For data from: June 2025 (month $dataMonth)</p>";
    
    // Check if year column exists
    echo "<h3>Table Structure Check:</h3>";
    $yearCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
    $hasYearColumn = $yearCheck->rowCount() > 0;
    echo "<p>user_monthly_retrocession_amounts has 'year' column: " . ($hasYearColumn ? "YES" : "NO") . "</p>";
    
    // Query for monthly amounts
    echo "<h3>Checking Monthly Amounts:</h3>";
    
    if ($hasYearColumn) {
        echo "<p>Using query WITH year column:</p>";
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month AND year = :year
        ");
        $stmt->execute(['user_id' => $remi['user_id'], 'month' => $dataMonth, 'year' => $dataYear]);
    } else {
        echo "<p>Using query WITHOUT year column:</p>";
        $stmt = $db->prepare("
            SELECT * FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month
        ");
        $stmt->execute(['user_id' => $remi['user_id'], 'month' => $dataMonth]);
    }
    
    $amounts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($amounts) {
        echo "<p style='color: green;'>✓ Found amounts for June:</p>";
        echo "<ul>";
        echo "<li>CNS Amount: " . number_format($amounts['cns_amount'], 2) . " €</li>";
        echo "<li>Patient Amount: " . number_format($amounts['patient_amount'], 2) . " €</li>";
        if (isset($amounts['year'])) {
            echo "<li>Year: {$amounts['year']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ No amounts found for June!</p>";
    }
    
    // Show ALL monthly amounts for Rémi
    echo "<h3>ALL Monthly Amounts for Rémi:</h3>";
    $stmt = $db->prepare("
        SELECT * FROM user_monthly_retrocession_amounts 
        WHERE user_id = :user_id
        ORDER BY " . ($hasYearColumn ? "year DESC, " : "") . "month
    ");
    $stmt->execute(['user_id' => $remi['user_id']]);
    $allAmounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Month</th>" . ($hasYearColumn ? "<th>Year</th>" : "") . "<th>CNS</th><th>Patient</th></tr>";
    foreach ($allAmounts as $amt) {
        echo "<tr>";
        echo "<td>{$amt['id']}</td>";
        echo "<td>{$amt['month']}</td>";
        if ($hasYearColumn) {
            echo "<td>" . ($amt['year'] ?? 'NULL') . "</td>";
        }
        echo "<td>" . number_format($amt['cns_amount'], 2) . " €</td>";
        echo "<td>" . number_format($amt['patient_amount'], 2) . " €</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check retrocession_data_entry
    echo "<h3>Checking retrocession_data_entry:</h3>";
    echo "<p>Note: practitioner_id in this table actually refers to client_id</p>";
    
    $stmt = $db->prepare("
        SELECT * FROM retrocession_data_entry 
        WHERE practitioner_id = :client_id 
        AND period_year = :year 
        AND period_month = :month
    ");
    $stmt->execute(['client_id' => $remi['client_id'], 'year' => $dataYear, 'month' => $dataMonth]);
    $dataEntry = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($dataEntry) {
        echo "<p>Found existing entry:</p>";
        echo "<ul>";
        echo "<li>Status: {$dataEntry['status']}</li>";
        echo "<li>Invoice ID: " . ($dataEntry['invoice_id'] ?? 'NULL') . "</li>";
        echo "</ul>";
        
        if ($dataEntry['invoice_id']) {
            echo "<p style='color: orange;'>⚠️ Already invoiced! This might be why generation fails.</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ No retrocession_data_entry found</p>";
    }
    
    // The issue
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Issue Summary:</h3>";
    
    if (!$amounts) {
        echo "<p>The problem is that the monthly amounts query is not finding data for June.</p>";
        if ($hasYearColumn) {
            echo "<p>The year column exists but might be NULL for Rémi's data.</p>";
        }
    } elseif ($dataEntry && $dataEntry['invoice_id']) {
        echo "<p>June has already been invoiced (Invoice ID: {$dataEntry['invoice_id']})</p>";
    }
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}