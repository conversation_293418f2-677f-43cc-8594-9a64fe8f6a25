<?php

namespace App\Services;

use Flight;
use PDO;

class DashboardService
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Get dashboard statistics
     */
    public function getStats()
    {
        return [
            'total_clients' => $this->getTotalClients(),
            'total_patients' => $this->getTotalPatients(),
            'total_invoices' => $this->getTotalInvoices(),
            'revenue_this_month' => $this->getRevenueThisMonth()
        ];
    }
    
    /**
     * Get total number of clients
     */
    private function getTotalClients()
    {
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM clients WHERE is_active = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }
    
    /**
     * Get total number of patients
     */
    private function getTotalPatients()
    {
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM patients WHERE is_active = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }
    
    /**
     * Get total number of invoices
     */
    private function getTotalInvoices()
    {
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM invoices");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }
    
    /**
     * Get revenue for current month
     */
    private function getRevenueThisMonth()
    {
        $firstDay = date('Y-m-01');
        $lastDay = date('Y-m-t');
        
        $stmt = $this->db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as revenue
            FROM invoices
            WHERE issue_date BETWEEN :start AND :end
            AND status != 'cancelled'
        ");
        
        $stmt->execute([
            'start' => $firstDay,
            'end' => $lastDay
        ]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return round($result['revenue'] ?? 0, 2);
    }
    
    /**
     * Get recent invoices
     */
    public function getRecentInvoices($limit = 10)
    {
        $stmt = $this->db->prepare("
            SELECT 
                i.id,
                i.invoice_number,
                i.issue_date,
                i.due_date,
                i.total_amount,
                i.status,
                COALESCE(c.name, CONCAT(p.first_name, ' ', p.last_name)) as client_name
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN patients p ON i.patient_id = p.id
            ORDER BY i.created_at DESC
            LIMIT :limit
        ");
        
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get monthly revenue for the last 12 months
     */
    public function getMonthlyRevenue()
    {
        $data = [];
        
        // Get data for last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-$i months"));
            $startDate = date('Y-m-01', strtotime("-$i months"));
            $endDate = date('Y-m-t', strtotime("-$i months"));
            
            $stmt = $this->db->prepare("
                SELECT COALESCE(SUM(total_amount), 0) as revenue
                FROM invoices
                WHERE issue_date BETWEEN :start AND :end
                AND status != 'cancelled'
            ");
            
            $stmt->execute([
                'start' => $startDate,
                'end' => $endDate
            ]);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $data[] = [
                'month' => date('M Y', strtotime($startDate)),
                'revenue' => round($result['revenue'] ?? 0, 2)
            ];
        }
        
        return $data;
    }
    
    /**
     * Get invoice status chart data
     */
    public function getInvoiceStatusChart()
    {
        $stmt = $this->db->query("
            SELECT 
                status,
                COUNT(*) as count
            FROM invoices
            WHERE YEAR(issue_date) = YEAR(CURRENT_DATE)
            GROUP BY status
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $data = [];
        foreach ($results as $row) {
            $data[] = [
                'status' => ucfirst($row['status']),
                'count' => $row['count']
            ];
        }
        
        return $data;
    }
    
    /**
     * Get recent activities
     */
    public function getRecentActivities()
    {
        // For now, return recent invoices as activities
        // This can be expanded to include other activities
        $stmt = $this->db->query("
            SELECT 
                'invoice_created' as type,
                i.invoice_number as reference,
                i.created_at as timestamp,
                COALESCE(c.name, CONCAT(p.first_name, ' ', p.last_name)) as entity
            FROM invoices i
            LEFT JOIN clients c ON i.client_id = c.id
            LEFT JOIN patients p ON i.patient_id = p.id
            ORDER BY i.created_at DESC
            LIMIT 20
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}