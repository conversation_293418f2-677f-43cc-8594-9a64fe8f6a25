<?php
/**
 * Check Email Logs Table Status
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Check Email Logs Table</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Email Logs Table Status</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // Check if email_logs table exists
        echo "<h2>1. Table Existence Check</h2>";
        $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
        $tableExists = $stmt->fetch();
        
        if ($tableExists) {
            echo "<p class='success'>✅ email_logs table EXISTS</p>";
            
            // Show table structure
            echo "<h3>Table Structure:</h3>";
            $stmt = $db->query("SHOW COLUMNS FROM email_logs");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($columns as $col) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($col['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Count records
            echo "<h3>Record Count:</h3>";
            $stmt = $db->query("SELECT COUNT(*) as total FROM email_logs");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>Total email logs: <strong>" . $count['total'] . "</strong></p>";
            
            // Show recent logs
            echo "<h3>Recent Email Logs (Last 10):</h3>";
            $stmt = $db->query("SELECT * FROM email_logs ORDER BY created_at DESC LIMIT 10");
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($logs) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Invoice ID</th><th>Recipient</th><th>Status</th><th>Created</th></tr>";
                foreach ($logs as $log) {
                    $statusClass = $log['status'] === 'sent' ? 'success' : 'error';
                    echo "<tr>";
                    echo "<td>" . $log['id'] . "</td>";
                    echo "<td>" . ($log['invoice_id'] ?: '-') . "</td>";
                    echo "<td>" . htmlspecialchars($log['recipient_email']) . "</td>";
                    echo "<td class='" . $statusClass . "'>" . $log['status'] . "</td>";
                    echo "<td>" . $log['created_at'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>No email logs found in the table.</p>";
            }
            
            // Check for invoice 279
            echo "<h3>Logs for Invoice #279 (FAC-DIV-2025-0190):</h3>";
            $stmt = $db->prepare("SELECT * FROM email_logs WHERE invoice_id = :id ORDER BY created_at DESC");
            $stmt->execute([':id' => 279]);
            $invoice279Logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($invoice279Logs) {
                echo "<pre>";
                print_r($invoice279Logs);
                echo "</pre>";
            } else {
                echo "<p class='error'>❌ No logs found for invoice #279</p>";
            }
            
        } else {
            echo "<p class='error'>❌ email_logs table DOES NOT EXIST!</p>";
            
            // Check for migration file
            $migrationFile = __DIR__ . '/../database/migrations/083_create_email_logs_table.sql';
            if (file_exists($migrationFile)) {
                echo "<div class='info'>";
                echo "<p>Migration file found at: <code>database/migrations/083_create_email_logs_table.sql</code></p>";
                echo "<p>To create the table, run this SQL:</p>";
                echo "<pre>" . htmlspecialchars(file_get_contents($migrationFile)) . "</pre>";
                echo "</div>";
                
                // Try to create the table
                echo "<h3>Attempting to create table...</h3>";
                try {
                    $sql = file_get_contents($migrationFile);
                    $db->exec($sql);
                    echo "<p class='success'>✅ Table created successfully!</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>Failed to create table: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            } else {
                echo "<p class='error'>Migration file not found!</p>";
            }
        }
        
        // Check EmailService logging
        echo "<h2>2. EmailService Configuration</h2>";
        echo "<div class='info'>";
        echo "<p><strong>Email Driver:</strong> " . ($_ENV['MAIL_DRIVER'] ?? 'Not set') . "</p>";
        echo "<p><strong>From Address:</strong> " . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'Not set') . "</p>";
        echo "<p><strong>From Name:</strong> " . ($_ENV['MAIL_FROM_NAME'] ?? 'Not set') . "</p>";
        
        // Check if EmailService class exists
        if (class_exists('App\Services\EmailService')) {
            echo "<p class='success'>✅ EmailService class exists</p>";
            
            // Check if logging method exists
            $emailService = new \App\Services\EmailService();
            $reflection = new ReflectionClass($emailService);
            
            $hasLogMethod = $reflection->hasMethod('logEmail');
            if ($hasLogMethod) {
                echo "<p class='success'>✅ EmailService has logEmail method</p>";
            } else {
                echo "<p class='warning'>⚠️ EmailService might not have logging implemented</p>";
            }
        } else {
            echo "<p class='error'>❌ EmailService class not found</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/send_invoice_email.php?id=279">Send Invoice Email</a> |
        <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Invoice Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>