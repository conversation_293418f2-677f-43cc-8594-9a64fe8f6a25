<?php
// Load composer autoloader first
require __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Now load bootstrap which depends on autoloader and env vars
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

// Get invoice 246 or the most recent invoice
$invoice = new Invoice();
$invoiceData = $invoice->find(246) ?: $invoice->query()->orderBy('id', 'DESC')->first();

if (!$invoiceData) {
    die("No invoices found in the database.");
}

// Load invoice with relations
$fullInvoice = $invoice->getInvoiceWithDetails($invoiceData->id);

echo "<h2>Invoice #" . $fullInvoice['invoice_number'] . " - Format Test</h2>";
echo "<h3>Currency and Percentage Formatting Check</h3>";

// Check invoice totals
echo "<h4>Invoice Totals:</h4>";
echo "<ul>";
echo "<li>Subtotal: " . number_format($fullInvoice['subtotal'], 2, ',', '.') . " €</li>";
echo "<li>VAT Amount: " . number_format($fullInvoice['vat_amount'], 2, ',', '.') . " €</li>";
echo "<li>Total: " . number_format($fullInvoice['total'], 2, ',', '.') . " €</li>";
echo "</ul>";

// Check line items
echo "<h4>Line Items:</h4>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Total</th></tr>";

foreach ($fullInvoice['lines'] as $line) {
    $lineTotal = $line['quantity'] * $line['unit_price'] * (1 + $line['vat_rate'] / 100);
    echo "<tr>";
    echo "<td>" . $line['description'] . "</td>";
    echo "<td>" . $line['quantity'] . "</td>";
    echo "<td>" . number_format($line['unit_price'], 2, ',', '.') . " €</td>";
    echo "<td>" . $line['vat_rate'] . " %</td>";
    echo "<td>" . number_format($lineTotal, 2, ',', '.') . " €</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h4>Expected Format:</h4>";
echo "<p>All currency amounts should show as: '720,00 €' (with space before €)</p>";
echo "<p>All percentages should show as: '17 %' (with space before %)</p>";

// Check current template output by looking at the raw HTML
echo "<h4>Template Check:</h4>";
echo "<p>To verify the template is rendering correctly, view the invoice at: ";
echo "<a href='/fit/public/invoices/" . $fullInvoice['id'] . "' target='_blank'>/invoices/" . $fullInvoice['id'] . "</a></p>";