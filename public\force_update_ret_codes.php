<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Force Update RET Codes</h2>";
    
    // First, let's see EXACTLY what we have
    echo "<h3>All RET-related entries:</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code LIKE '%ret%' OR prefix LIKE '%RET%'
        ORDER BY id
    ");
    $all = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($all);
    echo "</pre>";
    
    // Try a different approach - direct SQL without conditions
    echo "<h3>Executing direct updates:</h3>";
    
    try {
        // Update using direct SQL
        $queries = [
            "UPDATE config_invoice_types SET code = 'ret25' WHERE id = 37",
            "UPDATE config_invoice_types SET code = 'ret30' WHERE id = 38"
        ];
        
        foreach ($queries as $query) {
            echo "<p>Executing: <code>$query</code></p>";
            $result = $db->exec($query);
            echo "<p>Rows affected: $result</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error during update: " . $e->getMessage() . "</p>";
    }
    
    // Check the result
    echo "<h3>After update:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE id IN (28, 37, 38) OR code IN ('ret', 'ret25', 'ret30')
        ORDER BY id
    ");
    $after = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    
    foreach ($after as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $rowStyle = '';
        if ($row['id'] == 37) {
            $rowStyle = 'background-color: #d4edda;';
        } elseif ($row['id'] == 38) {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Alternative: Work with what we have
    echo "<h3>Alternative Solution:</h3>";
    echo "<p>Since the codes won't update, we can work with the existing IDs:</p>";
    echo "<ul>";
    echo "<li>ID 37 (ret2) = FAC-RET25 for 5% secretary</li>";
    echo "<li>ID 38 (ret3) = FAC-RET30 for 10% secretary</li>";
    echo "</ul>";
    
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Alternative Fix for Frank's Invoice</h3>";
    echo "<p>We can update Frank's invoice to use ID 37 (which has prefix FAC-RET25) even though the code is 'ret2'.</p>";
    echo "<p><a href='/fit/public/fix_frank_invoice_by_id.php' style='color: #856404; font-weight: bold;'>→ Fix Frank's invoice using ID 37</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}