<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Frank's Invoice #336</h2>";
    
    // Get invoice details
    $stmt = $db->prepare("
        SELECT i.*, cit.code as invoice_type_code, cit.prefix
        FROM invoices i
        LEFT JOIN config_invoice_types cit ON cit.id = i.type_id
        WHERE i.id = 336
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<h3>Invoice Details:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><td><strong>Invoice ID:</strong></td><td>{$invoice['id']}</td></tr>";
        echo "<tr><td><strong>Invoice Number:</strong></td><td>{$invoice['invoice_number']}</td></tr>";
        echo "<tr><td><strong>type_id:</strong></td><td>{$invoice['type_id']}</td></tr>";
        echo "<tr><td><strong>invoice_type_code:</strong></td><td>{$invoice['invoice_type_code']}</td></tr>";
        echo "<tr><td><strong>prefix:</strong></td><td>{$invoice['prefix']}</td></tr>";
        echo "<tr><td><strong>Status:</strong></td><td>{$invoice['status']}</td></tr>";
        echo "<tr><td><strong>Client:</strong></td><td>ID: {$invoice['client_id']}</td></tr>";
        echo "</table>";
        
        // Check Frank's secretary percentage
        echo "<h3>Frank's Settings:</h3>";
        $stmt = $db->prepare("
            SELECT urs.secretary_value, u.first_name, u.last_name
            FROM users u
            LEFT JOIN user_retrocession_settings urs ON urs.user_id = u.id
            WHERE u.id = 1
            ORDER BY urs.created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $frank = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($frank) {
            echo "<p><strong>User:</strong> {$frank['first_name']} {$frank['last_name']}</p>";
            echo "<p><strong>Secretary Fee:</strong> {$frank['secretary_value']}%</p>";
            echo "<p><strong>Should use:</strong> " . ($frank['secretary_value'] == 5 ? 'ret25' : 'ret30') . "</p>";
        }
        
        // Check what type_id should be used
        echo "<h3>Correct Type ID:</h3>";
        $correctCode = ($frank['secretary_value'] == 5) ? 'ret25' : 'ret30';
        $stmt = $db->prepare("SELECT id, prefix FROM config_invoice_types WHERE code = ?");
        $stmt->execute([$correctCode]);
        $correctType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($correctType) {
            echo "<p><strong>Correct type_id should be:</strong> {$correctType['id']}</p>";
            echo "<p><strong>Which would give prefix:</strong> {$correctType['prefix']}</p>";
            echo "<p><strong>Expected invoice number:</strong> {$correctType['prefix']}-2025-0200</p>";
        }
        
        // Fix suggestion
        if ($invoice['status'] == 'draft' && $correctType && $invoice['type_id'] != $correctType['id']) {
            echo "<h3 style='color: orange;'>⚠️ Fix Required:</h3>";
            echo "<p>This invoice is using the wrong type_id. Since it's still a draft, we can fix it.</p>";
            echo "<p>Current type_id: {$invoice['type_id']} → Should be: {$correctType['id']}</p>";
            
            // Show the SQL to fix it
            echo "<h4>SQL to fix:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo "UPDATE invoices SET type_id = {$correctType['id']} WHERE id = 336;";
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>Invoice #336 not found!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}