<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing LOY Invoice User Filtering</h2>";
    
    // Get Medical and Managers group members
    $medicalGroupId = 5; // Medical Staff
    $medicalGroupId2 = 23; // Medical Group (empty)
    $managersGroupId = 3; // Managers
    
    echo "<h3>1. Medical and Managers Group Members:</h3>";
    
    $sql = "SELECT u.id, u.username, u.email, 
                   CONCAT(u.first_name, ' ', u.last_name) as name,
                   u.can_be_invoiced,
                   GROUP_CONCAT(g.name ORDER BY g.name) as groups
            FROM users u
            JOIN user_group_members ugm ON u.id = ugm.user_id
            JOIN groups g ON ugm.group_id = g.id
            WHERE ugm.group_id IN (:medical1, :medical2, :managers)
            AND u.is_active = 1
            GROUP BY u.id
            ORDER BY u.first_name, u.last_name";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        'medical1' => $medicalGroupId,
        'medical2' => $medicalGroupId2,
        'managers' => $managersGroupId
    ]);
    $loyaltyUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($loyaltyUsers)) {
        echo "<p style='color: orange;'>⚠️ No Medical/Managers users found</p>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Username</th><th>Email</th><th>Groups</th><th>Can Be Invoiced</th>";
        echo "</tr>";
        foreach ($loyaltyUsers as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['groups']}</td>";
            echo "<td style='text-align: center;'>" . ($user['can_be_invoiced'] ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Total users eligible for LOY invoices: " . count($loyaltyUsers) . "</strong></p>";
    }
    
    // Check LOY invoice type
    echo "<h3>2. LOY Invoice Type Configuration:</h3>";
    $sql = "SELECT * FROM invoice_types WHERE prefix = 'LOY' OR code = 'LOY'";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $loyType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($loyType) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Property</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$loyType['id']}</td></tr>";
        echo "<tr><td>Name</td><td>{$loyType['name']}</td></tr>";
        echo "<tr><td>Prefix</td><td><strong>{$loyType['prefix']}</strong></td></tr>";
        echo "<tr><td>Code</td><td>{$loyType['code']}</td></tr>";
        echo "<tr><td>Active</td><td>" . ($loyType['is_active'] ? '✅ Yes' : '❌ No') . "</td></tr>";
        echo "<tr><td>Uses TTC</td><td>✅ Yes (configured in InvoiceTTCHelper.php)</td></tr>";
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ LOY invoice type not found in database!</p>";
    }
    
    // Check financial obligations for eligible users
    echo "<h3>3. Financial Obligations for LOY Users:</h3>";
    $sql = "SELECT u.id, CONCAT(u.first_name, ' ', u.last_name) as name,
                   fo.rent_amount, fo.charges_amount, fo.total_amount,
                   fo.effective_date
            FROM users u
            JOIN user_group_members ugm ON u.id = ugm.user_id
            LEFT JOIN user_financial_obligations fo ON u.id = fo.user_id 
                AND fo.is_active = 1
            WHERE ugm.group_id IN (:medical1, :medical2, :managers)
            AND u.is_active = 1
            GROUP BY u.id
            ORDER BY u.first_name, u.last_name";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        'medical1' => $medicalGroupId,
        'medical2' => $medicalGroupId2,
        'managers' => $managersGroupId
    ]);
    $obligations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>User</th><th>Rent</th><th>Charges</th><th>Total</th><th>Effective Date</th>";
    echo "</tr>";
    foreach ($obligations as $ob) {
        echo "<tr>";
        echo "<td>{$ob['name']}</td>";
        echo "<td>" . ($ob['rent_amount'] ? '€' . number_format($ob['rent_amount'], 2) : '-') . "</td>";
        echo "<td>" . ($ob['charges_amount'] ? '€' . number_format($ob['charges_amount'], 2) : '-') . "</td>";
        echo "<td>" . ($ob['total_amount'] ? '€' . number_format($ob['total_amount'], 2) : '-') . "</td>";
        echo "<td>" . ($ob['effective_date'] ?? '-') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>4. Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='/fit/public/invoices/create'>Create Invoice</a></li>";
    echo "<li>Select <strong>LOY</strong> invoice type</li>";
    echo "<li>Change billable type to <strong>User</strong></li>";
    echo "<li>The dropdown should only show users from Medical Staff and Managers groups</li>";
    echo "<li>Currently eligible users: " . implode(', ', array_column($loyaltyUsers, 'name')) . "</li>";
    echo "</ol>";
    
    echo "<h3>5. Implementation Status:</h3>";
    echo "<div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px;'>";
    echo "<p><strong>✅ Completed:</strong></p>";
    echo "<ul>";
    echo "<li>InvoiceController filters Medical/Managers users into \$loyaltyUsers array</li>";
    echo "<li>create-modern.twig has loyaltyUsersData JavaScript variable</li>";
    echo "<li>LOY invoices now use separate user list from LOC invoices</li>";
    echo "<li>TTC calculation still works for LOY invoices</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>