<?php
// Verify Invoice 263 (FAC-LOC-2025-0196) - Current State Check
// This will show if the invoice is already correct or if there's a display vs database discrepancy

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Verify Invoice 263 (FAC-LOC-2025-0196)</h1>";
    
    // Get invoice 263 details
    $stmt = $db->prepare("
        SELECT i.*, u.first_name, u.last_name, u.username 
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        WHERE i.id = 263
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>❌ Invoice 263 not found in database!</p>";
        echo "<p>This explains why the previous scripts couldn't find it.</p>";
        
        // Check if there's an invoice with the number FAC-LOC-2025-0196
        $stmt = $db->prepare("
            SELECT i.*, u.first_name, u.last_name, u.username 
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.invoice_number = 'FAC-LOC-2025-0196'
        ");
        $stmt->execute();
        $invoiceByNumber = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoiceByNumber) {
            echo "<h2>Found Invoice by Number: FAC-LOC-2025-0196</h2>";
            echo "<p>The invoice exists but with a different ID: {$invoiceByNumber['id']}</p>";
            echo "<p><a href='verify_invoice_263.php?use_id={$invoiceByNumber['id']}'>Check this invoice instead</a></p>";
        } else {
            echo "<p>Invoice FAC-LOC-2025-0196 also not found by number.</p>";
        }
        
        exit;
    }
    
    // Use alternative ID if provided
    if (isset($_GET['use_id'])) {
        $stmt = $db->prepare("
            SELECT i.*, u.first_name, u.last_name, u.username 
            FROM invoices i 
            JOIN users u ON i.client_id = u.id 
            WHERE i.id = ?
        ");
        $stmt->execute([$_GET['use_id']]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    echo "<h2>Invoice Details</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Database Value</th><th>Expected Value</th><th>Status</th></tr>";
    
    $checks = [
        'ID' => ['db' => $invoice['id'], 'expected' => '263', 'match' => $invoice['id'] == 263],
        'Invoice Number' => ['db' => $invoice['invoice_number'], 'expected' => 'FAC-LOC-2025-0196', 'match' => $invoice['invoice_number'] == 'FAC-LOC-2025-0196'],
        'Client' => ['db' => $invoice['first_name'] . ' ' . $invoice['last_name'], 'expected' => 'Nicolas Moineau', 'match' => ($invoice['first_name'] . ' ' . $invoice['last_name']) == 'Nicolas Moineau'],
        'Total' => ['db' => $invoice['total'], 'expected' => '930.00', 'match' => abs($invoice['total'] - 930.00) < 0.01],
        'Status' => ['db' => $invoice['status'], 'expected' => 'sent', 'match' => $invoice['status'] == 'sent']
    ];
    
    foreach ($checks as $field => $check) {
        $statusIcon = $check['match'] ? '✅' : '❌';
        $statusColor = $check['match'] ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>$field</td>";
        echo "<td>{$check['db']}</td>";
        echo "<td>{$check['expected']}</td>";
        echo "<td style='color: $statusColor;'>$statusIcon " . ($check['match'] ? 'Match' : 'Mismatch') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if total is already 930.00€
    if (abs($invoice['total'] - 930.00) < 0.01) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ Invoice is Already Correct!</h3>";
        echo "<p>The invoice total is already 930.00€ as expected.</p>";
        echo "<p>The issue may have been resolved previously, or the display in the interface is accurate.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ Total Mismatch</h3>";
        echo "<p>Database shows: €" . number_format($invoice['total'], 2) . "</p>";
        echo "<p>Expected: €930.00</p>";
        echo "<p>Difference: €" . number_format(930.00 - $invoice['total'], 2) . "</p>";
        echo "</div>";
    }
    
    // Get user's courses
    $stmt = $db->prepare("
        SELECT * FROM user_courses 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY display_order ASC
    ");
    $stmt->execute([$invoice['client_id']]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>User's Configured Courses</h2>";
    if (!empty($courses)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Course Name</th><th>Hourly Rate (TTC)</th><th>VAT Rate</th><th>Status</th></tr>";
        
        foreach ($courses as $course) {
            $status = $course['is_active'] ? 'Active' : 'Inactive';
            echo "<tr>";
            echo "<td>{$course['course_name']}</td>";
            echo "<td>€ " . number_format($course['hourly_rate'], 2) . "</td>";
            echo "<td>{$course['vat_rate']}%</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No courses configured for this user</p>";
    }
    
    // Get current invoice lines
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute([$invoice['id']]);
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Invoice Lines</h2>";
    if (!empty($lines)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Line Type</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
        
        $totalFromLines = 0;
        foreach ($lines as $line) {
            $totalFromLines += $line['line_total'];
            echo "<tr>";
            echo "<td>{$line['line_type']}</td>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>€ " . number_format($line['unit_price'], 2) . "</td>";
            echo "<td>{$line['vat_rate']}%</td>";
            echo "<td>€ " . number_format($line['line_total'], 2) . "</td>";
            echo "</tr>";
        }
        
        echo "<tr style='font-weight: bold; background: #f0f0f0;'>";
        echo "<td colspan='5'>TOTAL FROM LINES</td>";
        echo "<td>€ " . number_format($totalFromLines, 2) . "</td>";
        echo "</tr>";
        
        echo "<tr style='font-weight: bold; background: #e9ecef;'>";
        echo "<td colspan='5'>STORED INVOICE TOTAL</td>";
        echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
        echo "</tr>";
        
        // Check if line totals match invoice total
        if (abs($totalFromLines - $invoice['total']) < 0.01) {
            echo "<tr style='background: #d4edda; color: #155724;'>";
            echo "<td colspan='6'>✅ Line totals match invoice total</td>";
            echo "</tr>";
        } else {
            echo "<tr style='background: #f8d7da; color: #721c24;'>";
            echo "<td colspan='6'>❌ Discrepancy: €" . number_format($totalFromLines - $invoice['total'], 2) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No invoice lines found!</p>";
    }
    
    // Summary
    echo "<h2>Summary</h2>";
    if (abs($invoice['total'] - 930.00) < 0.01) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Invoice is Correct</h3>";
        echo "<p>Invoice 263 (FAC-LOC-2025-0196) already shows the correct total of €930.00</p>";
        echo "<p>No further action needed unless there are specific line item adjustments required.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Action Required</h3>";
        echo "<p>The invoice total needs to be adjusted from €" . number_format($invoice['total'], 2) . " to €930.00</p>";
        echo "<p><a href='fix_specific_invoice.php?id={$invoice['id']}&target=930'>Fix this invoice</a></p>";
        echo "</div>";
    }
    
    // Additional checks
    echo "<h2>Additional Information</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
    echo "<tr><td>Due Date</td><td>{$invoice['due_date']}</td></tr>";
    echo "<tr><td>Subtotal</td><td>€ " . number_format($invoice['subtotal'], 2) . "</td></tr>";
    echo "<tr><td>VAT Amount</td><td>€ " . number_format($invoice['vat_amount'], 2) . "</td></tr>";
    echo "<tr><td>Total</td><td>€ " . number_format($invoice['total'], 2) . "</td></tr>";
    echo "<tr><td>Created At</td><td>{$invoice['created_at']}</td></tr>";
    echo "<tr><td>Updated At</td><td>{$invoice['updated_at']}</td></tr>";
    echo "</table>";
    
    echo "<p><a href='http://localhost/fit/public/invoices/{$invoice['id']}' target='_blank'>View Invoice in Browser</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>