<?php
// Find the invoice that needs fixing - looking for one that should be 930.00€
// This will help identify the correct invoice ID

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Find Invoice with Rounding Issue</h1>";
    
    // Check if invoice 263 exists
    $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE id = 263");
    $stmt->execute();
    $exists = $stmt->fetchColumn();
    
    echo "<h2>Invoice 263 Status</h2>";
    echo "<p>Invoice 263 exists: " . ($exists ? "Yes" : "No") . "</p>";
    
    // Find recent invoices that might be the one with the issue
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.total,
            i.status,
            i.created_at,
            u.first_name,
            u.last_name,
            u.username
        FROM invoices i
        JOIN users u ON i.client_id = u.id
        WHERE i.status = 'sent'
        ORDER BY i.id DESC
        LIMIT 20
    ");
    $stmt->execute();
    $recentInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Recent Sent Invoices</h2>";
    if (!empty($recentInvoices)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Created</th><th>Could be 930.00€?</th></tr>";
        
        foreach ($recentInvoices as $invoice) {
            // Check if this could be the invoice that should be 930.00€
            $couldBe930 = false;
            $difference = abs($invoice['total'] - 930.00);
            
            if ($difference < 200) { // Within 200€ of 930.00€
                $couldBe930 = true;
            }
            
            $highlightStyle = $couldBe930 ? 'background-color: #ffffcc;' : '';
            
            echo "<tr style='$highlightStyle'>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td><a href='http://localhost/fit/public/invoices/{$invoice['id']}' target='_blank'>{$invoice['invoice_number']}</a></td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']} ({$invoice['username']})</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['created_at']}</td>";
            echo "<td>" . ($couldBe930 ? "✅ Possible (diff: €" . number_format($difference, 2) . ")" : "❌ No") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Look for invoices around 795€ (which might be the 794.97€ mentioned)
    echo "<h2>Invoices Around 795€</h2>";
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.total,
            i.status,
            u.first_name,
            u.last_name,
            u.username
        FROM invoices i
        JOIN users u ON i.client_id = u.id
        WHERE i.total BETWEEN 790 AND 800
        ORDER BY i.id DESC
    ");
    $stmt->execute();
    $around795 = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($around795)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Status</th><th>Missing to 930€</th><th>Action</th></tr>";
        
        foreach ($around795 as $invoice) {
            $missing = 930.00 - $invoice['total'];
            echo "<tr>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td><a href='http://localhost/fit/public/invoices/{$invoice['id']}' target='_blank'>{$invoice['invoice_number']}</a></td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']} ({$invoice['username']})</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['status']}</td>";
            echo "<td>€ " . number_format($missing, 2) . "</td>";
            echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}'>Fix This Invoice</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No invoices found around 795€</p>";
    }
    
    // Look for invoices with users who have courses configured
    echo "<h2>Invoices with Users Having Courses</h2>";
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.total,
            i.status,
            u.first_name,
            u.last_name,
            u.username,
            COUNT(uc.id) as course_count,
            GROUP_CONCAT(CONCAT(uc.course_name, ' (€', uc.hourly_rate, ')') SEPARATOR ', ') as courses
        FROM invoices i
        JOIN users u ON i.client_id = u.id
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        WHERE i.status = 'sent'
        GROUP BY i.id
        HAVING course_count > 0
        ORDER BY i.id DESC
        LIMIT 15
    ");
    $stmt->execute();
    $withCourses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($withCourses)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Client</th><th>Total</th><th>Courses</th><th>Action</th></tr>";
        
        foreach ($withCourses as $invoice) {
            echo "<tr>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td><a href='http://localhost/fit/public/invoices/{$invoice['id']}' target='_blank'>{$invoice['invoice_number']}</a></td>";
            echo "<td>{$invoice['first_name']} {$invoice['last_name']} ({$invoice['username']})</td>";
            echo "<td>€ " . number_format($invoice['total'], 2) . "</td>";
            echo "<td>{$invoice['courses']}</td>";
            echo "<td><a href='fix_specific_invoice.php?id={$invoice['id']}'>Fix This Invoice</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No invoices found with users having courses</p>";
    }
    
    // Check the highest invoice ID
    $stmt = $db->prepare("SELECT MAX(id) as max_id FROM invoices");
    $stmt->execute();
    $maxId = $stmt->fetchColumn();
    
    echo "<h2>Database Info</h2>";
    echo "<p>Highest invoice ID: $maxId</p>";
    
    // Check for invoice numbers containing the patterns we've seen
    echo "<h2>Search by Invoice Number Pattern</h2>";
    $patterns = ['FAC-LOC-2025', 'FAC-2025', 'LOC-2025'];
    
    foreach ($patterns as $pattern) {
        $stmt = $db->prepare("
            SELECT id, invoice_number, total, status 
            FROM invoices 
            WHERE invoice_number LIKE ? 
            ORDER BY id DESC 
            LIMIT 5
        ");
        $stmt->execute(["%$pattern%"]);
        $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($matches)) {
            echo "<h3>Invoices matching '$pattern':</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Invoice Number</th><th>Total</th><th>Status</th></tr>";
            
            foreach ($matches as $match) {
                echo "<tr>";
                echo "<td>{$match['id']}</td>";
                echo "<td>{$match['invoice_number']}</td>";
                echo "<td>€ " . number_format($match['total'], 2) . "</td>";
                echo "<td>{$match['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>