<?php
// Check coaches in group 2

// Load .env file
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Coach Group Analysis</h1>";

// Check group 2
echo "<h2>Group 2 Information:</h2>";
$stmt = $db->prepare("SELECT * FROM user_groups WHERE id = 2");
$stmt->execute();
$group = $stmt->fetch(PDO::FETCH_ASSOC);
if ($group) {
    echo "<p>Name: {$group['name']}</p>";
    echo "<p>Description: {$group['description']}</p>";
} else {
    echo "<p style='color: red;'>Group 2 not found!</p>";
}

// Check all members of group 2
echo "<h2>All Members of Group 2:</h2>";
$stmt = $db->prepare("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, 
           u.is_active, u.can_be_invoiced, u.course_name
    FROM users u
    JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE ugm.group_id = 2
    ORDER BY u.first_name, u.last_name
");
$stmt->execute();
$allMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Total members: " . count($allMembers) . "</p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Active</th><th>Can Invoice</th><th>Course</th></tr>";
foreach ($allMembers as $member) {
    echo "<tr>";
    echo "<td>{$member['id']}</td>";
    echo "<td>{$member['name']}</td>";
    echo "<td>{$member['username']}</td>";
    echo "<td>" . ($member['is_active'] ? '✅' : '❌') . "</td>";
    echo "<td>" . ($member['can_be_invoiced'] ? '✅' : '❌') . "</td>";
    echo "<td>{$member['course_name']}</td>";
    echo "</tr>";
}
echo "</table>";

// Check why coaches might be empty
echo "<h2>Active Coaches Who Can Be Invoiced:</h2>";
$activeCount = 0;
$canInvoiceCount = 0;
foreach ($allMembers as $member) {
    if ($member['is_active']) $activeCount++;
    if ($member['can_be_invoiced']) $canInvoiceCount++;
}
echo "<p>Active members: $activeCount</p>";
echo "<p>Can be invoiced: $canInvoiceCount</p>";

// Suggest users who might be coaches
echo "<h2>Potential Coaches (users with courses):</h2>";
$stmt = $db->query("
    SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, 
           u.course_name, u.is_active, u.can_be_invoiced,
           CASE WHEN ugm.user_id IS NOT NULL THEN ugm.group_id ELSE NULL END as current_group
    FROM users u
    LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
    WHERE u.course_name IS NOT NULL AND u.course_name != ''
    ORDER BY u.first_name, u.last_name
");
$potentialCoaches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Users with courses: " . count($potentialCoaches) . "</p>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Course</th><th>Active</th><th>Can Invoice</th><th>Current Group</th></tr>";
foreach ($potentialCoaches as $coach) {
    echo "<tr>";
    echo "<td>{$coach['id']}</td>";
    echo "<td>{$coach['name']}</td>";
    echo "<td>{$coach['username']}</td>";
    echo "<td>{$coach['course_name']}</td>";
    echo "<td>" . ($coach['is_active'] ? '✅' : '❌') . "</td>";
    echo "<td>" . ($coach['can_be_invoiced'] ? '✅' : '❌') . "</td>";
    echo "<td>{$coach['current_group']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<hr>";
echo "<p><a href='/fit/public/test_invoice_dropdowns.html'>Back to Test Page</a></p>";
?>