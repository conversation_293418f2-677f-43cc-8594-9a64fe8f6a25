<?php
// Debug the actual invoice 263 data to understand the 794.97 vs 930.00 issue

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Debug Invoice 263 - Actual Data Analysis</h1>";

// Get the actual invoice 263 data
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = 263");
$stmt->execute();
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$invoice) {
    die("Invoice 263 not found");
}

echo "<h2>Current Invoice 263 Data</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Field</th><th>Value</th></tr>";
echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
echo "<tr><td>Subtotal</td><td>{$invoice['subtotal']} €</td></tr>";
echo "<tr><td>VAT Amount</td><td>{$invoice['vat_amount']} €</td></tr>";
echo "<tr><td>Total</td><td><strong style='color: red;'>{$invoice['total']} €</strong> (should be 930.00 €)</td></tr>";
echo "<tr><td>Discrepancy</td><td><strong style='color: red;'>" . number_format(930.00 - $invoice['total'], 2) . " €</strong></td></tr>";
echo "</table>";

// Get all invoice lines
$stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 263 ORDER BY sort_order, id");
$stmt->execute();
$lines = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Invoice Lines Breakdown</h2>";
if (empty($lines)) {
    echo "<p style='color: red;'>❌ No invoice lines found! This might be the problem.</p>";
} else {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Line</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
    
    $calculatedSubtotal = 0;
    $calculatedVat = 0;
    $lineNumber = 1;
    
    foreach ($lines as $line) {
        $quantity = $line['quantity'];
        $unitPrice = $line['unit_price'];
        $vatRate = $line['vat_rate'];
        $lineTotal = $line['line_total'];
        
        // Calculate what the line should be
        $expectedSubtotal = $quantity * $unitPrice;
        $expectedVat = $expectedSubtotal * ($vatRate / 100);
        $expectedTotal = $expectedSubtotal + $expectedVat;
        
        $calculatedSubtotal += $expectedSubtotal;
        $calculatedVat += $expectedVat;
        
        echo "<tr>";
        echo "<td>$lineNumber</td>";
        echo "<td>{$line['description']}</td>";
        echo "<td>$quantity</td>";
        echo "<td>" . number_format($unitPrice, 2) . " €</td>";
        echo "<td>$vatRate%</td>";
        echo "<td>" . number_format($lineTotal, 2) . " € (expected: " . number_format($expectedTotal, 2) . " €)</td>";
        echo "</tr>";
        
        $lineNumber++;
    }
    
    echo "<tr style='font-weight: bold; background-color: #f0f0f0;'>";
    echo "<td colspan='5'>CALCULATED TOTALS</td>";
    echo "<td>" . number_format($calculatedSubtotal + $calculatedVat, 2) . " €</td>";
    echo "</tr>";
    echo "</table>";
    
    echo "<h3>Calculation Summary</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>From Lines</th><th>Stored in DB</th><th>Difference</th></tr>";
    echo "<tr><td>Subtotal</td><td>" . number_format($calculatedSubtotal, 2) . " €</td><td>" . number_format($invoice['subtotal'], 2) . " €</td><td>" . number_format($calculatedSubtotal - $invoice['subtotal'], 2) . " €</td></tr>";
    echo "<tr><td>VAT Amount</td><td>" . number_format($calculatedVat, 2) . " €</td><td>" . number_format($invoice['vat_amount'], 2) . " €</td><td>" . number_format($calculatedVat - $invoice['vat_amount'], 2) . " €</td></tr>";
    echo "<tr><td>Total</td><td>" . number_format($calculatedSubtotal + $calculatedVat, 2) . " €</td><td><strong>" . number_format($invoice['total'], 2) . " €</strong></td><td><strong>" . number_format(($calculatedSubtotal + $calculatedVat) - $invoice['total'], 2) . " €</strong></td></tr>";
    echo "</table>";
}

// Check if this is a retrocession invoice with missing patient line
echo "<h2>Retrocession Invoice Analysis</h2>";
$stmt = $db->prepare("
    SELECT u.first_name, u.last_name, u.exclude_patient_retrocession 
    FROM users u 
    JOIN invoices i ON u.id = i.client_id 
    WHERE i.id = 263
");
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if ($user) {
    echo "<p><strong>Client:</strong> {$user['first_name']} {$user['last_name']}</p>";
    echo "<p><strong>Exclude Patient Retrocession:</strong> " . ($user['exclude_patient_retrocession'] ? 'YES' : 'NO') . "</p>";
    
    if ($user['exclude_patient_retrocession']) {
        echo "<p style='color: orange;'>⚠️ This user has patient retrocession excluded. The missing amount might be the excluded patient retrocession part.</p>";
        
        // Calculate what the patient retrocession should be
        $missingAmount = 930.00 - $invoice['total'];
        echo "<p><strong>Missing amount:</strong> " . number_format($missingAmount, 2) . " € (this might be the excluded patient retrocession)</p>";
    }
}

// Check invoice type to understand if it's retrocession
echo "<h2>Invoice Type Analysis</h2>";
$stmt = $db->prepare("
    SELECT i.invoice_type, i.type_id, cit.name as type_name, cit.code as type_code
    FROM invoices i
    LEFT JOIN config_invoice_types cit ON i.type_id = cit.id
    WHERE i.id = 263
");
$stmt->execute();
$typeInfo = $stmt->fetch(PDO::FETCH_ASSOC);

if ($typeInfo) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Invoice Type</td><td>{$typeInfo['invoice_type']}</td></tr>";
    echo "<tr><td>Type ID</td><td>{$typeInfo['type_id']}</td></tr>";
    echo "<tr><td>Type Name</td><td>{$typeInfo['type_name']}</td></tr>";
    echo "<tr><td>Type Code</td><td>{$typeInfo['type_code']}</td></tr>";
    echo "</table>";
    
    if (strpos($typeInfo['invoice_type'], 'retrocession') !== false) {
        echo "<p style='color: blue;'>ℹ️ This is a retrocession invoice. The discrepancy might be due to excluded patient retrocession.</p>";
    }
}

// Propose solutions
echo "<h2>Proposed Solutions</h2>";
echo "<ol>";
echo "<li><strong>If patient retrocession was incorrectly excluded:</strong> Add missing patient retrocession line manually</li>";
echo "<li><strong>If total should be 930.00:</strong> Add adjustment line for the missing " . number_format(930.00 - $invoice['total'], 2) . " €</li>";
echo "<li><strong>If calculation is wrong:</strong> Recalculate all lines and update totals</li>";
echo "</ol>";

// Quick fix options
echo "<h2>Quick Fix Options</h2>";
echo "<form method='post' action='fix_invoice_263_total.php'>";
echo "<input type='hidden' name='invoice_id' value='263'>";
echo "<input type='hidden' name='current_total' value='{$invoice['total']}'>";
echo "<input type='hidden' name='desired_total' value='930.00'>";
echo "<p><strong>Option 1:</strong> <button type='submit' name='action' value='add_adjustment'>Add Adjustment Line (+". number_format(930.00 - $invoice['total'], 2) ." €)</button></p>";
echo "</form>";

echo "<form method='post' action='fix_invoice_263_total.php'>";
echo "<input type='hidden' name='invoice_id' value='263'>";
echo "<p><strong>Option 2:</strong> <button type='submit' name='action' value='add_patient_retrocession'>Add Patient Retrocession Line</button></p>";
echo "</form>";

echo "<form method='post' action='fix_invoice_263_total.php'>";
echo "<input type='hidden' name='invoice_id' value='263'>";
echo "<p><strong>Option 3:</strong> <button type='submit' name='action' value='recalculate_totals'>Recalculate Invoice Totals</button></p>";
echo "</form>";

echo "<p><a href='http://localhost/fit/public/invoices/263' target='_blank'>View Invoice 263 in Browser</a></p>";
?>