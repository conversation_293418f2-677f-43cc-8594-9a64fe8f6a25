<?php
/**
 * Simple Email Test for Invoice 279
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Services\EmailService;
use PHPMailer\PHPMailer\PHPMailer;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Simple Email Test</h1>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<h2>Test 1: Direct PHPMailer Test</h2>";
        
        try {
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = 'localhost';
            $mail->Port = 1025;
            $mail->SMTPAuth = false;
            $mail->SMTPSecure = false;
            
            // Recipients
            $mail->setFrom('<EMAIL>', 'Fit360 Test');
            $mail->addAddress('<EMAIL>', 'Frank Huet');
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = 'Test Email - Direct PHPMailer';
            $mail->Body = '<h1>Test Email</h1><p>This is a direct PHPMailer test at ' . date('Y-m-d H:i:s') . '</p>';
            $mail->AltBody = 'Test email sent at ' . date('Y-m-d H:i:s');
            
            $mail->send();
            echo '<div class="success">✅ Direct PHPMailer email sent successfully!</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ PHPMailer Error: ' . $e->getMessage() . '</div>';
            echo '<pre>Error Info: ' . $mail->ErrorInfo . '</pre>';
        }
        
        echo "<h2>Test 2: EmailService Send Test</h2>";
        
        try {
            $emailService = new EmailService();
            
            // Use reflection to call private send method
            $reflection = new ReflectionClass($emailService);
            $sendMethod = $reflection->getMethod('send');
            $sendMethod->setAccessible(true);
            
            $result = $sendMethod->invoke($emailService, [
                'to' => '<EMAIL>',
                'subject' => 'Test Email - EmailService Direct',
                'body_html' => '<h1>EmailService Test</h1><p>Testing at ' . date('Y-m-d H:i:s') . '</p>',
                'body_text' => 'EmailService test at ' . date('Y-m-d H:i:s')
            ]);
            
            echo '<div class="info">';
            echo '<h3>Result:</h3>';
            echo '<pre>' . print_r($result, true) . '</pre>';
            echo '</div>';
            
            if ($result['success']) {
                echo '<div class="success">✅ EmailService send successful!</div>';
            } else {
                echo '<div class="error">❌ EmailService send failed: ' . $result['message'] . '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ EmailService Error: ' . $e->getMessage() . '</div>';
        }
        
        echo "<h2>Test 3: Invoice 279 Email Template</h2>";
        
        try {
            $db = Flight::db();
            
            // Get the template that would be used
            $stmt = $db->prepare("
                SELECT * FROM email_templates
                WHERE email_type = 'new_invoice'
                AND is_active = TRUE
                AND (invoice_type = :invoice_type OR invoice_type IS NULL)
                ORDER BY invoice_type DESC, priority DESC
                LIMIT 1
            ");
            $stmt->execute([':invoice_type' => 'rental']);
            $template = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($template) {
                echo '<div class="success">✅ Found email template: ' . htmlspecialchars($template['name']) . '</div>';
                echo '<div class="info">';
                echo '<p><strong>Subject:</strong> ' . htmlspecialchars($template['subject']) . '</p>';
                echo '<p><strong>Type:</strong> ' . ($template['invoice_type'] ?: 'General') . '</p>';
                echo '</div>';
            } else {
                echo '<div class="error">❌ No email template found for rental invoices!</div>';
            }
            
            // Now try to send invoice 279
            echo "<h3>Sending Invoice 279...</h3>";
            
            $emailService = new EmailService();
            $result = $emailService->sendInvoiceEmail(279, '<EMAIL>');
            
            echo '<div class="info">';
            echo '<h4>Result:</h4>';
            echo '<pre>' . print_r($result, true) . '</pre>';
            echo '</div>';
            
            if (isset($result['success']) && $result['success']) {
                echo '<div class="success">✅ Invoice 279 email sent!</div>';
                
                // Check email logs
                $stmt = $db->prepare("SELECT * FROM email_logs WHERE invoice_id = 279 ORDER BY created_at DESC LIMIT 1");
                $stmt->execute();
                $log = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($log) {
                    echo '<div class="info">';
                    echo '<h4>Email Log Created:</h4>';
                    echo '<pre>' . print_r($log, true) . '</pre>';
                    echo '</div>';
                }
            } else {
                echo '<div class="error">❌ Failed to send invoice email</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error: ' . $e->getMessage() . '</div>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
        }
        
        echo '<hr>';
        echo '<p><a href="http://localhost:8025" target="_blank">Check Mailhog</a> for the emails.</p>';
        
    } else {
        ?>
        <form method="POST">
            <div class="info">
                <p>This will run three tests:</p>
                <ol>
                    <li>Direct PHPMailer test to verify SMTP connection</li>
                    <li>EmailService send method test</li>
                    <li>Full invoice 279 email send test</li>
                </ol>
                <button type="submit" style="padding: 10px 20px; font-size: 16px;">Run Email Tests</button>
            </div>
        </form>
        <?php
    }
    ?>
    
    <hr>
    <p>
        <a href="/fit/public/check_invoice_email_status.php?invoice=FAC-DIV-2025-0190">Check Email Status</a> |
        <a href="/fit/public/">Back to Dashboard</a>
    </p>
</body>
</html>