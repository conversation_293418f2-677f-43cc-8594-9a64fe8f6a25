<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Auto-fill Debugging</h2>";
    
    // Check the JavaScript code
    echo "<h3>1. Checking JavaScript Implementation:</h3>";
    $jsPath = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
    if (file_exists($jsPath)) {
        $content = file_get_contents($jsPath);
        
        // Check for LOY handling in handleInvoiceTypeChange
        if (strpos($content, "typeCode === 'LOY'") !== false) {
            echo "<p style='color: green;'>✅ LOY type code check exists in handleInvoiceTypeChange()</p>";
        } else {
            echo "<p style='color: red;'>❌ LOY type code check missing in handleInvoiceTypeChange()</p>";
        }
        
        // Check for subject auto-fill
        if (strpos($content, "LOYER + CHARGES") !== false) {
            echo "<p style='color: green;'>✅ 'LOYER + CHARGES' subject auto-fill found</p>";
        } else {
            echo "<p style='color: red;'>❌ 'LOYER + CHARGES' subject auto-fill missing</p>";
        }
        
        // Check for period update
        if (strpos($content, "updateRentalPeriod") !== false) {
            echo "<p style='color: green;'>✅ updateRentalPeriod() function exists</p>";
        } else {
            echo "<p style='color: red;'>❌ updateRentalPeriod() function missing</p>";
        }
    }
    
    echo "<h3>2. JavaScript Fix to Add:</h3>";
    echo "<div style='background-color: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<p>Add this code to trigger auto-fill on page load:</p>";
    echo "<pre style='background-color: white; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars("// Trigger invoice type change on page load if LOY is selected
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        if (invoiceTypeSelect && invoiceTypeSelect.value === '1') {
            // Manually trigger the change event
            const event = new Event('change', { bubbles: true });
            invoiceTypeSelect.dispatchEvent(event);
            console.log('Triggered LOY auto-fill on page load');
        }
    }, 500);
});");
    echo "</pre>";
    echo "</div>";
    
    echo "<h3>3. Quick JavaScript Fix (Run in Browser Console):</h3>";
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px;'>";
    echo "<p>Copy and paste this in your browser console to test:</p>";
    echo "<pre style='background-color: white; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars("// Auto-fill LOY fields
document.getElementById('subject').value = 'LOYER + CHARGES';
const frenchMonths = ['JANVIER', 'FÉVRIER', 'MARS', 'AVRIL', 'MAI', 'JUIN', 'JUILLET', 'AOÛT', 'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'];
const previousMonth = new Date();
previousMonth.setMonth(previousMonth.getMonth() - 1);
document.getElementById('period').value = frenchMonths[previousMonth.getMonth()] + ' ' + previousMonth.getFullYear();");
    echo "</pre>";
    echo "</div>";
    
    echo "<h3>4. Current Month Information:</h3>";
    $currentMonth = date('n');
    $currentYear = date('Y');
    $previousMonth = $currentMonth - 1;
    $previousYear = $currentYear;
    
    if ($previousMonth < 1) {
        $previousMonth = 12;
        $previousYear--;
    }
    
    $frenchMonths = [
        1 => 'JANVIER', 2 => 'FÉVRIER', 3 => 'MARS', 4 => 'AVRIL',
        5 => 'MAI', 6 => 'JUIN', 7 => 'JUILLET', 8 => 'AOÛT',
        9 => 'SEPTEMBRE', 10 => 'OCTOBRE', 11 => 'NOVEMBRE', 12 => 'DÉCEMBRE'
    ];
    
    echo "<ul>";
    echo "<li>Current date: " . date('d/m/Y') . "</li>";
    echo "<li>Expected period: <strong>{$frenchMonths[$previousMonth]} {$previousYear}</strong></li>";
    echo "<li>Expected subject: <strong>LOYER + CHARGES</strong></li>";
    echo "</ul>";
    
    echo "<h3>5. Recommended Solution:</h3>";
    echo "<p>The auto-fill logic is implemented but not triggering on page load. We need to add a trigger when the page loads with LOY pre-selected.</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<script>
// Test the auto-fill directly
function testLOYAutoFill() {
    console.log('Testing LOY auto-fill...');
    
    // Set subject
    const subjectField = document.getElementById('subject');
    if (subjectField) {
        subjectField.value = 'LOYER + CHARGES';
        console.log('✅ Subject set to: LOYER + CHARGES');
    } else {
        console.log('❌ Subject field not found');
    }
    
    // Set period
    const periodField = document.getElementById('period');
    if (periodField) {
        const frenchMonths = ['JANVIER', 'FÉVRIER', 'MARS', 'AVRIL', 'MAI', 'JUIN', 'JUILLET', 'AOÛT', 'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'];
        const previousMonth = new Date();
        previousMonth.setMonth(previousMonth.getMonth() - 1);
        const period = frenchMonths[previousMonth.getMonth()] + ' ' + previousMonth.getFullYear();
        periodField.value = period;
        console.log('✅ Period set to: ' + period);
    } else {
        console.log('❌ Period field not found');
    }
}
</script>

<button onclick="testLOYAutoFill()" style="background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
    Test LOY Auto-Fill
</button>