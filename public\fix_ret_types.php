<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing RET25 and RET30 Invoice Types</h2>";
    
    // First check what codes are already in use
    echo "<h3>Existing codes in config_invoice_types:</h3>";
    $stmt = $db->query("SELECT code, prefix FROM config_invoice_types ORDER BY code");
    $existing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Code</th><th>Prefix</th></tr>";
    foreach ($existing as $type) {
        echo "<tr><td>" . htmlspecialchars($type['code']) . "</td><td>" . htmlspecialchars($type['prefix']) . "</td></tr>";
    }
    echo "</table>";
    
    // Delete existing RET30 if it exists (to fix the issue)
    $stmt = $db->prepare("DELETE FROM config_invoice_types WHERE prefix = 'RET30'");
    $stmt->execute();
    echo "<p>Cleaned up any existing RET30 entries.</p>";
    
    // Create RET25 if not exists with unique code
    $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE prefix = 'RET25'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        echo "<p>Creating RET25 type...</p>";
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types (name, code, prefix, color, is_active)
            VALUES (:name, :code, :prefix, :color, 1)
        ");
        $stmt->execute([
            'name' => json_encode(['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%']),
            'code' => 'ret25',
            'prefix' => 'RET25',
            'color' => '#17a2b8'
        ]);
        echo "<p style='color: green;'>RET25 created successfully!</p>";
    } else {
        echo "<p>RET25 already exists.</p>";
    }
    
    // Create RET30 with unique code
    echo "<p>Creating RET30 type...</p>";
    $stmt = $db->prepare("
        INSERT INTO config_invoice_types (name, code, prefix, color, is_active)
        VALUES (:name, :code, :prefix, :color, 1)
    ");
    $stmt->execute([
        'name' => json_encode(['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%']),
        'code' => 'ret30',
        'prefix' => 'RET30',
        'color' => '#17a2b8'
    ]);
    echo "<p style='color: green;'>RET30 created successfully!</p>";
    
    // Also ensure they exist in invoice_types table
    echo "<h3>Updating invoice_types table</h3>";
    
    // Delete and recreate in invoice_types to ensure consistency
    $db->exec("DELETE FROM invoice_types WHERE code IN ('RET25', 'RET30')");
    
    $stmt = $db->prepare("
        INSERT INTO invoice_types (code, name, is_active)
        VALUES 
            ('RET25', 'Rétrocession 5%', 1),
            ('RET30', 'Rétrocession 10%', 1)
    ");
    $stmt->execute();
    echo "<p style='color: green;'>Invoice types updated!</p>";
    
    // Show all RET types
    echo "<h3>All Retrocession Types in config_invoice_types:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE prefix LIKE 'RET%' ORDER BY prefix");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Color</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . htmlspecialchars($type['code']) . "</td>";
        echo "<td>" . htmlspecialchars($type['prefix']) . "</td>";
        echo "<td>" . htmlspecialchars($type['name']) . "</td>";
        echo "<td style='background-color: " . $type['color'] . "; color: white;'>" . htmlspecialchars($type['color']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<p><strong>Done! The RET25 and RET30 types are now properly configured.</strong></p>";
    echo "<p><a href='/fit/public/invoices'>Go to invoices</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}