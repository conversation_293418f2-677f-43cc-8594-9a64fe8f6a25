<?php
// Fix Invoice 263 - Course-Specific Pricing Integration
// This script will recalculate invoice 263 using proper course-specific rates

// Load .env file
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $db = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Fix Invoice 263 - Course-Specific Pricing</h1>";
    
    // Get invoice 263 details
    $stmt = $db->prepare("
        SELECT i.*, u.first_name, u.last_name, u.username 
        FROM invoices i 
        JOIN users u ON i.client_id = u.id 
        WHERE i.id = 263
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        die("<p style='color: red;'>Invoice 263 not found!</p>");
    }
    
    echo "<h2>Invoice 263 Details</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Invoice Number</td><td>{$invoice['invoice_number']}</td></tr>";
    echo "<tr><td>Client</td><td>{$invoice['first_name']} {$invoice['last_name']} ({$invoice['username']})</td></tr>";
    echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
    echo "<tr><td>Current Total</td><td><strong style='color: red;'>{$invoice['total']} €</strong></td></tr>";
    echo "<tr><td>Target Total</td><td><strong style='color: green;'>930.00 €</strong></td></tr>";
    echo "<tr><td>Missing Amount</td><td><strong>" . number_format(930.00 - $invoice['total'], 2) . " €</strong></td></tr>";
    echo "</table>";
    
    // Get user's courses
    $stmt = $db->prepare("
        SELECT * FROM user_courses 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY display_order ASC
    ");
    $stmt->execute([$invoice['client_id']]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>User's Configured Courses</h2>";
    if (!empty($courses)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Course Name</th><th>Hourly Rate (TTC)</th><th>VAT Rate</th><th>Status</th></tr>";
        
        foreach ($courses as $course) {
            $status = $course['is_active'] ? 'Active' : 'Inactive';
            echo "<tr>";
            echo "<td>{$course['course_name']}</td>";
            echo "<td>€ " . number_format($course['hourly_rate'], 2) . "</td>";
            echo "<td>{$course['vat_rate']}%</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No courses configured for this user</p>";
    }
    
    // Get current invoice lines
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = 263 
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute();
    $currentLines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Invoice Lines</h2>";
    if (!empty($currentLines)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Line Type</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th></tr>";
        
        $totalFromLines = 0;
        foreach ($currentLines as $line) {
            $totalFromLines += $line['line_total'];
            echo "<tr>";
            echo "<td>{$line['line_type']}</td>";
            echo "<td>{$line['description']}</td>";
            echo "<td>{$line['quantity']}</td>";
            echo "<td>€ " . number_format($line['unit_price'], 2) . "</td>";
            echo "<td>{$line['vat_rate']}%</td>";
            echo "<td>€ " . number_format($line['line_total'], 2) . "</td>";
            echo "</tr>";
        }
        
        echo "<tr style='font-weight: bold; background: #f0f0f0;'>";
        echo "<td colspan='5'>TOTAL FROM LINES</td>";
        echo "<td>€ " . number_format($totalFromLines, 2) . "</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No invoice lines found!</p>";
    }
    
    // Calculate what should be the correct pricing
    echo "<h2>Recommended Course-Based Pricing</h2>";
    
    if (!empty($courses)) {
        echo "<h3>Scenario 1: Using Course-Specific Rates</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Course</th><th>Suggested Hours</th><th>Rate/Hour</th><th>VAT Rate</th><th>Subtotal</th><th>VAT</th><th>Total</th></tr>";
        
        $totalSubtotal = 0;
        $totalVAT = 0;
        $totalCourse = 0;
        
        foreach ($courses as $course) {
            // Suggest hours based on course type
            $suggestedHours = 0;
            if (strtolower($course['course_name']) === 'individuel') {
                $suggestedHours = 39; // To reach ~585€
            } elseif (strtolower($course['course_name']) === 'collectif') {
                $suggestedHours = 7; // To reach ~210€
            }
            
            $subtotal = $suggestedHours * $course['hourly_rate'];
            $vat = $subtotal * ($course['vat_rate'] / 100);
            $lineTotal = $subtotal + $vat;
            
            $totalSubtotal += $subtotal;
            $totalVAT += $vat;
            $totalCourse += $lineTotal;
            
            echo "<tr>";
            echo "<td>{$course['course_name']}</td>";
            echo "<td>$suggestedHours</td>";
            echo "<td>€ " . number_format($course['hourly_rate'], 2) . "</td>";
            echo "<td>{$course['vat_rate']}%</td>";
            echo "<td>€ " . number_format($subtotal, 2) . "</td>";
            echo "<td>€ " . number_format($vat, 2) . "</td>";
            echo "<td>€ " . number_format($lineTotal, 2) . "</td>";
            echo "</tr>";
        }
        
        // Add patient part to reach 930.00€
        $patientPart = 930.00 - $totalCourse;
        
        echo "<tr style='background: #e8f5e8;'>";
        echo "<td>Part Patient</td>";
        echo "<td>1</td>";
        echo "<td>€ " . number_format($patientPart, 2) . "</td>";
        echo "<td>0%</td>";
        echo "<td>€ " . number_format($patientPart, 2) . "</td>";
        echo "<td>€ 0.00</td>";
        echo "<td>€ " . number_format($patientPart, 2) . "</td>";
        echo "</tr>";
        
        echo "<tr style='font-weight: bold; background: #d4edda;'>";
        echo "<td colspan='4'>TOTAL WITH PATIENT PART</td>";
        echo "<td>€ " . number_format($totalSubtotal + $patientPart, 2) . "</td>";
        echo "<td>€ " . number_format($totalVAT, 2) . "</td>";
        echo "<td>€ " . number_format(930.00, 2) . "</td>";
        echo "</tr>";
        echo "</table>";
    }
    
    // Fix options
    echo "<h2>Fix Options</h2>";
    
    if (!isset($_GET['action'])) {
        echo "<div style='margin: 20px 0;'>";
        echo "<p><strong>Choose how to fix invoice 263:</strong></p>";
        
        echo "<p><strong>Option 1:</strong> Add missing patient retrocession (135.03€)</p>";
        echo "<a href='?action=add_patient_part' style='background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Add Patient Part</a>";
        
        echo "<p><strong>Option 2:</strong> Recalculate using course-specific rates</p>";
        echo "<a href='?action=recalculate_courses' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Recalculate with Courses</a>";
        
        echo "<p><strong>Option 3:</strong> Simple total adjustment</p>";
        echo "<a href='?action=simple_adjust' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Simple Adjustment</a>";
        echo "</div>";
    }
    
    // Handle fix actions
    if (isset($_GET['action'])) {
        $action = $_GET['action'];
        
        try {
            $db->beginTransaction();
            
            switch ($action) {
                case 'add_patient_part':
                    $missingAmount = 930.00 - $invoice['total'];
                    
                    // Add patient retrocession line
                    $stmt = $db->prepare("
                        INSERT INTO invoice_lines (
                            invoice_id, line_type, description, quantity, unit_price, 
                            vat_rate, line_total, sort_order
                        ) VALUES (?, 'patient_part', 'Part Patient', 1, ?, 0, ?, 10)
                    ");
                    $stmt->execute([263, $missingAmount, $missingAmount]);
                    
                    // Update totals
                    $stmt = $db->prepare("
                        UPDATE invoices 
                        SET total = 930.00, updated_at = NOW() 
                        WHERE id = 263
                    ");
                    $stmt->execute();
                    
                    echo "<p style='color: green;'>✅ Added patient retrocession line: € " . number_format($missingAmount, 2) . "</p>";
                    break;
                    
                case 'recalculate_courses':
                    // Delete current lines
                    $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = 263");
                    $stmt->execute();
                    
                    // Add course-based lines
                    $sortOrder = 1;
                    $newSubtotal = 0;
                    $newVAT = 0;
                    
                    foreach ($courses as $course) {
                        $hours = (strtolower($course['course_name']) === 'individuel') ? 39 : 7;
                        $subtotal = $hours * $course['hourly_rate'];
                        $vat = $subtotal * ($course['vat_rate'] / 100);
                        $lineTotal = $subtotal + $vat;
                        
                        $stmt = $db->prepare("
                            INSERT INTO invoice_lines (
                                invoice_id, line_type, description, quantity, unit_price, 
                                vat_rate, line_total, sort_order
                            ) VALUES (?, 'service', ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            263, 
                            $course['course_name'], 
                            $hours, 
                            $course['hourly_rate'], 
                            $course['vat_rate'], 
                            $lineTotal, 
                            $sortOrder++
                        ]);
                        
                        $newSubtotal += $subtotal;
                        $newVAT += $vat;
                    }
                    
                    // Add patient part to reach 930.00€
                    $patientPart = 930.00 - ($newSubtotal + $newVAT);
                    
                    $stmt = $db->prepare("
                        INSERT INTO invoice_lines (
                            invoice_id, line_type, description, quantity, unit_price, 
                            vat_rate, line_total, sort_order
                        ) VALUES (?, 'patient_part', 'Part Patient', 1, ?, 0, ?, ?)
                    ");
                    $stmt->execute([263, $patientPart, $patientPart, $sortOrder]);
                    
                    // Update invoice totals
                    $stmt = $db->prepare("
                        UPDATE invoices 
                        SET subtotal = ?, vat_amount = ?, total = 930.00, updated_at = NOW() 
                        WHERE id = 263
                    ");
                    $stmt->execute([$newSubtotal + $patientPart, $newVAT]);
                    
                    echo "<p style='color: green;'>✅ Recalculated invoice using course-specific rates</p>";
                    break;
                    
                case 'simple_adjust':
                    $stmt = $db->prepare("
                        UPDATE invoices 
                        SET total = 930.00, updated_at = NOW() 
                        WHERE id = 263
                    ");
                    $stmt->execute();
                    
                    echo "<p style='color: green;'>✅ Set total to 930.00€</p>";
                    break;
            }
            
            $db->commit();
            echo "<p><strong>Invoice 263 has been updated!</strong></p>";
            echo "<p><a href='http://localhost/fit/public/invoices/263' target='_blank'>View Updated Invoice</a></p>";
            
        } catch (Exception $e) {
            $db->rollBack();
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th { background-color: #f8f9fa; padding: 8px; text-align: left; }
td { padding: 8px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #666; }
</style>