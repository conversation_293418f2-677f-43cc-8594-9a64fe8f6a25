<?php
// Load .env file from parent directory
$envFile = dirname(__DIR__) . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at " . $envFile . "\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>LOY Invoice Secretary Lines Test ✅</h2>";
    
    echo "<div style='background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>What has been updated:</h3>";
    echo "<ol>";
    echo "<li>✅ Added missing translation: <code>invoices.replace_existing_items</code></li>";
    echo "<li>✅ API now returns all financial obligation fields including secretary services</li>";
    echo "<li>✅ JavaScript now adds secretary and TVA lines when values > 0</li>";
    echo "</ol>";
    echo "</div>";
    
    // Check users with secretary services
    echo "<h3>Users with Secretary Services (Managers):</h3>";
    $stmt = $db->prepare("
        SELECT u.id, u.first_name, u.last_name, 
               ufo.rent_amount, ufo.charges_amount,
               ufo.secretary_tvac_17, ufo.secretary_htva, ufo.tva_17,
               GROUP_CONCAT(g.name) as user_groups
        FROM users u
        INNER JOIN user_financial_obligations ufo ON u.id = ufo.user_id AND ufo.end_date IS NULL
        LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
        LEFT JOIN user_groups g ON ugm.group_id = g.id
        WHERE (ufo.secretary_tvac_17 > 0 OR ufo.secretary_htva > 0 OR ufo.tva_17 > 0)
           OR ugm.group_id = 3
        GROUP BY u.id
        ORDER BY u.last_name, u.first_name
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($users) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>User</th><th>Groups</th><th>Rent</th><th>Charges</th><th>Secretary TVAC 17%</th><th>Secretary HTVA</th><th>TVA 17%</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['first_name']} {$user['last_name']}</td>";
            echo "<td>{$user['user_groups']}</td>";
            echo "<td>€ " . number_format($user['rent_amount'], 2) . "</td>";
            echo "<td>€ " . number_format($user['charges_amount'], 2) . "</td>";
            echo "<td style='background-color: " . ($user['secretary_tvac_17'] > 0 ? '#e8f5e9' : '') . "'>€ " . number_format($user['secretary_tvac_17'], 2) . "</td>";
            echo "<td style='background-color: " . ($user['secretary_htva'] > 0 ? '#e8f5e9' : '') . "'>€ " . number_format($user['secretary_htva'], 2) . "</td>";
            echo "<td style='background-color: " . ($user['tva_17'] > 0 ? '#e8f5e9' : '') . "'>€ " . number_format($user['tva_17'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No users found with secretary services configured</p>";
    }
    
    // API test
    echo "<h3>API Response Example:</h3>";
    echo "<p>For a manager with secretary services, the API will return:</p>";
    echo "<pre style='background-color: #f0f0f0; padding: 10px; border-radius: 5px;'>";
    echo '{
    "success": true,
    "data": {
        "rent_amount": 720,
        "charges_amount": 160,
        "secretary_tvac_17": 100,
        "secretary_htva": 50,
        "tva_17": 17
    }
}';
    echo "</pre>";
    
    echo "<h3>Expected Invoice Lines:</h3>";
    echo "<p>When creating a LOY invoice for a manager with secretary services:</p>";
    echo "<ol>";
    echo "<li><strong>Loyer mensuel</strong> - €720.00 (0% VAT)</li>";
    echo "<li><strong>Charges location</strong> - €160.00 (0% VAT)</li>";
    echo "<li><strong>Secrétariat TVAC 17%</strong> - €100.00 (17% VAT)</li>";
    echo "<li><strong>Secrétariat HTVA</strong> - €50.00 (0% VAT)</li>";
    echo "<li><strong>TVA 17%</strong> - €17.00 (17% VAT)</li>";
    echo "</ol>";
    
    echo "<div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Clear browser cache (Ctrl+F5)</li>";
    echo "<li>Go to: <a href='/fit/public/invoices/create?type=loyer' target='_blank'><strong>/invoices/create?type=loyer</strong></a></li>";
    echo "<li>Select a user from the Managers group who has secretary services configured</li>";
    echo "<li>The invoice should load all applicable lines (rent, charges, secretary services)</li>";
    echo "<li>Check browser console for debug messages showing which lines are added</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Note on VAT Rates:</h3>";
    echo "<ul>";
    echo "<li>Rent and charges: 0% VAT (exempt)</li>";
    echo "<li>Secretary services with 'TVAC': 17% VAT included</li>";
    echo "<li>Secretary services with 'HTVA': 0% VAT</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<script>
console.log('LOY secretary lines test page loaded');
</script>