<?php
/**
 * Fix Invoice Types for LOY, RET, LOC
 * This script ensures invoice types exist in the actual table being used
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connected successfully\n\n";
} catch (Exception $e) {
    die("✗ Database connection failed: " . $e->getMessage() . "\n");
}

echo "=== Fixing Invoice Types ===\n\n";

// First check if invoice_types table exists (separate from config_invoice_types)
$stmt = $pdo->query("SHOW TABLES LIKE 'invoice_types'");
if ($stmt->rowCount() > 0) {
    echo "✓ Found invoice_types table\n";
    
    // Check if LOY, RET, LOC exist
    $types = ['LOY', 'RET', 'LOC'];
    
    foreach ($types as $type) {
        $stmt = $pdo->prepare("SELECT * FROM invoice_types WHERE code = :code");
        $stmt->execute(['code' => $type]);
        
        if ($stmt->rowCount() == 0) {
            // Insert the type
            $names = [
                'LOY' => ['fr' => 'Loyer', 'en' => 'Rent', 'de' => 'Miete'],
                'RET' => ['fr' => 'Rétrocession', 'en' => 'Retrocession', 'de' => 'Rückzahlung'],
                'LOC' => ['fr' => 'Location', 'en' => 'Rental', 'de' => 'Vermietung']
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO invoice_types (code, name, is_active, created_at, updated_at) 
                VALUES (:code, :name, 1, NOW(), NOW())
            ");
            
            $stmt->execute([
                'code' => $type,
                'name' => json_encode($names[$type])
            ]);
            
            echo "✓ Created invoice type: $type\n";
        } else {
            echo "ℹ Invoice type $type already exists\n";
        }
    }
} else {
    echo "ℹ invoice_types table not found, checking config_invoice_types\n";
}

// Also check config_invoice_types table
$stmt = $pdo->query("SHOW TABLES LIKE 'config_invoice_types'");
if ($stmt->rowCount() > 0) {
    echo "\n✓ Found config_invoice_types table\n";
    
    // First check what type_ids are being used in invoices
    $stmt = $pdo->query("SELECT DISTINCT type_id FROM invoices WHERE type_id IS NOT NULL");
    $usedIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Type IDs used in invoices: " . implode(', ', $usedIds) . "\n";
    
    $types = [
        ['id' => 1, 'code' => 'rental', 'prefix' => 'FAC-LOY', 'name' => json_encode(['fr' => 'Loyer', 'en' => 'Rent', 'de' => 'Miete'])],
        ['id' => 2, 'code' => 'retrocession', 'prefix' => 'FAC-RET30', 'name' => json_encode(['fr' => 'Rétrocession', 'en' => 'Retrocession', 'de' => 'Rückzahlung'])],
        ['id' => 3, 'code' => 'service', 'prefix' => 'FAC', 'name' => json_encode(['fr' => 'Service', 'en' => 'Service', 'de' => 'Dienstleistung'])]
    ];
    
    foreach ($types as $type) {
        $stmt = $pdo->prepare("SELECT * FROM config_invoice_types WHERE id = :id");
        $stmt->execute(['id' => $type['id']]);
        
        if ($stmt->rowCount() == 0) {
            // Check table structure first
            $columns = $pdo->query("SHOW COLUMNS FROM config_invoice_types")->fetchAll(PDO::FETCH_COLUMN);
            
            // Build insert query based on available columns
            $insertFields = ['id', 'prefix', 'name', 'created_at', 'updated_at'];
            $insertValues = [':id', ':prefix', ':name', 'NOW()', 'NOW()'];
            $bindParams = [
                'id' => $type['id'],
                'prefix' => $type['prefix'],
                'name' => $type['name']
            ];
            
            if (in_array('code', $columns)) {
                $insertFields[] = 'code';
                $insertValues[] = ':code';
                $bindParams['code'] = $type['code'];
            }
            
            if (in_array('is_active', $columns)) {
                $insertFields[] = 'is_active';
                $insertValues[] = '1';
            }
            
            $sql = "INSERT INTO config_invoice_types (" . implode(', ', $insertFields) . ") 
                    VALUES (" . implode(', ', $insertValues) . ")";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($bindParams);
            echo "✓ Created config_invoice_type: {$type['code']} (ID: {$type['id']})\n";
        } else {
            // Update existing to ensure correct values
            $stmt = $pdo->prepare("
                UPDATE config_invoice_types 
                SET code = :code, prefix = :prefix, name = :name 
                WHERE id = :id
            ");
            $stmt->execute([
                'id' => $type['id'],
                'code' => $type['code'],
                'prefix' => $type['prefix'],
                'name' => $type['name']
            ]);
            echo "✓ Updated config_invoice_type: {$type['code']} (ID: {$type['id']})\n";
        }
    }
}

// Update config values
echo "\n=== Updating Config Values ===\n";

$configs = [
    'ret_invoice_type' => 'RET',
    'loy_invoice_type' => 'LOY', 
    'loc_invoice_type' => 'LOC'
];

foreach ($configs as $key => $value) {
    $stmt = $pdo->prepare("INSERT INTO config (`key`, value) VALUES (:key, :value) ON DUPLICATE KEY UPDATE value = :value2");
    $stmt->execute(['key' => $key, 'value' => $value, 'value2' => $value]);
    echo "✓ Set config $key = $value\n";
}

// Show current status
echo "\n=== Current Invoice Types ===\n";

// Check invoice_types table
$stmt = $pdo->query("SHOW TABLES LIKE 'invoice_types'");
if ($stmt->rowCount() > 0) {
    echo "\nFrom invoice_types table:\n";
    $stmt = $pdo->query("SELECT * FROM invoice_types WHERE code IN ('LOY', 'RET', 'LOC')");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        echo "- {$row['code']}: $displayName (ID: {$row['id']})\n";
    }
}

// Check config_invoice_types table
echo "\nFrom config_invoice_types table:\n";
$stmt = $pdo->query("SELECT * FROM config_invoice_types WHERE prefix IN ('LOY', 'RET', 'LOC')");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $name = json_decode($row['name'], true);
    $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
    echo "- {$row['prefix']}: $displayName (ID: {$row['id']})\n";
}

echo "\n✓ Invoice types fixed successfully!\n";
echo "\nYou can now generate LOY invoices from the bulk generation page.\n";