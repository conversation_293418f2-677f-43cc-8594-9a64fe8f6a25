<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Invoice Type Display</h2>";
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Step 1: Update the type names
        echo "<h3>Step 1: Updating Type Names</h3>";
        
        // Update ret2 to show "Rétrocession 25%"
        $stmt = $db->prepare("
            UPDATE config_invoice_types 
            SET name = :name 
            WHERE id = 37 AND code = 'ret2'
        ");
        $newName25 = json_encode(['fr' => 'Rétrocession 25%', 'en' => 'Retrocession 25%']);
        $stmt->execute([':name' => $newName25]);
        echo "<p>✓ Updated ret2 (ID 37) to 'Rétrocession 25%'</p>";
        
        // Update ret3 to show "Rétrocession 30%"
        $stmt = $db->prepare("
            UPDATE config_invoice_types 
            SET name = :name 
            WHERE id = 38 AND code = 'ret3'
        ");
        $newName30 = json_encode(['fr' => 'Rétrocession 30%', 'en' => 'Retrocession 30%']);
        $stmt->execute([':name' => $newName30]);
        echo "<p>✓ Updated ret3 (ID 38) to 'Rétrocession 30%'</p>";
        
        // Step 2: Fix invoices with NULL type_id
        echo "<h3>Step 2: Fixing Invoices with NULL type_id</h3>";
        
        // Fix FAC-RET25 invoices
        $stmt = $db->prepare("
            UPDATE invoices 
            SET type_id = 37 
            WHERE invoice_number LIKE 'FAC-RET25-%' 
            AND type_id IS NULL
        ");
        $stmt->execute();
        $count25 = $stmt->rowCount();
        echo "<p>✓ Updated $count25 FAC-RET25 invoices to use type_id 37</p>";
        
        // Fix FAC-RET30 invoices
        $stmt = $db->prepare("
            UPDATE invoices 
            SET type_id = 38 
            WHERE invoice_number LIKE 'FAC-RET30-%' 
            AND type_id IS NULL
        ");
        $stmt->execute();
        $count30 = $stmt->rowCount();
        echo "<p>✓ Updated $count30 FAC-RET30 invoices to use type_id 38</p>";
        
        // Commit transaction
        $db->commit();
        echo "<h3 style='color: green;'>✓ All changes committed successfully!</h3>";
        
    } catch (Exception $e) {
        $db->rollBack();
        throw $e;
    }
    
    // Step 3: Verify the results
    echo "<h3>Step 3: Verification</h3>";
    
    // Check updated types
    echo "<h4>Updated Invoice Types:</h4>";
    $stmt = $db->query("
        SELECT id, code, prefix, name 
        FROM config_invoice_types 
        WHERE id IN (37, 38)
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($types as $type) {
        $nameData = json_decode($type['name'], true);
        $displayName = $nameData['fr'] ?? $type['name'];
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['code']}</td>";
        echo "<td>{$type['prefix']}</td>";
        echo "<td><strong>{$displayName}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check invoices
    echo "<h4>Sample Retrocession Invoices:</h4>";
    $stmt = $db->query("
        SELECT i.id, i.invoice_number, i.type_id, cit.name, c.name as client_name
        FROM invoices i
        LEFT JOIN config_invoice_types cit ON cit.id = i.type_id
        LEFT JOIN clients c ON c.id = i.client_id
        WHERE i.invoice_number LIKE '%RET%'
        ORDER BY i.id DESC
        LIMIT 10
    ");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Invoice Number</th><th>type_id</th><th>Will Display As</th><th>Client</th></tr>";
    foreach ($invoices as $invoice) {
        $displayName = 'N/A';
        if (!empty($invoice['name'])) {
            $nameData = json_decode($invoice['name'], true);
            $displayName = $nameData['fr'] ?? $invoice['name'];
        }
        
        $rowStyle = '';
        if (strpos($invoice['invoice_number'], 'RET25') !== false && $displayName == 'Rétrocession 25%') {
            $rowStyle = 'background-color: #d4edda;';
        } elseif (strpos($invoice['invoice_number'], 'RET30') !== false && $displayName == 'Rétrocession 30%') {
            $rowStyle = 'background-color: #cce5ff;';
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$invoice['invoice_number']}</td>";
        echo "<td>{$invoice['type_id']}</td>";
        echo "<td><strong>{$displayName}</strong></td>";
        echo "<td>{$invoice['client_name']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✓ Success!</h3>";
    echo "<p>All invoice types have been updated:</p>";
    echo "<ul>";
    echo "<li>FAC-RET25 invoices now display <strong>Rétrocession 25%</strong></li>";
    echo "<li>FAC-RET30 invoices now display <strong>Rétrocession 30%</strong></li>";
    echo "<li>All retrocession invoices have proper type_id values</li>";
    echo "</ul>";
    echo "<p><a href='/fit/public/invoices' style='color: #155724; font-weight: bold;'>→ View the invoice list</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
        echo "<p style='color: red;'>Transaction rolled back.</p>";
    }
}