name: Database Migration Check
description: Checks for pending database migrations and optionally runs them
version: 1.0.0
author: Claude-Flow

inputs:
  - name: auto_run
    type: boolean
    description: Automatically run pending migrations if found
    required: false
    default: false

  - name: dry_run
    type: boolean
    description: Show what migrations would be run without executing them
    required: false
    default: true

env:
  migration_dir: "database/migrations"
  database_url: "${DATABASE_URL}"

steps:
  - name: Check Current Migration Status
    type: shell
    command: |
      cd database && php -r "
      require_once '../vendor/autoload.php';
      \$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
      \$dotenv->load();
      
      \$pdo = new PDO(
          'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
          \$_ENV['DB_USER'],
          \$_ENV['DB_PASS']
      );
      
      // Check if migrations table exists
      \$result = \$pdo->query(\"SHOW TABLES LIKE 'migrations'\");
      if (\$result->rowCount() == 0) {
          echo json_encode([
              'status' => 'error',
              'message' => 'Migrations table does not exist. Run initial setup.'
          ]);
          exit(1);
      }
      
      // Get last migration
      \$stmt = \$pdo->query('SELECT migration FROM migrations ORDER BY id DESC LIMIT 1');
      \$lastMigration = \$stmt->fetchColumn();
      
      echo json_encode([
          'status' => 'ok',
          'last_migration' => \$lastMigration ?: 'none'
      ]);
      "
    output: current_status

  - name: Find Pending Migrations
    type: shell
    command: |
      php -r "
      \$migrationDir = '${env.migration_dir}';
      \$lastMigration = '{{ current_status.last_migration }}';
      
      \$files = glob(\$migrationDir . '/*.sql');
      sort(\$files);
      
      \$pending = [];
      \$foundLast = (\$lastMigration === 'none');
      
      foreach (\$files as \$file) {
          \$filename = basename(\$file);
          
          if (\$foundLast) {
              \$pending[] = \$filename;
          } elseif (\$filename === \$lastMigration) {
              \$foundLast = true;
          }
      }
      
      echo json_encode([
          'pending_count' => count(\$pending),
          'pending_migrations' => \$pending
      ]);
      "
    output: pending_migrations

  - name: Display Migration Status
    type: shell
    command: |
      php -r "
      \$pending = json_decode('{{ json_encode(pending_migrations) }}', true);
      
      echo \"\\n=== Database Migration Status ===\\n\";
      echo \"Last applied migration: {{ current_status.last_migration }}\\n\";
      echo \"Pending migrations: \" . \$pending['pending_count'] . \"\\n\";
      
      if (\$pending['pending_count'] > 0) {
          echo \"\\nPending migration files:\\n\";
          foreach (\$pending['pending_migrations'] as \$migration) {
              echo \"  - \$migration\\n\";
          }
          
          if ('${inputs.dry_run}' === 'true') {
              echo \"\\n[DRY RUN] No migrations will be executed.\\n\";
          }
      } else {
          echo \"\\nAll migrations are up to date!\\n\";
      }
      "

  - name: Run Pending Migrations
    type: conditional
    condition: "{{ pending_migrations.pending_count > 0 && inputs.auto_run && !inputs.dry_run }}"
    steps:
      - name: Execute Migrations
        type: shell
        command: |
          cd database && php migrate.php --run
        output: migration_result
      
      - name: Report Results
        type: shell
        command: |
          echo "\n=== Migration Results ==="
          echo "{{ migration_result }}"

  - name: Generate Health Check Report
    type: shell
    command: |
      php -r "
      require_once 'vendor/autoload.php';
      \$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
      \$dotenv->load();
      
      \$pdo = new PDO(
          'mysql:host=' . \$_ENV['DB_HOST'] . ';dbname=' . \$_ENV['DB_NAME'],
          \$_ENV['DB_USER'],
          \$_ENV['DB_PASS']
      );
      
      // Check key tables
      \$tables = ['users', 'invoices', 'clients', 'retrocession_data_entry'];
      \$report = [];
      
      foreach (\$tables as \$table) {
          \$stmt = \$pdo->query(\"SELECT COUNT(*) FROM \$table\");
          \$count = \$stmt->fetchColumn();
          \$report[\$table] = \$count;
      }
      
      echo \"\\n=== Database Health Check ===\\n\";
      foreach (\$report as \$table => \$count) {
          echo sprintf(\"%-30s: %d records\\n\", \$table, \$count);
      }
      "

on_error:
  - name: Log Error
    type: shell
    command: |
      echo "Migration check error: {{ error_message }}" >> storage/logs/migration-errors.log

on_success:
  - name: Log Success
    type: shell  
    command: |
      echo "Migration check completed successfully at $(date)" >> storage/logs/migration-success.log