<?php
/**
 * Test Script for Unified Invoice Generation System
 * 
 * This script tests all three invoice types (RET, LOY, LOC) with debugging capabilities
 * 
 * Usage:
 *   php test_unified_invoice_generation.php [options]
 * 
 * Options:
 *   --debug          Enable debug mode with step-by-step execution
 *   --type=TYPE      Test specific type only (RET, LOY, or LOC)
 *   --cleanup        Clean up test data after testing
 *   --no-pause       Don't pause between steps in debug mode
 */

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;
use App\Services\UnifiedInvoiceGenerator;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Parse command line arguments
$options = getopt('', ['debug', 'type:', 'cleanup', 'no-pause']);
$DEBUG = isset($options['debug']);
$SPECIFIC_TYPE = $options['type'] ?? null;
$CLEANUP = isset($options['cleanup']);
$NO_PAUSE = isset($options['no-pause']);

// Colors for output
$GREEN = "\033[0;32m";
$RED = "\033[0;31m";
$YELLOW = "\033[0;33m";
$BLUE = "\033[0;34m";
$RESET = "\033[0m";

// Database connection - using correct .env variable names
$dbHost = $_ENV['DB_HOST'] ?? 'localhost';
$dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
$dbUser = $_ENV['DB_USERNAME'] ?? 'root';
$dbPass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "{$GREEN}✓ Database connected successfully{$RESET}\n\n";
} catch (Exception $e) {
    die("{$RED}✗ Database connection failed: " . $e->getMessage() . "{$RESET}\n");
}

// Debug function
function debug($message, $data = null) {
    global $DEBUG, $BLUE, $RESET;
    if ($DEBUG) {
        echo "{$BLUE}[DEBUG]{$RESET} $message\n";
        if ($data !== null) {
            print_r($data);
        }
    }
}

// Pause function for step-by-step execution
function pauseIfDebug($message = "Press Enter to continue...") {
    global $DEBUG, $NO_PAUSE, $YELLOW, $RESET;
    if ($DEBUG && !$NO_PAUSE) {
        echo "{$YELLOW}$message{$RESET}";
        fgets(STDIN);
    }
}

// Success/Error output
function success($message) {
    global $GREEN, $RESET;
    echo "{$GREEN}✓ $message{$RESET}\n";
}

function error($message) {
    global $RED, $RESET;
    echo "{$RED}✗ $message{$RESET}\n";
}

function info($message) {
    global $YELLOW, $RESET;
    echo "{$YELLOW}ℹ $message{$RESET}\n";
}

// Initialize Flight database connection
Flight::register('db', 'PDO', [$pdo]);

// Test data
$TEST_USER_EMAIL = '<EMAIL>';
$TEST_ADMIN_ID = 1; // Assuming admin user ID is 1

echo "=== Unified Invoice Generation Test Script ===\n\n";

// Step 1: Ensure invoice types exist
echo "Step 1: Checking invoice type configurations...\n";

// First check if we're using config_invoice_types or invoice_types table
$stmt = $pdo->query("SHOW TABLES LIKE 'invoice_types'");
$invoiceTypesTableExists = $stmt->rowCount() > 0;

$stmt = $pdo->query("SHOW TABLES LIKE 'config_invoice_types'");
$configInvoiceTypesExists = $stmt->rowCount() > 0;

debug("invoice_types table exists: " . ($invoiceTypesTableExists ? 'Yes' : 'No'));
debug("config_invoice_types table exists: " . ($configInvoiceTypesExists ? 'Yes' : 'No'));

// We need to create invoice_types table or update the service to use config_invoice_types
if (!$invoiceTypesTableExists && $configInvoiceTypesExists) {
    info("Creating invoice_types view to map to config_invoice_types...");
    
    try {
        $pdo->exec("
            CREATE OR REPLACE VIEW invoice_types AS
            SELECT 
                id,
                prefix as code,
                name,
                1 as is_active,
                NOW() as created_at,
                NOW() as updated_at
            FROM config_invoice_types
        ");
        success("Created invoice_types view");
    } catch (Exception $e) {
        error("Failed to create view: " . $e->getMessage());
        info("Continuing anyway...");
    }
}

// Ensure invoice types exist for our test
$invoiceTypes = [
    ['code' => 'RET', 'prefix' => 'RET', 'name' => 'Rétrocession'],
    ['code' => 'LOY', 'prefix' => 'LOY', 'name' => 'Loyer'],
    ['code' => 'LOC', 'prefix' => 'LOC', 'name' => 'Location']
];

foreach ($invoiceTypes as $type) {
    $stmt = $pdo->prepare("SELECT id FROM config_invoice_types WHERE prefix = :prefix");
    $stmt->execute(['prefix' => $type['prefix']]);
    
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("
            INSERT INTO config_invoice_types (prefix, name, created_at, updated_at) 
            VALUES (:prefix, :name, NOW(), NOW())
        ");
        $stmt->execute(['prefix' => $type['prefix'], 'name' => $type['name']]);
        success("Created invoice type: {$type['name']}");
    } else {
        info("Invoice type {$type['name']} already exists");
    }
}

pauseIfDebug();

// Step 2: Create or get test user
echo "\nStep 2: Setting up test user...\n";

$stmt = $pdo->prepare("SELECT id FROM users WHERE email = :email");
$stmt->execute(['email' => $TEST_USER_EMAIL]);
$testUser = $stmt->fetch();

if (!$testUser) {
    $stmt = $pdo->prepare("
        INSERT INTO users (
            first_name, last_name, email, password, 
            company_name, address, postal_code, city, country,
            is_active, created_at
        ) VALUES (
            'Test', 'Generator', :email, 'not-used',
            'Test Invoice Company', '123 Test Street', 'L-1234', 'Luxembourg', 'Luxembourg',
            1, NOW()
        )
    ");
    $stmt->execute(['email' => $TEST_USER_EMAIL]);
    $testUserId = $pdo->lastInsertId();
    success("Created test user (ID: $testUserId)");
} else {
    $testUserId = $testUser['id'];
    info("Using existing test user (ID: $testUserId)");
}

// Add user to practitioner group (ID: 4) for RET invoices
$stmt = $pdo->prepare("
    INSERT IGNORE INTO user_groups_members (user_id, group_id) 
    VALUES (:user_id, 4)
");
$stmt->execute(['user_id' => $testUserId]);

// Add user to coach group (ID: 24) for LOC invoices
$stmt = $pdo->prepare("
    INSERT IGNORE INTO user_groups_members (user_id, group_id) 
    VALUES (:user_id, 24)
");
$stmt->execute(['user_id' => $testUserId]);

debug("Test user setup complete", ['user_id' => $testUserId]);
pauseIfDebug();

// Step 3: Test each invoice type
$typesToTest = $SPECIFIC_TYPE ? [$SPECIFIC_TYPE] : ['RET', 'LOY', 'LOC'];
$currentMonth = (int)date('n');
$currentYear = (int)date('Y');

foreach ($typesToTest as $invoiceType) {
    echo "\n=== Testing $invoiceType Invoice Generation ===\n";
    
    // Clean up any existing invoice for this period
    $stmt = $pdo->prepare("
        DELETE ugi FROM user_generated_invoices ugi
        WHERE user_id = :user_id 
        AND invoice_type = :type 
        AND period_month = :month 
        AND period_year = :year
    ");
    $stmt->execute([
        'user_id' => $testUserId,
        'type' => $invoiceType,
        'month' => $currentMonth,
        'year' => $currentYear
    ]);
    
    // Set up test data based on type
    switch ($invoiceType) {
        case 'RET':
            setupRetrocessionData($pdo, $testUserId, $currentMonth, $currentYear);
            break;
        case 'LOY':
            setupLoyerData($pdo, $testUserId);
            break;
        case 'LOC':
            setupLocationData($pdo, $testUserId, $currentMonth, $currentYear);
            break;
    }
    
    pauseIfDebug("Test data created for $invoiceType. Ready to generate invoice?");
    
    // Generate invoice
    try {
        info("Generating $invoiceType invoice...");
        
        $generator = new UnifiedInvoiceGenerator(
            $testUserId,
            $invoiceType,
            $currentMonth,
            $currentYear,
            $TEST_ADMIN_ID
        );
        
        debug("Generator initialized", [
            'user_id' => $testUserId,
            'type' => $invoiceType,
            'month' => $currentMonth,
            'year' => $currentYear
        ]);
        
        $result = $generator->generate();
        
        if ($result['success']) {
            success("Invoice generated successfully!");
            
            if (isset($result['invoice'])) {
                $invoice = $result['invoice'];
                echo "  Invoice Number: " . ($invoice['invoice_number'] ?? 'N/A') . "\n";
                echo "  Total: €" . number_format($invoice['total_ttc'] ?? 0, 2) . "\n";
                
                // Get invoice lines
                $stmt = $pdo->prepare("
                    SELECT description, quantity, unit_price, vat_rate, line_total 
                    FROM invoice_lines 
                    WHERE invoice_id = :invoice_id
                    ORDER BY display_order
                ");
                $stmt->execute(['invoice_id' => $invoice['id']]);
                $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if ($lines) {
                    echo "  Lines:\n";
                    foreach ($lines as $line) {
                        echo "    - {$line['description']}: ";
                        echo "{$line['quantity']} × €" . number_format($line['unit_price'], 2);
                        echo " = €" . number_format($line['line_total'], 2);
                        if ($line['vat_rate'] > 0) {
                            echo " (VAT {$line['vat_rate']}%)";
                        }
                        echo "\n";
                    }
                }
            }
        } else {
            error("Failed to generate invoice: " . $result['message']);
            debug("Full result", $result);
        }
        
    } catch (Exception $e) {
        error("Exception during generation: " . $e->getMessage());
        debug("Stack trace", $e->getTraceAsString());
    }
    
    pauseIfDebug();
}

// Cleanup if requested
if ($CLEANUP) {
    echo "\n=== Cleaning up test data ===\n";
    
    // Delete test invoices
    $stmt = $pdo->prepare("
        DELETE i FROM invoices i
        JOIN user_generated_invoices ugi ON i.id = ugi.invoice_id
        WHERE ugi.user_id = :user_id
    ");
    $stmt->execute(['user_id' => $testUserId]);
    
    // Delete test data
    $tables = [
        'user_generated_invoices',
        'user_monthly_retrocession_amounts',
        'user_financial_obligations',
        'user_monthly_course_counts',
        'user_retrocession_settings',
        'user_courses'
    ];
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("DELETE FROM $table WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $testUserId]);
    }
    
    // Delete test user
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $testUserId]);
    
    success("Test data cleaned up");
}

echo "\n=== Test Complete ===\n";

// Helper functions to set up test data

function setupRetrocessionData($pdo, $userId, $month, $year) {
    global $DEBUG;
    
    info("Setting up retrocession data...");
    
    // Create retrocession settings
    $stmt = $pdo->prepare("
        INSERT INTO user_retrocession_settings (
            user_id, cns_type, cns_value, patient_type, patient_value, 
            cns_label, patient_label, valid_from, created_by, created_at
        ) VALUES (
            :user_id, 'percentage', 20.00, 'percentage', 20.00,
            'CNS Services', 'Patient Services', CURDATE(), 1, NOW()
        )
        ON DUPLICATE KEY UPDATE
            cns_value = 20.00,
            patient_value = 20.00
    ");
    $stmt->execute(['user_id' => $userId]);
    
    // Create monthly amounts
    $stmt = $pdo->prepare("
        INSERT INTO user_monthly_retrocession_amounts (
            user_id, month, year, cns_amount, patient_amount, created_at
        ) VALUES (
            :user_id, :month, :year, 5000.00, 3000.00, NOW()
        )
        ON DUPLICATE KEY UPDATE
            cns_amount = 5000.00,
            patient_amount = 3000.00
    ");
    $stmt->execute([
        'user_id' => $userId,
        'month' => $month,
        'year' => $year
    ]);
    
    debug("Retrocession data created", [
        'cns_amount' => 5000.00,
        'patient_amount' => 3000.00,
        'percentages' => [20, 20]
    ]);
    
    success("Retrocession data ready");
}

function setupLoyerData($pdo, $userId) {
    global $DEBUG;
    
    info("Setting up loyer data...");
    
    $stmt = $pdo->prepare("
        INSERT INTO user_financial_obligations (
            user_id, rent_amount, charges_amount, 
            secretary_tvac_17, created_at
        ) VALUES (
            :user_id, 1200.00, 150.00, 
            500.00, NOW()
        )
        ON DUPLICATE KEY UPDATE
            rent_amount = 1200.00,
            charges_amount = 150.00,
            secretary_tvac_17 = 500.00
    ");
    $stmt->execute(['user_id' => $userId]);
    
    debug("Loyer data created", [
        'rent' => 1200.00,
        'charges' => 150.00,
        'secretary' => 500.00
    ]);
    
    success("Loyer data ready");
}

function setupLocationData($pdo, $userId, $month, $year) {
    global $DEBUG;
    
    info("Setting up location/course data...");
    
    // Create a test course
    $stmt = $pdo->prepare("
        INSERT INTO user_courses (
            user_id, course_name, hourly_rate, vat_rate, 
            is_active, created_at
        ) VALUES (
            :user_id, 'Test Yoga Course', 60.00, 17.00,
            1, NOW()
        )
        ON DUPLICATE KEY UPDATE
            hourly_rate = 60.00,
            vat_rate = 17.00
    ");
    $stmt->execute(['user_id' => $userId]);
    $courseId = $pdo->lastInsertId() ?: getCourseId($pdo, $userId, 'Test Yoga Course');
    
    // Create monthly course counts
    $stmt = $pdo->prepare("
        INSERT INTO user_monthly_course_counts (
            user_id, course_id, month, year, 
            hours_count, created_at
        ) VALUES (
            :user_id, :course_id, :month, :year,
            25, NOW()
        )
        ON DUPLICATE KEY UPDATE
            hours_count = 25
    ");
    $stmt->execute([
        'user_id' => $userId,
        'course_id' => $courseId,
        'month' => $month,
        'year' => $year
    ]);
    
    debug("Location data created", [
        'course' => 'Test Yoga Course',
        'hours' => 25,
        'rate' => 60.00,
        'vat' => 17.00
    ]);
    
    success("Location data ready");
}

function getCourseId($pdo, $userId, $courseName) {
    $stmt = $pdo->prepare("
        SELECT id FROM user_courses 
        WHERE user_id = :user_id AND course_name = :name
    ");
    $stmt->execute(['user_id' => $userId, 'name' => $courseName]);
    return $stmt->fetchColumn();
}