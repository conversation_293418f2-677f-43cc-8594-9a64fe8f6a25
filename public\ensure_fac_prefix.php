<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Ensuring FAC- Prefix for RET25/RET30</h2>";
    
    // Update config_invoice_types to use FAC- prefix
    $updates = [
        ['code' => 'ret25', 'prefix' => 'FAC-RET25', 'name' => ['fr' => 'Rétrocession 5%', 'en' => 'Retrocession 5%']],
        ['code' => 'ret30', 'prefix' => 'FAC-RET30', 'name' => ['fr' => 'Rétrocession 10%', 'en' => 'Retrocession 10%']]
    ];
    
    foreach ($updates as $update) {
        // Check if exists
        $stmt = $db->prepare("SELECT id, prefix FROM config_invoice_types WHERE code = :code");
        $stmt->execute(['code' => $update['code']]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            // Update prefix if needed
            if ($existing['prefix'] !== $update['prefix']) {
                $stmt = $db->prepare("
                    UPDATE config_invoice_types 
                    SET prefix = :prefix 
                    WHERE code = :code
                ");
                $stmt->execute(['prefix' => $update['prefix'], 'code' => $update['code']]);
                echo "<p style='color: green;'>✓ Updated {$update['code']} prefix from '{$existing['prefix']}' to '{$update['prefix']}'</p>";
            } else {
                echo "<p>✓ {$update['code']} already has correct prefix: {$update['prefix']}</p>";
            }
        } else {
            // Create new
            $stmt = $db->prepare("
                INSERT INTO config_invoice_types (code, prefix, name, color, is_active)
                VALUES (:code, :prefix, :name, '#17a2b8', 1)
            ");
            $stmt->execute([
                'code' => $update['code'],
                'prefix' => $update['prefix'],
                'name' => json_encode($update['name'])
            ]);
            echo "<p style='color: green;'>✓ Created {$update['code']} with prefix {$update['prefix']}</p>";
        }
    }
    
    // Show current state
    echo "<h3>Current RET Types in config_invoice_types:</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE code LIKE 'ret%' ORDER BY code");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Code</th><th>Prefix</th><th>Name</th></tr>";
    foreach ($types as $type) {
        $name = json_decode($type['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $type['name']) : $type['name'];
        echo "<tr>";
        echo "<td>{$type['code']}</td>";
        echo "<td><strong>{$type['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<p><strong>Invoice Number Format:</strong></p>";
    echo "<ul>";
    echo "<li>5% Secretary: FAC-RET25-2025-0001</li>";
    echo "<li>10% Secretary: FAC-RET30-2025-0001</li>";
    echo "</ul>";
    echo "<p><a href='/fit/public/invoices/bulk-generation?tab=retrocession'>Test Bulk Generation</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}