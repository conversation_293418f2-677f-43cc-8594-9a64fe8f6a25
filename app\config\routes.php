<?php
/**
 * Application Routes
 */

use App\Helpers\Language;

// Middleware to set language from user preferences
Flight::before('start', function(&$params, &$output){
    // Default language if none set
    $defaultLanguage = 'fr';
    
    // Check if user is logged in and has language preference
    if (isset($_SESSION['user']) && isset($_SESSION['user']['language'])) {
        Language::setLanguage($_SESSION['user']['language']);
        $_SESSION['user_language'] = $_SESSION['user']['language'];
    } elseif (isset($_SESSION['user_language'])) {
        Language::setLanguage($_SESSION['user_language']);
    } else {
        // Set default language if no preference exists
        Language::setLanguage($defaultLanguage);
        $_SESSION['user_language'] = $defaultLanguage;
    }
    
    // Check for language switch in URL
    if (isset($_GET['lang']) && in_array($_GET['lang'], ['fr', 'en', 'de'])) {
        Language::setLanguage($_GET['lang']);
        $_SESSION['user_language'] = $_GET['lang'];
        
        // Update user preference if logged in
        if (isset($_SESSION['user']['id'])) {
            try {
                $db = Flight::db();
                $stmt = $db->prepare("UPDATE users SET language = ? WHERE id = ?");
                $stmt->execute([$_GET['lang'], $_SESSION['user']['id']]);
                $_SESSION['user']['language'] = $_GET['lang'];
            } catch (Exception $e) {
                // Ignore errors
            }
        }
    }
});

// Home route with enhanced error handling
Flight::route('/', function() {
    try {
        $controller = new \App\Controllers\DashboardController();
        
        // Start output buffering to capture the echo from render()
        ob_start();
        
        // Call index which will echo the output
        $controller->index();
        
        // Get the output
        $output = ob_get_clean();
        
        // Send it through Flight's response system
        echo $output;
        
    } catch (Exception $e) {
        // Clean any output buffers
        while (ob_get_level() > 0) {
            ob_end_clean();
        }
        
        // Log the error
        error_log('Dashboard Route Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        
        // Re-throw to let Flight's error handler display it
        throw $e;
    } catch (Error $e) {
        // Catch PHP 7+ Error types (like TypeError)
        while (ob_get_level() > 0) {
            ob_end_clean();
        }
        
        error_log('Dashboard Route Error (Error): ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        
        // Convert to Exception for Flight's error handler
        throw new Exception($e->getMessage(), $e->getCode(), $e);
    }
});

// Health check
Flight::route('/health', function(){
    try {
        // Test database connection
        $db = Flight::db();
        $db->query('SELECT 1');
        
        Flight::json([
            'status' => 'ok',
            'time' => date('Y-m-d H:i:s'),
            'database' => 'connected'
        ]);
    } catch (Exception $e) {
        Flight::json([
            'status' => 'error',
            'message' => 'Database connection failed'
        ], 500);
    }
});

// API Routes Group
Flight::group('/api', function(){
    // API middleware would go here
    
    // VAT rates endpoint
    Flight::route('GET /vat-rates', [new App\Controllers\ApiController(), 'getVatRates']);
    
    // Search billable entities
    Flight::route('GET /search-billable', [new App\Controllers\ApiController(), 'searchBillable']);
    
    // Search catalog items
    Flight::route('GET /catalog/search', [new App\Controllers\ApiController(), 'searchCatalog']);
    
    // Invoice templates
    Flight::route('GET /invoice-templates', [new App\Controllers\ApiController(), 'getInvoiceTemplates']);
    Flight::route('GET /invoice-templates/@id:[0-9]+/details', [new App\Controllers\ApiController(), 'getTemplateDetails']);
    
    // Dashboard API endpoints
    Flight::route('GET /dashboard/stats', [new \App\Controllers\DashboardController(), 'getStats']);
    Flight::route('GET /dashboard/revenue-chart', [new \App\Controllers\DashboardController(), 'getRevenueChart']);
    Flight::route('GET /dashboard/invoice-status-chart', [new \App\Controllers\DashboardController(), 'getInvoiceStatusChart']);
    Flight::route('GET /dashboard/recent-activities', [new \App\Controllers\DashboardController(), 'getRecentActivities']);
    Flight::route('GET /dashboard/recent-invoices', [new \App\Controllers\DashboardController(), 'getRecentInvoices']);
});

// Include module routes
$modules = ['auth', 'users', 'invoices', 'payments', 'config', 'patients', 'clients', 'products', 'pos', 'packages', 'sales'];
foreach ($modules as $module) {
    $routeFile = __DIR__ . "/../modules/{$module}/routes.php";
    if (file_exists($routeFile)) {
        require $routeFile;
    }
}