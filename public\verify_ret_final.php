<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Final RET Configuration Verification</h2>";
    
    // Check config_invoice_types
    echo "<h3>config_invoice_types - RET entries:</h3>";
    $stmt = $db->query("
        SELECT * FROM config_invoice_types 
        WHERE code IN ('ret', 'ret25', 'ret30') 
        ORDER BY 
            CASE code 
                WHEN 'ret' THEN 1 
                WHEN 'ret25' THEN 2 
                WHEN 'ret30' THEN 3 
            END
    ");
    $configTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>ID</th><th>Code</th><th>Prefix</th><th>Name</th><th>Color</th><th>Active</th><th>Check</th>";
    echo "</tr>";
    
    $ret25Found = false;
    $ret30Found = false;
    $ret25Correct = false;
    $ret30Correct = false;
    
    foreach ($configTypes as $row) {
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        
        $check = '✓';
        $checkColor = 'green';
        $rowStyle = '';
        
        if ($row['code'] == 'ret25') {
            $ret25Found = true;
            $rowStyle = 'background-color: #d4edda;';
            if ($row['prefix'] == 'FAC-RET25') {
                $ret25Correct = true;
            } else {
                $check = '✗ Wrong prefix';
                $checkColor = 'red';
            }
        } elseif ($row['code'] == 'ret30') {
            $ret30Found = true;
            $rowStyle = 'background-color: #cce5ff;';
            if ($row['prefix'] == 'FAC-RET30') {
                $ret30Correct = true;
            } else {
                $check = '✗ Wrong prefix';
                $checkColor = 'red';
            }
        }
        
        echo "<tr style='$rowStyle'>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td><strong>{$row['prefix']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td><span style='display: inline-block; width: 20px; height: 20px; background-color: {$row['color']}; border: 1px solid #ccc;'></span></td>";
        echo "<td>" . ($row['is_active'] ? '✓' : '✗') . "</td>";
        echo "<td style='color: $checkColor; font-weight: bold;'>$check</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Summary
    echo "<h3>Configuration Summary:</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<ul>";
    echo "<li>ret25 (5% secretary): " . 
         ($ret25Found ? 
            ($ret25Correct ? "<span style='color: green;'>✓ Found with correct prefix FAC-RET25</span>" : "<span style='color: red;'>✗ Found but wrong prefix</span>") 
            : "<span style='color: red;'>✗ Not found</span>") . "</li>";
    echo "<li>ret30 (10% secretary): " . 
         ($ret30Found ? 
            ($ret30Correct ? "<span style='color: green;'>✓ Found with correct prefix FAC-RET30</span>" : "<span style='color: red;'>✗ Found but wrong prefix</span>") 
            : "<span style='color: red;'>✗ Not found</span>") . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test the actual invoice generation logic
    echo "<h3>Testing Invoice Type Selection:</h3>";
    
    // Simulate for 5%
    echo "<div style='background-color: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4 style='margin-top: 0;'>Frank Huet (5% secretary):</h4>";
    
    $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE code = 'ret25'");
    $stmt->execute();
    $type25 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($type25) {
        echo "<p>✓ Code 'ret25' found</p>";
        echo "<p>✓ Prefix: <strong>{$type25['prefix']}</strong></p>";
        echo "<p>✓ Will generate: <strong style='font-size: 1.2em;'>{$type25['prefix']}-2025-XXXX</strong></p>";
    } else {
        echo "<p style='color: red;'>✗ Code 'ret25' NOT FOUND - This will cause errors!</p>";
    }
    echo "</div>";
    
    // Simulate for 10%
    echo "<div style='background-color: #cce5ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4 style='margin-top: 0;'>User with 10% secretary:</h4>";
    
    $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE code = 'ret30'");
    $stmt->execute();
    $type30 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($type30) {
        echo "<p>✓ Code 'ret30' found</p>";
        echo "<p>✓ Prefix: <strong>{$type30['prefix']}</strong></p>";
        echo "<p>✓ Will generate: <strong style='font-size: 1.2em;'>{$type30['prefix']}-2025-XXXX</strong></p>";
    } else {
        echo "<p style='color: red;'>✗ Code 'ret30' NOT FOUND - This will cause errors!</p>";
    }
    echo "</div>";
    
    // Final status
    if ($ret25Found && $ret25Correct && $ret30Found && $ret30Correct) {
        echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ Configuration is PERFECT!</h3>";
        echo "<p style='font-size: 1.1em; margin-bottom: 0;'>The system is ready to generate:</p>";
        echo "<ul style='font-size: 1.1em; margin-bottom: 0;'>";
        echo "<li><strong>FAC-RET25-2025-XXXX</strong> for 5% secretary fee</li>";
        echo "<li><strong>FAC-RET30-2025-XXXX</strong> for 10% secretary fee</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p style='text-align: center; margin-top: 30px;'>";
        echo "<a href='/fit/public/invoices/bulk-generation?tab=retrocession' style='display: inline-block; padding: 15px 40px; font-size: 1.3em; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;'>🎉 Generate Retrocession Invoices Now!</a>";
        echo "</p>";
    } else {
        echo "<div style='background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>⚠️ Configuration Issues Detected</h3>";
        echo "<p>Please run the rebuild script again or contact support.</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}