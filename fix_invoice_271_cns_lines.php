<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Fixing CNS Lines for Invoice ID: $invoiceId</h2>";
    
    // First, verify this is Rémi Heine's invoice
    $userSql = "SELECT i.*, u.first_name, u.last_name 
                FROM invoices i
                LEFT JOIN users u ON i.user_id = u.id
                WHERE i.id = :invoice_id";
    $userStmt = $db->prepare($userSql);
    $userStmt->execute(['invoice_id' => $invoiceId]);
    $invoice = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Invoice Details:</h3>";
    echo "<ul>";
    echo "<li>Number: {$invoice['invoice_number']}</li>";
    echo "<li>User: {$invoice['first_name']} {$invoice['last_name']}</li>";
    echo "<li>Total: €" . number_format($invoice['total'], 2) . "</li>";
    echo "</ul>";
    
    // Check if it's Rémi Heine
    if ($invoice['first_name'] !== 'Rémi' || $invoice['last_name'] !== 'Heine') {
        echo "<p style='color: orange;'>This invoice is not for Rémi Heine. No changes needed.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ This is Rémi Heine's invoice - proceeding with CNS line update</p>";
    
    // Now check for invoice items
    $itemsSql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id ORDER BY id";
    $itemsStmt = $db->prepare($itemsSql);
    $itemsStmt->execute(['invoice_id' => $invoiceId]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($items)) {
        echo "<p style='color: red;'>No invoice items found! The invoice might be missing its line items.</p>";
        
        // Try to recreate the lines based on the invoice total
        echo "<h3>Attempting to recreate invoice lines...</h3>";
        
        // For a RET25 invoice, we typically have:
        // - Retrocession CNS line (20% of base)
        // - Secretary fees line (5% of base with 17% VAT)
        
        // Calculate base amount from total
        // Total = CNS (20% of base) + Secretary (5% of base * 1.17)
        // Total = 0.20 * base + 0.05 * base * 1.17
        // Total = 0.20 * base + 0.0585 * base
        // Total = 0.2585 * base
        // base = Total / 0.2585
        
        $total = floatval($invoice['total']);
        $baseAmount = $total / 0.2585;
        
        echo "<p>Calculated base amount: €" . number_format($baseAmount, 2) . "</p>";
        
        // Insert CNS line (for Rémi Heine, without "CNS")
        $insertCNS = "INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, vat_rate, vat_amount, total, base_amount) 
                      VALUES (:invoice_id, :description, 1, :unit_price, 0, 0, :total, :base_amount)";
        
        $cnsAmount = $baseAmount * 0.20;
        $stmt = $db->prepare($insertCNS);
        $stmt->execute([
            'invoice_id' => $invoiceId,
            'description' => 'RÉTROCESSION 20.00%',  // Without CNS for Rémi Heine
            'unit_price' => $cnsAmount,
            'total' => $cnsAmount,
            'base_amount' => $baseAmount
        ]);
        
        echo "<p>✓ Created RÉTROCESSION line: €" . number_format($cnsAmount, 2) . "</p>";
        
        // Insert Secretary line
        $insertSecretary = "INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, vat_rate, vat_amount, total, base_amount) 
                           VALUES (:invoice_id, :description, 1, :unit_price, 17, :vat_amount, :total, :base_amount)";
        
        $secretaryBase = $baseAmount * 0.05;
        $secretaryVat = $secretaryBase * 0.17;
        $secretaryTotal = $secretaryBase + $secretaryVat;
        
        $stmt = $db->prepare($insertSecretary);
        $stmt->execute([
            'invoice_id' => $invoiceId,
            'description' => 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL 5.00%',
            'unit_price' => $secretaryBase,
            'vat_amount' => $secretaryVat,
            'total' => $secretaryTotal,
            'base_amount' => $baseAmount
        ]);
        
        echo "<p>✓ Created Secretary line: €" . number_format($secretaryTotal, 2) . " (Base: €" . number_format($secretaryBase, 2) . " + VAT: €" . number_format($secretaryVat, 2) . ")</p>";
        
    } else {
        echo "<p>Found " . count($items) . " existing invoice items:</p>";
        
        // Display current items
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Description</th><th>Total</th><th>Action</th></tr>";
        
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['description']}</td>";
            echo "<td>€" . number_format($item['total'], 2) . "</td>";
            
            if (strpos($item['description'], 'RÉTROCESSION CNS') !== false) {
                echo "<td style='color: red;'>Will update to remove 'CNS'</td>";
            } else {
                echo "<td style='color: green;'>OK</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // Update lines containing "RÉTROCESSION CNS"
        $updateSql = "UPDATE invoice_items 
                     SET description = REPLACE(description, 'RÉTROCESSION CNS', 'RÉTROCESSION')
                     WHERE invoice_id = :invoice_id 
                     AND description LIKE '%RÉTROCESSION CNS%'";
        
        $updateStmt = $db->prepare($updateSql);
        $updateStmt->execute(['invoice_id' => $invoiceId]);
        $updatedCount = $updateStmt->rowCount();
        
        if ($updatedCount > 0) {
            echo "<h3 style='color: green;'>✓ Successfully updated $updatedCount line(s)</h3>";
        } else {
            echo "<p>No lines needed updating (already correct)</p>";
        }
    }
    
    // Update invoice subtotal and VAT if needed
    $recalcSql = "SELECT 
                    SUM(CASE WHEN vat_rate = 0 THEN total ELSE total / (1 + vat_rate/100) END) as subtotal,
                    SUM(CASE WHEN vat_rate = 0 THEN 0 ELSE total - (total / (1 + vat_rate/100)) END) as vat_amount,
                    SUM(total) as total
                  FROM invoice_items 
                  WHERE invoice_id = :invoice_id";
    
    $recalcStmt = $db->prepare($recalcSql);
    $recalcStmt->execute(['invoice_id' => $invoiceId]);
    $totals = $recalcStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($totals) {
        $updateTotalsSql = "UPDATE invoices 
                           SET subtotal = :subtotal, 
                               vat_amount = :vat_amount, 
                               total = :total 
                           WHERE id = :id";
        
        $updateTotalsStmt = $db->prepare($updateTotalsSql);
        $updateTotalsStmt->execute([
            'subtotal' => $totals['subtotal'],
            'vat_amount' => $totals['vat_amount'],
            'total' => $totals['total'],
            'id' => $invoiceId
        ]);
        
        echo "<h3>Updated Invoice Totals:</h3>";
        echo "<ul>";
        echo "<li>Subtotal: €" . number_format($totals['subtotal'], 2) . "</li>";
        echo "<li>VAT: €" . number_format($totals['vat_amount'], 2) . "</li>";
        echo "<li>Total: €" . number_format($totals['total'], 2) . "</li>";
        echo "</ul>";
    }
    
    echo "<p><a href='/fit/public/invoices/$invoiceId' class='btn' style='background: green; color: white; padding: 10px; text-decoration: none;'>View Updated Invoice</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>