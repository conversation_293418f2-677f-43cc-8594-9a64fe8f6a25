<?php
/**
 * Test Email Logging
 */

require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Services\EmailService;

echo "<h1>Test Email Logging</h1>";
echo "<pre>";

// Check email_logs table structure
$db = Flight::db();
echo "Checking email_logs table structure:\n";
$stmt = $db->query("DESCRIBE email_logs");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($columns as $col) {
    echo "  - {$col['Field']} ({$col['Type']})\n";
}

echo "\n\nCurrent email logs count:\n";
$stmt = $db->query("SELECT COUNT(*) as count FROM email_logs");
$count = $stmt->fetch(PDO::FETCH_ASSOC);
echo "Total logs: {$count['count']}\n";

// Show recent logs
echo "\nRecent email logs:\n";
$stmt = $db->query("
    SELECT 
        el.*,
        i.invoice_number
    FROM email_logs el
    LEFT JOIN invoices i ON el.invoice_id = i.id
    ORDER BY el.created_at DESC
    LIMIT 10
");
$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($logs)) {
    echo "No email logs found.\n";
} else {
    foreach ($logs as $log) {
        echo "\n---\n";
        echo "ID: {$log['id']}\n";
        echo "Invoice: {$log['invoice_number']} (ID: {$log['invoice_id']})\n";
        echo "To: {$log['recipient_email']}\n";
        echo "Subject: {$log['subject']}\n";
        echo "Status: {$log['status']}\n";
        echo "Sent at: {$log['sent_at']}\n";
        if ($log['error_message']) {
            echo "Error: {$log['error_message']}\n";
        }
    }
}

// Test email logging with a mock invoice
if (isset($_GET['test']) && $_GET['test'] === 'yes') {
    echo "\n\nTesting email logging...\n";
    
    // Get a recent invoice
    $stmt = $db->query("
        SELECT id, invoice_number 
        FROM invoices 
        ORDER BY id DESC 
        LIMIT 1
    ");
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "Using invoice: {$invoice['invoice_number']} (ID: {$invoice['id']})\n";
        
        // Create EmailService and test logging
        $emailService = new EmailService();
        
        // Mock successful result
        $result = [
            'success' => true,
            'message' => 'Test email sent',
            'subject' => 'Test Invoice Email'
        ];
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($emailService);
        $method = $reflection->getMethod('logEmail');
        $method->setAccessible(true);
        
        try {
            $method->invoke($emailService, $invoice['id'], null, '<EMAIL>', $result);
            echo "✓ Email log created successfully!\n";
            
            // Check if it was saved
            $stmt = $db->prepare("
                SELECT * FROM email_logs 
                WHERE invoice_id = :invoice_id 
                AND recipient_email = '<EMAIL>'
                ORDER BY id DESC
                LIMIT 1
            ");
            $stmt->execute([':invoice_id' => $invoice['id']]);
            $newLog = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($newLog) {
                echo "\nNew log entry:\n";
                echo "  ID: {$newLog['id']}\n";
                echo "  Subject: {$newLog['subject']}\n";
                echo "  Status: {$newLog['status']}\n";
            }
        } catch (Exception $e) {
            echo "✗ Error logging email: " . $e->getMessage() . "\n";
        }
    } else {
        echo "No invoices found to test with.\n";
    }
} else {
    echo "\n\n<a href='?test=yes'>Click here to test email logging</a>";
}

echo "</pre>";
echo "<hr>";
echo '<p><a href="/fit/public/invoices/create">Back to Create Invoice</a></p>';
?>