<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Check current labels
$stmt = $db->prepare("
    SELECT id, user_id, cns_label, patient_label, secretary_label 
    FROM user_retrocession_settings 
    WHERE user_id = 1 
    ORDER BY created_at DESC 
    LIMIT 1
");
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    echo "Current labels for user 1:\n";
    echo "CNS: " . $result['cns_label'] . "\n";
    echo "Patient: " . $result['patient_label'] . "\n";
    echo "Secretary: " . $result['secretary_label'] . "\n\n";
    
    // Check if they need updating
    $needsUpdate = false;
    $updates = [];
    
    if ($result['cns_label'] === 'RÉTROCESSION CNS') {
        $updates['cns_label'] = 'Rétrocession CNS';
        $needsUpdate = true;
    }
    
    if ($result['patient_label'] === 'RÉTROCESSION PATIENTS') {
        $updates['patient_label'] = 'Rétrocession PATIENTS';
        $needsUpdate = true;
    }
    
    if ($result['secretary_label'] === 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL') {
        $updates['secretary_label'] = 'Frais secrétariat et mise à disposition matériel';
        $needsUpdate = true;
    }
    
    if ($needsUpdate) {
        echo "Updating labels to proper case...\n";
        
        $sql = "UPDATE user_retrocession_settings SET ";
        $params = [];
        $setParts = [];
        
        foreach ($updates as $field => $value) {
            $setParts[] = "$field = :$field";
            $params[$field] = $value;
        }
        
        $sql .= implode(', ', $setParts);
        $sql .= " WHERE id = :id";
        $params['id'] = $result['id'];
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        echo "Labels updated successfully!\n";
        echo "New values:\n";
        foreach ($updates as $field => $value) {
            echo "- $field: $value\n";
        }
    } else {
        echo "Labels are already in proper case. No update needed.\n";
    }
} else {
    echo "No retrocession settings found for user 1\n";
}

// Also update any other users with uppercase labels
echo "\nChecking other users...\n";
$stmt = $db->prepare("
    SELECT COUNT(*) as count 
    FROM user_retrocession_settings 
    WHERE cns_label = 'RÉTROCESSION CNS' 
    OR patient_label = 'RÉTROCESSION PATIENTS'
    OR secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL'
");
$stmt->execute();
$count = $stmt->fetchColumn();

if ($count > 0) {
    echo "Found $count records with uppercase labels. Updating all...\n";
    
    $stmt = $db->prepare("
        UPDATE user_retrocession_settings 
        SET 
            cns_label = CASE 
                WHEN cns_label = 'RÉTROCESSION CNS' THEN 'Rétrocession CNS'
                ELSE cns_label 
            END,
            patient_label = CASE 
                WHEN patient_label = 'RÉTROCESSION PATIENTS' THEN 'Rétrocession PATIENTS'
                ELSE patient_label 
            END,
            secretary_label = CASE 
                WHEN secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL' 
                THEN 'Frais secrétariat et mise à disposition matériel'
                ELSE secretary_label 
            END
        WHERE cns_label = 'RÉTROCESSION CNS' 
        OR patient_label = 'RÉTROCESSION PATIENTS'
        OR secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL'
    ");
    $stmt->execute();
    
    echo "All records updated!\n";
}