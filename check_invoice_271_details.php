<?php
// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found\n");
}

// Parse .env file
$envVars = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) continue;
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $envVars[trim($key)] = trim($value, '"\'');
    }
}

// Get database configuration from .env
$host = $envVars['DB_HOST'] ?? 'localhost';
$dbname = $envVars['DB_DATABASE'] ?? 'healthcenter_billing';
$username = $envVars['DB_USERNAME'] ?? 'root';
$password = $envVars['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceId = 271;
    
    echo "<h2>Detailed Analysis of Invoice ID: $invoiceId</h2>";
    
    // Get invoice details with all fields
    $sql = "SELECT i.*, 
            it.name as invoice_type_name, 
            it.code as invoice_type_code,
            u.first_name, u.last_name,
            c.name as client_name
            FROM invoices i
            LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
            LEFT JOIN users u ON i.user_id = u.id
            LEFT JOIN clients c ON i.client_id = c.id
            WHERE i.id = :id";
    
    $stmt = $db->prepare($sql);
    $stmt->execute(['id' => $invoiceId]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice not found!</p>";
        exit;
    }
    
    echo "<h3>Invoice Header:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Number</td><td>{$invoice['invoice_number']}</td></tr>";
    echo "<tr><td>Type</td><td>{$invoice['invoice_type_name']} ({$invoice['invoice_type_code']})</td></tr>";
    echo "<tr><td>Status</td><td>{$invoice['status']}</td></tr>";
    echo "<tr><td>Period</td><td>{$invoice['period']}</td></tr>";
    echo "<tr><td>Issue Date</td><td>{$invoice['issue_date']}</td></tr>";
    echo "<tr><td>Due Date</td><td>{$invoice['due_date']}</td></tr>";
    
    if ($invoice['user_id']) {
        echo "<tr><td>User</td><td>{$invoice['first_name']} {$invoice['last_name']} (ID: {$invoice['user_id']})</td></tr>";
    }
    if ($invoice['client_id']) {
        echo "<tr><td>Client</td><td>{$invoice['client_name']} (ID: {$invoice['client_id']})</td></tr>";
    }
    
    echo "<tr><td>Subtotal</td><td>€ " . number_format($invoice['subtotal'], 2) . "</td></tr>";
    echo "<tr><td>VAT Amount</td><td>€ " . number_format($invoice['vat_amount'], 2) . "</td></tr>";
    echo "<tr><td>Total</td><td>€ " . number_format($invoice['total'], 2) . "</td></tr>";
    echo "</table>";
    
    // Check invoice items/lines
    echo "<h3>Invoice Lines:</h3>";
    
    // Try invoice_items table
    $itemsSql = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id ORDER BY id";
    $itemsStmt = $db->prepare($itemsSql);
    $itemsStmt->execute(['invoice_id' => $invoiceId]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($items)) {
        echo "<p>Found " . count($items) . " items in invoice_items table:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Total</th></tr>";
        
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['description']}</td>";
            echo "<td>{$item['quantity']}</td>";
            echo "<td>€ " . number_format($item['unit_price'], 2) . "</td>";
            echo "<td>{$item['vat_rate']}%</td>";
            echo "<td>€ " . number_format($item['total'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if any lines need updating
        echo "<h4>Lines containing 'RÉTROCESSION CNS':</h4>";
        $cnsLines = array_filter($items, function($item) {
            return strpos($item['description'], 'RÉTROCESSION CNS') !== false;
        });
        
        if (!empty($cnsLines)) {
            echo "<p>Found " . count($cnsLines) . " line(s) to update:</p>";
            echo "<ul>";
            foreach ($cnsLines as $line) {
                echo "<li>ID {$line['id']}: {$line['description']}</li>";
            }
            echo "</ul>";
            
            // Offer to update
            if (!isset($_GET['update'])) {
                echo "<p><a href='?update=1' style='background: green; color: white; padding: 10px; text-decoration: none;'>Click here to update these lines</a></p>";
            } else {
                // Perform update
                $updateSql = "UPDATE invoice_items 
                             SET description = REPLACE(description, 'RÉTROCESSION CNS', 'RÉTROCESSION')
                             WHERE invoice_id = :invoice_id 
                             AND description LIKE '%RÉTROCESSION CNS%'";
                
                $updateStmt = $db->prepare($updateSql);
                $updateStmt->execute(['invoice_id' => $invoiceId]);
                
                echo "<p style='color: green;'>Updated " . $updateStmt->rowCount() . " line(s) successfully!</p>";
                echo "<p><a href='check_invoice_271_details.php'>Refresh to see updated lines</a></p>";
            }
        } else {
            echo "<p>No lines found containing 'RÉTROCESSION CNS'</p>";
        }
    } else {
        echo "<p style='color: orange;'>No items found in invoice_items table</p>";
        
        // Try invoice_lines table (alternative table name)
        $linesSql = "SELECT * FROM invoice_lines WHERE invoice_id = :invoice_id ORDER BY id";
        try {
            $linesStmt = $db->prepare($linesSql);
            $linesStmt->execute(['invoice_id' => $invoiceId]);
            $lines = $linesStmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($lines)) {
                echo "<p>Found " . count($lines) . " items in invoice_lines table</p>";
                // Display lines...
            }
        } catch (PDOException $e) {
            echo "<p>invoice_lines table does not exist</p>";
        }
    }
    
    echo "<p><a href='/fit/public/invoices/$invoiceId'>View Invoice in Application</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>