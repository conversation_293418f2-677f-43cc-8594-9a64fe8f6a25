<?php
// Load environment
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) continue;
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value, '"\'');
    }
}

// Database connection
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$user = $_ENV['DB_USERNAME'] ?? 'root';
$pass = $_ENV['DB_PASSWORD'] ?? '';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Email Templates Check\n";
    echo "====================\n\n";
    
    // Check if email_templates table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() > 0) {
        echo "✓ email_templates table exists\n\n";
        
        // Count templates
        $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total templates: " . $result['count'] . "\n";
        
        if ($result['count'] == 0) {
            echo "\n✗ NO EMAIL TEMPLATES FOUND!\n";
            echo "This is why emails are not being sent.\n";
            echo "\nThe email_templates table exists but is empty.\n";
            echo "Email templates need to be added to the database.\n";
        } else {
            // Show templates
            $stmt = $db->query("SELECT * FROM email_templates LIMIT 5");
            $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "\nSample templates:\n";
            foreach ($templates as $t) {
                echo "- {$t['name']} (Type: {$t['email_type']}, Active: {$t['is_active']})\n";
            }
        }
    } else {
        echo "✗ email_templates table does NOT exist!\n";
    }
    
    echo "\n\nEmail Logs Check\n";
    echo "================\n";
    
    // Check email_logs
    $stmt = $db->query("SHOW TABLES LIKE 'email_logs'");
    if ($stmt->rowCount() > 0) {
        echo "✓ email_logs table exists\n";
        
        // Recent logs
        $stmt = $db->query("SELECT COUNT(*) as count FROM email_logs");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total logs: " . $result['count'] . "\n";
    } else {
        echo "✗ email_logs table does NOT exist\n";
    }
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}