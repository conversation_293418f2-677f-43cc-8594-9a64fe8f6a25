<?php
// Direct database connection
$host = '127.0.0.1';
$dbname = 'fitapp';
$username = 'root';
$password = 'test1234';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Invoice Number Generation</h2>";
    
    // Check the invoice with double FAC
    echo "<h3>Invoice with double FAC:</h3>";
    $stmt = $db->query("
        SELECT id, invoice_number, type_id 
        FROM invoices 
        WHERE invoice_number LIKE '%FAC-FAC%'
        LIMIT 5
    ");
    $doubleInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($doubleInvoices as $inv) {
        echo "<p>ID: {$inv['id']}, Number: <strong>{$inv['invoice_number']}</strong>, type_id: {$inv['type_id']}</p>";
    }
    
    // Check config_invoice_types
    echo "<h3>config_invoice_types prefixes:</h3>";
    $stmt = $db->query("
        SELECT id, code, prefix 
        FROM config_invoice_types 
        WHERE code IN ('ret', 'ret2', 'ret3')
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Code</th><th>Prefix</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>{$type['id']}</td>";
        echo "<td>{$type['code']}</td>";
        echo "<td><strong>{$type['prefix']}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Issue:</h3>";
    echo "<p>The prefix 'FAC-RET25' already contains 'FAC-', but the invoice generation is adding another 'FAC-' prefix.</p>";
    
    // Fix the invoices
    echo "<h3>Fixing double FAC invoices:</h3>";
    
    $stmt = $db->prepare("
        UPDATE invoices 
        SET invoice_number = REPLACE(invoice_number, 'FAC-FAC-', 'FAC-')
        WHERE invoice_number LIKE 'FAC-FAC-%'
    ");
    $stmt->execute();
    $count = $stmt->rowCount();
    
    echo "<p style='color: green;'>✓ Fixed $count invoices by removing the duplicate 'FAC-'</p>";
    
    // Show fixed invoices
    echo "<h3>After fix:</h3>";
    $stmt = $db->query("
        SELECT id, invoice_number, type_id 
        FROM invoices 
        WHERE invoice_number LIKE 'FAC-RET%'
        ORDER BY id DESC
        LIMIT 10
    ");
    $fixedInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Invoice Number</th><th>type_id</th></tr>";
    foreach ($fixedInvoices as $inv) {
        echo "<tr>";
        echo "<td>{$inv['id']}</td>";
        echo "<td><strong>{$inv['invoice_number']}</strong></td>";
        echo "<td>{$inv['type_id']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>Note:</h3>";
    echo "<p>The invoice generation code might be adding 'FAC-' to all prefixes. This needs to be checked in the invoice number generation logic.</p>";
    echo "<p>For now, the duplicate 'FAC-' has been removed from existing invoices.</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}